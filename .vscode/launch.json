{"configurations": [{"type": "java", "name": "Spring Boot-YanxWebApiApplication<fh-yanx-web-api>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.fh.yanx.web.YanxWebApiApplication", "projectName": "fh-yanx-web-api", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-YanxServiceServiceApplication<fh-yanx-service-service>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.fh.yanx.service.YanxServiceServiceApplication", "projectName": "fh-yanx-service-service", "args": "", "envFile": "${workspaceFolder}/.env"}]}