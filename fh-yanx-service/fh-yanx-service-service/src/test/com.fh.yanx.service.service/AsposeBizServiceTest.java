package com.fh.yanx.service.service;

import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSONObject;
import com.fh.yanx.service.aspose.AsposeBizService;
import com.light.base.attachment.entity.vo.AttachmentVo;

/**
 * 测试用例
 * 
 * <AUTHOR>
 * @date 2024/3/14 15:23
 */
@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
public class AsposeBizServiceTest {
    @Resource
    private AsposeBizService asposeBizService;

    @Test
    void testAsposeWord2Pdf() throws Exception {
        AttachmentVo test = asposeBizService.test();
        System.out.println("result is :" + JSONObject.toJSONString(test));
        assertNotNull(test);
    }
}
