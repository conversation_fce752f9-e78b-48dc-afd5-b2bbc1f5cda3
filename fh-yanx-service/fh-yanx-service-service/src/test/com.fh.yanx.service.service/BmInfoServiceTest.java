package com.fh.yanx.service.service;

import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.service.IBmInfoService;
import com.light.core.entity.AjaxResult;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.CountDownLatch;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.util.AssertionErrors.*;

/**
 * 单元测试
 * 
 * <AUTHOR>
 * @date 2023/6/20 11:24
 */
@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
class BmInfoServiceTest {
    @Resource
    private IBmInfoService bmInfoService;

    @Test
    void testCode() throws Exception {
        BmInfoBo bmInfoBo = new BmInfoBo();
        bmInfoBo.setInfoId(1L);
        bmInfoBo.setActivId(1L);
        String s = bmInfoService.generateNotRepeatSignInCode(bmInfoBo, false);
        assertTrue(StringUtils.isNotBlank(s));
        System.out.println("code is :" + s);
    }

}
