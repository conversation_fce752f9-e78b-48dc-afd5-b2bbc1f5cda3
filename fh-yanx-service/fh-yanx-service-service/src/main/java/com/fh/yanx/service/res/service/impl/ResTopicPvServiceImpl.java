package com.fh.yanx.service.res.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.service.consts.ConstString;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicPvDto;
import com.fh.yanx.service.res.entity.bo.ResTopicPvConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicPvBo;
import com.fh.yanx.service.res.entity.vo.ResTopicPvVo;
import com.fh.yanx.service.res.service.IResTopicPvService;
import com.fh.yanx.service.res.mapper.ResTopicPvMapper;
import com.light.core.entity.AjaxResult;
/**
 * 课题pv记录接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResTopicPvServiceImpl extends ServiceImpl<ResTopicPvMapper, ResTopicPvDto> implements IResTopicPvService {

	@Resource
	private ResTopicPvMapper resTopicPvMapper;

	@Resource
	private RedisComponent redisComponent;
	
    @Override
	public List<ResTopicPvVo> getResTopicPvListByCondition(ResTopicPvConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicPvMapper.getResTopicPvListByCondition(condition);
	}

	@Override
	public AjaxResult addResTopicPv(ResTopicPvBo resTopicPvBo) {
		ResTopicPvDto resTopicPv = new ResTopicPvDto();
		BeanUtils.copyProperties(resTopicPvBo, resTopicPv);
		resTopicPv.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resTopicPv)){
			Long topicId = resTopicPv.getTopicId();
			redisComponent.incr(ConstString.PV_KEY.concat(topicId.toString()),1);
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResTopicPv(ResTopicPvBo resTopicPvBo) {
		ResTopicPvDto resTopicPv = new ResTopicPvDto();
		BeanUtils.copyProperties(resTopicPvBo, resTopicPv);
		if(updateById(resTopicPv)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResTopicPvVo getDetail(Long id) {
		ResTopicPvConditionBo condition = new ResTopicPvConditionBo();
		condition.setPvId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResTopicPvVo> list = resTopicPvMapper.getResTopicPvListByCondition(condition);
		ResTopicPvVo vo = new ResTopicPvVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}


	/**
	 * 获取主题的浏览量
	 * @param topicId
	 * @return
	 */
	public Integer getPVByCache(Long topicId) {
		Object object = redisComponent.get(ConstString.PV_KEY.concat(topicId.toString()));
		if (null != object) {
			return JSONObject.parseObject(object.toString(), Integer.class);
		} else {
			return 0;
		}
	}
	/**
	* 新增缓存PV
	*
	* @param topicId
	* @return void
	* <AUTHOR>
	* @date 2023/2/6 17:38
	*/
	public void addCachePV(Long topicId) {
		redisComponent.incr(ConstString.PV_KEY.concat(topicId.toString()), 1);
	}

}