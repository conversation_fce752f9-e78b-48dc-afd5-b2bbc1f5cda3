package com.fh.yanx.service.utils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2022/6/24 17:24
 */
public class ListKit {
    /**
     * List<String> -> List<Integer>
     */
    public static List<Integer> convertList(List<String> list) {
        List<Integer> retList = new ArrayList<Integer>();
        if (CollectionUtils.isEmpty(list)) {
            return retList;
        } else {
            for (String s : list) {
                retList.add(Integer.valueOf(s));
            }
        }
        return retList;
    }

    /**
     * List<String> -> List<Long>
     */
    public static List<Long> convertList2Long(List<String> list) {
        List<Long> retList = new ArrayList<Long>();
        if (CollectionUtils.isEmpty(list)) {
            return retList;
        } else {
            for (String s : list) {
                retList.add(Long.valueOf(s));
            }
        }
        return retList;
    }

    /**
     * 去除数组的重复数据
     */
    public static Integer[] removerDuplicateArray(Integer[] objs) {
        if (objs == null || objs.length <= 0) {
            return objs;
        }
        Set<Integer> set = new HashSet<Integer>();
        for (Integer obj : objs) {
            if (obj != null) {
                set.add(obj);
            }
        }
        Integer[] ret = new Integer[set.size()];
        Object[] objArray = set.toArray();
        for (int i = 0; objArray != null && i < objArray.length; i++) {
            ret[i] = (Integer)objArray[i];
        }
        return ret;
    }

    /**
     * Integer[] -> List<Integer>
     *
     * @param args
     */
    public static List<Integer> array2list(Integer[] array) {
        List<Integer> list = new ArrayList<Integer>();
        if (array == null || array.length <= 0) {
            return list;
        }
        CollectionUtils.addAll(list, array);
        return list;
    }

    /**
     * String[] -> List<String>
     *
     * @param args
     */
    public static List<String> array2list(String[] array) {
        List<String> list = new ArrayList<String>();
        if (array == null || array.length <= 0) {
            return list;
        }
        CollectionUtils.addAll(list, array);
        return list;
    }

    /**
     * String[] -> List<Integer>
     *
     * @param args
     */
    public static List<Integer> array2listInteger(String[] array) {
        List<Integer> list = new ArrayList<Integer>();
        if (array != null && array.length > 0) {
            for (String str : array) {
                list.add(Integer.valueOf(str));
            }
        }
        return list;
    }

    /**
     * list转数组
     *
     * @param list the list
     * @return integer [ ]
     * <AUTHOR>
     * @date 2018 -03-13 15:58:32
     */
    public static Integer[] list2ArrayInteger(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new Integer[] {};
        }
        Integer[] result = new Integer[list.size()];
        return list.toArray(result);
    }

    /**
     * 移除重复数据
     * 
     * @param list
     */
    public static void removeDuplicate(List list) {
        LinkedHashSet h = new LinkedHashSet(list);
        list.clear();
        list.addAll(h);

    }
}
