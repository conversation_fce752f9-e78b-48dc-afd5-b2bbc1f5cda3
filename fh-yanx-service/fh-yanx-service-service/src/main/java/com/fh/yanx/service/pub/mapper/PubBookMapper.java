package com.fh.yanx.service.pub.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.pub.entity.dto.PubBookDto;
import com.fh.yanx.service.pub.entity.bo.PubBookConditionBo;
import com.fh.yanx.service.pub.entity.vo.PubBookVo;
import org.apache.ibatis.annotations.Param;

/**
 * 融合出版书Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface PubBookMapper extends BaseMapper<PubBookDto> {

	List<PubBookVo> getPubBookListByCondition(PubBookConditionBo condition);

	PubBookVo getPubBookByCondition(PubBookConditionBo condition);

	/**
	 * 增加书籍阅读次数
	 * @param bookId
	 */
	void incrPubBookReadTimes(@Param("bookId") Long bookId);

}
