package com.fh.yanx.service.res.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.enums.ResTopicAttachmentEnum;
import com.fh.yanx.service.enums.ResTopicProcessEnum;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CreateTodoEvent;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.entity.dto.ResTopicStudentDto;
import com.fh.yanx.service.res.service.*;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import com.light.user.organization.api.OrganizationApi;
import com.light.user.organization.entity.bo.OrganizationConditionBo;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicRepAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicRepAttachmentVo;
import com.fh.yanx.service.res.mapper.ResTopicRepAttachmentMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 结题答辩附件表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Service
public class ResTopicRepAttachmentServiceImpl extends ServiceImpl<ResTopicRepAttachmentMapper, ResTopicRepAttachmentDto>
        implements IResTopicRepAttachmentService {

    @Resource
    private ResTopicRepAttachmentMapper resTopicRepAttachmentMapper;
    @Resource
    private IResTopicStudentService resTopicStudentService;

    @Resource
    private OrganizationApi organizationApi;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private IResTopicService resTopicService;
    @Resource
    private IResTopicTeacherService resTopicTeacherService;

    @Override
    public List<ResTopicRepAttachmentVo>
    getResTopicRepAttachmentListByCondition(ResTopicRepAttachmentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<ResTopicRepAttachmentVo> repAttachmentVos =
                resTopicRepAttachmentMapper.getResTopicRepAttachmentListByCondition(condition);
        // 封装学生，及学校。浏览量待定

        if (CollectionUtils.isNotEmpty(repAttachmentVos)) {
            List<Long> organizationIds = repAttachmentVos.stream()
                    .filter(x -> ResTopicAttachmentEnum.RS_SOURCE_IN.getValue().equals(x.getRsSource()))
                    .map(ResTopicRepAttachmentVo::getOrganizationId).collect(Collectors.toList());
            // 封装学校
            Map<Long, String> organizationIdNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(organizationIds)) {
                // 获取论文课题对应的所有组织学校
                OrganizationConditionBo conditionBo = new OrganizationConditionBo();
                conditionBo.setPageNo(SystemConstants.NO_PAGE);
                conditionBo.setOrganizationIds(organizationIds);
                AjaxResult ajaxResult = organizationApi.getOrganizationListByCondition(conditionBo);
                if (ajaxResult.isSuccess() && null != ajaxResult.getData()) {
                    Map<String, Object> map = (Map<String, Object>) ajaxResult.getData();
                    List<OrganizationVo> organizationVos =
                            JSONObject.parseArray(JSONObject.toJSONString(map.get("list")), OrganizationVo.class);
                    organizationIdNameMap = organizationVos.stream()
                            .collect(Collectors.toMap(OrganizationVo::getId, OrganizationVo::getName));
                }
            }
            // 循环封装学校
            for (ResTopicRepAttachmentVo repAttachmentVo : repAttachmentVos) {
                if (ResTopicAttachmentEnum.RS_SOURCE_IN.getValue().equals(repAttachmentVo.getRsSource())) {
                    repAttachmentVo.setOrganizationName(organizationIdNameMap.get(repAttachmentVo.getOrganizationId()));
                } else {
                    repAttachmentVo.setOrganizationName(repAttachmentVo.getRepOrganizationName());
                }
            }
            // 封装学生
            List<Long> topicIds = repAttachmentVos.stream().filter(x -> null != x.getTopicId())
                    .map(ResTopicRepAttachmentVo::getTopicId).collect(Collectors.toList());
            // 获取论文课题对应的学生oid列表
            List<ResTopicStudentDto> studentDtos = new ArrayList<>();
            List<String> userOids = null;
            if (CollectionUtils.isNotEmpty(topicIds)) {
                studentDtos = resTopicStudentService
                        .list(new LambdaQueryWrapper<ResTopicStudentDto>().in(ResTopicStudentDto::getTopicId, topicIds));
                userOids = studentDtos.stream().map(ResTopicStudentDto::getUserOid).collect(Collectors.toList());
            }

            // 获取学生姓名
            Map<String, String> userOidNameMap = new HashMap<>();
            Map<Long, List<ResTopicStudentDto>> topicIdStudentMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(userOids)) {
                userOidNameMap = baseDataService.getRealNameByUserOids(userOids);

                // 把学生姓名封装进论文
                topicIdStudentMap = studentDtos.stream().collect(Collectors.groupingBy(ResTopicStudentDto::getTopicId));
            }
            for (ResTopicRepAttachmentVo repAttachmentVo : repAttachmentVos) {
                repAttachmentVo.setStudentNames(new ArrayList<>());
                if (ResTopicAttachmentEnum.RS_SOURCE_IN.getValue().equals(repAttachmentVo.getRsSource())) {
                    List<ResTopicStudentDto> topicStudentDtos = topicIdStudentMap.get(repAttachmentVo.getTopicId());
                    if (CollectionUtils.isNotEmpty(topicStudentDtos)) {
                        for (ResTopicStudentDto topicStudentDto : topicStudentDtos) {
                            String userName = userOidNameMap.get(topicStudentDto.getUserOid());
                            repAttachmentVo.getStudentNames().add(userName);
                        }
                    }
                } else {
                    repAttachmentVo.getStudentNames().add(repAttachmentVo.getRepAuthorName());
                }
            }
        }
        return repAttachmentVos;
    }

    @Override
    public AjaxResult addResTopicRepAttachment(ResTopicRepAttachmentBo resTopicRepAttachmentBo) {
        ResTopicRepAttachmentDto resTopicRepAttachment = new ResTopicRepAttachmentDto();
        BeanUtils.copyProperties(resTopicRepAttachmentBo, resTopicRepAttachment);
        resTopicRepAttachment.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (ResTopicAttachmentEnum.DISSERTATION_NAME.getName().equals(resTopicRepAttachment.getRepName())) {
            resTopicRepAttachment.setRepType(ResTopicAttachmentEnum.RES_TYPE_ONE.getValue());
        } else {
            resTopicRepAttachment.setRepType(ResTopicAttachmentEnum.RES_TYPE_TWO.getValue());
        }
        save(resTopicRepAttachment);
        // 成代办事件及流转记录
        ResTopicDto byId = resTopicService.getById(resTopicRepAttachmentBo.getTopicId());
        if (null == byId) {
            return AjaxResult.fail("课题不存在");
        }
        String currentUserOid = resTopicRepAttachmentBo.getCurrentUserOid();
        if (null == currentUserOid) {
            currentUserOid = byId.getSubmitUser();
        }
        // 修改课题状态
        ResTopicBo resTopicBo = new ResTopicBo();
        resTopicBo.setTopicId(byId.getTopicId());
        resTopicBo.setTopicProcess(ResTopicProcessEnum.REP_ATTACHMENT.getCode());
        resTopicService.updateResTopicStatus(resTopicBo);
        List<String> oids = resTopicTeacherService.getResTopicTeacherOidListByTopicId(byId.getTopicId());
        resTopicService.publishEvent(byId.getTopicId(), ResTopicProcessEnum.REP_ATTACHMENT.getCode(),
                byId.getTopicName(), oids, currentUserOid, currentUserOid);

        return AjaxResult.success("保存成功");

    }

    @Override
    public AjaxResult updateResTopicRepAttachment(ResTopicRepAttachmentBo resTopicRepAttachmentBo) {
        LambdaUpdateWrapper<ResTopicRepAttachmentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResTopicRepAttachmentDto::getRepFileOid, resTopicRepAttachmentBo.getRepFileOid());

        ResTopicRepAttachmentDto resTopicRepAttachment = new ResTopicRepAttachmentDto();
        BeanUtils.copyProperties(resTopicRepAttachmentBo, resTopicRepAttachment);
        if (update(resTopicRepAttachment, updateWrapper)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 批量修改附件表
     *
     * @param resTopicRepAttachmentBos
     */
    @Override
    public AjaxResult updateBatchResTopicRepAttachment(List<ResTopicRepAttachmentBo> resTopicRepAttachmentBos) {
        //论文单独处理，修改摘要不存在的情况下
        ResTopicRepAttachmentBo resTopicRepAttachmentBo = resTopicRepAttachmentBos.get(0);
        if (ResTopicAttachmentEnum.DISSERTATION_NAME.getName().equals(resTopicRepAttachmentBo.getRepName()) &&
                null == resTopicRepAttachmentBo.getRepFileOid()) {
            //检索毕业论文空文件是否存在，
            LambdaQueryWrapper<ResTopicRepAttachmentDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ResTopicRepAttachmentDto::getTopicId, resTopicRepAttachmentBo.getTopicId());
            lqw.eq(ResTopicRepAttachmentDto::getRepName, ResTopicAttachmentEnum.DISSERTATION_NAME.getName());
            lqw.isNull(ResTopicRepAttachmentDto::getRepFileOid);
            List<ResTopicRepAttachmentDto> list = this.list(lqw);
            //判断生成还是修改，毕业论文空文件摘要记录
            if (CollectionUtils.isEmpty(list)) {
                addResTopicRepAttachment(resTopicRepAttachmentBo);
            } else {
                ResTopicRepAttachmentDto resTopicRepAttachmentDto = new ResTopicRepAttachmentDto();
                BeanUtils.copyProperties(resTopicRepAttachmentBo, resTopicRepAttachmentDto);
                resTopicRepAttachmentDto.setId(list.get(0).getId());
                this.updateById(resTopicRepAttachmentDto);
            }
            return AjaxResult.success();
        }
        //正常批量修改项目名称及摘要(多个文件)
        if (ResTopicAttachmentEnum.DISSERTATION_NAME.getName().equals(resTopicRepAttachmentBo.getRepName())) {
            LambdaUpdateWrapper<ResTopicRepAttachmentDto> luw = new LambdaUpdateWrapper<>();
            luw.eq(ResTopicRepAttachmentDto::getTopicId, resTopicRepAttachmentBo.getTopicId());
            luw.eq(ResTopicRepAttachmentDto::getRepName, ResTopicAttachmentEnum.DISSERTATION_NAME.getName());
            luw.set(ResTopicRepAttachmentDto::getRepDesc, resTopicRepAttachmentBo.getRepDesc());
            this.update(luw);
        } else {
            for (ResTopicRepAttachmentBo topicRepAttachmentBo : resTopicRepAttachmentBos) {
                updateResTopicRepAttachment(topicRepAttachmentBo);
            }
        }

        return AjaxResult.success();
    }

    @Override
    public ResTopicRepAttachmentVo getDetail(Long id) {
        ResTopicRepAttachmentConditionBo condition = new ResTopicRepAttachmentConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicRepAttachmentVo> list =
                resTopicRepAttachmentMapper.getResTopicRepAttachmentListByCondition(condition);
        ResTopicRepAttachmentVo vo = new ResTopicRepAttachmentVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public List<String> getResTopicRepAttachmentYearsByCondition(ResTopicRepAttachmentConditionBo condition) {
        return resTopicRepAttachmentMapper.getResTopicRepAttachmentYearsByCondition(condition);
    }
}