package com.fh.yanx.service.pub.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryAttachmentDto;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentBo;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryAttachmentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 融合出版书目录附件接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface IPubBookDirectoryAttachmentService extends IService<PubBookDirectoryAttachmentDto> {

    List<PubBookDirectoryAttachmentVo> getPubBookDirectoryAttachmentListByCondition(PubBookDirectoryAttachmentConditionBo condition);

	AjaxResult addPubBookDirectoryAttachment(PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo);

	AjaxResult updatePubBookDirectoryAttachment(PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo);

	PubBookDirectoryAttachmentVo getPubBookDirectoryAttachmentByCondition(PubBookDirectoryAttachmentConditionBo condition);

}

