package com.fh.yanx.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 事件发布对象
 *
 * <AUTHOR>
 * @date 2022/5/10 16:55
 */
@Data
public class EventPublishDto implements Serializable {

    /**
     * 事件类型（等同与消息推送类型）
     */
    private Integer eventType;

    /**
     * 课题id
     */
    private Long topicId;

    /**
     * 课题名称
     */
    private String topicName;

    /**
     * 事件处理人userOids，当多人的时候使用userOids
     */
    private List<String> userOids;

    /**
     * 教师名称
     */
    private String teacherName;

    /**
     * 提交事件的时间
     */
    private Date date;

    /**
     * 事件消息---> 可以自定义事件消息。暂时用不到，先预留
     */
    private String msg;

    /**
     * 待办id
     */
    private Long todoId;
}
