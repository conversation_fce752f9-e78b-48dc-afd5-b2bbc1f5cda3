package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课题荣誉表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_topic_honor_attachment")
public class ResTopicHonorAttachmentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 课题id
	 */
	@TableField("topic_id")
	private Long topicId;

	/**
	 * 研究附件名称
	 */
	@TableField("honor_name")
	private String honorName;

	/**
	 * 附件文件原始名称-带后缀
	 */
	@TableField("honor_file_name_ori")
	private String honorFileNameOri;

	/**
	 * 附件文件名称-不带后缀
	 */
	@TableField("honor_file_name")
	private String honorFileName;

	/**
	 * 附件文件地址
	 */
	@TableField("honor_file_url")
	private String honorFileUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 文件oid
	 */
	@TableField("honor_file_oid")
	private String honorFileOid;

}
