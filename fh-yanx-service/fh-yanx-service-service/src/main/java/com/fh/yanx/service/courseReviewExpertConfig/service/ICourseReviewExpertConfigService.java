package com.fh.yanx.service.courseReviewExpertConfig.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.courseReviewExpertConfig.entity.dto.CourseReviewExpertConfigDto;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程审核专家配置表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
public interface ICourseReviewExpertConfigService extends IService<CourseReviewExpertConfigDto> {

    List<CourseReviewExpertConfigVo> getCourseReviewExpertConfigListByCondition(CourseReviewExpertConfigConditionBo condition);

	AjaxResult addCourseReviewExpertConfig(CourseReviewExpertConfigBo courseReviewExpertConfigBo);

	AjaxResult addCourseReviewExpertConfig(List<CourseReviewExpertConfigBo> courseReviewExpertConfigBoList);

	AjaxResult updateCourseReviewExpertConfig(CourseReviewExpertConfigBo courseReviewExpertConfigBo);

	AjaxResult updateCourseReviewExpertConfig(List<CourseReviewExpertConfigBo> courseReviewExpertConfigBo);

	AjaxResult deleteCourseReviewExpertConfig(List<CourseReviewExpertConfigBo> courseReviewExpertConfigBo);

	CourseReviewExpertConfigVo getCourseReviewExpertConfigByCondition(CourseReviewExpertConfigConditionBo condition);

}

