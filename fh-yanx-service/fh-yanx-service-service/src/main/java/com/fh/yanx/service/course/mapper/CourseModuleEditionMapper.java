package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseModuleEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleEditionVo;

/**
 * 课程模块版本记录表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:17
 */
public interface CourseModuleEditionMapper extends BaseMapper<CourseModuleEditionDto> {

	List<CourseModuleEditionVo> getCourseModuleEditionListByCondition(CourseModuleEditionConditionBo condition);

	CourseModuleEditionVo getCourseModuleEditionByCondition(CourseModuleEditionConditionBo condition);

}
