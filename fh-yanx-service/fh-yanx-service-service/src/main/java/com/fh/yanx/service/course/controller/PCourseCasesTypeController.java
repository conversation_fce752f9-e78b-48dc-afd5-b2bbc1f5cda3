package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.PCourseCasesTypeApi;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesTypeDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesTypeVo;
import com.fh.yanx.service.course.service.IPCourseCasesTypeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 校本课程案例类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@RestController
@Validated
public class PCourseCasesTypeController implements PCourseCasesTypeApi{
	
    @Autowired
    private IPCourseCasesTypeService pCourseCasesTypeService;

    /**
     * 查询校本课程案例类型分页列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PageInfo<PCourseCasesTypeVo>> getPCourseCasesTypePageListByCondition(@RequestBody PCourseCasesTypeConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<PCourseCasesTypeVo> pageInfo = new PageInfo<>(pCourseCasesTypeService.getPCourseCasesTypeListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询校本课程案例类型列表
	 * <AUTHOR>
	 * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult<List<PCourseCasesTypeVo>> getPCourseCasesTypeListByCondition(@RequestBody PCourseCasesTypeConditionBo condition){
		List<PCourseCasesTypeVo> list = pCourseCasesTypeService.getPCourseCasesTypeListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增校本课程案例类型
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
	@Override
    public AjaxResult addPCourseCasesType(@Validated @RequestBody PCourseCasesTypeBo pCourseCasesTypeBo){
		return pCourseCasesTypeService.addPCourseCasesType(pCourseCasesTypeBo);
    }

    /**
	 * 修改校本课程案例类型
	 * @param pCourseCasesTypeBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult updatePCourseCasesType(@Validated @RequestBody PCourseCasesTypeBo pCourseCasesTypeBo) {
		if(null == pCourseCasesTypeBo.getId()) {
			return AjaxResult.fail("校本课程案例类型id不能为空");
		}
		return pCourseCasesTypeService.updatePCourseCasesType(pCourseCasesTypeBo);
	}

	/**
	 * 查询校本课程案例类型详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult<PCourseCasesTypeVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("校本课程案例类型id不能为空");
		}
		PCourseCasesTypeConditionBo condition = new PCourseCasesTypeConditionBo();
		condition.setId(id);
		PCourseCasesTypeVo vo = pCourseCasesTypeService.getPCourseCasesTypeByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除校本课程案例类型
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		PCourseCasesTypeDto pCourseCasesTypeDto = new PCourseCasesTypeDto();
		pCourseCasesTypeDto.setId(id);
		pCourseCasesTypeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(pCourseCasesTypeService.updateById(pCourseCasesTypeDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
