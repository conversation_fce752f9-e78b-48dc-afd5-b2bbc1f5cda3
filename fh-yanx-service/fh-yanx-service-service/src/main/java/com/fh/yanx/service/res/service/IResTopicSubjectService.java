package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicSubjectDto;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicSubjectVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 关联科目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface IResTopicSubjectService extends IService<ResTopicSubjectDto> {

    List<ResTopicSubjectVo> getResTopicSubjectListByCondition(ResTopicSubjectConditionBo condition);

	AjaxResult addResTopicSubject(ResTopicSubjectBo resTopicSubjectBo);

	AjaxResult updateResTopicSubject(ResTopicSubjectBo resTopicSubjectBo);

	ResTopicSubjectVo getDetail(Long id);

}

