package com.fh.yanx.service.jz.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesInfoDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoConditionBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesInfoVo;

/**
 * 金中-校本课程案例详细信息Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesInfoMapper extends BaseMapper<JzCourseCasesInfoDto> {

	List<JzCourseCasesInfoVo> getJzCourseCasesInfoListByCondition(JzCourseCasesInfoConditionBo condition);

	JzCourseCasesInfoVo getJzCourseCasesInfoByCondition(JzCourseCasesInfoConditionBo condition);

}
