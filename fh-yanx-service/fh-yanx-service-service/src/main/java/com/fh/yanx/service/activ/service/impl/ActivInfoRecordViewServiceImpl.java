package com.fh.yanx.service.activ.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewConditionBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordViewDto;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo;
import com.fh.yanx.service.activ.mapper.ActivInfoRecordViewMapper;
import com.fh.yanx.service.activ.service.IActivInfoRecordViewService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 新时代文化校园活动内容观看记录表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
@Service
public class ActivInfoRecordViewServiceImpl extends ServiceImpl<ActivInfoRecordViewMapper, ActivInfoRecordViewDto>
    implements IActivInfoRecordViewService {

    @Resource
    private ActivInfoRecordViewMapper activInfoRecordViewMapper;

    @Override
    public List<ActivInfoRecordViewVo> getActivInfoRecordViewListByCondition(ActivInfoRecordViewConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return activInfoRecordViewMapper.getActivInfoRecordViewListByCondition(condition);
    }

    @Override
    public AjaxResult addActivInfoRecordView(ActivInfoRecordViewBo activInfoRecordViewBo) {
        ActivInfoRecordViewDto activInfoRecordView = new ActivInfoRecordViewDto();
        BeanUtils.copyProperties(activInfoRecordViewBo, activInfoRecordView);
        activInfoRecordView.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(activInfoRecordView)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateActivInfoRecordView(ActivInfoRecordViewBo activInfoRecordViewBo) {
        ActivInfoRecordViewDto activInfoRecordView = new ActivInfoRecordViewDto();
        BeanUtils.copyProperties(activInfoRecordViewBo, activInfoRecordView);
        if (updateById(activInfoRecordView)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ActivInfoRecordViewVo getActivInfoRecordViewByCondition(ActivInfoRecordViewConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ActivInfoRecordViewVo vo = activInfoRecordViewMapper.getActivInfoRecordViewByCondition(condition);
        return vo;
    }

    @Override
    public List<ActivInfoRecordViewVo> getActivInfoRecordViewStatistics(ActivInfoRecordViewConditionBo condition) {
        return activInfoRecordViewMapper.getActivInfoRecordViewStatistics(condition);
    }
}