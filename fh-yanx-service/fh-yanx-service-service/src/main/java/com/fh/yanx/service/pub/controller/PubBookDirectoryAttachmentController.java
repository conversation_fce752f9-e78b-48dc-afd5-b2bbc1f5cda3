package com.fh.yanx.service.pub.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.pub.api.PubBookDirectoryAttachmentApi;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentConditionBo;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryAttachmentDto;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryAttachmentVo;
import com.fh.yanx.service.pub.service.IPubBookDirectoryAttachmentService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 融合出版书目录附件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@RestController
@Validated
public class PubBookDirectoryAttachmentController implements PubBookDirectoryAttachmentApi{
	
    @Autowired
    private IPubBookDirectoryAttachmentService pubBookDirectoryAttachmentService;

    /**
     * 查询融合出版书目录附件分页列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult<PageInfo<PubBookDirectoryAttachmentVo>> getPubBookDirectoryAttachmentPageListByCondition(@RequestBody PubBookDirectoryAttachmentConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<PubBookDirectoryAttachmentVo> pageInfo = new PageInfo<>(pubBookDirectoryAttachmentService.getPubBookDirectoryAttachmentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询融合出版书目录附件列表
	 * <AUTHOR>
	 * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult<List<PubBookDirectoryAttachmentVo>> getPubBookDirectoryAttachmentListByCondition(@RequestBody PubBookDirectoryAttachmentConditionBo condition){
		List<PubBookDirectoryAttachmentVo> list = pubBookDirectoryAttachmentService.getPubBookDirectoryAttachmentListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增融合出版书目录附件
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
	@Override
    public AjaxResult addPubBookDirectoryAttachment(@Validated @RequestBody PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo){
		return pubBookDirectoryAttachmentService.addPubBookDirectoryAttachment(pubBookDirectoryAttachmentBo);
    }

    /**
	 * 修改融合出版书目录附件
	 * @param pubBookDirectoryAttachmentBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult updatePubBookDirectoryAttachment(@Validated @RequestBody PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo) {
		if(null == pubBookDirectoryAttachmentBo.getBookDirectoryAttachmentId()) {
			return AjaxResult.fail("融合出版书目录附件id不能为空");
		}
		return pubBookDirectoryAttachmentService.updatePubBookDirectoryAttachment(pubBookDirectoryAttachmentBo);
	}

	/**
	 * 查询融合出版书目录附件详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult<PubBookDirectoryAttachmentVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("融合出版书目录附件id不能为空");
		}
		PubBookDirectoryAttachmentConditionBo condition = new PubBookDirectoryAttachmentConditionBo();
		condition.setBookDirectoryAttachmentId(id);
		PubBookDirectoryAttachmentVo vo = pubBookDirectoryAttachmentService.getPubBookDirectoryAttachmentByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除融合出版书目录附件
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		PubBookDirectoryAttachmentDto pubBookDirectoryAttachmentDto = new PubBookDirectoryAttachmentDto();
		pubBookDirectoryAttachmentDto.setBookDirectoryAttachmentId(id);
		pubBookDirectoryAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(pubBookDirectoryAttachmentService.updateById(pubBookDirectoryAttachmentDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
