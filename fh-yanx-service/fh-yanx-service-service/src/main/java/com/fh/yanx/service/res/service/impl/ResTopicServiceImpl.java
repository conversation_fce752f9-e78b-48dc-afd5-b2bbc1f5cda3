package com.fh.yanx.service.res.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.enums.ExcellentEnums;
import com.fh.yanx.service.enums.ResTopicAttachmentEnum;
import com.fh.yanx.service.enums.ResTopicProcessEnum;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CompleteTodoEvent;
import com.fh.yanx.service.event.CreateTodoEvent;
import com.fh.yanx.service.res.entity.bo.*;
import com.fh.yanx.service.res.entity.dto.*;
import com.fh.yanx.service.res.entity.vo.*;
import com.fh.yanx.service.res.mapper.ResTopicHonorAttachmentMapper;
import com.fh.yanx.service.res.mapper.ResTopicRepAttachmentMapper;
import com.fh.yanx.service.res.mapper.ResTopicStuAttachmentMapper;
import com.fh.yanx.service.res.service.*;
import com.google.common.collect.Lists;
import com.light.base.category.entity.vo.CategoryVo;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.vo.UserVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.mapper.ResTopicMapper;
import com.light.core.entity.AjaxResult;

/**
 * 课题表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResTopicServiceImpl extends ServiceImpl<ResTopicMapper, ResTopicDto> implements IResTopicService {

    @Resource
    private ResTopicMapper resTopicMapper;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private IResTopicStudentService resTopicStudentService;
    @Resource
    private IResTopicTeacherService resTopicTeacherService;
    @Resource
    private IResTopicCollectService resTopicCollectService;
    @Resource
    private ResTopicStuAttachmentMapper resTopicStuAttachmentMapper;
    @Resource
    private ResTopicRepAttachmentMapper resTopicRepAttachmentMapper;
    @Resource
    private ResTopicHonorAttachmentMapper resTopicHonorAttachmentMapper;
    @Resource
    private IResTopicApprovalService resTopicApprovalService;
    @Resource
    private IResTopicProcessRecordService resTopicProcessRecordService;
    @Resource
    private IResTopicPvService topicPvService;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public List<ResTopicVo> getResTopicListByCondition(ResTopicConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<ResTopicVo> resTopicVos = resTopicMapper.getResTopicListByCondition(condition);
        if (CollectionUtils.isNotEmpty(resTopicVos)) {
            List<Long> topicIds = resTopicVos.stream().map(ResTopicVo::getTopicId).collect(Collectors.toList());
            // 封装小组成员
            List<ResTopicStudentDto> topicStudentDtos = resTopicStudentService
                .list(new LambdaQueryWrapper<ResTopicStudentDto>()
                        .in(ResTopicStudentDto::getTopicId, topicIds).eq(ResTopicStudentDto::getIsDelete,StatusEnum.NOTDELETE.getCode()));
            List<String> userOIds =
                topicStudentDtos.stream().map(ResTopicStudentDto::getUserOid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userOIds)) {
                Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(userOIds);
                if (!userNameMap.isEmpty()) {
                    for (ResTopicVo resTopicVo : resTopicVos) {
                        List<String> topicStudentNames = new ArrayList<>();
                        for (ResTopicStudentDto topicStudentDto : topicStudentDtos) {
                            if (topicStudentDto.getTopicId().equals(resTopicVo.getTopicId())) {
                                topicStudentNames.add(userNameMap.get(topicStudentDto.getUserOid()));
                            }
                        }
                        resTopicVo.setTopicStudentNames(topicStudentNames);
                        // 审核列表封装，学生班级
                        // resTopicVo.setc todo 审批列表高保真添加学生班级字段，暂时不显示
                    }
                }
            }

            // 封装 收藏者
            String currentUserOid = condition.getCurrentUserOid();
            for (ResTopicVo resTopicVo : resTopicVos) {
                resTopicVo.setIsCollect(false);
                List<ResTopicCollectDto> list = resTopicCollectService.list(new LambdaQueryWrapper<ResTopicCollectDto>()
                    .eq(ResTopicCollectDto::getTopicId, resTopicVo.getTopicId())
                    .eq(ResTopicCollectDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
                resTopicVo.setCollectNumber(list.size());
                if (null != currentUserOid && CollectionUtils.isNotEmpty(list)) {
                    for (ResTopicCollectDto resTopicCollectDto : list) {
                        if (resTopicCollectDto.getUserOid().equals(currentUserOid)) {
                            resTopicVo.setIsCollect(true);
                            break;
                        }
                    }
                }

                // 封装 浏览量
                resTopicVo.setPvNumber(topicPvService.getPVByCache(resTopicVo.getTopicId()));
            }
            // 封装学校名称、
            List<Long> organizationIds = resTopicVos.stream().filter(x -> x.getOrganizationId() != null)
                .map(ResTopicVo::getOrganizationId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(organizationIds) && CollectionUtils.isNotEmpty(resTopicVos)) {
                List<OrganizationVo> organizationVoList = baseDataService.getOrganizationVoList(organizationIds);
                Map<Long, List<OrganizationVo>> orgMap =
                    organizationVoList.stream().collect(Collectors.groupingBy(OrganizationVo::getId));
                for (ResTopicVo resTopicVo : resTopicVos) {
                    if (resTopicVo.getOrganizationId() != null) {
                        resTopicVo.setOrganizationName(orgMap.get(resTopicVo.getOrganizationId()).get(0).getName());
                    }
                }
            }
        }
        return resTopicVos;
    }

    /**
     * 我的课题暂存
     *
     * @param resTopicBo
     * @return
     */
    @Override
    public AjaxResult addResTopic(ResTopicBo resTopicBo) {

        List<String> studentOids =
            resTopicBo.getStudentUserBos().stream().map(UserBo::getOid).collect(Collectors.toList());
        List<String> teacherOids =
            resTopicBo.getTeacherUserBos().stream().map(UserBo::getOid).collect(Collectors.toList());

        // 同步添加指导老师和学生，并判断学生是否有参加其他小组
        ResTopicDto resTopic = new ResTopicDto();
        BeanUtils.copyProperties(resTopicBo, resTopic);
        resTopic.setIsDelete(StatusEnum.NOTDELETE.getCode());
        resTopic.setSubmitUser(resTopicBo.getCurrentUserOid());
        LambdaQueryWrapper<ResTopicStudentDto> studentLQW = new LambdaQueryWrapper<>();
        studentLQW.in(ResTopicStudentDto::getUserOid, studentOids);
        studentLQW.in(ResTopicStudentDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        List<ResTopicStudentDto> queryStudentList = resTopicStudentService.list(studentLQW);

        // 判断是否第一次暂存,提交
        if (resTopicBo.getTopicId() == null) {
            if (CollectionUtils.isNotEmpty(queryStudentList)) {
                return AjaxResult.fail("成员已在其他小组，添加失败");
            }
            save(resTopic);
            // 保存学生
            List<ResTopicStudentDto> topicStudentDtos = generateTopicStudentByOid(studentOids, resTopic.getTopicId());
            resTopicStudentService.saveBatch(topicStudentDtos);
            // 保存教师
            List<ResTopicTeacherDto> resTopicTeacherDtos =
                generateTopicTeacherByOid(teacherOids, resTopic.getTopicId());
            resTopicTeacherService.saveBatch(resTopicTeacherDtos);
            // 生成代办
            publishEvent(resTopic.getTopicId(), resTopic.getTopicProcess(), resTopic.getTopicName(), teacherOids,
                resTopicBo.getCurrentUserOid(), resTopicBo.getCurrentUserOid());
            return AjaxResult.success(resTopic.getTopicId());
        }
        // 驳回修改 或修改之后暂存，提交
        if (CollectionUtils.isNotEmpty(queryStudentList)) {
            for (ResTopicStudentDto studentDto : queryStudentList) {
                if (studentOids.contains(studentDto.getUserOid())
                    && !studentDto.getTopicId().equals(resTopicBo.getTopicId())) {
                    return AjaxResult.fail("成员已在其他小组，保存失败");
                }
            }
        }
        ResTopicDto byId = this.getById(resTopicBo.getTopicId());
        // 驳回之后暂存，状态记录
        if (ResTopicProcessEnum.TEMP.getCode().equals(resTopicBo.getTopicProcess())
            && (ResTopicProcessEnum.TEACHER_REJECT.getCode().equals(byId.getTopicProcess())
                || ResTopicProcessEnum.UPDATE_TEMP.getCode().equals(byId.getTopicProcess()))) {
            resTopic.setTopicProcess(ResTopicProcessEnum.UPDATE_TEMP.getCode());
        }

        // 保存或提交课题
        updateById(resTopic);
        if (CollectionUtils.isNotEmpty(studentOids)) {
            // 删除学生
            LambdaUpdateWrapper<ResTopicStudentDto> studentUpdateWrapper = new LambdaUpdateWrapper<>();
            studentUpdateWrapper.set(ResTopicStudentDto::getIsDelete, StatusEnum.ISDELETE.getCode());
            studentUpdateWrapper.eq(ResTopicStudentDto::getTopicId, resTopic.getTopicId());
            resTopicStudentService.update(studentUpdateWrapper);
            // 新增学生
            List<ResTopicStudentDto> topicStudentDtos = generateTopicStudentByOid(studentOids, resTopic.getTopicId());
            resTopicStudentService.saveBatch(topicStudentDtos);
        }
        if (CollectionUtils.isNotEmpty(teacherOids)) {
            // 删除教师
            LambdaUpdateWrapper<ResTopicTeacherDto> teacherUpdateWrapper = new LambdaUpdateWrapper<>();
            teacherUpdateWrapper.set(ResTopicTeacherDto::getIsDelete, StatusEnum.ISDELETE.getCode());
            teacherUpdateWrapper.eq(ResTopicTeacherDto::getTopicId, resTopic.getTopicId());
            resTopicTeacherService.update(teacherUpdateWrapper);
            // 新增教师
            List<ResTopicTeacherDto> resTopicTeacherDtos =
                generateTopicTeacherByOid(teacherOids, resTopic.getTopicId());
            resTopicTeacherService.saveBatch(resTopicTeacherDtos);
        }

        // 生成代办
        publishEvent(resTopic.getTopicId(), resTopic.getTopicProcess(), resTopic.getTopicName(), teacherOids,
            resTopicBo.getCurrentUserOid(), resTopicBo.getCurrentUserOid());
        return AjaxResult.success(resTopic.getTopicId());
    }

    /**
     * 课题生成代办事件及流转记录
     *
     * @param topicId,topicProcess,topicName
     * @param teacherOids 待办：学生列表或者教师列表
     * @param currentUserOid 当前登陆人
     * @return void
     * <AUTHOR>
     * @date 2023/2/7 11:33
     */
    @Override
    public void publishEvent(Long topicId, Integer topicProcess, String topicName, List<String> teacherOids,
        String currentUserOid, String submitUser) {
        // 暂存
        if (ResTopicProcessEnum.TEMP.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo resTopicProcessRecordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.TEMP.getValue());
            resTopicProcessRecordBo.setProcessSubmitUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecordWithCompare(resTopicProcessRecordBo);
            // 生成学生代办
            applicationContext
                .publishEvent(CreateTodoEvent.produceTopicEvent(TopicTodoType.STUDENT_TOPIC_STASH.getType(), topicId,
                    topicName, new Date(), "", "", Lists.newArrayList(currentUserOid)));
        }
        // 提交
        if (ResTopicProcessEnum.SUBMIT_EXAMINE.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo resTopicProcessRecordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.SUBMIT_EXAMINE.getValue());
            resTopicProcessRecordBo.setProcessSubmitUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecord(resTopicProcessRecordBo);
            // 生成教师代办
            applicationContext.publishEvent(CreateTodoEvent.produceTopicEvent(
                TopicTodoType.TEACHER_TOPIC_SUBMIT.getType(), topicId, topicName, new Date(), "", "", teacherOids));
            //取消学生代办
            applicationContext.publishEvent(CompleteTodoEvent.produceTopicEvent(
                    TopicTodoType.STUDENT_TOPIC_EDIT.getType(), topicId, new Date(), "", currentUserOid,null ));
        }
        // 审核通过
        if (ResTopicProcessEnum.TEACHER_PASS.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo resTopicProcessRecordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.TEACHER_PASS.getValue());
            resTopicProcessRecordBo.setProcessVerifyUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecord(resTopicProcessRecordBo);
            // 生成学生代办
            applicationContext
                .publishEvent(CreateTodoEvent.produceTopicEvent(TopicTodoType.STUDENT_TOPIC_PASS.getType(), topicId,
                    topicName, new Date(), "", "", Lists.newArrayList(submitUser)));
        }
        // 审核拒绝
        if (ResTopicProcessEnum.TEACHER_REJECT.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo resTopicProcessRecordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.TEACHER_REJECT.getValue());
            resTopicProcessRecordBo.setProcessVerifyUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecord(resTopicProcessRecordBo);
            // 生成学生代办
            applicationContext
                .publishEvent(CreateTodoEvent.produceTopicEvent(TopicTodoType.STUDENT_TOPIC_REJECT.getType(), topicId,
                    topicName, new Date(), "", "", Lists.newArrayList(submitUser)));
        }
        // 驳回重新编辑暂存
        if (ResTopicProcessEnum.UPDATE_TEMP.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo resTopicProcessRecordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.UPDATE_TEMP.getValue());
            resTopicProcessRecordBo.setProcessSubmitUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecordWithCompare(resTopicProcessRecordBo);
            // 生成学生代办
            applicationContext
                .publishEvent(CreateTodoEvent.produceTopicEvent(TopicTodoType.STUDENT_TOPIC_STASH.getType(), topicId,
                    topicName, new Date(), "", "", Lists.newArrayList(currentUserOid)));
        }
        // 课题研究
        if (ResTopicProcessEnum.STU_ATTACHMENT.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo recordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.STU_ATTACHMENT.getValue());
            recordBo.setProcessSubmitUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecordWithCompare(recordBo);
            // 生成代办
            applicationContext
                .publishEvent(CreateTodoEvent.produceTopicEvent(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getType(),
                    topicId, topicName, new Date(), "", "", teacherOids));
        }
        // 论文，答题材料
        if (ResTopicProcessEnum.REP_ATTACHMENT.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo recordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.REP_ATTACHMENT.getValue());
            recordBo.setProcessSubmitUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecordWithCompare(recordBo);
            // 生成代办
            applicationContext.publishEvent(
                CreateTodoEvent.produceTopicEvent(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getType(),
                    topicId, topicName, new Date(), "", "", teacherOids));
        }
        // 学生评价
        if (ResTopicProcessEnum.STUDENT_EVALUATE.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo recordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.STUDENT_EVALUATE.getValue());
            recordBo.setProcessSubmitUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecordWithCompare(recordBo);
            // 生成代办
            applicationContext.publishEvent(CreateTodoEvent.produceTopicEvent(
                TopicTodoType.TEACHER_TOPIC_EVALUATE.getType(), topicId, topicName, new Date(), "", "", teacherOids));
        }
        // 教师评定
        if (ResTopicProcessEnum.TEACHER_EVALUATE.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo recordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.TEACHER_EVALUATE.getValue());
            recordBo.setProcessVerifyUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecordWithCompare(recordBo);
            // 生成学生代办
            applicationContext.publishEvent(
                CreateTodoEvent.produceTopicEvent(TopicTodoType.STUDENT_TOPIC_TEACHER.getType(),
                    topicId, topicName, new Date(), "", "", Lists.newArrayList(submitUser)));
        }
        // 学校评定
        if (ResTopicProcessEnum.SCHOOL_EVALUATE.getCode().equals(topicProcess)) {
            // 生成流转记录
            ResTopicProcessRecordBo recordBo =
                generateResTopicProcessRecord(topicId, topicProcess, ResTopicProcessEnum.SCHOOL_EVALUATE.getValue());
            recordBo.setProcessVerifyUser(currentUserOid);
            resTopicProcessRecordService.addResTopicProcessRecord(recordBo);

            // 生成代办,学校通过的时候给老师学生都推送,驳回只通知教师
            ResTopicDto byId = this.getById(topicId);
            if (ExcellentEnums.RESULT_NO.getValue().equals(byId.getEvaluateResult())) {
                //生成教师驳回代办
                List<String> oidListByTopicId = resTopicTeacherService.getResTopicTeacherOidListByTopicId(topicId);
                applicationContext.publishEvent(CreateTodoEvent.produceTopicEvent(
                        TopicTodoType.TEACHER_TOPIC_SCHOOL_REJECT.getType(), topicId, topicName, new Date(), "", "", oidListByTopicId));
            } else if (ExcellentEnums.RESULT_YES.getValue().equals(byId.getEvaluateResult())) {
                //生成教师、学生通过代办
                Integer teacherExcellentType = null;
                Integer studentExcellentType = null;
                //设置优秀课题     1不是优秀课题，2是优秀课题
                if (ExcellentEnums.TYPE_YES.getValue().equals(byId.getExcellentType())) {
                    teacherExcellentType = TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getType();
                    studentExcellentType = TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getType();
                } else if (ExcellentEnums.TYPE_NO.getValue().equals(byId.getExcellentType())) {
                    //未设置优秀课题
                    teacherExcellentType = TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getType();
                    studentExcellentType = TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getType();
                }
                //生成教师代办
                List<String> oidListByTopicId = resTopicTeacherService.getResTopicTeacherOidListByTopicId(topicId);
                applicationContext.publishEvent(CreateTodoEvent.produceTopicEvent(
                        teacherExcellentType, topicId, topicName, new Date(), "", "", oidListByTopicId));
                //生成学生代办
                applicationContext.publishEvent(CreateTodoEvent.produceTopicEvent(
                        studentExcellentType, topicId, topicName, new Date(), "", "",  Lists.newArrayList(submitUser)));
            }
        }
    }

    /**
     * 生成流转记录bo
     *
     * @param value
     * @return
     */
    private ResTopicProcessRecordBo generateResTopicProcessRecord(Long topicId, Integer topicProcess, String value) {
        // 添加流转记录
        ResTopicProcessRecordBo resTopicProcessRecordBo = new ResTopicProcessRecordBo();
        resTopicProcessRecordBo.setTopicId(topicId);
        resTopicProcessRecordBo.setTopicProcess(topicProcess);
        resTopicProcessRecordBo.setProcessName(value);
        return resTopicProcessRecordBo;
    }

    /**
     * 根据课题id和oid列表生成课题学生记录
     *
     * @param studentOids, topicId
     * @return java.util.List<com.fh.yanx.service.res.entity.dto.ResTopicStudentDto>
     * <AUTHOR>
     * @date 2023/2/2 17:22
     */
    private List<ResTopicStudentDto> generateTopicStudentByOid(List<String> studentOids, Long topicId) {
        List<ResTopicStudentDto> resTopicStudents = new ArrayList<>();
        for (String studentOid : studentOids) {
            ResTopicStudentDto studentDto = new ResTopicStudentDto();
            studentDto.setUserOid(studentOid);
            studentDto.setTopicId(topicId);
            resTopicStudents.add(studentDto);
        }
        return resTopicStudents;
    }

    /**
     * 根据课题id和oid列表生成课题教师记录
     *
     * @param teacherOids, topicId
     * @return java.util.List<com.fh.yanx.service.res.entity.dto.ResTopicStudentDto>
     * <AUTHOR>
     * @date 2023/2/2 17:22
     */
    private List<ResTopicTeacherDto> generateTopicTeacherByOid(List<String> teacherOids, Long topicId) {
        List<ResTopicTeacherDto> resTopicTeacherDtos = new ArrayList<>();
        for (String teacherOid : teacherOids) {
            ResTopicTeacherDto topicTeacherDto = new ResTopicTeacherDto();
            topicTeacherDto.setUserOid(teacherOid);
            topicTeacherDto.setTopicId(topicId);
            resTopicTeacherDtos.add(topicTeacherDto);
        }
        return resTopicTeacherDtos;
    }

    @Override
    public AjaxResult updateResTopicStatus(ResTopicBo resTopicBo) {
        ResTopicDto resTopicDto = this.getById(resTopicBo.getTopicId());
        ResTopicDto resTopic = new ResTopicDto();
        BeanUtils.copyProperties(resTopicBo, resTopic);
        resTopic.setVerifyUser(resTopicBo.getCurrentUserOid());
        updateById(resTopic);

        // 审核通过，审核拒绝 ,学校评定生成代办事项
        if (ResTopicProcessEnum.TEACHER_REJECT.getCode().equals(resTopicBo.getTopicProcess()) ||
                ResTopicProcessEnum.TEACHER_PASS.getCode().equals(resTopicBo.getTopicProcess()) ||
                ResTopicProcessEnum.SCHOOL_EVALUATE.getCode().equals(resTopicBo.getTopicProcess())) {
            publishEvent(resTopicBo.getTopicId(), resTopicBo.getTopicProcess(), resTopicDto.getTopicName(), null,
                    resTopicBo.getCurrentUserOid(), resTopicDto.getSubmitUser());

        }
        return AjaxResult.success();
    }

    @Override
    public ResTopicVo getDetail(Long id, Long organizationId) {
        ResTopicConditionBo condition = new ResTopicConditionBo();
        condition.setTopicId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrganizationId(organizationId);
        condition.setIsDetailQuery(StatusEnum.YES.getCode());
        List<ResTopicVo> list = resTopicMapper.getResTopicListByCondition(condition);
        ResTopicVo vo = new ResTopicVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
            // 封装课题组成员及老师名称
            List<ResTopicStudentDto> topicStudents =
                resTopicStudentService.list(new LambdaQueryWrapper<ResTopicStudentDto>()
                    .eq(ResTopicStudentDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                    .eq(ResTopicStudentDto::getTopicId, vo.getTopicId()));
            List<String> userOIds =
                topicStudents.stream().map(ResTopicStudentDto::getUserOid).collect(Collectors.toList());
            List<ResTopicTeacherDto> topicTeachers =
                resTopicTeacherService.list(new LambdaQueryWrapper<ResTopicTeacherDto>()
                    .eq(ResTopicTeacherDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                    .eq(ResTopicTeacherDto::getTopicId, vo.getTopicId()));
            userOIds.addAll(topicTeachers.stream().map(ResTopicTeacherDto::getUserOid).collect(Collectors.toList()));
            Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(userOIds);
            if (CollectionUtils.isNotEmpty(userOIds) && !userNameMap.isEmpty()) {
                List<UserVo> teacherUserVos = new ArrayList<>();
                List<UserVo> studentUserVos = new ArrayList<>();
                List<String> topicStudentNames = new ArrayList<>();
                for (ResTopicStudentDto topicStudentDto : topicStudents) {
                    if (topicStudentDto.getTopicId().equals(vo.getTopicId())) {
                        topicStudentNames.add(userNameMap.get(topicStudentDto.getUserOid()));
                        UserVo userVo = new UserVo();
                        userVo.setOid(topicStudentDto.getUserOid());
                        userVo.setRealName(userNameMap.get(topicStudentDto.getUserOid()));
                        studentUserVos.add(userVo);
                    }
                }
                vo.setTopicStudentNames(topicStudentNames);
                List<String> teacherNames = new ArrayList<>();
                for (ResTopicTeacherDto resTopicTeacherDto : topicTeachers) {
                    if (resTopicTeacherDto.getTopicId().equals(vo.getTopicId())) {
                        teacherNames.add(userNameMap.get(resTopicTeacherDto.getUserOid()));
                        UserVo userVo = new UserVo();
                        userVo.setOid(resTopicTeacherDto.getUserOid());
                        userVo.setRealName(userNameMap.get(resTopicTeacherDto.getUserOid()));
                        teacherUserVos.add(userVo);
                    }
                    vo.setTopicTeacherNames(teacherNames);
                }
                vo.setStudentUserVos(studentUserVos);
                vo.setTeacherUserVos(teacherUserVos);
            }
            // 封装小组成员评语列表
            if (CollectionUtils.isNotEmpty(topicStudents)) {
                List<ResTopicStudentVo> resTopicStudentVos = topicStudents.stream().map(val -> {
                    ResTopicStudentVo resTopicStudentVo = new ResTopicStudentVo();
                    BeanUtils.copyProperties(val, resTopicStudentVo);
                    resTopicStudentVo.setUserName(userNameMap.get(resTopicStudentVo.getUserOid()));
                    return resTopicStudentVo;
                }).collect(Collectors.toList());
                vo.setResTopicStudentVos(resTopicStudentVos);
            }
            // 封装指导教师评语列表
            if (CollectionUtils.isNotEmpty(topicTeachers)) {
                List<ResTopicTeacherVo> resTopicTeacherVos = topicTeachers.stream().map(val -> {
                    ResTopicTeacherVo resTopicTeacherVo = new ResTopicTeacherVo();
                    BeanUtils.copyProperties(val, resTopicTeacherVo);
                    resTopicTeacherVo.setUserName(userNameMap.get(resTopicTeacherVo.getUserOid()));
                    return resTopicTeacherVo;
                }).collect(Collectors.toList());
                vo.setResTopicTeacherVos(resTopicTeacherVos);
            }

            // 研究成果，附件
            ResTopicStuAttachmentConditionBo conditionBo = new ResTopicStuAttachmentConditionBo();
            conditionBo.setTopicId(vo.getTopicId());
            conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            List<ResTopicStuAttachmentVo> resTopicStuAttachmentVos =
                resTopicStuAttachmentMapper.getResTopicStuAttachmentListByCondition(conditionBo);
            if (CollectionUtils.isNotEmpty(resTopicStuAttachmentVos)) {
                Map<String, List<ResTopicStuAttachmentVo>> stuMap =
                    resTopicStuAttachmentVos.stream().filter(x -> StringUtil.isNotBlank(x.getStuName()))
                        .collect(Collectors.groupingBy(ResTopicStuAttachmentVo::getStuName));
                //按项目名称排序
                List<String> stuNames = resTopicStuAttachmentVos.stream().sorted(Comparator.comparing(ResTopicStuAttachmentVo::getCreateTime))
                        .map(ResTopicStuAttachmentVo::getStuName).distinct().collect(Collectors.toList());
                List<Map<String, Object>> mapList = new LinkedList<>();
                if (CollectionUtils.isNotEmpty(stuNames)) {
                    for (String key : stuNames) {
                        Map<String, Object> map = new LinkedHashMap<>();
                        List<ResTopicStuAttachmentVo> vos = stuMap.get(key);
                        map.put("name", key);
                        map.put("idfoIDS", vos);
                        mapList.add(map);
                    }
                }
                vo.setResTopicStuAttachmentVosMap(mapList);
            }

            // 毕业论文、答辩材料
            ResTopicRepAttachmentConditionBo repCondition = new ResTopicRepAttachmentConditionBo();
            repCondition.setTopicId(vo.getTopicId());
            repCondition.setIsDelete(StatusEnum.NOTDELETE.getCode());
            List<ResTopicRepAttachmentVo> repAttachmentVos =
                resTopicRepAttachmentMapper.getResTopicRepAttachmentListByCondition(repCondition);
            if (CollectionUtils.isNotEmpty(repAttachmentVos)) {
                Map<String, List<ResTopicRepAttachmentVo>> repMap =
                    repAttachmentVos.stream().filter(x -> StringUtil.isNotBlank(x.getRepName()))
                        .collect(Collectors.groupingBy(ResTopicRepAttachmentVo::getRepName));
                List<Map<String, Object>> mapList = new LinkedList<>();
                //按项目名称排序
                List<String> repNames = repAttachmentVos.stream().sorted(Comparator.comparing(ResTopicRepAttachmentVo::getCreateTime))
                        .map(ResTopicRepAttachmentVo::getRepName).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(repNames)) {
                    for (String key : repNames) {
                        Map<String, Object> map = new LinkedHashMap<>();
                        List<ResTopicRepAttachmentVo> vos = repMap.get(key);
                        map.put("name", key);
                        map.put("repDesc", vos.get(0).getRepDesc());
                       vos=vos.stream().filter(x->StringUtil.isNotBlank(x.getRepFileOid())).collect(Collectors.toList());
                        map.put("idfoIDS", vos);
                        if (key.equals(ResTopicAttachmentEnum.DISSERTATION_NAME.getName())) {
                            mapList.add(0, map);
                        } else {
                            mapList.add(map);
                        }

                    }
                }
                vo.setResTopicRepAttachmentVosMap(mapList);
            }

            // 课题评定 教师评语
            List<ResTopicApprovalDto> topicApprovalDtos =
                resTopicApprovalService.list(new LambdaQueryWrapper<ResTopicApprovalDto>()
                    .eq(ResTopicApprovalDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                    .eq(ResTopicApprovalDto::getTopicId, vo.getTopicId()));
            if (CollectionUtils.isNotEmpty(topicApprovalDtos)) {
                List<ResTopicApprovalVo> resTopicApprovalVos = topicApprovalDtos.stream().map(topicApprovalDto -> {
                    ResTopicApprovalVo resTopicApprovalVo = new ResTopicApprovalVo();
                    BeanUtils.copyProperties(topicApprovalDto, resTopicApprovalVo);
                    resTopicApprovalVo.setVerifyTeacherName(userNameMap.get(resTopicApprovalVo.getVerifyTeacher()));
                    return resTopicApprovalVo;
                }).collect(Collectors.toList());
                vo.setResTopicApprovalVos(resTopicApprovalVos);
            }

            // 课题评定 学校荣誉
            ResTopicHonorAttachmentConditionBo honorConditionBo = new ResTopicHonorAttachmentConditionBo();
            honorConditionBo.setTopicId(vo.getTopicId());
            honorConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            List<ResTopicHonorAttachmentVo> honorAttachmentVos =
                resTopicHonorAttachmentMapper.getResTopicHonorAttachmentListByCondition(honorConditionBo);
            if (CollectionUtils.isNotEmpty(honorAttachmentVos)) {
                Map<String, List<ResTopicHonorAttachmentVo>> honorMap =
                    honorAttachmentVos.stream().filter(x -> StringUtil.isNotBlank(x.getHonorName()))
                        .collect(Collectors.groupingBy(ResTopicHonorAttachmentVo::getHonorName));
                Set<String> keySet = honorMap.keySet();
                List<Map<String, Object>> mapList = new LinkedList<>();
                if (CollectionUtils.isNotEmpty(keySet)) {
                    for (String key : keySet) {
                        Map<String, Object> map = new HashMap<>();
                        List<ResTopicHonorAttachmentVo> vos = honorMap.get(key);
                        map.put("name", key);
                        map.put("idfoIDS", vos);
                        mapList.add(map);
                    }
                }
                vo.setResTopicHonorAttachmentVosMap(mapList);
            }
            // 流转记录
            List<ResTopicProcessRecordDto> topicProcessRecordDtos =
                resTopicProcessRecordService.list(new LambdaQueryWrapper<ResTopicProcessRecordDto>()
                    .eq(ResTopicProcessRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                    .eq(ResTopicProcessRecordDto::getTopicId, vo.getTopicId()));
            if (CollectionUtils.isNotEmpty(topicProcessRecordDtos)) {
                List<ResTopicProcessRecordVo> resTopicProcessRecordVos =
                    topicProcessRecordDtos.stream().map(topicProcessRecordDto -> {
                        ResTopicProcessRecordVo resTopicProcessRecordVo = new ResTopicProcessRecordVo();
                        BeanUtils.copyProperties(topicProcessRecordDto, resTopicProcessRecordVo);
                        resTopicProcessRecordVo
                            .setProcessVerifyUserName(userNameMap.get(resTopicProcessRecordVo.getProcessVerifyUser()));
                        resTopicProcessRecordVo
                            .setProcessSubmitUserName(userNameMap.get(resTopicProcessRecordVo.getProcessSubmitUser()));
                        return resTopicProcessRecordVo;
                    }).collect(Collectors.toList());
                vo.setResTopicProcessRecordVos(resTopicProcessRecordVos);
            }
            // 科目封装
            String subjectCode = vo.getSubjectCode();
            if (StringUtils.isNotBlank(subjectCode)) {
                String[] split = subjectCode.split(",");
                List<String> subjectCodes = Arrays.asList(split);
                List<CategoryVo> subjectList = baseDataService.getSubjectList();
                List<CategoryVo> collect =
                    subjectList.stream().filter(x -> subjectCodes.contains(x.getCode())).collect(Collectors.toList());
                vo.setSubjectCodes(collect);
            }

        }
        return vo;
    }

}