package com.fh.yanx.service.course.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigBo;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseHomeConfigDto;
import com.fh.yanx.service.course.entity.vo.CourseHomeConfigVo;
import com.fh.yanx.service.course.mapper.CourseHomeConfigMapper;
import com.fh.yanx.service.course.service.ICourseHomeConfigService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 推荐展示位接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Service
public class CourseHomeConfigServiceImpl extends ServiceImpl<CourseHomeConfigMapper, CourseHomeConfigDto>
    implements ICourseHomeConfigService {

    @Resource
    private CourseHomeConfigMapper courseHomeConfigMapper;
    @Resource
    private ICourseHomeConfigService courseHomeConfigService;

    @Override
    public List<CourseHomeConfigVo> getCourseHomeConfigListByCondition(CourseHomeConfigConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return courseHomeConfigMapper.getCourseHomeConfigListByCondition(condition);
    }

    @Override
    public AjaxResult addCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo) {
        CourseHomeConfigDto courseHomeConfig = new CourseHomeConfigDto();
        BeanUtils.copyProperties(courseHomeConfigBo, courseHomeConfig);
        courseHomeConfig.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(courseHomeConfig)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo) {
        CourseHomeConfigDto courseHomeConfig = new CourseHomeConfigDto();
        BeanUtils.copyProperties(courseHomeConfigBo, courseHomeConfig);
        if (updateById(courseHomeConfig)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public CourseHomeConfigVo getCourseHomeConfigByCondition(CourseHomeConfigConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        CourseHomeConfigVo vo = courseHomeConfigMapper.getCourseHomeConfigByCondition(condition);
        return vo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Long saveCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo) {
        // 删除该删除的数据
        if (courseHomeConfigBo.getDelHomeConfigId() != null) {
            CourseHomeConfigBo courseHomeConfigBoDel = new CourseHomeConfigBo();
            courseHomeConfigBoDel.setHomeConfigId(courseHomeConfigBo.getDelHomeConfigId());
            courseHomeConfigBoDel.setIsDelete(StatusEnum.ISDELETE.getCode());
            courseHomeConfigService.updateCourseHomeConfig(courseHomeConfigBoDel);
        }

        // 新增或更新数据
        CourseHomeConfigDto courseHomeConfig = new CourseHomeConfigDto();
        BeanUtils.copyProperties(courseHomeConfigBo, courseHomeConfig);
        courseHomeConfigService.saveOrUpdate(courseHomeConfig);
        return courseHomeConfig.getHomeConfigId();
    }
}