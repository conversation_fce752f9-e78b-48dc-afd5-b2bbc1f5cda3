package com.fh.yanx.service.cm.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 代理商信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cm_info")
public class CmInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 活动id
	 */
	@TableField("activ_id")
	private Long activId;

	/**
	 * 活动名称
	 */
	@TableField("activ_name")
	private String activName;

	/**
	 * 用户oid
	 */
	@TableField("admin_oid")
	private String adminOid;

	/**
	 * 代理商名称
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 手机号
	 */
	@TableField("phone")
	private String phone;

	/**
	 * 代理商参数
	 */
	@TableField("cm")
	private String cm;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
