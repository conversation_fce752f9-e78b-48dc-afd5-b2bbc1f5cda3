package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicTeacherDto;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课题指导老师表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface IResTopicTeacherService extends IService<ResTopicTeacherDto> {

    List<ResTopicTeacherVo> getResTopicTeacherListByCondition(ResTopicTeacherConditionBo condition);

	List<String> getResTopicTeacherOidListByTopicId(Long topicId);

	AjaxResult addResTopicTeacher(ResTopicTeacherBo resTopicTeacherBo);

	AjaxResult updateResTopicTeacher(ResTopicTeacherBo resTopicTeacherBo);

	ResTopicTeacherVo getDetail(Long id);

}

