package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 校本课程案例类型
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_course_cases_type")
public class PCourseCasesTypeDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 类型名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 类型
	 */
	@TableField("type")
	private Long type;

	/**
	 * 0-未删除 1-已删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 
	 */
	@TableField("create_date")
	private Date createDate;

	/**
	 * 
	 */
	@TableField("update_date")
	private Date updateDate;

}
