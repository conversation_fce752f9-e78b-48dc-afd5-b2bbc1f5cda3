package com.fh.yanx.service.activ.controller;

import com.fh.yanx.service.activ.api.ActivInfoScheduleApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoScheduleDto;
import com.fh.yanx.service.activ.service.IActivInfoViewPermissionService;
import com.fh.yanx.service.enums.ActivInfoViewType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo;
import com.fh.yanx.service.activ.service.IActivInfoScheduleService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 新时代活动日程表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
@RestController
@Validated
public class ActivInfoScheduleController implements ActivInfoScheduleApi{
	
    @Autowired
    private IActivInfoScheduleService activInfoScheduleService;
    @Autowired
	private IActivInfoViewPermissionService activInfoViewPermissionService;

    /**
     * 查询新时代活动日程表分页列表
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ActivInfoScheduleVo>> getActivInfoSchedulePageListByCondition(@RequestBody ActivInfoScheduleConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ActivInfoScheduleVo> pageInfo = new PageInfo<>(activInfoScheduleService.getActivInfoScheduleListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询新时代活动日程表列表
	 * <AUTHOR>
	 * @date 2023-07-27 17:29:00
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ActivInfoScheduleVo>> getActivInfoScheduleListByCondition(@RequestBody ActivInfoScheduleConditionBo condition){
		List<ActivInfoScheduleVo> list = activInfoScheduleService.getActivInfoScheduleListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增新时代活动日程表
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addActivInfoSchedule(@Validated @RequestBody ActivInfoScheduleBo activInfoScheduleBo){
		return activInfoScheduleService.addActivInfoSchedule(activInfoScheduleBo);
    }

    /**
	 * 修改新时代活动日程表
	 * @param activInfoScheduleBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateActivInfoSchedule(@Validated @RequestBody ActivInfoScheduleBo activInfoScheduleBo) {
		if(null == activInfoScheduleBo.getScheduleId()) {
			return AjaxResult.fail("新时代活动日程表id不能为空");
		}
		return activInfoScheduleService.updateActivInfoSchedule(activInfoScheduleBo);
	}

	/**
	 * 查询新时代活动日程表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ActivInfoScheduleVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("新时代活动日程表id不能为空");
		}
		ActivInfoScheduleConditionBo condition = new ActivInfoScheduleConditionBo();
		condition.setScheduleId(id);
		ActivInfoScheduleVo vo = activInfoScheduleService.getActivInfoScheduleByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除新时代活动日程表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ActivInfoScheduleDto activInfoScheduleDto = new ActivInfoScheduleDto();
		activInfoScheduleDto.setScheduleId(id);
		activInfoScheduleDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(activInfoScheduleService.updateById(activInfoScheduleDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	/**
	 * 保存活动日程
	 *
	 * @param activInfoBo
	 * @return com.light.core.entity.AjaxResult 
	 * <AUTHOR>
	 * @date 2023/7/28 15:30
	 **/
	@Override
	public AjaxResult saveActivSchedules(@RequestBody ActivInfoBo activInfoBo) {
		if (activInfoBo.getActivId() == null) {
			return AjaxResult.fail("活动id不能为空");
		}
		if (activInfoScheduleService.deleteAndAddActivInfoSchedules(activInfoBo)) {
			activInfoBo.setViewType(ActivInfoViewType.ACTIV_SCHEDULE.getCode());
			activInfoViewPermissionService.deleteAndAddActivInfoViewPermission(activInfoBo);
			return AjaxResult.success("保存活动日程成功");
		}
		return AjaxResult.fail("保存活动日程失败");
	}

}
