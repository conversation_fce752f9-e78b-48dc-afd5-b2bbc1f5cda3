package com.fh.yanx.service.pub.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 融合出版书目录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pub_book_directory")
public class PubBookDirectoryDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "book_directory_id", type = IdType.AUTO)
	private Long bookDirectoryId;

	/**
	 * FK出版书id，pub_book表逐渐
	 */
	@TableField("book_id")
	private Long bookId;

	/**
	 * 目录名称
	 */
	@TableField("book_directory_name")
	private String bookDirectoryName;

	/**
	 * 作者名称
	 */
	@TableField("author_name")
	private String authorName;

	/**
	 * 作者学校
	 */
	@TableField("author_school")
	private String authorSchool;

	/**
	 * 书籍目录顺序
	 */
	@TableField("book_directory_index")
	private String bookDirectoryIndex;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
