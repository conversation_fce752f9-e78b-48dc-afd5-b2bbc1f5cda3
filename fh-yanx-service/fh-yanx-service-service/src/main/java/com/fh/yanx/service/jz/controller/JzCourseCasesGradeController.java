package com.fh.yanx.service.jz.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.jz.api.JzCourseCasesGradeApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesGradeDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesGradeVo;
import com.fh.yanx.service.jz.service.IJzCourseCasesGradeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 金中-校本课程案例年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@RestController
@Validated
public class JzCourseCasesGradeController implements JzCourseCasesGradeApi{
	
    @Autowired
    private IJzCourseCasesGradeService jzCourseCasesGradeService;

    /**
     * 查询金中-校本课程案例年级分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @Override
    public AjaxResult<PageInfo<JzCourseCasesGradeVo>> getJzCourseCasesGradePageListByCondition(@RequestBody JzCourseCasesGradeConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<JzCourseCasesGradeVo> pageInfo = new PageInfo<>(jzCourseCasesGradeService.getJzCourseCasesGradeListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询金中-校本课程案例年级列表
	 * <AUTHOR>
	 * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<List<JzCourseCasesGradeVo>> getJzCourseCasesGradeListByCondition(@RequestBody JzCourseCasesGradeConditionBo condition){
		List<JzCourseCasesGradeVo> list = jzCourseCasesGradeService.getJzCourseCasesGradeListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增金中-校本课程案例年级
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
	@Override
    public AjaxResult addJzCourseCasesGrade(@Validated @RequestBody JzCourseCasesGradeBo jzCourseCasesGradeBo){
		return jzCourseCasesGradeService.addJzCourseCasesGrade(jzCourseCasesGradeBo);
    }

    /**
	 * 修改金中-校本课程案例年级
	 * @param jzCourseCasesGradeBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult updateJzCourseCasesGrade(@Validated @RequestBody JzCourseCasesGradeBo jzCourseCasesGradeBo) {
		if(null == jzCourseCasesGradeBo.getId()) {
			return AjaxResult.fail("金中-校本课程案例年级id不能为空");
		}
		return jzCourseCasesGradeService.updateJzCourseCasesGrade(jzCourseCasesGradeBo);
	}

	/**
	 * 查询金中-校本课程案例年级详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<JzCourseCasesGradeVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("金中-校本课程案例年级id不能为空");
		}
		JzCourseCasesGradeConditionBo condition = new JzCourseCasesGradeConditionBo();
		condition.setId(id);
		JzCourseCasesGradeVo vo = jzCourseCasesGradeService.getJzCourseCasesGradeByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除金中-校本课程案例年级
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		JzCourseCasesGradeDto jzCourseCasesGradeDto = new JzCourseCasesGradeDto();
		jzCourseCasesGradeDto.setId(id);
		jzCourseCasesGradeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(jzCourseCasesGradeService.updateById(jzCourseCasesGradeDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
