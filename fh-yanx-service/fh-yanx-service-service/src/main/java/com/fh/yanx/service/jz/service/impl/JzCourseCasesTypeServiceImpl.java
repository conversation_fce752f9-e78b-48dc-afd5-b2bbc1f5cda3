package com.fh.yanx.service.jz.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesTypeDto;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesTypeDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesTypeVo;
import com.fh.yanx.service.jz.mapper.JzCourseCasesTypeMapper;
import com.fh.yanx.service.jz.service.IJzCourseCasesTypeService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * 金中-校本课程案例类型接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@Service
public class JzCourseCasesTypeServiceImpl extends ServiceImpl<JzCourseCasesTypeMapper, JzCourseCasesTypeDto> implements IJzCourseCasesTypeService {

	@Resource
	private JzCourseCasesTypeMapper jzCourseCasesTypeMapper;
	
    @Override
	public List<JzCourseCasesTypeVo> getJzCourseCasesTypeListByCondition(JzCourseCasesTypeConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return jzCourseCasesTypeMapper.getJzCourseCasesTypeListByCondition(condition);
	}

	@Override
	public AjaxResult addJzCourseCasesType(JzCourseCasesTypeBo jzCourseCasesTypeBo) {
		JzCourseCasesTypeDto jzCourseCasesType = new JzCourseCasesTypeDto();
		BeanUtils.copyProperties(jzCourseCasesTypeBo, jzCourseCasesType);
		jzCourseCasesType.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(jzCourseCasesType)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateJzCourseCasesType(JzCourseCasesTypeBo jzCourseCasesTypeBo) {
		JzCourseCasesTypeDto jzCourseCasesType = new JzCourseCasesTypeDto();
		BeanUtils.copyProperties(jzCourseCasesTypeBo, jzCourseCasesType);
		if(updateById(jzCourseCasesType)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public JzCourseCasesTypeVo getJzCourseCasesTypeByCondition(JzCourseCasesTypeConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		JzCourseCasesTypeVo vo = jzCourseCasesTypeMapper.getJzCourseCasesTypeByCondition(condition);
		return vo;
	}

	@Override
	public List<String> getCasesTypeNameList(Long casesId) {
		List<String> casesTypeNameList = new ArrayList<>();
		QueryWrapper<JzCourseCasesTypeDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("cases_id", casesId).eq("is_delete", 0);
		List<JzCourseCasesTypeDto> list = jzCourseCasesTypeMapper.selectList(queryWrapper);
		for (JzCourseCasesTypeDto item : list) {
			casesTypeNameList.add(item.getName());
		}
		return casesTypeNameList;
	}
}