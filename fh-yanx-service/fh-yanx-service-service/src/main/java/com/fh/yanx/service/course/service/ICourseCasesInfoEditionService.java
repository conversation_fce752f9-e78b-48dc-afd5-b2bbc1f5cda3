package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseCasesInfoEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本课程案例详细信息版本记录接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
public interface ICourseCasesInfoEditionService extends IService<CourseCasesInfoEditionDto> {

    List<CourseCasesInfoEditionVo> getCourseCasesInfoEditionListByCondition(CourseCasesInfoEditionConditionBo condition);

	AjaxResult addCourseCasesInfoEdition(CourseCasesInfoEditionBo courseCasesInfoEditionBo);

	AjaxResult updateCourseCasesInfoEdition(CourseCasesInfoEditionBo courseCasesInfoEditionBo);

	CourseCasesInfoEditionVo getCourseCasesInfoEditionByCondition(CourseCasesInfoEditionConditionBo condition);

	/**
	 * 获取详情
	 *
	 * @param casesEditionId the cases id
	 * @return p course cases info vo
	 * <AUTHOR>
	 * @date 2023 -08-14 14:17:01
	 */
	CourseCasesInfoEditionVo infoDetail(Long casesEditionId);
}

