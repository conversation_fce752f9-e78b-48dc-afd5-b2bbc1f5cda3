package com.fh.yanx.service.pub.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryDto;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryConditionBo;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryVo;

/**
 * 融合出版书目录Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface PubBookDirectoryMapper extends BaseMapper<PubBookDirectoryDto> {

	List<PubBookDirectoryVo> getPubBookDirectoryListByCondition(PubBookDirectoryConditionBo condition);

	PubBookDirectoryVo getPubBookDirectoryByCondition(PubBookDirectoryConditionBo condition);

}
