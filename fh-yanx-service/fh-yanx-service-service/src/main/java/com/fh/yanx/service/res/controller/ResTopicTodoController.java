package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.enums.TodoStatusType;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CompleteTodoEvent;
import com.fh.yanx.service.res.api.ResTopicTodoApi;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.fh.yanx.service.res.entity.dto.ResTopicTodoDto;
import com.fh.yanx.service.res.entity.vo.ResTopicTodoVo;
import com.fh.yanx.service.res.service.IResTopicTodoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户待办事项
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
public class ResTopicTodoController implements ResTopicTodoApi {

    @Autowired
    private IResTopicTodoService resTopicTodoService;
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 查询用户待办事项分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<PageInfo<ResTopicTodoVo>>
        getResTopicTodoPageListByCondition(@RequestBody ResTopicTodoConditionBo condition) {
        condition.setOrderBy("update_time desc");
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicTodoVo> pageInfo =
            new PageInfo<>(resTopicTodoService.getResTopicTodoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询用户待办事项列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<List<ResTopicTodoVo>>
        getResTopicTodoListByCondition(@RequestBody ResTopicTodoConditionBo condition) {
        condition.setOrderBy("update_time desc");
        List<ResTopicTodoVo> list = resTopicTodoService.getResTopicTodoListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增用户待办事项
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult addResTopicTodo(@Validated @RequestBody ResTopicTodoBo resTopicTodoBo) {
        return resTopicTodoService.addResTopicTodo(resTopicTodoBo);
    }

    /**
     * 修改用户待办事项
     *
     * @param resTopicTodoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult updateResTopicTodo(@Validated @RequestBody ResTopicTodoBo resTopicTodoBo) {
        if (null == resTopicTodoBo.getTodoId()) {
            return AjaxResult.fail("用户待办事项id不能为空");
        }
        return resTopicTodoService.updateResTopicTodo(resTopicTodoBo);
    }

    /**
     * 查询用户待办事项详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<ResTopicTodoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("用户待办事项id不能为空");
        }
        ResTopicTodoVo vo = resTopicTodoService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除用户待办事项
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicTodoDto resTopicTodoDto = new ResTopicTodoDto();
        resTopicTodoDto.setTodoId(id);
        resTopicTodoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicTodoService.updateById(resTopicTodoDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult completeTodo(Long id, boolean student) {
        if (student) {
            applicationContext.publishEvent(CompleteTodoEvent
                .produceTopicEvent(TopicTodoType.STUDENT_TOPIC_VIEW.getType(), null, null, null, null, id));
        } else {
            applicationContext.publishEvent(CompleteTodoEvent
                .produceTopicEvent(TopicTodoType.TEACHER_TOPIC_VIEW.getType(), null, null, null, null, id));
        }
        return AjaxResult.success("处理成功");
    }
}
