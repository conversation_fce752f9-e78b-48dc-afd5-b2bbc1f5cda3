package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResOpenCourseApi;
import com.fh.yanx.service.res.entity.dto.ResOpenCourseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResOpenCourseConditionBo;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseBo;
import com.fh.yanx.service.res.entity.vo.ResOpenCourseVo;
import com.fh.yanx.service.res.service.IResOpenCourseService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 公开课表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
@RestController
@Validated
public class ResOpenCourseController implements ResOpenCourseApi {

    @Autowired
    private IResOpenCourseService resOpenCourseService;

    /**
     * 查询公开课表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @Override
    public AjaxResult<PageInfo<ResOpenCourseVo>> getResOpenCoursePageListByCondition(@RequestBody ResOpenCourseConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResOpenCourseVo> pageInfo = new PageInfo<>(resOpenCourseService.getResOpenCourseListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询公开课表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @Override
    public AjaxResult<List<ResOpenCourseVo>> getResOpenCourseListByCondition(@RequestBody ResOpenCourseConditionBo condition) {
        List<ResOpenCourseVo> list = resOpenCourseService.getResOpenCourseListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增公开课表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @Override
    public AjaxResult addResOpenCourse(@Validated @RequestBody ResOpenCourseBo resOpenCourseBo) {
        return resOpenCourseService.addResOpenCourse(resOpenCourseBo);
    }

    /**
     * 修改公开课表
     *
     * @param resOpenCourseBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @Override
    public AjaxResult updateResOpenCourse(@Validated @RequestBody ResOpenCourseBo resOpenCourseBo) {
        if (null == resOpenCourseBo.getOpenCourseId()) {
            return AjaxResult.fail("公开课表id不能为空");
        }
        return resOpenCourseService.updateResOpenCourse(resOpenCourseBo);
    }

    /**
     * 查询公开课表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @Override
    public AjaxResult<ResOpenCourseVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("公开课表id不能为空");
        }
        ResOpenCourseVo vo = resOpenCourseService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除公开课表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResOpenCourseDto resOpenCourseDto = new ResOpenCourseDto();
        resOpenCourseDto.setOpenCourseId(id);
        resOpenCourseDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resOpenCourseService.updateById(resOpenCourseDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
