package com.fh.yanx.service.bm.entity.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代文化校园报名活动申请表-参与人信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bm_info_joiner")
public class BmInfoJoinerDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 申请记录主表id
	 */
	@TableField("info_id")
	private Long infoId;

	/**
	 * 姓名
	 */
	@TableField("joiner_name")
	private String joinerName;

	/**
	 * 性别：1男，2女
	 */
	@TableField("joiner_gender")
	private Integer joinerGender;

	/**
	 * 民族
	 */
	@TableField("joiner_nation")
	private String joinerNation;

	/**
	 * 
	 */
	@TableField("joiner_duties")
	private String joinerDuties;

	/**
	 * 教学学科/工作
	 */
	@TableField("joiner_teach")
	private String joinerTeach;

	/**
	 * 邮箱
	 */
	@TableField("joiner_mail")
	private String joinerMail;

	/**
	 * 手机号码
	 */
	@TableField("joiner_mobile")
	private String joinerMobile;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 学习时长
	 */
	@TableField("study_hours")
	private BigDecimal studyHours;

	/**
	 * 参会证明文件oid
	 */
	@TableField("join_prove_id")
	private String joinProveId;

	/**
	 * 参会证明文件地址
	 */
	@TableField("join_prove_url")
	private String joinProveUrl;

	/**
	 * 参会证明文件名不带后缀
	 */
	@TableField("join_prove_name")
	private String joinProveName;

	/**
	 * 参会证明文件名带后缀
	 */
	@TableField("join_prove_name_ori")
	private String joinProveNameOri;

	/**
	 * 学时证明生成状态 1-未生成 2-生成中 3-已生成
	 */
	@TableField("study_prove_type")
	private Integer studyProveType;

	/**
	 * 学时证明文件oid
	 */
	@TableField("study_prove_id")
	private String studyProveId;

	/**
	 * 学时证明文件地址
	 */
	@TableField("study_prove_url")
	private String studyProveUrl;

	/**
	 * 学时证明文件名不带后缀
	 */
	@TableField("study_prove_name")
	private String studyProveName;

	/**
	 * 学时证明文件名带后缀
	 */
	@TableField("study_prove_name_ori")
	private String studyProveNameOri;
}
