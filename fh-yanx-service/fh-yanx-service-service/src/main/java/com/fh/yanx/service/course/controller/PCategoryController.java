package com.fh.yanx.service.course.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fh.yanx.service.consts.ConstString;
import com.fh.yanx.service.course.api.PCategoryApi;
import com.fh.yanx.service.course.entity.bo.PCategoryBo;
import com.fh.yanx.service.course.entity.bo.PCategoryConditionBo;
import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.entity.vo.PCategoryVo;
import com.fh.yanx.service.course.service.IPCategoryService;
import com.fh.yanx.service.utils.CopyUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.UnifiedException;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 资源类别
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@RestController
@Validated
public class PCategoryController implements PCategoryApi {

    @Autowired
    private IPCategoryService pCategoryService;

    // 公共组织id
    public static final Long ORGANIZATION_PUBLIC_ID = 0L;
    // 父级最高ID
    public final static Long PARENT_HIGHEST_ID = 0L;
    // 不分页
    public final static Integer PAGE_UNLIMIT = 0;
    // 不分页
    public final static Integer PAGE_NOLIMIT = -1;

    /**
     * 查询资源类别分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PageInfo<PCategoryVo>>
        getPCategoryPageListByCondition(@RequestBody PCategoryConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<PCategoryVo> pageInfo = new PageInfo<>(pCategoryService.getPCategoryListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询资源类别列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<List<PCategoryVo>> getPCategoryListByCondition(@RequestBody PCategoryConditionBo condition) {
        List<PCategoryVo> list = pCategoryService.getPCategoryListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增资源类别
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult addPCategory(@Validated @RequestBody PCategoryBo pCategoryBo) {
        return pCategoryService.addPCategory(pCategoryBo);
    }

    /**
     * 修改资源类别
     * 
     * @param pCategoryBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult updatePCategory(@Validated @RequestBody PCategoryBo pCategoryBo) {
        if (null == pCategoryBo.getId()) {
            return AjaxResult.fail("资源类别id不能为空");
        }
        return pCategoryService.updatePCategory(pCategoryBo);
    }

    /**
     * 查询资源类别详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PCategoryVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("资源类别id不能为空");
        }
        PCategoryConditionBo condition = new PCategoryConditionBo();
        condition.setId(id);
        PCategoryVo vo = pCategoryService.getPCategoryByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除资源类别
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        if (pCategoryService.removeById(id)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult getPageTreeList(PCategoryConditionBo condition) {
        // 获得所有未删除的分类
        AjaxResult<List<PCategoryVo>> allResult = getCategoryTreeList(condition);
        List<PCategoryVo> all = allResult.getData();
        // 总数量
        int total = all.size();
        List<PCategoryVo> pageList;
        if (total == 0) {
            pageList = Collections.emptyList();
        } else {
            // limit为-1，标识不分页，不限制
            if (condition.getPageNo() == SystemConstants.NO_PAGE) {
                pageList = all.stream()
                    // 转换为集合
                    .collect(Collectors.toList());
            } else {
                pageList = all.stream()
                    // 跳过start个元素
                    .skip(condition.getStart())
                    // 截取limit个元素
                    .limit(condition.getPageSize())
                    // 转换为集合
                    .collect(Collectors.toList());
            }

        }
        PageInfo<PCategoryVo> pageInfo = new PageInfo<>(pageList);
        pageInfo.setList(pageList);
        pageInfo.setTotal(total);
        return AjaxResult.success(pageInfo);

    }

    @Override
    public AjaxResult<List<PCategoryVo>> getCategoryTreeList(PCategoryConditionBo condition) {

        QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
        if (condition.getFlag()) {
            qw.eq("state", StatusEnum.ENABLE.getCode());
        } else {
            qw.ne("state", StatusEnum.DISABLE.getCode());
        }
        // 支持公共类别，即组织机构id为0的
        // qw.eq("organization_id", pageLimitBo.getOrganizationId());
        qw.and(wrapper -> wrapper.eq("organization_id", condition.getOrganizationId()).or().eq("organization_id",
            ORGANIZATION_PUBLIC_ID));
        if (condition.getModuleId() != null) {
            qw.eq("module_id", condition.getModuleId());
        }
        if (null != condition.getParentId()) {
            qw.eq("parent_id", condition.getParentId());
        }
        // 查询出指定应用下所有类别
        List<PCategoryDto> list = pCategoryService.list(qw);

        // 对象拷贝
        List<PCategoryVo> voList = CopyUtils.parse(list, PCategoryVo.class);

        // 过滤出一级类别
        if (condition.getParentId() == null) {
            condition.setParentId(PARENT_HIGHEST_ID);
        }
        if (condition.getCategoryLevel() != null && condition.getCategoryLevel() - PAGE_UNLIMIT != 0) {
            // 说明分类要分级
            Integer i = 1;
            List<PCategoryVo> sumList = voList.stream()
                .filter(category -> condition.getParentId().equals(category.getParentId())).map(category -> {
                    // 调用方法 递归获得子级集合
                    category.setChildrenList(childrenLevelList(category, voList, condition.getCategoryLevel(), i));
                    return category;
                })
                // 根据sequence字段排序
                .sorted(Comparator.comparingInt(cat -> cat.getSequence() == null ? 0 : cat.getSequence()))
                .collect(Collectors.toList());
            return AjaxResult.success(sumList);
        } else {
            List<PCategoryVo> sumList = voList.stream()
                .filter(category -> condition.getParentId().equals(category.getParentId())).map(category -> {
                    // 调用方法 递归获得子级集合
                    category.setChildrenList(childrenList(category, voList));
                    return category;
                })
                // 根据sequence字段排序
                .sorted(Comparator.comparingInt(cat -> cat.getSequence() == null ? 0 : cat.getSequence()))
                .collect(Collectors.toList());
            return AjaxResult.success(sumList);
        }
    }

    @Override
    public AjaxResult<List<PCategoryVo>> getCategoryList(PCategoryConditionBo condition) {
        QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
        if (condition.getFlag()) {
            qw.eq("state", StatusEnum.ENABLE.getCode());
        } else {
            qw.ne("state", StatusEnum.DISABLE.getCode());
        }
        // 支持公共类别，即组织机构id为0的
        // qw.eq("organization_id", pageLimitBo.getOrganizationId());
        qw.and(wrapper -> wrapper.eq("organization_id", condition.getOrganizationId()).or().eq("organization_id",
            ORGANIZATION_PUBLIC_ID));
        if (condition.getModuleId() != null) {
            qw.eq("module_id", condition.getModuleId());
        }
        if (null != condition.getParentId()) {
            qw.eq("parent_id", condition.getParentId());
        }
        qw.orderByAsc("sequence");
        // 查询出指定应用下所有类别
        List<PCategoryDto> list = pCategoryService.list(qw);

        // 对象拷贝
        List<PCategoryVo> voList = CopyUtils.parse(list, PCategoryVo.class);

        return AjaxResult.success(voList);
    }

    @Override
    public AjaxResult addCategory(PCategoryBo pCategoryBo) {
        // 先查询同级 有无重复名称
        QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
        qw.eq("organization_id", pCategoryBo.getOrganizationId()).ne("state", StatusEnum.ISDELETE.getCode())
            .eq("name", pCategoryBo.getName()).eq("module_id", pCategoryBo.getModuleId())
            .eq("parent_id", pCategoryBo.getParentId());

        PCategoryDto category = pCategoryService.getOne(qw);
        if (category == null) {
            // 名称不重复 判断顺序
            QueryWrapper<PCategoryDto> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", pCategoryBo.getParentId())
                .eq("organization_id", pCategoryBo.getOrganizationId()).eq("module_id", pCategoryBo.getModuleId())
                .ne("state", StatusEnum.ISDELETE.getCode());
            List<PCategoryDto> list = pCategoryService.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                // 判断子分类中是否包含此顺序和名称
                boolean sequenceFlag = list.stream().anyMatch(c -> c.getSequence().equals(pCategoryBo.getSequence()));
                if (sequenceFlag) {
                    // 顺序重复 直接返回
                    return AjaxResult.fail("顺序重复");
                }

            }

            PCategoryDto categoryDto = new PCategoryDto();
            if (pCategoryBo.getParentId() > 0) {
                // 如果是父级则查出其module_id
                QueryWrapper<PCategoryDto> qwr = new QueryWrapper<>();
                qwr.eq("id", pCategoryBo.getParentId());
                PCategoryDto one = pCategoryService.getOne(qwr);
                categoryDto.setModuleId(one.getModuleId());
                categoryDto.setLevel(one.getLevel() + 1);// level加一级
            } else {
                categoryDto.setLevel(1);// 否则为第一等级
            }
            pCategoryBo.setLevel(categoryDto.getLevel());
            // 拷贝对象属性到dto
            BeanUtils.copyProperties(pCategoryBo, categoryDto);
            categoryDto.setCreateTime(new Date());
            categoryDto.setOrganizationId(pCategoryBo.getOrganizationId());
            categoryDto.setState(StatusEnum.ENABLE.getCode());
            boolean save = pCategoryService.save(categoryDto);
            if (save) {

                return AjaxResult.success(true);
            } else {
                return AjaxResult.fail();
            }
        } else {
            // 对象存在 id还不一样 直接返回
            return AjaxResult.fail();
        }
    }

    @Override
    public AjaxResult updateCategory(PCategoryBo pCategoryBo) {
        // 先查询有无重复名称
        QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
        qw.eq("organization_id", pCategoryBo.getOrganizationId()).ne("state", StatusEnum.ISDELETE.getCode())
            .eq("name", pCategoryBo.getName()).eq("module_id", pCategoryBo.getModuleId())
            .eq("parent_id", pCategoryBo.getParentId());
        PCategoryDto category = pCategoryService.getOne(qw);
        if (category == null || category.getId().equals(pCategoryBo.getId())) {
            // 不存在或者 id
            QueryWrapper<PCategoryDto> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", pCategoryBo.getParentId())
                .eq("organization_id", pCategoryBo.getOrganizationId()).eq("module_id", pCategoryBo.getModuleId())
                .ne("state", StatusEnum.ISDELETE.getCode()).ne("id", pCategoryBo.getId());
            List<PCategoryDto> list = pCategoryService.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                // 判断子分类中是否包含此顺序
                boolean sequenceFlag = list.stream().anyMatch(c -> c.getSequence().equals(pCategoryBo.getSequence()));
                if (sequenceFlag) {
                    // 顺序重复 直接返回
                    return AjaxResult.fail();
                }
            }
            UpdateWrapper<PCategoryDto> upw = new UpdateWrapper<>();
            upw.eq("id", pCategoryBo.getId()).set("name", pCategoryBo.getName())
                .set("sequence", pCategoryBo.getSequence()).set("module_id", pCategoryBo.getModuleId())
                .set("update_time", new Date());
            boolean update = pCategoryService.update(upw);
            if (update) {
                return AjaxResult.success();
            } else {
                return AjaxResult.fail();
            }
        } else {
            // 对象存在 id还不一样 直接返回
            return AjaxResult.fail();
        }
    }

    @Override
    public AjaxResult<Boolean> deleteCategory(List<Long> ids) {
        UpdateWrapper<PCategoryDto> upw = new UpdateWrapper<>();
        upw.in("id", ids).set("state", StatusEnum.ISDELETE.getCode());
        boolean update = pCategoryService.update(upw);
        return update ? AjaxResult.success(true) : AjaxResult.fail();
    }

    @Override
    public AjaxResult editStatus(Integer state, Long id) {
        UpdateWrapper<PCategoryDto> upw = new UpdateWrapper<>();
        upw.eq("id", id).set("state", state).set("update_time", new Date());
        // 判断该id下是否含有子级分类
        QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
        qw.eq("parent_id", id).ne("state", StatusEnum.ISDELETE.getCode());
        List<PCategoryDto> list = pCategoryService.list(qw);

        if (CollectionUtil.isEmpty(list)) {
            // 没有子级直接更新状态
            if (pCategoryService.update(upw)) {
                return AjaxResult.success();
            } else {
                return AjaxResult.fail();
            }
        } else {
            // 有子级 一起更新
            UpdateWrapper<PCategoryDto> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("parent_id", id).set("state", state).set("update_time", new Date());
            boolean updateChildren = pCategoryService.update(updateWrapper);
            boolean update = pCategoryService.update(upw);
            if (update && updateChildren) {
                return AjaxResult.success();
            } else {
                return AjaxResult.fail();
            }
        }

    }

    @Override
    public AjaxResult<List<PCategoryVo>> getListByIds(String categoryIds) {
        List<String> list = Arrays.asList(categoryIds.substring(1).split(ConstString.ywdh));
        List<PCategoryDto> categoryList;
        if (CollectionUtil.isNotEmpty(list)) {
            List<Long> ids = list.stream()
                // 字符串转long
                .map(Long::valueOf).collect(Collectors.toList());
            QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
            // 条件查询未删除的相册
            qw.in("id", ids).ne("state", StatusEnum.ISDELETE.getCode());
            categoryList = pCategoryService.list(qw);
        } else {
            throw new UnifiedException("获取相册id集合失败");
        }
        return AjaxResult.success(categoryList);
    }

    @Override
    public AjaxResult<List<PCategoryVo>> getListByIdsList(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.success(Lists.newArrayList());
        }

        List<PCategoryDto> categoryList;
        QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
        qw.in("id", ids).ne("state", StatusEnum.ISDELETE.getCode());
        categoryList = pCategoryService.list(qw);
        return AjaxResult.success(categoryList);
    }

    @Override
    public AjaxResult<List<Map<String, Long>>> getCategoryMap(List<Long> ids) {
        AjaxResult<List<PCategoryVo>> listAjaxResult = getListByIdsList(ids);
        List<PCategoryVo> list = listAjaxResult.getData();
        List<Map<String, Long>> mapList = new ArrayList<>();
        for (PCategoryVo category : list) {
            Map<String, Long> map = new HashMap<>(2);
            // 遍历集合 获得类别id 和父级id
            map.put("parent_id", category.getParentId());
            map.put("id", category.getId());
            mapList.add(map);
        }
        return AjaxResult.success(mapList);
    }

    @Override
    public AjaxResult<PCategoryVo> getOneByName1(String name, Integer parentId, Long organizationId) {
        QueryWrapper<PCategoryDto> queryWrapper = new QueryWrapper();
        queryWrapper.eq("name", name);

        // 支持公共类别，即组织机构id为0的
        // qw.eq("organization_id", pageLimitBo.getOrganizationId());
        queryWrapper.and(wrapper -> wrapper.eq("organization_id", organizationId).or().eq("organization_id",
            ORGANIZATION_PUBLIC_ID));

        if (parentId != null) {
            queryWrapper.eq("parent_id", parentId);
        }
        PCategoryDto pCategoryDto = pCategoryService.getOne(queryWrapper);
        return AjaxResult.success(pCategoryDto);
    }

    @Override
    public AjaxResult<Map<Long, String>> getMapByIds(List<Long> targetIds) {
        LambdaQueryWrapper<PCategoryDto> lqw = new LambdaQueryWrapper<PCategoryDto>();
        lqw.in(PCategoryDto::getId, targetIds);
        Map<Long, String> map = new HashMap<Long, String>();
        List<PCategoryDto> list = pCategoryService.list(lqw);
        if (!CollectionUtils.isEmpty(list)) {
            map = list.stream().collect(Collectors.toMap(PCategoryDto::getId, PCategoryDto::getName));
        }
        return AjaxResult.success(map);
    }

    @Override
    public AjaxResult<List<PCategoryVo>> getAcademicSectionList(Long organizationId) {
        QueryWrapper<PCategoryDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", 0).eq("state", 1).eq("module_id", 1);
        queryWrapper.and(wrapper -> wrapper.eq("organization_id", organizationId).or().eq("organization_id",
            ORGANIZATION_PUBLIC_ID));
        List<PCategoryDto> list = pCategoryService.list(queryWrapper);
        List<PCategoryVo> voList = CopyUtils.parse(list, PCategoryVo.class);
        return AjaxResult.success(voList);
    }

    /**
     * 递归 获得子级集合
     *
     * @param pCategoryVo
     * @param list
     * @return
     */
    private List<PCategoryVo> childrenList(PCategoryVo pCategoryVo, List<PCategoryVo> list) {
        List<PCategoryVo> children = list.stream()
            // 判断id和集合对象的父级id是否相同
            .filter(category -> pCategoryVo.getId().equals(category.getParentId()))
            // 映射 递归调用 方法 获得子级类别
            .map(category -> {
                category.setChildrenList(childrenList(category, list));
                return category;
            })
            // 根据sequence排序
            .sorted(Comparator.comparingInt(category -> category.getSequence() == null ? 0 : category.getSequence()))
            // 转化为list集合
            .collect(Collectors.toList());
        return children;
    }

    /**
     * 递归 获得子级集合
     *
     * @param pCategoryVo
     * @param list
     * @return
     */
    private List<PCategoryVo> childrenLevelList(PCategoryVo pCategoryVo, List<PCategoryVo> list, Long categoryLevel,
        Integer i) {
        if (categoryLevel - i == 0) {
            List<PCategoryVo> children = new ArrayList<>();
            return children;
        }
        List<PCategoryVo> children = list.stream()
            // 判断id和集合对象的父级id是否相同
            .filter(category -> pCategoryVo.getId().equals(category.getParentId()))
            // 映射 递归调用 方法 获得子级类别
            .map(category -> {
                category.setChildrenList(childrenLevelList(category, list, categoryLevel, i + 1));
                return category;
            })
            // 根据sequence排序
            .sorted(Comparator.comparingInt(category -> category.getSequence() == null ? 0 : category.getSequence()))
            // 转化为list集合
            .collect(Collectors.toList());
        return children;
    }
}
