package com.fh.yanx.service.activ.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代活动日程表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activ_info_schedule")
public class ActivInfoScheduleDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "schedule_id", type = IdType.AUTO)
	private Long scheduleId;

	/**
	 * 活动id
	 */
	@TableField("activ_id")
	private Long activId;

	/**
	 * 日程名称
	 */
	@TableField("schedule_name")
	private String scheduleName;

	/**
	 * 拉流地址
	 */
	@TableField("schedule_url")
	private String scheduleUrl;

	/**
	 * 活动日程顺序
	 */
	@TableField("schedule_index")
	private Long scheduleIndex;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
