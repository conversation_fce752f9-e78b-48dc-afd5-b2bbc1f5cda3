package com.fh.yanx.service.baseinfo.impl;

import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.baseinfo.BaseDataType;
import com.light.user.teacher.entity.vo.TeacherVo;

/**
 * 云屏自己基础业务的实现。（教育级别、学段、年级的工具类需要替换修改）
 *
 * <AUTHOR>
 * @date 2022/6/1 10:29
 */
public class BaseDataYanxServiceImpl implements BaseDataService {

    @Override
    public String name() {
        return BaseDataType.YANX_BASE.getName();
    }

    @Override
    public String echo() {
        return "BaseDataYanxServiceImpl...";
    }
}
