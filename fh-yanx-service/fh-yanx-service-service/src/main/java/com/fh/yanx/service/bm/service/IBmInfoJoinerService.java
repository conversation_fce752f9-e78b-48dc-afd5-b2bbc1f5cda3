package com.fh.yanx.service.bm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoCheckBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoJoinerDto;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerConditionBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 新时代文化校园报名活动申请表-参与人信息表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
public interface IBmInfoJoinerService extends IService<BmInfoJoinerDto> {

    List<BmInfoJoinerVo> getBmInfoJoinerListByCondition(BmInfoJoinerConditionBo condition);

    AjaxResult addBmInfoJoiner(BmInfoJoinerBo bmInfoJoinerBo);

    AjaxResult updateBmInfoJoiner(BmInfoJoinerBo bmInfoJoinerBo);

    BmInfoJoinerVo getBmInfoJoinerByCondition(BmInfoJoinerConditionBo condition);

    /**
     * 查询报名活动下参与人数量
     *
     * @param infoIds the info ids
     * @return joiner count by info id
     */
    List<BmInfoVo> getJoinerCountByInfoId(List<Long> infoIds);

    /**
     * 批量更新（先删除，再新增）
     *
     * @param infoId the info id
     * @param bmInfoJoinerBos the bm info joiner bos
     * @return boolean boolean
     */
    boolean deleteAndBatchAddBmInfoJoiner(Long infoId, List<BmInfoJoinerBo> bmInfoJoinerBos);

    /**
     * 校验参与人是否已参与活动报名
     *
     * @param checkBo
     * @return java.util.List<com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo>
     * <AUTHOR>
     * @date 2023/8/4 13:56
     **/
    List<BmInfoJoinerVo> checkBmInfoJoiner(BmInfoCheckBo checkBo);

    /**
     * 根据activId查询本次活动所有已确认的参与人
     *
     * @param activId the activ id
     * @param submitType the submit type
     * @return list list
     * <AUTHOR>
     * @date 2023 -08-09 10:44:54
     */
    List<BmInfoJoinerVo> listJoinerByActivId(Long activId, Integer submitType);

    /**
     * 生成学时证明
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/3/26 17:40
     **/
    AjaxResult studyProveGenerate();

    /**
     * 根据info_id和手机号更新参会人信息
     *
     * @return
     */
    AjaxResult updateByMobile(BmInfoJoinerBo bmInfoJoinerBo);

    /**
     * 根据infoId和手机号获取参会人信息
     *
     * @param bmInfoJoinerBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/4/18 16:03
     **/
    AjaxResult getByMobile(BmInfoJoinerBo bmInfoJoinerBo);
}
