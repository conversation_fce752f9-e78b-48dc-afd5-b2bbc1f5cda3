package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicHonorAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicHonorAttachmentVo;

/**
 * 课题荣誉表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicHonorAttachmentMapper extends BaseMapper<ResTopicHonorAttachmentDto> {

	List<ResTopicHonorAttachmentVo> getResTopicHonorAttachmentListByCondition(ResTopicHonorAttachmentConditionBo condition);

}
