package com.fh.yanx.service.activ.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代文化校园活动内容观看记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activ_info_record_view")
public class ActivInfoRecordViewDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 新时代文化校园活动内容表id
	 */
	@TableField("record_id")
	private Long recordId;

	/**
	 * FK新时代文化校园活动信息表的id，冗余
	 */
	@TableField("activ_id")
	private Long activId;

	/**
	 * 资源标题名称
	 */
	@TableField("view_name")
	private String viewName;

	/**
	 * 观看日期(点击了观看的日期)
	 */
	@TableField("view_date")
	private Date viewDate;

	/**
	 * 观看行为：1点击观看
	 */
	@TableField("view_action_type")
	private Integer viewActionType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
