package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicStuAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStuAttachmentVo;

/**
 * 课题研究附件表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicStuAttachmentMapper extends BaseMapper<ResTopicStuAttachmentDto> {

	List<ResTopicStuAttachmentVo> getResTopicStuAttachmentListByCondition(ResTopicStuAttachmentConditionBo condition);

}
