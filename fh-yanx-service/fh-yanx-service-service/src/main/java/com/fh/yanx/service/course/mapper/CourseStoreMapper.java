package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseStoreDto;
import com.fh.yanx.service.course.entity.bo.CourseStoreConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseStoreVo;

/**
 * 课程收藏表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-04 18:33:34
 */
public interface CourseStoreMapper extends BaseMapper<CourseStoreDto> {

	List<CourseStoreVo> getCourseStoreListByCondition(CourseStoreConditionBo condition);

	CourseStoreVo getCourseStoreByCondition(CourseStoreConditionBo condition);

}
