package com.fh.yanx.service.activ.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代文化校园活动介绍表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activ_info_introduce")
public class ActivInfoIntroduceDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "introduce_id", type = IdType.AUTO)
	private Long introduceId;

	/**
	 * FK新时代文化校园活动信息表的id
	 */
	@TableField("activ_id")
	private Long activId;

	/**
	 * 介绍类型：1标题，2图片，3文字
	 */
	@TableField("introduce_type")
	private Integer introduceType;

	/**
	 * 顺序
	 */
	@TableField("introduce_sort")
	private Long introduceSort;

	/**
	 * 活动标题
	 */
	@TableField("introduce_title")
	private String introduceTitle;

	/**
	 * 活动介绍图片文件oid
	 */
	@TableField("introduce_image_id")
	private String introduceImageId;

	/**
	 * 活动介绍图片文件地址
	 */
	@TableField("introduce_image_url")
	private String introduceImageUrl;

	/**
	 * 活动介绍内容
	 */
	@TableField("introduce_content")
	private String introduceContent;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
