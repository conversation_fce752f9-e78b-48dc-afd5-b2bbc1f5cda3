package com.fh.yanx.service.org.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyConditionBo;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyDto;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyVo;
import com.fh.yanx.service.org.mapper.OrganizationApplyMapper;
import com.fh.yanx.service.org.service.IOrganizationApplyService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 组织申请表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
@Service
public class OrganizationApplyServiceImpl extends ServiceImpl<OrganizationApplyMapper, OrganizationApplyDto> implements IOrganizationApplyService {

	@Resource
	private OrganizationApplyMapper organizationApplyMapper;
	
    @Override
	public List<OrganizationApplyVo> getOrganizationApplyListByCondition(OrganizationApplyConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return organizationApplyMapper.getOrganizationApplyListByCondition(condition);
	}

	@Override
	public AjaxResult addOrganizationApply(OrganizationApplyBo organizationApplyBo) {
		OrganizationApplyDto organizationApply = new OrganizationApplyDto();
		BeanUtils.copyProperties(organizationApplyBo, organizationApply);
		organizationApply.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(organizationApply)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateOrganizationApply(OrganizationApplyBo organizationApplyBo) {
		OrganizationApplyDto organizationApply = new OrganizationApplyDto();
		BeanUtils.copyProperties(organizationApplyBo, organizationApply);
		if(updateById(organizationApply)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public OrganizationApplyVo getOrganizationApplyByCondition(OrganizationApplyConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return organizationApplyMapper.getOrganizationApplyByCondition(condition);
	}

}