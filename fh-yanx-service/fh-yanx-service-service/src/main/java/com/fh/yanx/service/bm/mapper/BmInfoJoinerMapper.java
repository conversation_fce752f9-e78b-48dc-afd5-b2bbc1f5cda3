package com.fh.yanx.service.bm.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.bm.entity.bo.BmInfoCheckBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoJoinerDto;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerConditionBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * 新时代文化校园报名活动申请表-参与人信息表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
public interface BmInfoJoinerMapper extends BaseMapper<BmInfoJoinerDto> {

    List<BmInfoJoinerVo> getBmInfoJoinerListByCondition(BmInfoJoinerConditionBo condition);

    BmInfoJoinerVo getBmInfoJoinerByCondition(BmInfoJoinerConditionBo condition);

    /**
     * 查询活动参与人的数量
     * 
     * @param infoIds
     * @return
     */
    List<BmInfoVo> getJoinerCountByInfoId(@Param("infoIds") List<Long> infoIds);

    /**
     * 根据手机号码查询活动参与人
     *
     * @param activId
     * @param mobiles
     * @return java.util.List<com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo>
     * <AUTHOR>
     * @date 2023/8/4 13:58
     **/
    List<BmInfoJoinerVo> getBmInfoJoinerByMobile(@Param("activId") Long activId,
        @Param("mobiles") List<String> mobiles);

    /**
     * 根据activId查询本次活动所有已确认的参与人
     *
     * @param activId the activ id
     * @param submitType the submit type
     * @return list list
     * <AUTHOR>
     * @date 2023 -08-09 10:44:54
     */
    List<BmInfoJoinerVo> listJoinerByActivId(@Param("activId") Long activId, @Param("submitType") Integer submitType);

}
