package com.fh.yanx.service.course.controller;

import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.course.api.CourseStoreApi;
import com.fh.yanx.service.course.entity.dto.CourseStoreDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.entity.bo.CourseStoreConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseStoreBo;
import com.fh.yanx.service.course.entity.vo.CourseStoreVo;
import com.fh.yanx.service.course.service.ICourseStoreService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 课程收藏表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-04 18:33:34
 */
@RestController
@Validated
public class CourseStoreController implements CourseStoreApi{
	
    @Autowired
    private ICourseStoreService courseStoreService;

	@Autowired
	private BaseDataService baseDataService;

    /**
     * 查询课程收藏表分页列表
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @Override
    public AjaxResult<PageInfo<CourseStoreVo>> getCourseStorePageListByCondition(@RequestBody CourseStoreConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseStoreVo> pageInfo = new PageInfo<>(courseStoreService.getCourseStoreListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程收藏表列表
	 * <AUTHOR>
	 * @date 2024-12-04 18:33:34
	 */
	@Override
	public AjaxResult<List<CourseStoreVo>> getCourseStoreListByCondition(@RequestBody CourseStoreConditionBo condition){
		List<CourseStoreVo> list = courseStoreService.getCourseStoreListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程收藏表
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
	@Override
    public AjaxResult addCourseStore(@Validated @RequestBody CourseStoreBo courseStoreBo){
		// 设置当前登录人查询
		String currentUserOid = baseDataService.getCurrentUserOid();
		courseStoreBo.setUserOid(currentUserOid);
		return courseStoreService.addCourseStore(courseStoreBo);
    }

    /**
	 * 修改课程收藏表
	 * @param courseStoreBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
	 */
	@Override
	public AjaxResult updateCourseStore(@Validated @RequestBody CourseStoreBo courseStoreBo) {
		if(null == courseStoreBo.getId()) {
			return AjaxResult.fail("课程收藏表id不能为空");
		}
		return courseStoreService.updateCourseStore(courseStoreBo);
	}

	/**
	 * 修改课程收藏表
	 * @param courseStoreBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
	 */
	@Override
	public AjaxResult deleteCourseStore(@Validated @RequestBody CourseStoreBo courseStoreBo) {
		if(null == courseStoreBo.getCasesId()) {
			return AjaxResult.fail("校本课程案例ID不能为空");
		}
		// 设置当前登录人查询
		String currentUserOid = baseDataService.getCurrentUserOid();
		if(null == currentUserOid) {
			return AjaxResult.fail("用户oid不能为空");
		}
		courseStoreBo.setUserOid(currentUserOid);
		return courseStoreService.deleteCourseStore(courseStoreBo);
	}

	/**
	 * 查询课程收藏表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
	 */
	@Override
	public AjaxResult<CourseStoreVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程收藏表id不能为空");
		}
		CourseStoreConditionBo condition = new CourseStoreConditionBo();
		condition.setId(id);
		CourseStoreVo vo = courseStoreService.getCourseStoreByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程收藏表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseStoreDto courseStoreDto = new CourseStoreDto();
		courseStoreDto.setId(id);
		courseStoreDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseStoreService.updateById(courseStoreDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
