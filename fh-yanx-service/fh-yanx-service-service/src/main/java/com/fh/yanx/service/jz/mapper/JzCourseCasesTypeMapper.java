package com.fh.yanx.service.jz.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesTypeDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeConditionBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesTypeVo;

/**
 * 金中-校本课程案例类型Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesTypeMapper extends BaseMapper<JzCourseCasesTypeDto> {

	List<JzCourseCasesTypeVo> getJzCourseCasesTypeListByCondition(JzCourseCasesTypeConditionBo condition);

	JzCourseCasesTypeVo getJzCourseCasesTypeByCondition(JzCourseCasesTypeConditionBo condition);

}
