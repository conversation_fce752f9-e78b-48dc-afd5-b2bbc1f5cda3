package com.fh.yanx.service.pub.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryConditionBo;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryDto;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryVo;
import com.fh.yanx.service.pub.mapper.PubBookDirectoryMapper;
import com.fh.yanx.service.pub.service.IPubBookDirectoryService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 融合出版书目录接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Service
public class PubBookDirectoryServiceImpl extends ServiceImpl<PubBookDirectoryMapper, PubBookDirectoryDto>
    implements IPubBookDirectoryService {

    @Resource
    private PubBookDirectoryMapper pubBookDirectoryMapper;

    @Override
    public List<PubBookDirectoryVo> getPubBookDirectoryListByCondition(PubBookDirectoryConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return pubBookDirectoryMapper.getPubBookDirectoryListByCondition(condition);
    }

    @Override
    public AjaxResult addPubBookDirectory(PubBookDirectoryBo pubBookDirectoryBo) {
        PubBookDirectoryDto pubBookDirectory = new PubBookDirectoryDto();
        BeanUtils.copyProperties(pubBookDirectoryBo, pubBookDirectory);
        pubBookDirectory.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pubBookDirectory)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePubBookDirectory(PubBookDirectoryBo pubBookDirectoryBo) {
        PubBookDirectoryDto pubBookDirectory = new PubBookDirectoryDto();
        BeanUtils.copyProperties(pubBookDirectoryBo, pubBookDirectory);
        if (updateById(pubBookDirectory)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PubBookDirectoryVo getPubBookDirectoryByCondition(PubBookDirectoryConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PubBookDirectoryVo vo = pubBookDirectoryMapper.getPubBookDirectoryByCondition(condition);
        return vo;
    }

}