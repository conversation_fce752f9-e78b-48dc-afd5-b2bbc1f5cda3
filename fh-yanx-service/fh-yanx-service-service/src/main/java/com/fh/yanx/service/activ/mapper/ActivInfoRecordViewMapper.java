package com.fh.yanx.service.activ.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordViewDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 新时代文化校园活动内容观看记录表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
public interface ActivInfoRecordViewMapper extends BaseMapper<ActivInfoRecordViewDto> {

	List<ActivInfoRecordViewVo> getActivInfoRecordViewListByCondition(ActivInfoRecordViewConditionBo condition);

	ActivInfoRecordViewVo getActivInfoRecordViewByCondition(ActivInfoRecordViewConditionBo condition);

	/**
	 * 查询观看记录统计信息
	 *
	 * @param condition the condition
	 * @return activ info record view statistics
	 */
	List<ActivInfoRecordViewVo> getActivInfoRecordViewStatistics(ActivInfoRecordViewConditionBo condition);

}
