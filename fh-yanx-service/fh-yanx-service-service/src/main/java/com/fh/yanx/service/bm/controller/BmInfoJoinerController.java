package com.fh.yanx.service.bm.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.bm.api.BmInfoJoinerApi;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoJoinerDto;
import com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo;
import com.fh.yanx.service.bm.service.IBmInfoJoinerService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 新时代文化校园报名活动申请表-参与人信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@RestController
@Validated
public class BmInfoJoinerController implements BmInfoJoinerApi {

    @Autowired
    private IBmInfoJoinerService bmInfoJoinerService;

    /**
     * 查询新时代文化校园报名活动申请表-参与人信息表分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult<PageInfo<BmInfoJoinerVo>>
        getBmInfoJoinerPageListByCondition(@RequestBody BmInfoJoinerConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<BmInfoJoinerVo> pageInfo =
            new PageInfo<>(bmInfoJoinerService.getBmInfoJoinerListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询新时代文化校园报名活动申请表-参与人信息表列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult<List<BmInfoJoinerVo>>
        getBmInfoJoinerListByCondition(@RequestBody BmInfoJoinerConditionBo condition) {
        List<BmInfoJoinerVo> list = bmInfoJoinerService.getBmInfoJoinerListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增新时代文化校园报名活动申请表-参与人信息表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult addBmInfoJoiner(@Validated @RequestBody BmInfoJoinerBo bmInfoJoinerBo) {
        return bmInfoJoinerService.addBmInfoJoiner(bmInfoJoinerBo);
    }

    /**
     * 修改新时代文化校园报名活动申请表-参与人信息表
     * 
     * @param bmInfoJoinerBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult updateBmInfoJoiner(@Validated @RequestBody BmInfoJoinerBo bmInfoJoinerBo) {
        if (null == bmInfoJoinerBo.getId()) {
            return AjaxResult.fail("新时代文化校园报名活动申请表-参与人信息表id不能为空");
        }
        return bmInfoJoinerService.updateBmInfoJoiner(bmInfoJoinerBo);
    }

    /**
     * 查询新时代文化校园报名活动申请表-参与人信息表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult<BmInfoJoinerVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("新时代文化校园报名活动申请表-参与人信息表id不能为空");
        }
        BmInfoJoinerConditionBo condition = new BmInfoJoinerConditionBo();
        condition.setId(id);
        BmInfoJoinerVo vo = bmInfoJoinerService.getBmInfoJoinerByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除新时代文化校园报名活动申请表-参与人信息表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        BmInfoJoinerDto bmInfoJoinerDto = new BmInfoJoinerDto();
        bmInfoJoinerDto.setId(id);
        bmInfoJoinerDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (bmInfoJoinerService.updateById(bmInfoJoinerDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult updateByMobile(@RequestBody BmInfoJoinerBo bmInfoJoinerBo) {
        if (StringUtils.isBlank(bmInfoJoinerBo.getJoinerMobile()) || bmInfoJoinerBo.getInfoId() == null) {
            return AjaxResult.fail("参数错误");
        }
        return bmInfoJoinerService.updateByMobile(bmInfoJoinerBo);
    }

    @Override
    public AjaxResult getByMobile(BmInfoJoinerBo bmInfoJoinerBo) {
        if (StringUtils.isBlank(bmInfoJoinerBo.getJoinerMobile()) || bmInfoJoinerBo.getInfoId() == null) {
            return AjaxResult.fail("参数错误");
        }
        return bmInfoJoinerService.getByMobile(bmInfoJoinerBo);
    }

}
