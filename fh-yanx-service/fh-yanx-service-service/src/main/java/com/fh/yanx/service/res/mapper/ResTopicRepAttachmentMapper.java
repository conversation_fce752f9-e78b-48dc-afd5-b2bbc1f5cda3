package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicRepAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicRepAttachmentVo;

/**
 * 结题答辩附件表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicRepAttachmentMapper extends BaseMapper<ResTopicRepAttachmentDto> {

    List<ResTopicRepAttachmentVo> getResTopicRepAttachmentListByCondition(ResTopicRepAttachmentConditionBo condition);

    /**
     * 查询论文附件的年份集合
     *
     * @param condition
     * @return
     */
    List<String> getResTopicRepAttachmentYearsByCondition(ResTopicRepAttachmentConditionBo condition);
}
