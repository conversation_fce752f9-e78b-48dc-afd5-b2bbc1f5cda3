package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicTodoDto;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTodoVo;

/**
 * 用户待办事项Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicTodoMapper extends BaseMapper<ResTopicTodoDto> {

	List<ResTopicTodoVo> getResTopicTodoListByCondition(ResTopicTodoConditionBo condition);

}
