package com.fh.yanx.service.bm.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoDto;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportZdVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoStatisticsVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.light.core.entity.AjaxResult;

/**
 * 新时代文化校园报名活动申请表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
public interface IBmInfoService extends IService<BmInfoDto> {

    List<BmInfoVo> getBmInfoListByCondition(BmInfoConditionBo condition);

    AjaxResult addBmInfo(BmInfoBo bmInfoBo);

    AjaxResult updateBmInfo(BmInfoBo bmInfoBo);

    AjaxResult updateBmInfoWbij(BmInfoBo bmInfoBo);

    BmInfoVo getBmInfoByCondition(BmInfoConditionBo condition);

    /**
     * 新增报名活动带参与人一起处理（不支持更新）
     *
     * @param bmInfoBo the bm info bo
     * @return ajax result
     */
    AjaxResult addBmInfoWithJoiner(BmInfoBo bmInfoBo);

    /**
     * 根据infoId查询活动信息以及参与人信息
     *
     * @param infoId the info id
     * @return bm info with joiner
     */
    BmInfoVo getBmInfoWithJoiner(Long infoId);

    /**
     * 查询导出数据
     *
     * @param condition the condition
     * @return bm info list by condition export
     */
    List<BmInfoExportVo> getBmInfoListByConditionExport(BmInfoConditionBo condition);

    /**
     * 查询导出数据-征订
     *
     * @param condition the condition
     * @return bm info list by condition export
     */
    List<BmInfoExportZdVo> getBmInfoListByConditionExportZd(BmInfoConditionBo condition);

    /**
     * 校验联系人是否已报名、参会
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/3 18:10
     **/
    AjaxResult checkActivContact(BmInfoBo bmInfoBo);

    /**
     * 根据联系人手机号查询报名详情
     *
     * @param condition
     * @return java.util.List<com.fh.yanx.service.bm.entity.vo.BmInfoVo>
     * <AUTHOR>
     * @date 2023/8/4 15:38
     **/
    List<BmInfoVo> bmInfoDetailByMobile(BmInfoConditionBo condition);

    /**
     * 查询报名列表（包含订单信息）
     *
     * @param condition
     * @return java.util.List<com.fh.yanx.service.bm.entity.vo.BmInfoVo>
     * <AUTHOR>
     * @date 2023/8/4 15:46
     **/
    List<BmInfoVo> getBmInfoListWithOrder(BmInfoConditionBo condition);

    /**
     * 查询报名统计信息
     *
     * @param condition the condition
     * @return java.util.List<com.fh.yanx.service.bm.entity.vo.BmInfoVo>  bm info statistics
     * <AUTHOR>
     * @date 2023 /8/4 15:46
     */
    BmInfoStatisticsVo getBmInfoStatistics(BmInfoConditionBo condition);

    /**
     * 查询报名总参会人数
     *
     * @param conditionBo
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2023/9/19 14:18
     **/
    Integer countBmInfoJoiner(BmInfoConditionBo conditionBo);

    /**
     * 生成并设置报名签到码
     *
     * @param bmInfoBo 需要传参：infoId和activId
     * @return 签到码
     * <AUTHOR>
     */
    String generateAndSetSignInCode(BmInfoBo bmInfoBo, boolean saveSignCode);

    /**
     * 生成并设置不重复的报名签到码
     *
     * @param bmInfoBo the bm info bo
     * @return string string
     * <AUTHOR>
     * @date 2023 -08-04 15:41:00
     */
    String generateNotRepeatSignInCode(BmInfoBo bmInfoBo, boolean saveSignCode);

    /**
     * 生成一次报名活动的已经确认的用户的账号
     *
     * @param bmInfoBo the bm info bo
     * @return boolean
     * <AUTHOR>
     * @date 2023 -08-09 10:23:06
     */
    boolean generateAccount(BmInfoBo bmInfoBo);

    /**
     * 活动签到
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/8 10:04
     **/
    AjaxResult bmInfoSignIn(BmInfoBo bmInfoBo);

    /**
     * 校验手机号是否可以登录
     *
     * @param phone
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/9 14:26
     **/
    AjaxResult<Boolean> checkPhoneForLogin(String phone);


    /**
     * 只更新报名信息
     *
     * @param bmInfoBo
     * @return
     */
    AjaxResult updateBmInfoByInfoId(BmInfoBo bmInfoBo);

    /**
     * 批量更新支付类型
     *
     * @param infoIds
     * @param payType
     * @return
     */
    boolean updatePayTypeBatch(List<Long> infoIds, Integer payType);

}
