package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.CourseVerifyLogApi;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseVerifyLogDto;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.fh.yanx.service.course.service.ICourseVerifyLogService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 课程审核流水表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@RestController
@Validated
public class CourseVerifyLogController implements CourseVerifyLogApi {

    @Autowired
    private ICourseVerifyLogService courseVerifyLogService;

    /**
     * 查询课程审核流水表分页列表
     * 
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @Override
    public AjaxResult<PageInfo<CourseVerifyLogVo>>
        getCourseVerifyLogPageListByCondition(@RequestBody CourseVerifyLogConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<CourseVerifyLogVo> pageInfo =
            new PageInfo<>(courseVerifyLogService.getCourseVerifyLogListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课程审核流水表列表
     * 
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @Override
    public AjaxResult<List<CourseVerifyLogVo>>
        getCourseVerifyLogListByCondition(@RequestBody CourseVerifyLogConditionBo condition) {
        List<CourseVerifyLogVo> list = courseVerifyLogService.getCourseVerifyLogListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增课程审核流水表
     * 
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @Override
    public AjaxResult addCourseVerifyLog(@Validated @RequestBody CourseVerifyLogBo courseVerifyLogBo) {
        return courseVerifyLogService.addCourseVerifyLog(courseVerifyLogBo);
    }

    /**
     * 修改课程审核流水表
     * 
     * @param courseVerifyLogBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @Override
    public AjaxResult updateCourseVerifyLog(@Validated @RequestBody CourseVerifyLogBo courseVerifyLogBo) {
        if (null == courseVerifyLogBo.getId()) {
            return AjaxResult.fail("课程审核流水表id不能为空");
        }
        return courseVerifyLogService.updateCourseVerifyLog(courseVerifyLogBo);
    }

    /**
     * 查询课程审核流水表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @Override
    public AjaxResult<CourseVerifyLogVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课程审核流水表id不能为空");
        }
        CourseVerifyLogConditionBo condition = new CourseVerifyLogConditionBo();
        condition.setId(id);
        CourseVerifyLogVo vo = courseVerifyLogService.getCourseVerifyLogByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课程审核流水表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        CourseVerifyLogDto courseVerifyLogDto = new CourseVerifyLogDto();
        courseVerifyLogDto.setId(id);
        courseVerifyLogDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (courseVerifyLogService.updateById(courseVerifyLogDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult deleteAndSave(CourseVerifyLogBo courseVerifyLogBo) {
        try {
            boolean result = courseVerifyLogService.deleteAndSave(courseVerifyLogBo);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.fail();
        }
    }
}
