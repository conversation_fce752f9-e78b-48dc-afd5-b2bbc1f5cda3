package com.fh.yanx.service.activ.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewConditionBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordViewDto;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 新时代文化校园活动内容观看记录表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
public interface IActivInfoRecordViewService extends IService<ActivInfoRecordViewDto> {

    List<ActivInfoRecordViewVo> getActivInfoRecordViewListByCondition(ActivInfoRecordViewConditionBo condition);

    AjaxResult addActivInfoRecordView(ActivInfoRecordViewBo activInfoRecordViewBo);

    AjaxResult updateActivInfoRecordView(ActivInfoRecordViewBo activInfoRecordViewBo);

    ActivInfoRecordViewVo getActivInfoRecordViewByCondition(ActivInfoRecordViewConditionBo condition);

    /**
     * 查询观看记录统计信息，支持查询全局统计信息和前N天统计信息
     *
     * @param condition the condition
     * @return activ info record view statistics
     */
    List<ActivInfoRecordViewVo> getActivInfoRecordViewStatistics(@RequestBody ActivInfoRecordViewConditionBo condition);
}
