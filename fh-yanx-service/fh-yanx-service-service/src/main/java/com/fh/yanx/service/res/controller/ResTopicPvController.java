package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResTopicPvApi;
import com.fh.yanx.service.res.entity.bo.ResTopicPvBo;
import com.fh.yanx.service.res.entity.bo.ResTopicPvConditionBo;
import com.fh.yanx.service.res.entity.dto.ResTopicPvDto;
import com.fh.yanx.service.res.entity.vo.ResTopicPvVo;
import com.fh.yanx.service.res.service.IResTopicPvService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 课题pv记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResTopicPvController implements ResTopicPvApi {

    @Autowired
    private IResTopicPvService resTopicPvService;

    /**
     * 查询课题pv记录分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResTopicPvVo>> getResTopicPvPageListByCondition(@RequestBody ResTopicPvConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicPvVo> pageInfo = new PageInfo<>(resTopicPvService.getResTopicPvListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题pv记录列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResTopicPvVo>> getResTopicPvListByCondition(@RequestBody ResTopicPvConditionBo condition) {
        List<ResTopicPvVo> list = resTopicPvService.getResTopicPvListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增课题pv记录
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResTopicPv(@Validated @RequestBody ResTopicPvBo resTopicPvBo) {
        return resTopicPvService.addResTopicPv(resTopicPvBo);
    }

    /**
     * 修改课题pv记录
     *
     * @param resTopicPvBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResTopicPv(@Validated @RequestBody ResTopicPvBo resTopicPvBo) {
        if (null == resTopicPvBo.getPvId()) {
            return AjaxResult.fail("课题pv记录id不能为空");
        }
        return resTopicPvService.updateResTopicPv(resTopicPvBo);
    }

    /**
     * 查询课题pv记录详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResTopicPvVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题pv记录id不能为空");
        }
        ResTopicPvVo vo = resTopicPvService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课题pv记录
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicPvDto resTopicPvDto = new ResTopicPvDto();
        resTopicPvDto.setPvId(id);
        resTopicPvDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicPvService.updateById(resTopicPvDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
