package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentEditionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程资源或成果样例附件表-模块附件版本表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:25
 */
public interface ICourseModuleAttachmentEditionService extends IService<CourseModuleAttachmentEditionDto> {

    List<CourseModuleAttachmentEditionVo> getCourseModuleAttachmentEditionListByCondition(CourseModuleAttachmentEditionConditionBo condition);

	AjaxResult addCourseModuleAttachmentEdition(CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo);

	AjaxResult updateCourseModuleAttachmentEdition(CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo);

	CourseModuleAttachmentEditionVo getCourseModuleAttachmentEditionByCondition(CourseModuleAttachmentEditionConditionBo condition);

}

