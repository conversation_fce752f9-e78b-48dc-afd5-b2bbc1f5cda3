package com.fh.yanx.service.course.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.service.IPCategoryService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.PLessonCasesApi;
import com.fh.yanx.service.course.entity.bo.PLessonCasesBo;
import com.fh.yanx.service.course.entity.bo.PLessonCasesConditionBo;
import com.fh.yanx.service.course.entity.dto.PLessonCasesDto;
import com.fh.yanx.service.course.entity.vo.PLessonCasesVo;
import com.fh.yanx.service.course.service.IPLessonCasesService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 课例表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@RestController
@Validated
public class PLessonCasesController implements PLessonCasesApi {

    @Autowired
    private IPLessonCasesService pLessonCasesService;
    @Autowired
    private IPCategoryService pCategoryService;

    /**
     * 查询课例表分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PageInfo<PLessonCasesVo>>
        getPLessonCasesPageListByCondition(@RequestBody PLessonCasesConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<PLessonCasesVo> pLessonCasesListByCondition =
            pLessonCasesService.getPLessonCasesListByCondition(condition);
        setCategoryValue(pLessonCasesListByCondition);
        PageInfo<PLessonCasesVo> pageInfo = new PageInfo<>(pLessonCasesListByCondition);
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课例表列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<List<PLessonCasesVo>>
        getPLessonCasesListByCondition(@RequestBody PLessonCasesConditionBo condition) {
        List<PLessonCasesVo> list = pLessonCasesService.getPLessonCasesListByCondition(condition);
        setCategoryValue(list);
        return AjaxResult.success(list);
    }

    /**
     * 新增课例表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult addPLessonCases(@Validated @RequestBody PLessonCasesBo pLessonCasesBo) {
        return pLessonCasesService.addPLessonCases(pLessonCasesBo);
    }

    /**
     * 修改课例表
     * 
     * @param pLessonCasesBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult updatePLessonCases(@Validated @RequestBody PLessonCasesBo pLessonCasesBo) {
        if (null == pLessonCasesBo.getId()) {
            return AjaxResult.fail("课例表id不能为空");
        }
        return pLessonCasesService.updatePLessonCases(pLessonCasesBo);
    }

    /**
     * 查询课例表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PLessonCasesVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课例表id不能为空");
        }
        PLessonCasesConditionBo condition = new PLessonCasesConditionBo();
        condition.setId(id);
        PLessonCasesVo vo = pLessonCasesService.getPLessonCasesByCondition(condition);
        List<PLessonCasesVo> pLessonCasesVos = Lists.newArrayList(vo);
        setCategoryValue(pLessonCasesVos);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课例表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        PLessonCasesDto pLessonCasesDto = new PLessonCasesDto();
        pLessonCasesDto.setId(id);
        pLessonCasesDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (pLessonCasesService.updateById(pLessonCasesDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 设置枚举值
     *
     * <AUTHOR>
     */
    private void setCategoryValue(List<PLessonCasesVo> pLessonCasesVos) {
        if (CollectionUtils.isEmpty(pLessonCasesVos)) {
            return;
        }
        List<Long> categoryIds = Lists.newArrayList();
        for (PLessonCasesVo pLessonCasesVo : pLessonCasesVos) {
            categoryIds.add(pLessonCasesVo.getLcZone());
            categoryIds.add(pLessonCasesVo.getLcSubject());
        }
        List<PCategoryDto> pCategoryDtos = pCategoryService.getListByIdsList(categoryIds);
        Map<Long, PCategoryDto> pCategoryDtoMap =
            pCategoryDtos.stream().collect(Collectors.toMap(PCategoryDto::getId, a -> a, (k1, k2) -> k1));
        for (PLessonCasesVo pLessonCasesVo : pLessonCasesVos) {
            PCategoryDto pCategoryDtoLcSubject = pCategoryDtoMap.get(pLessonCasesVo.getLcSubject());
            PCategoryDto pCategoryDtoLcZone = pCategoryDtoMap.get(pLessonCasesVo.getLcZone());
            if (pCategoryDtoLcSubject != null) {
                pLessonCasesVo.setLcSubjectName(pCategoryDtoLcSubject.getName());
            }
            if (pCategoryDtoLcZone != null) {
                pLessonCasesVo.setLcZoneName(pCategoryDtoLcZone.getName());
            }
        }
    }

}
