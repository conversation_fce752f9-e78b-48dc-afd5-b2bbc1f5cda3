package com.fh.yanx.service.pub.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 融合出版书目录附件
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pub_book_directory_attachment")
public class PubBookDirectoryAttachmentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "book_directory_attachment_id", type = IdType.AUTO)
	private Long bookDirectoryAttachmentId;

	/**
	 * 冗余，FK出版书id，pub_book表逐渐
	 */
	@TableField("book_id")
	private Long bookId;

	/**
	 * 融合出版书目录id,pub_book_directory的主键
	 */
	@TableField("book_directory_id")
	private Long bookDirectoryId;

	/**
	 * 目录对应的文件名称
	 */
	@TableField("book_directory_file_name")
	private String bookDirectoryFileName;

	/**
	 * 目录对应的文件url地址
	 */
	@TableField("book_directory_file_url")
	private String bookDirectoryFileUrl;

	/**
	 * 目录对应的文件的oid
	 */
	@TableField("book_directory_file_oid")
	private String bookDirectoryFileOid;

	/**
	 * 目录对应的文件的顺序
	 */
	@TableField("book_directory_file_index")
	private String bookDirectoryFileIndex;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
