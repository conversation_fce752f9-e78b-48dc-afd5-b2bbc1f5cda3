package com.fh.yanx.service.course.controller;

import com.fh.yanx.service.course.api.CourseCasesTypeEditionApi;
import com.fh.yanx.service.course.entity.dto.CourseCasesTypeEditionDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesTypeEditionVo;
import com.fh.yanx.service.course.service.ICourseCasesTypeEditionService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 校本课程案例类型版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
@RestController
@Validated
public class CourseCasesTypeEditionController implements CourseCasesTypeEditionApi{
	
    @Autowired
    private ICourseCasesTypeEditionService courseCasesTypeEditionService;

    /**
     * 查询校本课程案例类型版本记录分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
    @Override
    public AjaxResult<PageInfo<CourseCasesTypeEditionVo>> getCourseCasesTypeEditionPageListByCondition(@RequestBody CourseCasesTypeEditionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseCasesTypeEditionVo> pageInfo = new PageInfo<>(courseCasesTypeEditionService.getCourseCasesTypeEditionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询校本课程案例类型版本记录列表
	 * <AUTHOR>
	 * @date 2024-12-05 10:41:20
	 */
	@Override
	public AjaxResult<List<CourseCasesTypeEditionVo>> getCourseCasesTypeEditionListByCondition(@RequestBody CourseCasesTypeEditionConditionBo condition){
		List<CourseCasesTypeEditionVo> list = courseCasesTypeEditionService.getCourseCasesTypeEditionListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增校本课程案例类型版本记录
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
	@Override
    public AjaxResult addCourseCasesTypeEdition(@Validated @RequestBody CourseCasesTypeEditionBo courseCasesTypeEditionBo){
		return courseCasesTypeEditionService.addCourseCasesTypeEdition(courseCasesTypeEditionBo);
    }

    /**
	 * 修改校本课程案例类型版本记录
	 * @param courseCasesTypeEditionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
	 */
	@Override
	public AjaxResult updateCourseCasesTypeEdition(@Validated @RequestBody CourseCasesTypeEditionBo courseCasesTypeEditionBo) {
		if(null == courseCasesTypeEditionBo.getId()) {
			return AjaxResult.fail("校本课程案例类型版本记录id不能为空");
		}
		return courseCasesTypeEditionService.updateCourseCasesTypeEdition(courseCasesTypeEditionBo);
	}

	/**
	 * 查询校本课程案例类型版本记录详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
	 */
	@Override
	public AjaxResult<CourseCasesTypeEditionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("校本课程案例类型版本记录id不能为空");
		}
		CourseCasesTypeEditionConditionBo condition = new CourseCasesTypeEditionConditionBo();
		condition.setId(id);
		CourseCasesTypeEditionVo vo = courseCasesTypeEditionService.getCourseCasesTypeEditionByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除校本课程案例类型版本记录
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseCasesTypeEditionDto courseCasesTypeEditionDto = new CourseCasesTypeEditionDto();
		courseCasesTypeEditionDto.setId(id);
		courseCasesTypeEditionDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseCasesTypeEditionService.updateById(courseCasesTypeEditionDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
