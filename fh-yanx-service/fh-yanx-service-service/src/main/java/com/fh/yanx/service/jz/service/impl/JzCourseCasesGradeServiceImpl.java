package com.fh.yanx.service.jz.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesGradeDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesGradeVo;
import com.fh.yanx.service.jz.mapper.JzCourseCasesGradeMapper;
import com.fh.yanx.service.jz.service.IJzCourseCasesGradeService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * 金中-校本课程案例年级接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@Service
public class JzCourseCasesGradeServiceImpl extends ServiceImpl<JzCourseCasesGradeMapper, JzCourseCasesGradeDto> implements IJzCourseCasesGradeService {

	@Resource
	private JzCourseCasesGradeMapper jzCourseCasesGradeMapper;
	
    @Override
	public List<JzCourseCasesGradeVo> getJzCourseCasesGradeListByCondition(JzCourseCasesGradeConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return jzCourseCasesGradeMapper.getJzCourseCasesGradeListByCondition(condition);
	}

	@Override
	public AjaxResult addJzCourseCasesGrade(JzCourseCasesGradeBo jzCourseCasesGradeBo) {
		JzCourseCasesGradeDto jzCourseCasesGrade = new JzCourseCasesGradeDto();
		BeanUtils.copyProperties(jzCourseCasesGradeBo, jzCourseCasesGrade);
		jzCourseCasesGrade.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(jzCourseCasesGrade)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateJzCourseCasesGrade(JzCourseCasesGradeBo jzCourseCasesGradeBo) {
		JzCourseCasesGradeDto jzCourseCasesGrade = new JzCourseCasesGradeDto();
		BeanUtils.copyProperties(jzCourseCasesGradeBo, jzCourseCasesGrade);
		if(updateById(jzCourseCasesGrade)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public JzCourseCasesGradeVo getJzCourseCasesGradeByCondition(JzCourseCasesGradeConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		JzCourseCasesGradeVo vo = jzCourseCasesGradeMapper.getJzCourseCasesGradeByCondition(condition);
		return vo;
	}

}