package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResStationContentDetailDto;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentDetailVo;
import com.fh.yanx.service.res.service.IResStationContentDetailService;
import com.fh.yanx.service.res.mapper.ResStationContentDetailMapper;
import com.light.core.entity.AjaxResult;
/**
 * 工作站具体内容详情接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResStationContentDetailServiceImpl extends ServiceImpl<ResStationContentDetailMapper, ResStationContentDetailDto> implements IResStationContentDetailService {

	@Resource
	private ResStationContentDetailMapper resStationContentDetailMapper;
	
    @Override
	public List<ResStationContentDetailVo> getResStationContentDetailListByCondition(ResStationContentDetailConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resStationContentDetailMapper.getResStationContentDetailListByCondition(condition);
	}

	@Override
	public AjaxResult addResStationContentDetail(ResStationContentDetailBo resStationContentDetailBo) {
		ResStationContentDetailDto resStationContentDetail = new ResStationContentDetailDto();
		BeanUtils.copyProperties(resStationContentDetailBo, resStationContentDetail);
		resStationContentDetail.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resStationContentDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResStationContentDetail(ResStationContentDetailBo resStationContentDetailBo) {
		ResStationContentDetailDto resStationContentDetail = new ResStationContentDetailDto();
		BeanUtils.copyProperties(resStationContentDetailBo, resStationContentDetail);
		if(updateById(resStationContentDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResStationContentDetailVo getDetail(Long id) {
		ResStationContentDetailConditionBo condition = new ResStationContentDetailConditionBo();
		condition.setStationContentDetailId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResStationContentDetailVo> list = resStationContentDetailMapper.getResStationContentDetailListByCondition(condition);
		ResStationContentDetailVo vo = new ResStationContentDetailVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}