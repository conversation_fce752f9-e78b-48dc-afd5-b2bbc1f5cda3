package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicProcessRecordDto;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.vo.ResTopicProcessRecordVo;
import com.fh.yanx.service.res.service.IResTopicProcessRecordService;
import com.fh.yanx.service.res.mapper.ResTopicProcessRecordMapper;
import com.light.core.entity.AjaxResult;

/**
 * 课题流程记录接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResTopicProcessRecordServiceImpl extends ServiceImpl<ResTopicProcessRecordMapper, ResTopicProcessRecordDto>
    implements IResTopicProcessRecordService {

    @Resource
    private ResTopicProcessRecordMapper resTopicProcessRecordMapper;
    @Resource
    private BaseDataService baseDataService;
    @Autowired
    private IResTopicProcessRecordService resTopicProcessRecordService;

    @Override
    public List<ResTopicProcessRecordVo>
        getResTopicProcessRecordListByCondition(ResTopicProcessRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<ResTopicProcessRecordVo> processRecordVos =
            resTopicProcessRecordMapper.getResTopicProcessRecordListByCondition(condition);
        // 封装申报及审批用户名称
        if (CollectionUtils.isNotEmpty(processRecordVos)) {
            List<String> oids = processRecordVos.stream().map(ResTopicProcessRecordVo::getProcessVerifyUser)
                .collect(Collectors.toList());
            oids.addAll(processRecordVos.stream().map(ResTopicProcessRecordVo::getProcessSubmitUser)
                .collect(Collectors.toList()));
            Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(oids);

            processRecordVos.forEach(x -> {
                x.setProcessVerifyUserName(userNameMap.get(x.getProcessVerifyUser()));
                x.setProcessSubmitUserName(userNameMap.get(x.getProcessSubmitUser()));
            });
        }

        return processRecordVos;
    }

    @Override
    public AjaxResult addResTopicProcessRecord(ResTopicProcessRecordBo resTopicProcessRecordBo) {
        ResTopicProcessRecordDto resTopicProcessRecord = new ResTopicProcessRecordDto();
        BeanUtils.copyProperties(resTopicProcessRecordBo, resTopicProcessRecord);
        resTopicProcessRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(resTopicProcessRecord)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateResTopicProcessRecord(ResTopicProcessRecordBo resTopicProcessRecordBo) {
        ResTopicProcessRecordDto resTopicProcessRecord = new ResTopicProcessRecordDto();
        BeanUtils.copyProperties(resTopicProcessRecordBo, resTopicProcessRecord);
        if (updateById(resTopicProcessRecord)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ResTopicProcessRecordVo getDetail(Long id) {
        ResTopicProcessRecordConditionBo condition = new ResTopicProcessRecordConditionBo();
        condition.setProcessRecordId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicProcessRecordVo> list = this.getResTopicProcessRecordListByCondition(condition);
        ResTopicProcessRecordVo vo = new ResTopicProcessRecordVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public AjaxResult addResTopicProcessRecordWithCompare(ResTopicProcessRecordBo resTopicProcessRecordBo) {
        // 查询是否有数据:topicId、topicProcess、processSubmitUser
        ResTopicProcessRecordConditionBo resTopicProcessRecordConditionBo = new ResTopicProcessRecordConditionBo();
        resTopicProcessRecordConditionBo.setTopicId(resTopicProcessRecordBo.getTopicId());
        resTopicProcessRecordConditionBo.setTopicProcess(resTopicProcessRecordBo.getTopicProcess());
        if(StringUtils.isNotBlank(resTopicProcessRecordBo.getProcessSubmitUser())){
            resTopicProcessRecordConditionBo.setProcessSubmitUser(resTopicProcessRecordBo.getProcessSubmitUser());
        }
        if(StringUtils.isNotBlank(resTopicProcessRecordBo.getProcessVerifyUser())){
            resTopicProcessRecordConditionBo.setProcessVerifyUser(resTopicProcessRecordBo.getProcessVerifyUser());
        }
        List<ResTopicProcessRecordVo> resTopicProcessRecordListByCondition =
            resTopicProcessRecordService.getResTopicProcessRecordListByCondition(resTopicProcessRecordConditionBo);
        if (CollectionUtils.isEmpty(resTopicProcessRecordListByCondition)) {
            // 新增
            resTopicProcessRecordService.addResTopicProcessRecord(resTopicProcessRecordBo);
        } else {
            // 更新
            ResTopicProcessRecordVo resTopicProcessRecordVo = resTopicProcessRecordListByCondition.get(0);
            BeanUtils.copyProperties(resTopicProcessRecordVo, resTopicProcessRecordBo);
            resTopicProcessRecordService.updateResTopicProcessRecord(resTopicProcessRecordBo);
        }
        return AjaxResult.success();
    }
}