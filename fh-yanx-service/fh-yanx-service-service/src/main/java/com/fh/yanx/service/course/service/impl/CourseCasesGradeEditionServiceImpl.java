package com.fh.yanx.service.course.service.impl;

import com.fh.yanx.service.course.entity.dto.PCourseCasesGradeDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseCasesGradeEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesGradeEditionVo;
import com.fh.yanx.service.course.service.ICourseCasesGradeEditionService;
import com.fh.yanx.service.course.mapper.CourseCasesGradeEditionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 校本课程案例年级版本记录接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:00
 */
@Service
public class CourseCasesGradeEditionServiceImpl extends ServiceImpl<CourseCasesGradeEditionMapper, CourseCasesGradeEditionDto> implements ICourseCasesGradeEditionService {

	@Resource
	private CourseCasesGradeEditionMapper courseCasesGradeEditionMapper;
	
    @Override
	public List<CourseCasesGradeEditionVo> getCourseCasesGradeEditionListByCondition(CourseCasesGradeEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseCasesGradeEditionMapper.getCourseCasesGradeEditionListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseCasesGradeEdition(CourseCasesGradeEditionBo courseCasesGradeEditionBo) {
		CourseCasesGradeEditionDto courseCasesGradeEdition = new CourseCasesGradeEditionDto();
		BeanUtils.copyProperties(courseCasesGradeEditionBo, courseCasesGradeEdition);
		courseCasesGradeEdition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseCasesGradeEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseCasesGradeEdition(CourseCasesGradeEditionBo courseCasesGradeEditionBo) {
		CourseCasesGradeEditionDto courseCasesGradeEdition = new CourseCasesGradeEditionDto();
		BeanUtils.copyProperties(courseCasesGradeEditionBo, courseCasesGradeEdition);
		if(updateById(courseCasesGradeEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CourseCasesGradeEditionVo getCourseCasesGradeEditionByCondition(CourseCasesGradeEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return courseCasesGradeEditionMapper.getCourseCasesGradeEditionByCondition(condition);
	}

	@Override
	public void saveCourseGradeEdition(Long casesEditionId, List<CourseCasesGradeEditionBo> courseCasesGradeEditionBos) {
		// 版本记录仅保存，不做删除更新
		// 新增或更新
		List<CourseCasesGradeEditionDto> courseCasesGradeEditionDtos = courseCasesGradeEditionBos.stream().map(courseCasesGradeBo -> {
			CourseCasesGradeEditionDto courseCasesGradeEditionDto = new CourseCasesGradeEditionDto();
			BeanUtils.copyProperties(courseCasesGradeBo, courseCasesGradeEditionDto);
			courseCasesGradeEditionDto.setCasesEditionId(casesEditionId);
			return courseCasesGradeEditionDto;
		}).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(courseCasesGradeEditionDtos)) {
			saveOrUpdateBatch(courseCasesGradeEditionDtos);
		}
	}

}