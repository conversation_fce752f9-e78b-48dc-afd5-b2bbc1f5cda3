package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程模块
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_module")
public class CourseModuleDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "course_module_id", type = IdType.AUTO)
	private Long courseModuleId;

	/**
	 * 案例id
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 课程模块类型：1课程资源，2成果样例
	 */
	@TableField("course_module_type")
	private Integer courseModuleType;

	/**
	 * 标题
	 */
	@TableField("course_module_title")
	private String courseModuleTitle;

	/**
	 * 副标题
	 */
	@TableField("course_module_sub_title")
	private String courseModuleSubTitle;

	/**
	 * 课程资源排列方式：1列表展示，2块状展示
	 */
	@TableField("course_module_res_style")
	private Integer courseModuleResStyle;

	/**
	 * 课程模块排序，默认1
	 */
	@TableField("course_module_index")
	private Long courseModuleIndex;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 前端生成提交存储，用于前端和布局里面的信息做映射
	 */
	@TableField("uuid")
	private String uuid;
}
