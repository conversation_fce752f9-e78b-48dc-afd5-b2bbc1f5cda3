package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程版本表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_course_cases_edition")
public class CourseCasesEditionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 案例id
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 
	 */
	@TableField("user_id")
	private Long userId;

	/**
	 * 学校名称
	 */
	@TableField("school_name")
	private String schoolName;

	/**
	 * 课程名称
	 */
	@TableField("course_name")
	private String courseName;

	/**
	 * 省份ID
	 */
	@TableField("province_id")
	private Long provinceId;

	/**
	 * 市区ID
	 */
	@TableField("city_id")
	private Long cityId;

	/**
	 * 区县ID
	 */
	@TableField("county_id")
	private Long countyId;

	/**
	 * 学段
	 */
	@TableField("phase")
	private Long phase;

	/**
	 * 年份
	 */
	@TableField("year")
	private Long year;

	/**
	 * 图片
	 */
	@TableField("picture")
	private String picture;

	/**
	 * 
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 实施者
	 */
	@TableField("operation")
	private String operation;

	/**
	 * 联系方式
	 */
	@TableField("phone")
	private String phone;

	/**
	 * 简介
	 */
	@TableField("introduction")
	private String introduction;

	/**
	 * 浏览量
	 */
	@TableField("views")
	private Long views;

	/**
	 * 是否授权 0 否 1是
	 */
	@TableField("is_auth")
	private Integer isAuth;

	/**
	 * 展示首页的类型，1：大首页，2：校本课程平台首页
	 */
	@TableField("home_type")
	private Integer homeType;

	/**
	 * 0-未删除 1-已删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 
	 */
	@TableField("create_date")
	private Date createDate;

	/**
	 * 
	 */
	@TableField("update_date")
	private Date updateDate;

	/**
	 * 文件地址
	 */
	@TableField("file_path")
	private String filePath;

	/**
	 * word文档地址
	 */
	@TableField("word_path")
	private String wordPath;

	/**
	 * 是否上传文件 ： 0 否 1是
	 */
	@TableField("is_upload")
	private Integer isUpload;

	/**
	 * 
	 */
	@TableField("customer_id")
	private Long customerId;

	/**
	 * 0课程案例，1典型案例，2精品课程
	 */
	@TableField("is_excellent")
	private Integer isExcellent;

	/**
	 * 新时代用户的oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 来源类型：1资源网导入，2新时代新增
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 精品课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存，5初审通过，6初审不通过
	 */
	@TableField("best_verify_type")
	private Integer bestVerifyType;

	/**
	 * 模板类型：默认空，1特色课程模板，2整体课程模板
	 */
	@TableField("template_type")
	private Integer templateType;

	/**
	 * 精品课程的模板内容布局-tab1的布局，存储布局json信息
	 */
	@TableField("template_layout_one")
	private String templateLayoutOne;

	/**
	 * 精品课程的模板内容布局-tab2的布局，存储布局json信息
	 */
	@TableField("template_layout_two")
	private String templateLayoutTwo;

	/**
	 * 精品课程的模板内容布局-tab3的布局，存储布局json信息
	 */
	@TableField("template_layout_three")
	private String templateLayoutThree;

	/**
	 * 课程上下架：1上架，2下架
	 */
	@TableField("hold_type")
	private Integer holdType;

	/**
	 * 普通课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存，5初审通过，6初审不通过
	 */
	@TableField("normal_verify_type")
	private Integer normalVerifyType;

	/**
	 * 审核进度类型，默认空：1初审，2终审
	 */
	@TableField("verify_process_type")
	private Integer verifyProcessType;

	/**
	 * 课程类型标签，曾经是这个类型的都会被记录，类型同is_excellent，多个使用英文逗号分割
	 */
	@TableField("is_excellent_label")
	private String isExcellentLabel;

	/**
	 * 观看权限（数字,分隔） 2-未认证学校用户 3-已认证学校用户 4-案例持有者
	 */
	@TableField("view_permission")
	private String viewPermission;

}
