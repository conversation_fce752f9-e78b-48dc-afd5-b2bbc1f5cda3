package com.fh.yanx.service.pub.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.pub.entity.dto.PubBookDto;
import com.fh.yanx.service.pub.entity.bo.PubBookConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookBo;
import com.fh.yanx.service.pub.entity.vo.PubBookVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 融合出版书接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface IPubBookService extends IService<PubBookDto> {

    List<PubBookVo> getPubBookListByCondition(PubBookConditionBo condition);

    AjaxResult addPubBook(PubBookBo pubBookBo);

    AjaxResult updatePubBook(PubBookBo pubBookBo);

    PubBookVo getPubBookByCondition(PubBookConditionBo condition);

    /**
     * 增加书籍阅读次数
     *
     * @param bookId the book id
     * <AUTHOR>
     * @date 2023 -12-20 11:24:18
     */
    void incrPubBookReadTimes(Long bookId);
}
