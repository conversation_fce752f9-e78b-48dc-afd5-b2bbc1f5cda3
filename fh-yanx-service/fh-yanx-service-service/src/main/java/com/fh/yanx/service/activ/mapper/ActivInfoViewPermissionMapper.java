package com.fh.yanx.service.activ.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoViewPermissionDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo;

/**
 * 活动内容观看权限表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
public interface ActivInfoViewPermissionMapper extends BaseMapper<ActivInfoViewPermissionDto> {

	List<ActivInfoViewPermissionVo> getActivInfoViewPermissionListByCondition(ActivInfoViewPermissionConditionBo condition);

	ActivInfoViewPermissionVo getActivInfoViewPermissionByCondition(ActivInfoViewPermissionConditionBo condition);

}
