package com.fh.yanx.service.course.controller;

import com.fh.yanx.service.course.api.CourseModuleEditionApi;
import com.fh.yanx.service.course.entity.dto.CourseModuleEditionDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.entity.bo.CourseModuleEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleEditionVo;
import com.fh.yanx.service.course.service.ICourseModuleEditionService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 课程模块版本记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:17
 */
@RestController
@Validated
public class CourseModuleEditionController implements CourseModuleEditionApi{
	
    @Autowired
    private ICourseModuleEditionService courseModuleEditionService;

    /**
     * 查询课程模块版本记录表分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
    @Override
    public AjaxResult<PageInfo<CourseModuleEditionVo>> getCourseModuleEditionPageListByCondition(@RequestBody CourseModuleEditionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseModuleEditionVo> pageInfo = new PageInfo<>(courseModuleEditionService.getCourseModuleEditionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程模块版本记录表列表
	 * <AUTHOR>
	 * @date 2024-12-05 10:40:17
	 */
	@Override
	public AjaxResult<List<CourseModuleEditionVo>> getCourseModuleEditionListByCondition(@RequestBody CourseModuleEditionConditionBo condition){
		List<CourseModuleEditionVo> list = courseModuleEditionService.getCourseModuleEditionListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程模块版本记录表
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
	@Override
    public AjaxResult addCourseModuleEdition(@Validated @RequestBody CourseModuleEditionBo courseModuleEditionBo){
		return courseModuleEditionService.addCourseModuleEdition(courseModuleEditionBo);
    }

    /**
	 * 修改课程模块版本记录表
	 * @param courseModuleEditionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
	 */
	@Override
	public AjaxResult updateCourseModuleEdition(@Validated @RequestBody CourseModuleEditionBo courseModuleEditionBo) {
		if(null == courseModuleEditionBo.getCourseModuleEditionId()) {
			return AjaxResult.fail("课程模块版本记录表id不能为空");
		}
		return courseModuleEditionService.updateCourseModuleEdition(courseModuleEditionBo);
	}

	/**
	 * 查询课程模块版本记录表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
	 */
	@Override
	public AjaxResult<CourseModuleEditionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程模块版本记录表id不能为空");
		}
		CourseModuleEditionConditionBo condition = new CourseModuleEditionConditionBo();
		condition.setCourseModuleEditionId(id);
		CourseModuleEditionVo vo = courseModuleEditionService.getCourseModuleEditionByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程模块版本记录表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseModuleEditionDto courseModuleEditionDto = new CourseModuleEditionDto();
		courseModuleEditionDto.setCourseModuleEditionId(id);
		courseModuleEditionDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseModuleEditionService.updateById(courseModuleEditionDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
