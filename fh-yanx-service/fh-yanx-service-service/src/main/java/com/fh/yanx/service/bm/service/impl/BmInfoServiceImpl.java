package com.fh.yanx.service.bm.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUtil;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoDto;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;
import com.fh.yanx.service.activ.service.IActivInfoService;
import com.fh.yanx.service.aspose.AsposeBizService;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.bm.entity.bo.*;
import com.fh.yanx.service.bm.entity.vo.*;
import com.fh.yanx.service.bm.service.IBmPayAdjustRecordService;
import com.fh.yanx.service.enums.UserIdentityType;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.user.entity.bo.UserBo;
import org.apache.commons.collections.CollectionUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.yanx.service.consts.*;
import com.fh.yanx.service.enums.*;
import com.fh.yanx.service.consts.ConstantsInteger;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.service.IActivOrderService;
import com.google.common.collect.Lists;
import com.light.core.utils.StringUtils;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.bm.entity.dto.BmInfoDto;
import com.fh.yanx.service.bm.entity.dto.BmInfoJoinerDto;
import com.fh.yanx.service.bm.mapper.BmInfoMapper;
import com.fh.yanx.service.bm.service.IBmInfoJoinerService;
import com.fh.yanx.service.bm.service.IBmInfoService;
import com.fh.yanx.service.consts.ConstString;
import com.fh.yanx.service.enums.ActivBmCheckType;
import com.fh.yanx.service.enums.BmInfoSubmitType;
import com.fh.yanx.service.enums.ChargeType;
import com.fh.yanx.service.enums.GoodsType;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 新时代文化校园报名活动申请表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Service
public class BmInfoServiceImpl extends ServiceImpl<BmInfoMapper, BmInfoDto> implements IBmInfoService {

    @Resource
    private BmInfoMapper bmInfoMapper;
    @Lazy
    @Autowired
    private IBmInfoJoinerService bmInfoJoinerService;
    @Lazy
    @Autowired
    private IBmInfoService bmInfoService;
    @Lazy
    @Autowired
    private IActivOrderService activOrderService;
    @Autowired
    private BaseDataService baseDataService;
    @Resource
    private IActivInfoService activInfoService;
    @Value("${ineoschool.organization.id:1}")
    private Long ineoschoolOrganizationId;
    @Lazy
    @Resource
    private IBmPayAdjustRecordService bmPayAdjustRecordService;

    // 学校教师角色id
    private static final Long SCHOOL_TEACHER = 8L;

    @Override
    public List<BmInfoVo> getBmInfoListByCondition(BmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return bmInfoMapper.getBmInfoListByCondition(condition);
    }

    @Override
    public AjaxResult addBmInfo(BmInfoBo bmInfoBo) {
        BmInfoDto bmInfo = new BmInfoDto();
        BeanUtils.copyProperties(bmInfoBo, bmInfo);
        bmInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(bmInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult addBmInfoWithJoiner(BmInfoBo bmInfoBo) {
        // 1、各种校验
        ActivInfoConditionBo activInfoConditionBo = new ActivInfoConditionBo();
        activInfoConditionBo.setActivId(bmInfoBo.getActivId());
        activInfoConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ActivInfoVo activInfoVo = activInfoService.getActivInfoByCondition(activInfoConditionBo);
        if (activInfoVo == null) {
            return AjaxResult.fail("活动不存在");
        }
        BmInfoCheckVo checkVo = new BmInfoCheckVo();
        List<BmInfoJoinerBo> joinerBos = bmInfoBo.getBmInfoJoinerBoList();
        // 校验参会人是否重复参会
        BmInfoCheckBo checkBo = new BmInfoCheckBo();
        List<BmInfoJoinerVo> joinerVos = Lists.newArrayList();
        checkBo.setActivId(bmInfoBo.getActivId());
        if (CollectionUtils.isNotEmpty(joinerBos)) {
            checkBo.setMobiles(joinerBos.stream().map(BmInfoJoinerBo::getJoinerMobile).collect(Collectors.toList()));
            joinerVos = bmInfoJoinerService.checkBmInfoJoiner(checkBo);
        }
        if (CollectionUtil.isNotEmpty(joinerVos)) {
            Map<String, BmInfoJoinerBo> joinerBoMap =
                joinerBos.stream().collect(Collectors.toMap(BmInfoJoinerBo::getJoinerMobile, j -> j, (v1, v2) -> v1));
            checkVo.setType(ActivBmCheckType.AS_JOINER_SUBMIT.getCode());
            List<BmInfoJoinerVo> collectExistRepeatList = new ArrayList<>();
            for (BmInfoJoinerVo joinerVo : joinerVos) {
                BmInfoJoinerBo joinerBo = joinerBoMap.get(joinerVo.getJoinerMobile());
                if (joinerBo != null) {
                    BmInfoJoinerVo vo = new BmInfoJoinerVo();
                    BeanUtils.copyProperties(joinerBo, vo);
                    collectExistRepeatList.add(vo);
                }
            }
            checkVo.setJoiners(collectExistRepeatList);
            return AjaxResult.success(checkVo);
        }

        // 2、保存主表信息
        BmInfoDto bmInfo = new BmInfoDto();
        BeanUtils.copyProperties(bmInfoBo, bmInfo);
        bmInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (ChargeType.FREE.getCode().equals(activInfoVo.getChargeType())) {
            bmInfo.setSubmitType(BmInfoSubmitType.ATTEND.getCode());
            bmInfo.setPayType(BmInfoPayType.ALREADY_PAY.getCode());
        }
        boolean save = save(bmInfo);
        if (!save) {
            return AjaxResult.fail("保存失败");
        }

        // 订单金额
        BigDecimal orderAmount = new BigDecimal(0);
        // 3、主记录保存成功再保存参与人记录
        if (CollectionUtils.isNotEmpty(joinerBos)) {
            List<BmInfoJoinerDto> bmInfoJoinerDtos = joinerBos.stream().map(bmInfoJoinerBo -> {
                BmInfoJoinerDto bmInfoJoinerDto = new BmInfoJoinerDto();
                BeanUtils.copyProperties(bmInfoJoinerBo, bmInfoJoinerDto);
                bmInfoJoinerDto.setInfoId(bmInfo.getInfoId());
                return bmInfoJoinerDto;
            }).collect(Collectors.toList());
            boolean saveJoiner = bmInfoJoinerService.saveBatch(bmInfoJoinerDtos);
            if (!saveJoiner) {
                return AjaxResult.fail("保存失败");
            }
            // 普通活动、收费，费用 = 报名配置金额（元/人）* 人数
            if (activInfoVo.getActivBizType() != null && activInfoVo.getActivBizType()
                .equals(ActivBizType.NORMAL.getValue()) && ChargeType.CHARGE.getCode()
                .equals(activInfoVo.getChargeType())) {
                // 订单金额为报名配置缴费金额（元/人）*人数
                orderAmount = activInfoVo.getChargeAmount().multiply(new BigDecimal(bmInfoJoinerDtos.size()));
                // 如果普通活动且是选择的材料则费用 = 报名配置缴费金额（元/人）*人数 + 材料费用
                if (bmInfoBo.getSelectMaterialType() != null && bmInfoBo.getSelectMaterialType()
                    .equals(SelectMaterialType.SELECT_YES.getValue()) && bmInfoBo.getSubBookTotalPrice() != null) {
                    orderAmount = orderAmount.add(bmInfoBo.getSubBookTotalPrice());
                }
            }
            checkVo.setJoinerCount(bmInfoJoinerDtos.size());
        }
        // 征订活动，生成订单金额 = 入参订单金额
        if (activInfoVo.getActivBizType() != null && activInfoVo.getActivBizType()
            .equals(ActivBizType.SUB.getValue())) {
            orderAmount = bmInfoBo.getSubBookTotalPrice();
        }

        // 4、需要缴费，创建订单
        if (ChargeType.CHARGE.getCode().equals(activInfoVo.getChargeType())) {
            ActivOrderBo orderBo = new ActivOrderBo();
            orderBo.setGoodsId(bmInfo.getInfoId());
            orderBo.setGoodsType(GoodsType.ACTIV_BM.getCode());
            orderBo.setOrderAmount(orderAmount);
            ActivOrderVo orderVo = activOrderService.createOrder(orderBo);
            checkVo.setOrderNumber(orderVo.getOrderNumber());
        }
        checkVo.setType(ActivBmCheckType.SUCCESS.getCode());
        checkVo.setOrderAmount(orderAmount);

        return AjaxResult.success(checkVo);
    }

    @Override
    public AjaxResult updateBmInfo(BmInfoBo bmInfoBo) {
        BmInfoConditionBo conditionBo = new BmInfoConditionBo();
        conditionBo.setInfoId(bmInfoBo.getInfoId());
        List<BmInfoVo> list = baseMapper.getBmInfoListWithOrder(conditionBo);
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.fail("未找到数据");
        }
        BmInfoVo bmInfoVo = list.get(0);

        BmInfoDto bmInfo = new BmInfoDto();
        BeanUtils.copyProperties(bmInfoBo, bmInfo);

        Integer orderState = null;
        Integer orderType = null;
        Integer payMode = null;
        if (bmInfoBo.getSubmitType() != null && bmInfoBo.getSubmitType().equals(BmInfoSubmitType.REFUSE.getCode())) {
            orderState = OrderEnum.ORDER_STATE_CANCEL_ENUM.getCode();
            bmInfo.setPayType(BmInfoPayType.CANCEL.getCode());
        }
        // 已缴费，修改订单状态
        if (bmInfoBo.getPayType() != null && BmInfoPayType.ALREADY_PAY.getCode().equals(bmInfoBo.getPayType())) {
            orderState = OrderEnum.ORDER_STATE_PAY.getCode();
            orderType = OrderEnum.ORDER_TYPE_MANAGER_EDIT.getCode();
            payMode = OrderEnum.PAY_MODE_MANAGER.getCode();
        }
        // 已退款，修改订单状态
        if (bmInfoBo.getPayType() != null && BmInfoPayType.REFUND.getCode().equals(bmInfoBo.getPayType())) {
            orderState = OrderEnum.ORDER_STATE_REFUNDED.getCode();
            orderType = OrderEnum.ORDER_TYPE_MANAGER_EDIT.getCode();
            payMode = OrderEnum.PAY_MODE_MANAGER.getCode();
        }

        if (updateById(bmInfo)) {
            // 征订活动，价格更改，调用订单改价
            if (bmInfoBo.isChangeOrderPrice() && ActivBizType.SUB.getValue()
                .equals(bmInfoVo.getActivBizType()) && !bmInfoVo.getSubBookTotalPrice()
                .equals(bmInfoBo.getSubBookTotalPrice())) {
                ActivOrderBo activOrderBo = new ActivOrderBo();
                activOrderBo.setGoodsId(bmInfoBo.getInfoId());
                activOrderBo.setGoodsType(GoodsType.ACTIV_BM.getCode());
                activOrderBo.setOrderAmount(bmInfoBo.getSubBookTotalPrice());
                activOrderService.changeOrderAmount(activOrderBo);
            }
            // 后台管理确认时候，如果拒绝则关闭订单
            // 后台调整缴费状态为已缴费时，订单修改为已支付
            if (bmInfoBo.isChangeOrderState()) {
                ActivOrderBo activOrderBo = new ActivOrderBo();
                activOrderBo.setGoodsId(bmInfoBo.getInfoId());
                activOrderBo.setGoodsType(GoodsType.ACTIV_BM.getCode());
                activOrderBo.setOrderState(orderState);
                activOrderBo.setOrderType(orderType);
                activOrderBo.setPayMode(payMode);
                activOrderService.changeOrderState(activOrderBo);
            }
            // 调整支付方式
            if (bmInfoBo.getChangePayWayType()) {
                // 保存调整记录
                BmPayAdjustRecordBo bmPayAdjustRecordBo = new BmPayAdjustRecordBo();
                bmPayAdjustRecordBo.setInfoId(bmInfoVo.getInfoId());
                bmPayAdjustRecordBo.setPayId(bmInfoVo.getPayId());
                bmPayAdjustRecordBo.setPayUrl(bmInfoVo.getPayUrl());
                bmPayAdjustRecordBo.setPayTime(bmInfoVo.getPayTime());
                bmPayAdjustRecordBo.setPayUserOid(bmInfoVo.getPayUserOid());
                bmPayAdjustRecordBo.setPayUserName(bmInfoVo.getPayUserName());
                bmPayAdjustRecordBo.setPayWayType(bmInfoVo.getPayWayType());
                bmPayAdjustRecordBo.setPayRecord(bmInfoVo.getPayRecord());
                bmPayAdjustRecordBo.setTransactionId(bmInfoVo.getTransactionId());
                bmPayAdjustRecordBo.setPayAdjustReason(bmInfoBo.getPayAdjustReason());
                bmPayAdjustRecordService.addBmPayAdjustRecord(bmPayAdjustRecordBo);
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult updateBmInfoWbij(BmInfoBo bmInfoBo) {
        BmInfoCheckVo checkVo = new BmInfoCheckVo();
        // 校验参会人是否重复
        BmInfoCheckBo checkBo = new BmInfoCheckBo();
        checkBo.setActivId(bmInfoBo.getActivId());
        checkBo.setMobiles(bmInfoBo.getBmInfoJoinerBoList().stream().map(BmInfoJoinerBo::getJoinerMobile)
            .collect(Collectors.toList()));
        List<BmInfoJoinerVo> joinerVos = bmInfoJoinerService.checkBmInfoJoiner(checkBo);
        if (CollectionUtil.isNotEmpty(joinerVos)) {
            // 更新报名记录时候，手机号在db重复的数据
            List<BmInfoJoinerVo> collectExistRepeatList =
                joinerVos.stream().filter(j -> !bmInfoBo.getInfoId().equals(j.getInfoId()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collectExistRepeatList)) {
                checkVo.setType(ActivBmCheckType.AS_JOINER_SUBMIT.getCode());
                checkVo.setJoiners(collectExistRepeatList);
                return AjaxResult.success(checkVo);
            }
        }
        // 更新主表信息
        AjaxResult ajaxResult = bmInfoService.updateBmInfo(bmInfoBo);
        if (ajaxResult.isSuccess()) {
            // 删除和更新参与表
            boolean result = bmInfoJoinerService.deleteAndBatchAddBmInfoJoiner(bmInfoBo.getInfoId(),
                bmInfoBo.getBmInfoJoinerBoList());
            if (result) {
                ActivInfoConditionBo activInfoConditionBo = new ActivInfoConditionBo();
                activInfoConditionBo.setActivId(bmInfoBo.getActivId());
                activInfoConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
                ActivInfoVo activInfoVo = activInfoService.getActivInfoByCondition(activInfoConditionBo);
                if (activInfoVo != null && ChargeType.CHARGE.getCode().equals(activInfoVo.getChargeType())) {
                    ActivOrderBo orderBo = new ActivOrderBo();
                    orderBo.setGoodsId(bmInfoBo.getInfoId());
                    orderBo.setGoodsType(GoodsType.ACTIV_BM.getCode());
                    orderBo.setOrderAmount(activInfoVo.getChargeAmount()
                        .multiply(new BigDecimal(bmInfoBo.getBmInfoJoinerBoList().size())));
                    if (activInfoVo.getActivBizType() != null && activInfoVo.getActivBizType()
                        .equals(ActivBizType.NORMAL.getValue())) {
                        // 如果普通活动且是选择的材料则费用 = 报名配置缴费金额（元/人）*人数 + 材料费用
                        if (bmInfoBo.getSelectMaterialType() != null && bmInfoBo.getSelectMaterialType().equals(
                            SelectMaterialType.SELECT_YES.getValue()) && bmInfoBo.getSubBookTotalPrice() != null) {
                            orderBo.setOrderAmount(orderBo.getOrderAmount().add(bmInfoBo.getSubBookTotalPrice()));
                        }
                        activOrderService.changeOrderAmount(orderBo);
                    }
                }
                checkVo.setType(ActivBmCheckType.SUCCESS.getCode());
                return AjaxResult.success(checkVo);
            }
            return AjaxResult.fail("保存失败");
        }
        return ajaxResult;
    }

    @Override
    public BmInfoVo getBmInfoByCondition(BmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        BmInfoVo vo = bmInfoMapper.getBmInfoByCondition(condition);
        if (null != vo) {
            ActivInfoDto activInfoDto = activInfoService.getById(vo.getActivId());
            if (activInfoDto != null) {
                vo.setChargeType(activInfoDto.getChargeType());
                vo.setActivBizType(activInfoDto.getActivBizType());
            }
            return vo;
        }
        return null;
    }

    @Override
    public BmInfoVo getBmInfoWithJoiner(Long infoId) {
        BmInfoConditionBo condition = new BmInfoConditionBo();
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setInfoId(infoId);
        List<BmInfoVo> vos = bmInfoMapper.getBmInfoListWithOrder(condition);
        if (CollectionUtil.isEmpty(vos)) {
            return null;
        }
        BmInfoVo vo = vos.get(ConstantsInteger.NUM_0);

        BmInfoJoinerConditionBo bmInfoJoinerConditionBo = new BmInfoJoinerConditionBo();
        bmInfoJoinerConditionBo.setInfoId(infoId);
        bmInfoJoinerConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        bmInfoJoinerConditionBo.setPageNo(SystemConstants.NO_PAGE);
        List<BmInfoJoinerVo> bmInfoJoinerVos =
            bmInfoJoinerService.getBmInfoJoinerListByCondition(bmInfoJoinerConditionBo);
        if (CollectionUtils.isNotEmpty(bmInfoJoinerVos)) {
            vo.setBmInfoJoinerVoList(bmInfoJoinerVos);
        }

        BmPayAdjustRecordConditionBo bmPayAdjustRecordConditionBo = new BmPayAdjustRecordConditionBo();
        bmPayAdjustRecordConditionBo.setInfoId(infoId);
        List<BmPayAdjustRecordVo> bmPayAdjustRecordVos =
            bmPayAdjustRecordService.getBmPayAdjustRecordListByCondition(bmPayAdjustRecordConditionBo);
        if (CollectionUtil.isNotEmpty(bmPayAdjustRecordVos)) {
            vo.setBmPayAdjustRecordList(bmPayAdjustRecordVos);
        }

        return vo;
    }

    @Override
    public List<BmInfoExportVo> getBmInfoListByConditionExport(BmInfoConditionBo condition) {
        List<BmInfoExportVo> bmInfoListByConditionExport = bmInfoMapper.getBmInfoListByConditionExport(condition);
        if (CollectionUtils.isEmpty(bmInfoListByConditionExport)) {
            return bmInfoListByConditionExport;
        }

        List<BmInfoExportVo> resultList = Lists.newArrayList();
        Map<Long, List<BmInfoExportVo>> listMap =
            bmInfoListByConditionExport.stream().collect(Collectors.groupingBy(BmInfoExportVo::getInfoId));
        for (Long infoId : listMap.keySet()) {
            List<BmInfoExportVo> bmInfoExportVos = listMap.get(infoId);
            BmInfoExportVo bmInfoExportVo = bmInfoExportVos.get(0);
            List<BmInfoJoinerExportVo> bmInfoJoinerExportVos =
                bmInfoExportVos.stream().map(BmInfoExportVo::getBmInfoJoinerExportVo).collect(Collectors.toList());
            bmInfoExportVo.setBmInfoJoinerExportVos(bmInfoJoinerExportVos);
            bmInfoExportVo.setBmInfoJoinerExportVo(null);
            resultList.add(bmInfoExportVo);
        }
        return resultList;
    }

    @Override
    public List<BmInfoExportZdVo> getBmInfoListByConditionExportZd(BmInfoConditionBo condition) {
        List<BmInfoExportZdVo> bmInfoListByConditionExport = bmInfoMapper.getBmInfoListByConditionExportZd(condition);
        return bmInfoListByConditionExport;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public String generateAndSetSignInCode(BmInfoBo bmInfoBo, boolean saveSignCode) {
        if (bmInfoBo.getActivId() == null) {
            return "";
        }
        String code = RandomUtil.randomString(ConstString.RANDOM_SAMPLE_STRING, ConstantsInteger.NUM_4);

        BmInfoConditionBo bmInfoConditionBo = new BmInfoConditionBo();
        bmInfoConditionBo.setActivId(bmInfoBo.getActivId());
        bmInfoConditionBo.setSignInCode(code);
        BmInfoVo bmInfoByCondition = bmInfoService.getBmInfoByCondition(bmInfoConditionBo);
        // 仅做3次重试，如果3次重试仍然重复，后面走异常重试机制
        for (int i = 0; i < ConstantsInteger.NUM_3 && bmInfoByCondition != null; i++) {
            code = RandomUtil.randomString(ConstString.RANDOM_SAMPLE_STRING, ConstantsInteger.NUM_4);
            bmInfoConditionBo.setSignInCode(code);
            bmInfoByCondition = bmInfoService.getBmInfoByCondition(bmInfoConditionBo);
        }

        // 更新db
        if (saveSignCode) {
            bmInfoBo.setSignInCode(code);
            bmInfoService.updateBmInfo(bmInfoBo);
        }
        return code;
    }

    @Override
    public String generateNotRepeatSignInCode(BmInfoBo bmInfoBo, boolean saveSignCode) {
        String code = "";
        // 仅做3次重复校验，如果失败就前端提示重试：所以一共是3*3=9次生成，如果9次生成都重复则直接返回。
        for (int i = 0; i < ConstantsInteger.NUM_3; i++) {
            try {
                code = bmInfoService.generateAndSetSignInCode(bmInfoBo, saveSignCode);
                return code;
            } catch (DuplicateKeyException de) {
                log.error("generateNotRepeatSignInCode generateNotRepeatSignInCode error:", de);
            } catch (Exception e) {
                log.error("generateNotRepeatSignInCode error:", e);
                return "";
            }
        }
        return code;
    }

    @Override
    public AjaxResult bmInfoSignIn(BmInfoBo bmInfoBo) {
        List<BmInfoDto> list = baseMapper.selectList(
            new LambdaQueryWrapper<BmInfoDto>().eq(BmInfoDto::getActivId, bmInfoBo.getActivId())
                .eq(BmInfoDto::getContactMobile, bmInfoBo.getContactMobile())
                .eq(BmInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.success(ConstCode.SUCCESS_CODE, "未找到报名信息");
        }
        list =
            list.stream().filter(b -> bmInfoBo.getSignInCode().equals(b.getSignInCode())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.success(ConstCode.SUCCESS_CODE, "签到码错误");
        }
        BmInfoDto entity = list.get(ConstantsInteger.NUM_0);
        if (BmInfoSignInState.BM_INFO_SIGN.getCode().equals(entity.getSignInState())) {
            return AjaxResult.success(ConstCode.SUCCESS_CODE, "请勿重复签到");
        }
        entity.setSignInState(BmInfoSignInState.BM_INFO_SIGN.getCode());
        if (updateById(entity)) {
            return AjaxResult.success("签到成功");
        }
        return AjaxResult.fail("签到失败");
    }

    @Override
    public AjaxResult<Boolean> checkPhoneForLogin(String phone) {
        if (StringUtils.isNotEmpty(baseMapper.checkPhoneForLogin(phone))) {
            return AjaxResult.success(true);
        }
        return AjaxResult.success(false);
    }

    @Override
    public AjaxResult updateBmInfoByInfoId(BmInfoBo bmInfoBo) {
        BmInfoDto bmInfo = new BmInfoDto();
        BeanUtils.copyProperties(bmInfoBo, bmInfo);
        if (updateById(bmInfo)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult checkActivContact(BmInfoBo bmInfoBo) {
        BmInfoCheckVo checkVo = new BmInfoCheckVo();
        if (!bmInfoBo.isSkipCheckBmContact()) {
            // 校验手机号是否在联系人列表
            List<BmInfoDto> bmInfos = baseMapper.selectList(
                new LambdaQueryWrapper<BmInfoDto>().eq(BmInfoDto::getActivId, bmInfoBo.getActivId())
                    .eq(BmInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                    .eq(BmInfoDto::getContactMobile, bmInfoBo.getContactMobile()));
            if (StringUtils.isNotEmpty(bmInfos)) {
                checkVo.setType(ActivBmCheckType.ALREADY_SUBMIT.getCode());
                checkVo.setInfoId(bmInfos.get(ConstantsInteger.NUM_0).getInfoId());
                return AjaxResult.success(checkVo);
            }
        }
        // 校验手机号是否在参与人列表
        List<String> mobiles = Lists.newArrayList(bmInfoBo.getContactMobile());
        BmInfoCheckBo checkBo = new BmInfoCheckBo();
        checkBo.setActivId(bmInfoBo.getActivId());
        checkBo.setMobiles(mobiles);
        List<BmInfoJoinerVo> joinerVos = bmInfoJoinerService.checkBmInfoJoiner(checkBo);
        if (StringUtils.isNotEmpty(joinerVos)) {
            checkVo.setType(ActivBmCheckType.AS_JOINER_SUBMIT.getCode());
            checkVo.setContactMobile(joinerVos.get(ConstantsInteger.NUM_0).getContactMobile());
            checkVo.setContactName(joinerVos.get(ConstantsInteger.NUM_0).getContactName());
            checkVo.setInfoId(joinerVos.get(ConstantsInteger.NUM_0).getInfoId());
            return AjaxResult.success(checkVo);
        }
        checkVo.setType(ActivBmCheckType.SUCCESS.getCode());
        return AjaxResult.success(checkVo);
    }

    @Override
    public List<BmInfoVo> bmInfoDetailByMobile(BmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<BmInfoVo> list = baseMapper.getBmInfoListWithOrder(condition);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        for (BmInfoVo bmInfoVo : list) {
            BmInfoJoinerConditionBo bmInfoJoinerConditionBo = new BmInfoJoinerConditionBo();
            bmInfoJoinerConditionBo.setInfoId(bmInfoVo.getInfoId());
            bmInfoJoinerConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            bmInfoJoinerConditionBo.setPageNo(SystemConstants.NO_PAGE);
            List<BmInfoJoinerVo> bmInfoJoinerVos =
                bmInfoJoinerService.getBmInfoJoinerListByCondition(bmInfoJoinerConditionBo);
            if (CollectionUtils.isNotEmpty(bmInfoJoinerVos)) {
                bmInfoVo.setBmInfoJoinerVoList(bmInfoJoinerVos);
            }
        }
        return list;
    }

    @Override
    public List<BmInfoVo> getBmInfoListWithOrder(BmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return baseMapper.getBmInfoListWithOrder(condition);
    }

    @Override
    public BmInfoStatisticsVo getBmInfoStatistics(BmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return baseMapper.getBmInfoStatistics(condition);
    }

    @Override
    public Integer countBmInfoJoiner(BmInfoConditionBo conditionBo) {
        return baseMapper.countBmInfoJoiner(conditionBo);
    }

    @Override
    public boolean generateAccount(BmInfoBo bmInfoBo) {
        // 查询本次报名活动且已确认的用户（手机号码不为空，未开通账号）
        // 查询已确认且手机号码不为空的用户list
        List<BmInfoJoinerVo> bmInfoJoinerVos =
            bmInfoJoinerService.listJoinerByActivId(bmInfoBo.getActivId(), BmInfoSubmitType.ATTEND.getCode());
        if (CollectionUtils.isEmpty(bmInfoJoinerVos)) {
            return false;
        }
        List<BmInfoJoinerVo> hasMobileBmInfoJoinerVos =
            bmInfoJoinerVos.stream().filter(bmInfoJoinerVo -> StringUtils.isNotBlank(bmInfoJoinerVo.getJoinerMobile()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasMobileBmInfoJoinerVos)) {
            return false;
        }

        // 查询未开通账号的list
        List<String> allMobiles =
            hasMobileBmInfoJoinerVos.stream().map(BmInfoJoinerVo::getJoinerMobile).collect(Collectors.toList());
        // 未注册的手机号
        List<String> unregisterMobiles = baseDataService.listUnregisterMobiles(allMobiles);
        // 手机号码有账号：所在机构是新时代，所在机构不是新时代
        List<BmInfoJoinerVo> insertBmInfoJoinerVos = hasMobileBmInfoJoinerVos.stream()
            .filter(bmInfoJoinerVo -> unregisterMobiles.contains(bmInfoJoinerVo.getJoinerMobile()))
            .collect(Collectors.toList());

        // 给未开通的用户开通账号
        List<TeacherBo> teacherBos = new ArrayList<>();
        for (BmInfoJoinerVo insertBmInfoJoinerVo : insertBmInfoJoinerVos) {
            TeacherBo teacherBo = new TeacherBo();
            teacherBo.setUserRealName(insertBmInfoJoinerVo.getJoinerName());
            UserBo user = new UserBo();
            user.setRealName(insertBmInfoJoinerVo.getJoinerName());
            user.setPhone(insertBmInfoJoinerVo.getJoinerMobile());
            user.setSex(insertBmInfoJoinerVo.getJoinerGender());
            user.setOrganizationId(ineoschoolOrganizationId);
            user.setUserIdentityType(UserIdentityType.TEACHER.getValue());
            // 教师角色
            user.setRoleIds(Lists.newArrayList(SCHOOL_TEACHER));
            teacherBo.setUser(user);
            teacherBo.setGeneratorAccount(true);
            teacherBos.add(teacherBo);
        }
        baseDataService.addBathTeacher(teacherBos);
        return true;
    }

    /**
     * 对象装换未teacherBo
     *
     * @return list
     * <AUTHOR>
     * @date 2023 -08-09 11:36:22
     */
    private List<TeacherBo> transfer2TeacherBo() {
        return null;
    }

    @Override
    public boolean updatePayTypeBatch(List<Long> infoIds, Integer payType) {
        return bmInfoMapper.updatePayTypeBatch(infoIds, payType);
    }
}