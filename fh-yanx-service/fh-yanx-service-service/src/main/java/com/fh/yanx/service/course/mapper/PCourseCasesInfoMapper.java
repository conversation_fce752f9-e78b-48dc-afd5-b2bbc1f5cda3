package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesInfoDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoConditionBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;

/**
 * 校本课程案例详细信息Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesInfoMapper extends BaseMapper<PCourseCasesInfoDto> {

	List<PCourseCasesInfoVo> getPCourseCasesInfoListByCondition(PCourseCasesInfoConditionBo condition);

	PCourseCasesInfoVo getPCourseCasesInfoByCondition(PCourseCasesInfoConditionBo condition);

}
