package com.fh.yanx.service.utils;

import com.google.common.collect.Lists;
import com.light.core.utils.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 集合的对象拷贝
 *
 * <AUTHOR>
 */
public class CopyUtils {

    public static List parse(List list, Class toClazz) {
        List resp = Lists.newArrayListWithCapacity(list.size());
        resp = (List)list.stream().map(ff -> {
            Object obj = null;
            try {
                obj = toClazz.newInstance();
            } catch (Exception e) {
                e.printStackTrace();
            }
            BeanUtils.copyProperties(ff, obj);
            return obj;
        }).collect(Collectors.toList());
        return resp;
    }

}
