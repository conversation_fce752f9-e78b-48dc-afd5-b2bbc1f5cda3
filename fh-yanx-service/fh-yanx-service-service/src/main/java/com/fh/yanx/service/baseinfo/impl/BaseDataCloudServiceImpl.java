package com.fh.yanx.service.baseinfo.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.baseinfo.BaseDataType;
import com.fh.yanx.service.consts.*;
import com.fh.yanx.service.enums.*;
import com.fh.yanx.service.org.entity.bo.OrganizationBoExt;
import com.fh.yanx.service.org.entity.bo.OrganizationExtConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationVoExt;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.service.org.entity.vo.TeacherVoExt;
import com.fh.yanx.service.org.entity.vo.UserRoleVoExt;
import com.fh.yanx.service.org.entity.vo.UserVoExt;
import com.google.common.collect.Lists;
import com.light.base.area.api.AreaApi;
import com.light.base.attachment.api.AttachmentApi;
import com.light.base.attachment.entity.bo.MultipartFormData;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.category.api.CategoryApi;
import com.light.base.category.entity.bo.CategoryConditionBo;
import com.light.base.category.entity.vo.CategoryVo;
import com.light.base.dictionary.entity.bo.DictionaryDataListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.base.dictionary.service.DictionaryDataApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.BeanUtils;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.RSAUtil;
import com.light.security.service.CurrentUserService;
import com.light.user.account.api.AccountApi;
import com.light.user.account.entity.bo.AccountBo;
import com.light.user.account.entity.bo.AccountConditionBo;
import com.light.user.account.entity.vo.AccountVo;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.admin.api.AdminApi;
import com.light.user.admin.entity.bo.AdminBo;
import com.light.user.admin.entity.bo.AdminConditionBo;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.organization.api.OrganizationSettingApi;
import com.light.user.organization.api.OrganizationSetupApi;
import com.light.user.organization.entity.bo.*;
import com.light.user.organization.entity.vo.OrganizationSettingVo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.organization.service.OrganizationApiService;
import com.light.user.role.api.RoleApi;
import com.light.user.role.entity.bo.RoleConditionBo;
import com.light.user.role.entity.vo.RoleVo;
import com.light.user.student.api.StudentApi;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.student.service.StudentApiService;
import com.light.user.teacher.api.TeacherApi;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.teacher.service.TeacherApiService;
import com.light.user.user.api.*;
import com.light.user.user.entity.bo.*;
import com.light.user.user.entity.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ThreadUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 公共cloud服务的实现(http调用user和base的服务)
 *
 * <AUTHOR>
 * @date 2022/6/1 10:28
 */
@Slf4j
public class BaseDataCloudServiceImpl implements BaseDataService {

    @Resource
    private UserApi userApi;
    @Resource
    private TeacherApi teacherApi;
    @Resource
    private StudentApi studentApi;
    @Resource
    private CurrentUserService currentUserService;
    @Resource
    private CategoryApi categoryApi;
    @Resource
    private StudentApiService studentApiService;
    @Resource
    private TeacherApiService teacherApiService;
    @Resource
    private DictionaryDataApiService dictionaryDataApiService;
    @Resource
    private AreaApi areaApi;
    @Resource
    private OrganizationApiService organizationApiService;
    @Resource
    private AttachmentApi attachmentApi;
    @Resource
    private AccountApi accountApi;
    @Resource
    private OrganizationSetupApi organizationSetupApi;
    @Resource
    private OrganizationSettingApi organizationSettingApi;
    @Resource
    private UserRoleApi userRoleApi;
    @Resource
    private UserBorrowApi userBorrowApi;
    @Resource
    private UserTransferApi userTransferApi;
    @Resource
    private AdminApi adminApi;
    @Resource
    private RoleApi roleApi;

    @Value("${default.password}")
    private String DEFAULT_PASSWORD;

    @Value("${server.encry.publicKey}")
    private String PUBLIC_KEY;

    @Value("${server.encry.privateKey}")
    private String PRIVATE_KEY;

    @Resource
    private UserOrgApi userOrgApi;

    private static final ExecutorService executor = Executors.newFixedThreadPool(5);

    // 学校教师角色id
    private static final Long SCHOOL_TEACHER = 8L;

    @Override
    public String name() {
        return BaseDataType.CLOUD_BASE.getName();
    }

    @Override
    public String echo() {
        return "BaseDataCloudServiceImpl...";
    }

    @Override
    public Map<String, String> getRealNameByUserOids(List<String> userOids) {
        if (CollectionUtils.isEmpty(userOids)) {
            return Maps.newHashMap();
        }
        Map<String, String> userNameMap = new HashMap<>();
        AjaxResult<List<UserVo>> ajaxResult = userApi.getByOidList(userOids);
        if (ajaxResult.isSuccess() && CollectionUtils.isNotEmpty(ajaxResult.getData())) {
            userNameMap =
                ajaxResult.getData().stream().collect(Collectors.toMap(UserVo::getUserOid, UserVo::getRealName));
        }
        return userNameMap;
    }

    @Override
    public TeacherVo getTeacherVoByUserOid(String userOid) {
        if (StringUtils.isBlank(userOid)) {
            return null;
        }
        AjaxResult ajaxResult = teacherApi.getByUserOid(userOid);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return null;
        }
        TeacherVo teacherVo = JSONObject.parseObject(JSONObject.toJSONString(ajaxResult.getData()), TeacherVo.class);
        AjaxResult ajaxResultDetail = teacherApi.getDetail(teacherVo.getId());
        if (ajaxResultDetail.isFail() || ajaxResultDetail.getData() == null) {
            return null;
        }
        teacherVo = JSONObject.parseObject(JSONObject.toJSONString(ajaxResultDetail.getData()), TeacherVo.class);
        if (teacherVo == null || teacherVo.getUserVo() == null) {
            return null;
        }
        UserVo userVo = teacherVo.getUserVo();
        String registeredResidence = "";
        if (userVo.getRegisteredResidenceProvince() != null || userVo.getRegisteredResidenceCity() != null
            || userVo.getRegisteredResidenceArea() != null) {
            String prov = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceProvince()).getData();
            String city = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceCity()).getData();
            String area = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceArea()).getData();
            registeredResidence = registeredResidence.concat(prov == null ? "" : prov).concat(",")
                .concat(city == null ? "" : city).concat(",").concat(area == null ? "" : area).concat(" ");
        }
        registeredResidence = userVo.getRegisteredResidence() == null ? registeredResidence
            : registeredResidence.concat(userVo.getRegisteredResidence());
        userVo.setRegisteredResidence(registeredResidence);
        return teacherVo;
    }

    @Override
    public StudentVo getStudentVoByUserOid(String userOid) {
        final AjaxResult rest = this.studentApi.getByUserOid(userOid);
        if (rest.isFail() || rest.getData() == null) {
            return null;
        }
        final Map<String, Object> studentMap = (Map<String, Object>)rest.getData();
        StudentVo studentVo = BeanUtil.mapToBean(studentMap, StudentVo.class, true);

        AjaxResult detail = studentApi.getDetail(studentVo.getId());
        if (detail.isFail() || null == detail.getData()) {
            return null;
        }
        final Map<String, Object> map = (Map<String, Object>)detail.getData();
        studentVo = BeanUtil.mapToBean(map, StudentVo.class, true);
        if (studentVo == null || studentVo.getUserVo() == null) {
            return null;
        }
        UserVo userVo = studentVo.getUserVo();
        String homeAddress = "";
        if (userVo.getHomeAddressProvince() != null) {
            String prov = areaApi.getAreaNameFromCache(userVo.getHomeAddressProvince()).getData();
            String city = areaApi.getAreaNameFromCache(userVo.getHomeAddressCity()).getData();
            String area = areaApi.getAreaNameFromCache(userVo.getHomeAddressArea()).getData();
            homeAddress = homeAddress.concat(prov == null ? "" : prov).concat(",").concat(city == null ? "" : city)
                .concat(",").concat(area == null ? "" : area).concat(" ");
        }
        homeAddress = userVo.getHomeAddress() == null ? homeAddress : homeAddress.concat(userVo.getHomeAddress());
        userVo.setHomeAddress(homeAddress);
        return studentVo;
    }

    /**
     * 获取当前用户oid
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/2/2 15:19
     */
    @Override
    public String getCurrentUserOid() {
        return currentUserService.getCurrentOid();
    }

    /**
     * 获取当前登录用户
     *
     * @return com.light.user.account.entity.vo.LoginAccountVo
     * <AUTHOR>
     * @date 2023/8/3 11:30
     **/
    @Override
    public LoginAccountVo getCurrentUser() {
        return currentUserService.getCurrentUser();
    }

    /**
     * 查询学科（科目）列表
     *
     * @return java.util.List<com.light.base.category.entity.vo.CategoryVo>
     * <AUTHOR>
     * @date 2022/5/5 15:39
     */
    @Override
    public List<CategoryVo> getSubjectList() {
        CategoryConditionBo sectionCondition = new CategoryConditionBo();
        sectionCondition.setPageNo(SystemConstants.NO_PAGE);
        sectionCondition.setParentId(0L);
        sectionCondition.setModuleCode(DictType.YANX_SUBJECT.getValue());
        AjaxResult sectionResult = categoryApi.getCategoryListByCondition(sectionCondition);
        if (sectionResult.isFail()) {
            return null;
        }
        Map<String, Object> sectionMap = (Map<String, Object>)sectionResult.getData();
        List<Map> sectionList = (List)sectionMap.get("list");
        List<CategoryVo> categoryVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(sectionList)) {
            categoryVos = JSONObject.parseArray(JSONObject.toJSONString(sectionList), CategoryVo.class);
        }
        return categoryVos;
    }

    /**
     * 根据条件获取学生信息
     *
     * @param studentConditionBo
     * @return
     */
    @Override
    public List<StudentVo> getStudentVoList(StudentConditionBo studentConditionBo) {
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        final AjaxResult result = this.studentApiService.getStudentListByCondition(studentConditionBo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return null;
        }
        Map<String, Object> map = (Map<String, Object>)data;
        List<StudentVo> studentVos =
            JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(map.get("list"))), StudentVo.class);
        return studentVos;
    }

    /**
     * 根据条件获取老师基础信息
     *
     * @param teacherConditionBo
     * @return
     */
    @Override
    public List<TeacherVo> getTeacherVoList(TeacherConditionBo teacherConditionBo) {
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        teacherConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        final AjaxResult result = this.teacherApiService.getTeacherListByCondition(teacherConditionBo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return null;
        }
        Map<String, Object> map = (Map<String, Object>)data;
        List<TeacherVo> studentVos =
            JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(map.get("list"))), TeacherVo.class);
        return studentVos;
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictTypes the dict types
     * @return value by type and label
     * <AUTHOR>
     * @date 2022 -04-21 17:58:28
     */
    @Override
    public List<DictionaryDataVo> listValueByTypes(List<String> dictTypes) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dictTypes)) {
            return new ArrayList<>();
        }

        // 去重
        dictTypes = dictTypes.stream().distinct().collect(Collectors.toList());

        List<DictionaryDataVo> resultList = new ArrayList<>();
        for (String dictType : dictTypes) {
            List<DictionaryDataVo> temp = listValueByTypeAndLabel(dictType);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(temp)) {
                continue;
            }
            resultList.addAll(temp);
        }
        return resultList;
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictType the dict type
     * @param dictLabel the dict label
     * @return value by type and label
     * <AUTHOR>
     * @date 2022 -04-21 17:58:28
     */
    private List<DictionaryDataVo> listValueByTypeAndLabel(String dictType) {
        DictionaryDataListConditionBo dictionaryDataListConditionBo = new DictionaryDataListConditionBo();
        dictionaryDataListConditionBo.setDictType(dictType);
        dictionaryDataListConditionBo.setPageNo(ConstantsInteger.NUM_1);
        dictionaryDataListConditionBo.setPageSize(ConstantsInteger.NUM_1000);
        AjaxResult<Object> ajaxResult =
            dictionaryDataApiService.getAllDictionaryDataList(dictionaryDataListConditionBo);

        if (ajaxResult.isFail()) {
            return new ArrayList<>();
        }
        Object data = ajaxResult.getData();

        List<DictionaryDataVo> dictionaryDataVos =
            JSONObject.parseArray(JSONObject.toJSONString(data), DictionaryDataVo.class);
        return dictionaryDataVos;
    }

    /**
     * 通过userOids学生列表
     *
     * @param userOids
     * @return java.util.List<com.light.user.student.entity.vo.StudentVo>
     * <AUTHOR>
     * @date 2023/2/6 16:41
     */
    @Override
    public List<StudentVo> getStudentVolistByUserOids(List<String> userOids) {
        StudentConditionBo conditionBo = new StudentConditionBo();
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setUserOids(userOids);
        final AjaxResult rest = this.studentApiService.getStudentListByCondition(conditionBo);
        if (rest.isFail() || rest.getData() == null) {
            return null;
        }
        final Map<String, Object> studentMap = (Map<String, Object>)rest.getData();
        StudentVo studentVo = BeanUtil.mapToBean(studentMap, StudentVo.class, true);

        AjaxResult detail = studentApiService.getDetail(studentVo.getId());
        if (detail.isFail() || null == detail.getData()) {
            return null;
        }
        final Map<String, Object> map = (Map<String, Object>)detail.getData();
        // return BeanUtil.mapToBean(map, StudentVo.class, true);
        return null;
    }

    /**
     * 获取组织机构列表
     *
     * @param orgIds
     * @return
     */
    @Override
    public List<OrganizationVo> getOrganizationVoList(List<Long> orgIds) {
        OrganizationConditionBo condition = new OrganizationConditionBo();
        condition.setOrganizationIds(orgIds);
        condition.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult ajaxResult = organizationApiService.getOrganizationListByCondition(condition);
        if (ajaxResult.isFail()) {
            return null;
        }
        Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
        return JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), OrganizationVo.class);
    }

    public AttachmentVo upload(MultipartFormData multipartFormData) {
        AjaxResult result;
        try {
            result = attachmentApi.upload(multipartFormData);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        if (null == result || result.isFail() || null == result.getData()) {
            return null;
        }
        final Map<String, Object> map = (Map<String, Object>)result.getData();
        return BeanUtil.mapToBean(map, AttachmentVo.class, true);
    }

    public AttachmentVo upload(MultipartFile file) {
        AjaxResult result;
        try {
            result = attachmentApi.upload(file);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        if (null == result || result.isFail() || null == result.getData()) {
            return null;
        }
        final Map<String, Object> map = (Map<String, Object>)result.getData();
        return BeanUtil.mapToBean(map, AttachmentVo.class, true);
    }

    @Override
    public Map<String, Object> getOrganizationListByCondition(OrganizationExtConditionBo conditionBo) {
        List<Long> organizationIds = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date nowDay = new Date();

        OrganizationSettingConditionBo settingConditionBo = new OrganizationSettingConditionBo();
        settingConditionBo.setK(ConstOrgAuth.ORG_AUTH);
        settingConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult<List<OrganizationSettingVo>> settingVosResult =
            organizationSettingApi.getOrganizationSettingListByCondition(settingConditionBo);
        if (settingVosResult.isFail()) {
            return null;
        }

        List<Long> authOrgIds = new ArrayList<>();
        List<OrganizationSettingVo> settingVos = settingVosResult.getData();
        Map<Long, OrganizationSettingVo> authOrgMap = settingVos.stream()
            .collect(Collectors.toMap(OrganizationSettingVo::getOrganizationId, s -> s, (v1, v2) -> v1));
        try {
            for (OrganizationSettingVo settingVo : settingVos) {
                if (OrganizationAuthType.AUTH.getCode().equals(Integer.valueOf(settingVo.getRemark()))
                    && sdf.parse(sdf.format(nowDay)).compareTo(sdf.parse(settingVo.getVal())) <= 0) {
                    authOrgIds.add(settingVo.getOrganizationId());
                }
            }
        } catch (Exception e) {
            log.error("get orgAuth error : ", e);
            return null;
        }

        if (conditionBo.getAuthType() != null) {
            if (conditionBo.getAuthType().equals(OrganizationAuthType.AUTH.getCode())) {
                organizationIds = authOrgIds;
            } else {
                List<Long> allOrgIds = getAllYanxOrgIdList();
                if (allOrgIds == null) {
                    return null;
                }
                for (Long orgId : allOrgIds) {
                    if (!authOrgIds.contains(orgId)) {
                        organizationIds.add(orgId);
                    }
                }
            }
            if (CollectionUtil.isEmpty(organizationIds)) {
                Map<String, Object> map = new HashMap<>();
                map.put("total", ConstantsInteger.NUM_0);
                map.put("list", new ArrayList<>());
                return map;
            }
        }

        if (CollectionUtil.isNotEmpty(organizationIds)) {
            conditionBo.setOrganizationIds(organizationIds);
        }
        if (!(conditionBo.getType().equals(OrgType.ORG.getValue())
            && conditionBo.getParentId().equals(ConstantsLong.NUM_0))) {
            if (CollectionUtil.isEmpty(organizationIds)) {
                List<Long> orgIds = getAllYanxOrgIdList();
                if (CollectionUtil.isEmpty(orgIds)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("total", ConstantsInteger.NUM_0);
                    map.put("list", new ArrayList<>());
                    return map;
                }
                conditionBo.setOrganizationIds(orgIds);
            }
        }
        if (conditionBo.getType().equals(OrgType.ORG.getValue())
            && conditionBo.getParentId().equals(ConstantsLong.NUM_0)) {
            conditionBo.setOrderBy("CONVERT(name USING GBK)");
        } else {
            conditionBo.setOrderBy("create_time desc");
        }
        AjaxResult ajaxResult = organizationApiService.getOrganizationListByCondition(conditionBo);
        if (ajaxResult.isFail()) {
            return null;
        }
        Map<String, Object> map = (Map<String, Object>)ajaxResult.getData();

        List<OrganizationVoExt> list =
            JSONArray.parseArray(JSONArray.toJSONString(map.get("list")), OrganizationVoExt.class);
        // 额外查询本分页内的其他信息
        List<Long> organizationParentIdsDb = Lists.newArrayList();
        Map<Long, OrganizationVoExt> organizationParentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(list)) {
            organizationParentIdsDb = list.stream().map(OrganizationVoExt::getParentId).collect(Collectors.toList());
            OrganizationConditionBo organizationConditionBoParent = new OrganizationConditionBo();
            organizationConditionBoParent.setOrganizationIds(organizationParentIdsDb);
            organizationConditionBoParent.setPageNo(SystemConstants.NO_PAGE);
            AjaxResult ajaxResultParent =
                organizationApiService.getOrganizationListByCondition(organizationConditionBoParent);
            if (ajaxResultParent.isSuccess()) {
                Map<String, Object> mapParent = (Map<String, Object>)ajaxResultParent.getData();
                List<OrganizationVoExt> listParent =
                    JSONArray.parseArray(JSONArray.toJSONString(mapParent.get("list")), OrganizationVoExt.class);
                organizationParentMap =
                    listParent.stream().collect(Collectors.toMap(OrganizationVoExt::getId, o -> o, (v1, v2) -> v1));
            }
        }

        for (OrganizationVoExt organizationVo : list) {
            AjaxResult orgSuperAdminDetail = getOrgSuperAdminDetail(organizationVo.getId(), organizationVo.getType());
            AccountVo accountVo =
                JSONObject.parseObject(JSONObject.toJSONString(orgSuperAdminDetail.getData()), AccountVo.class);
            if (orgSuperAdminDetail.isSuccess() && accountVo != null) {
                organizationVo.setAccountName(accountVo.getAccountName());
                organizationVo.setAccountId(accountVo.getId());
            }
            if (organizationParentMap.containsKey(organizationVo.getParentId())) {
                organizationVo.setParentName(organizationParentMap.get(organizationVo.getParentId()).getName());
            }

            OrganizationSettingVo settingVo = authOrgMap.get(organizationVo.getId());
            try {
                if (settingVo != null) {
                    if (OrganizationAuthType.AUTH.getCode().equals(Integer.valueOf(settingVo.getRemark()))
                        && sdf.parse(sdf.format(nowDay)).compareTo(sdf.parse(settingVo.getVal())) <= 0) {
                        organizationVo.setAuthType(OrganizationAuthType.AUTH.getCode());
                    } else {
                        organizationVo.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
                    }
                    organizationVo.setAuthEndDay(sdf.parse(settingVo.getVal()));
                } else {
                    organizationVo.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
                }
            } catch (Exception e) {
                log.error("set org authType error : ", e);
            }
        }

        map.put("list", list);
        return map;
    }

    @Override
    public AjaxResult getOrganizationByCondition(OrganizationConditionBo conditionBo) {
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setType(OrgType.SCHOOL.getValue());
        AjaxResult<List<OrganizationVo>> ajaxResult = organizationApiService.getByCondition(conditionBo);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        if (CollectionUtil.isEmpty(ajaxResult.getData())) {
            return AjaxResult.success();
        }
        List<OrganizationVo> list = ajaxResult.getData();
        list = list.stream().filter(o -> conditionBo.getName().equals(o.getName())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.success();
        }
        OrganizationVo organizationVo = list.get(ConstantsInteger.NUM_0);
        OrganizationSettingConditionBo condition = new OrganizationSettingConditionBo();
        condition.setOrganizationId(organizationVo.getId());
        condition.setK(ConstServiceName.PLATFORM_KEY);
        condition.setEqVal(ConstServiceName.PLATFORM_NAME);
        AjaxResult<List<OrganizationSettingVo>> settingResult =
            organizationSettingApi.getOrganizationSettingListByCondition(condition);
        if (settingResult.isFail()) {
            return settingResult;
        }
        if (CollectionUtils.isNotEmpty(settingResult.getData())) {
            return AjaxResult.fail("当前学校已创建，请勿重复创建");
        }
        return AjaxResult.success(organizationVo);
    }

    /**
     * 新增组织机构
     *
     * @return
     */
    @Override
    public Long addOrganization(OrganizationBo organizationBo) {
        if (StringUtils.isBlank(organizationBo.getCode())) {
            organizationBo.setCode(IdUtil.fastSimpleUUID());
        }
        final AjaxResult addOrganizationResult = organizationApiService.addOrganization(organizationBo);
        if (addOrganizationResult.isSuccess()) {
            Long orgId = Long.parseLong(addOrganizationResult.getData().toString());
            organizationBo.setId(orgId);
            // 学术组织不创建管理员账号
            if (organizationBo.getType() != 3) {
                addSuperAdmin(organizationBo);
            }
            return orgId;
        }
        return null;
    }

    @Override
    public boolean updateOrganization(OrganizationBoExt organizationBo) {
        final AjaxResult updateOrgResult = organizationApiService.updateOrganization(organizationBo);
        if (updateOrgResult.isFail()) {
            return BaseDataService.super.updateOrganization(organizationBo);
        }
        AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationBo.getId());
        if (byOrgId.isFail()) {
            return false;
        }
        OrganizationSetupVo data = byOrgId.getData();
        OrganizationSetupBo organizationSetupBo = new OrganizationSetupBo();
        organizationSetupBo.setOrganizationId(organizationBo.getId());
        organizationSetupBo.setLogo(organizationBo.getLogo());
        organizationSetupBo.setWebName(organizationBo.getWebName());
        organizationSetupBo.setOtherConfig(organizationBo.getOtherConfig());
        if (data == null) {
            organizationSetupApi.saveOrganizationSetup(organizationSetupBo);
        } else {
            organizationSetupBo.setId(data.getId());
            organizationSetupApi.updateOrganizationSetup(organizationSetupBo);
        }
        return true;
    }

    @Override
    public OrganizationVoExt getOrganizationDetail(Long organizationId) {
        AjaxResult detailResult = organizationApiService.getDetail(organizationId);
        if (detailResult.isFail() || detailResult.getData() == null) {
            return null;
        }
        Map<String, Object> map = (Map<String, Object>)detailResult.getData();
        OrganizationVoExt organizationVo =
            JSONObject.parseObject(JSONObject.toJSONString(map.get("organizationVo")), OrganizationVoExt.class);
        if (null != organizationVo) {
            // 副标题，logo,建校日期存org_setup
            AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationId);
            if (byOrgId.isSuccess() && byOrgId.getData() != null) {
                OrganizationSetupVo organizationSetupVo = byOrgId.getData();
                organizationVo.setLogo(organizationSetupVo.getLogo());
                organizationVo.setWebName(organizationSetupVo.getWebName());
                organizationVo.setOtherConfig(organizationSetupVo.getOtherConfig());
            }
        }
        return organizationVo;
    }

    @Override
    public boolean addOrUpdateOrganizationSetting(OrganizationSettingBo organizationSettingBo) {
        OrganizationSettingConditionBo settingConditionBo = new OrganizationSettingConditionBo();
        settingConditionBo.setOrganizationId(organizationSettingBo.getOrganizationId());
        settingConditionBo.setK(organizationSettingBo.getK());
        final AjaxResult<List<OrganizationSettingVo>> settingResult =
            organizationSettingApi.getOrganizationSettingListByCondition(settingConditionBo);
        if (settingResult.isFail()) {
            return false;
        }
        if (CollectionUtil.isEmpty(settingResult.getData())) {
            final AjaxResult ajaxResult = organizationSettingApi.addOrganizationSetting(organizationSettingBo);
            return !ajaxResult.isFail();
        } else {
            OrganizationSettingVo organizationSettingVo = settingResult.getData().get(ConstantsInteger.NUM_0);
            organizationSettingBo.setId(organizationSettingVo.getId());
            final AjaxResult ajaxResult = organizationSettingApi.updateOrganizationSetting(organizationSettingBo);
            return !ajaxResult.isFail();
        }

    }

    @Override
    public OrganizationSettingVo getOrganizationSetting(OrganizationSettingConditionBo conditionBo) {
        final AjaxResult<List<OrganizationSettingVo>> ajaxResult =
            organizationSettingApi.getOrganizationSettingListByCondition(conditionBo);
        if (ajaxResult.isFail() || CollectionUtil.isEmpty(ajaxResult.getData())) {
            return null;
        }
        return ajaxResult.getData().get(ConstantsInteger.NUM_0);
    }

    @Override
    public AjaxResult changePassword(String oldPassword, String password) {
        try {
            oldPassword = RSAUtil.privateDecrypt(oldPassword, PRIVATE_KEY);
            password = RSAUtil.privateDecrypt(password, PRIVATE_KEY);
            return accountApi.changePasswordSimple(oldPassword, password);
        } catch (Exception e) {
            log.error("changePassword error : ", e);
        }
        return BaseDataService.super.changePassword(oldPassword, password);
    }

    @Override
    public AjaxResult updateAccount(AccountBo accountBo) {
        return accountApi.updateAccount(accountBo);
    }

    @Override
    public boolean updateUser(UserBo userBo) {
        AjaxResult updateUserResult = userApi.updateUser(userBo);
        return !updateUserResult.isFail();
    }

    @Override
    public boolean resetPassword(Long accountId) {
        final AjaxResult accountDetailResult = accountApi.getDetail(accountId);
        if (accountDetailResult.isFail() || accountDetailResult.getData() == null) {
            return false;
        }
        Map<String, Object> map = (Map<String, Object>)accountDetailResult.getData();
        AccountVo accountVo = JSONObject.parseObject(JSONObject.toJSONString(map.get("accountVo")), AccountVo.class);
        final AjaxResult ajaxResult = accountApi.resetPassword(accountId);
        if (!ajaxResult.isFail()) {
            userApi.kick(accountVo.getAccountName());
            return true;
        }
        return false;
    }

    @Override
    public AjaxResult addTeacher(TeacherBo teacherBo) {
        teacherBo.setType(TeacherType.TEACH_USER.getCode());
        return teacherApi.addTeacher(teacherBo);
    }

    @Override
    public AjaxResult addBathTeacher(List<TeacherBo> teacherBos) {
        if (CollectionUtils.isNotEmpty(teacherBos)) {
            teacherBos.forEach(teacherBo -> teacherBo.setType(TeacherType.TEACH_USER.getCode()));
        }
        return teacherApi.addBatchTeacher(teacherBos);
    }

    @Override
    public AjaxResult updateTeacher(TeacherBo teacherBo) {
        final AjaxResult ajaxResult = teacherApi.updateTeacher(teacherBo);
        // 获取用户角色列表
        AjaxResult<List<UserRoleVo>> userRoleListResult = userRoleApi.getListByUserOid(teacherBo.getUser().getOid());
        // 新时代前台案例持有者角色
        List<Long> receptionRoleIds = Arrays.stream(ReceptionRoleEnum.values()).map(ReceptionRoleEnum::getRoleId)
                .collect(Collectors.toList());
        if (userRoleListResult.isSuccess()) {
            List<UserRoleVo> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(userRoleListResult.getData())) {
                list = userRoleListResult.getData().stream()
                        .filter(x -> receptionRoleIds.contains(x.getRoleId())).collect(Collectors.toList());
            }
            List<Long> userRoleIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                userRoleIds = list.stream().map(UserRoleVo::getRoleId).collect(Collectors.toList());
            }
            // 判断是否新增角色
            if (CollectionUtils.isNotEmpty(teacherBo.getUser().getRoleIds())) {
                Long organizationId = 0L;
                if (StringUtils.isNotBlank(teacherBo.getUser().getOrganizationIds())) {
                    String[] organizationIds = teacherBo.getUser().getOrganizationIds().split(",");
                    organizationId = Long.parseLong(organizationIds[ConstantsInteger.NUM_0]);
                 }
                for (Long roleId : teacherBo.getUser().getRoleIds()) {
                    if (userRoleIds.contains(roleId)) {
                        continue;
                    }
                    UserRoleBo userRoleBo = new UserRoleBo();
                    userRoleBo.setRoleId(roleId);
                    userRoleBo.setUserOid(teacherBo.getUser().getOid());
                    userRoleBo.setOrganizationId(organizationId);
                    userRoleApi.addUserRole(userRoleBo);
                }
            }
            // 判断移除
            if (CollectionUtil.isNotEmpty(list)) {
                if (CollectionUtils.isNotEmpty(teacherBo.getUser().getRoleIds())) {
                    list = list.stream().filter(ur -> !teacherBo.getUser().getRoleIds().contains(ur.getRoleId())).collect(Collectors.toList());
                }
                for (UserRoleVo userRoleVo : list) {
                    // 教师角色不处理
                    if (ReceptionRoleEnum.SCHOOL_TEACHER.getRoleId().equals(userRoleVo.getRoleId())) {
                        continue;
                    }
                    userRoleApi.delete(userRoleVo.getId());
                }
            }
        }

        return ajaxResult;
    }

    @Override
    public TeacherVoExt getTeacherDetail(String userOid) {
        AjaxResult ajaxResult = teacherApi.getByUserOid(userOid);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return null;
        }
        TeacherVo teacherVo = JSONObject.parseObject(JSONObject.toJSONString(ajaxResult.getData()), TeacherVo.class);
        AjaxResult detailResult = teacherApi.getDetail(teacherVo.getId());
        if (detailResult.isFail() || detailResult.getData() == null) {
            return null;
        }
        TeacherVoExt vo = JSONObject.parseObject(JSONObject.toJSONString(detailResult.getData()), TeacherVoExt.class);
        AjaxResult<List<UserRoleVo>> userRoleVoResult = userRoleApi.getListByUserOid(userOid);
        if (userRoleVoResult.isSuccess() && CollectionUtils.isNotEmpty(userRoleVoResult.getData())) {
            List<Long> receptionRoleIds = Arrays.stream(ReceptionRoleEnum.values()).map(ReceptionRoleEnum::getRoleId)
                    .collect(Collectors.toList());
            List<UserRoleVoExt> userRoleList = userRoleVoResult.getData().stream()
                    .filter(x -> receptionRoleIds.contains(x.getRoleId()))
                    .map(x -> {
                        UserRoleVoExt voExt = new UserRoleVoExt();
                        org.springframework.beans.BeanUtils.copyProperties(x, voExt);
                        ReceptionRoleEnum roleEnum = ReceptionRoleEnum.getByRoleId(x.getRoleId());
                        if (roleEnum != null) {
                            voExt.setName(roleEnum.getName());
                            voExt.setCode(roleEnum.getCode());
                        }
                        return voExt;
                    })
                    .collect(Collectors.toList());
            vo.setUserRoleList(userRoleList);
        }
        return vo;
    }

    @Override
    public boolean delTeacher(Long teacherId) {
        // 借调逻辑判断处理
        try {
            AjaxResult teacherAjaxResult = teacherApi.getDetail(teacherId);
            if (teacherAjaxResult.isSuccess()) {
                TeacherVo teacherVo =
                    JSONObject.parseObject(JSONObject.toJSONString(teacherAjaxResult.getData()), TeacherVo.class);
                if (teacherVo != null) {
                    // 是否有借调信息，有则删除user_org，没有则删除教师
                    UserBorrowListConditionBo userBorrowListConditionBo = new UserBorrowListConditionBo();
                    userBorrowListConditionBo
                        .setTargetOrganizationId(teacherVo.getUserVo().getUserOrgList().get(0).getOrganizationId());
                    userBorrowListConditionBo.setUserOid(teacherVo.getUserOid());
                    userBorrowListConditionBo.setPageNo(SystemConstants.NO_PAGE);
                    if (userBorrowListConditionBo.getTargetOrganizationId() == null
                        || StringUtils.isBlank(userBorrowListConditionBo.getUserOid())) {
                        throw new RuntimeException("参数错误");
                    }
                    AjaxResult userBorrowAjaxResult =
                        userBorrowApi.getUserBorrowListByCondition(userBorrowListConditionBo);
                    if (userBorrowAjaxResult.isSuccess()) {
                        List<UserBorrowVo> userBorrowVos = JSONObject
                            .parseArray(JSONObject.toJSONString(userBorrowAjaxResult.getData()), UserBorrowVo.class);
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userBorrowVos)) {
                            for (UserBorrowVo userBorrowVo : userBorrowVos) {
                                userBorrowApi.cancel(userBorrowVo.getId());
                            }
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("删除教师信息处理借调逻辑错误：", e);
        }

        // 删除教师主体逻辑（上面如果出现异常会吃掉异常，不影响下面主体逻辑）
        TeacherBo teacherBo = new TeacherBo();
        teacherBo.setTeacherId(teacherId);
        teacherBo.setDelAccount(true);
        AjaxResult ajaxResult = teacherApi.delete(teacherBo);
        return !ajaxResult.isFail();
    }

    @Override
    public Map<String, Object> getTeacherList(TeacherConditionBo conditionBo) {
        OrganizationExtConditionBo organizationConditionBo = new OrganizationExtConditionBo();
        organizationConditionBo.setType(OrgType.SCHOOL.getValue());
        organizationConditionBo.setPageNo(SystemConstants.NO_PAGE);
        Map<String, Object> organizationMap = getOrganizationListByCondition(organizationConditionBo);
        if (organizationMap == null || !organizationMap.containsKey("list")) {
            return null;
        }
        List<OrganizationVoExt> organizationVos = JSONArray.parseArray(JSONArray.toJSONString(organizationMap.get("list")), OrganizationVoExt.class);
        if (CollectionUtils.isEmpty(organizationVos)) {
            return null;
        }
        List<Long> orgIds = organizationVos.stream().map(OrganizationVoExt::getId).collect(Collectors.toList());
        conditionBo.setOrganizationIds(orgIds);
        if (StringUtils.isNotBlank(conditionBo.getRealName())) {
            conditionBo.setRealName(FuzzyQueryUtil.transferMean(conditionBo.getRealName()));
        }
        if (StringUtils.isNotBlank(conditionBo.getOrganizationName())) {
            conditionBo.setOrganizationName(FuzzyQueryUtil.transferMean(conditionBo.getOrganizationName()));
        }
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        conditionBo.setType(TeacherType.TEACH_USER.getCode());
        AjaxResult ajaxResult = teacherApi.getTeacherListByCondition(conditionBo);
        if (ajaxResult.isFail()) {
            return null;
        }

        Map<String, Object> map = (Map<String, Object>)ajaxResult.getData();
        List<TeacherVoExt> list = JSONArray.parseArray(JSONArray.toJSONString(map.get("list")), TeacherVoExt.class);

        if (CollectionUtil.isNotEmpty(list)) {
            OrganizationSettingConditionBo settingConditionBo = new OrganizationSettingConditionBo();
            settingConditionBo.setK(ConstOrgAuth.ORG_AUTH);
            settingConditionBo.setPageNo(SystemConstants.NO_PAGE);
            AjaxResult<List<OrganizationSettingVo>> settingVosResult =
                organizationSettingApi.getOrganizationSettingListByCondition(settingConditionBo);
            if (settingVosResult.isFail()) {
                return map;
            }

            List<OrganizationSettingVo> settingVos = settingVosResult.getData();
            Map<Long, OrganizationSettingVo> authOrgMap = settingVos.stream()
                .collect(Collectors.toMap(OrganizationSettingVo::getOrganizationId, s -> s, (v1, v2) -> v1));

            UserConditionBo userConditionBo = new UserConditionBo();
            userConditionBo.setOids(list.stream().map(TeacherVo::getUserOid).collect(Collectors.toList()));
            AjaxResult userListResult = userApi.getUserListByCondition(userConditionBo);
            if (userListResult.isFail()) {
                return map;
            }
            List<UserVo> userVos = JSONArray.parseArray(
                JSONArray.toJSONString(((Map<String, Object>)userListResult.getData()).get("list")), UserVo.class);
            Map<String, UserVo> userVoMap =
                userVos.stream().collect(Collectors.toMap(UserVo::getUserOid, u -> u, (v1, v2) -> v1));

            Map<Long, OrganizationVoExt> orgMap = new HashMap<>();
            List<Long> userOrgIds =
                userVos.stream().map(UserVo::getOrganizationId).distinct().collect(Collectors.toList());
            OrganizationConditionBo orgConditionBo = new OrganizationConditionBo();
            orgConditionBo.setOrganizationIds(userOrgIds);
            AjaxResult userOrgResult = organizationApiService.getOrganizationListByCondition(orgConditionBo);
            if (userOrgResult.isSuccess()) {
                Map<String, Object> userOrgResultMap = (Map<String, Object>)userOrgResult.getData();
                List<OrganizationVoExt> userOrgResultList =
                    JSONArray.parseArray(JSONArray.toJSONString(userOrgResultMap.get("list")), OrganizationVoExt.class);
                for (OrganizationVoExt organizationVoExt : userOrgResultList) {
                    if (organizationVoExt.getProvinceId() != null) {
                        organizationVoExt.setProvinceName(areaApi.getAreaNameFromCache(organizationVoExt.getProvinceId()).getData());
                    }
                    if (organizationVoExt.getCityId() != null) {
                        organizationVoExt.setCityName(areaApi.getAreaNameFromCache(organizationVoExt.getCityId()).getData());
                    }
                    if (organizationVoExt.getProvinceId() != null) {
                        organizationVoExt.setAreaName(areaApi.getAreaNameFromCache(organizationVoExt.getAreaId()).getData());
                    }
                }
                orgMap =
                    userOrgResultList.stream().collect(Collectors.toMap(OrganizationBo::getId, o -> o, (v1, v2) -> v1));
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date nowDay = new Date();
            List<String> dictTypes = Lists.newArrayList("title_type");
            List<DictionaryDataVo> dictionaryDataVos = listValueByTypes(dictTypes);
            Map<String, String> dicDataMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(dictionaryDataVos)) {
                dicDataMap = dictionaryDataVos.stream().collect(Collectors.toMap(DictionaryDataVo::getDictValue, DictionaryDataVo::getDictLabel));
            }
            for (TeacherVoExt teacherVoExt : list) {
                UserVo userVo = userVoMap.get(teacherVoExt.getUserOid());
                if (userVo != null) {
                    OrganizationVoExt organizationVo = orgMap.get(userVo.getOrganizationId());
                    if (organizationVo != null) {
                        teacherVoExt.setOrganizationId(organizationVo.getId());
                        teacherVoExt.setOrganizationName(organizationVo.getName());
                        teacherVoExt.setOrgProvinceId(organizationVo.getProvinceId());
                        teacherVoExt.setOrgProvinceName(organizationVo.getProvinceName());
                        teacherVoExt.setOrgCityId(organizationVo.getCityId());
                        teacherVoExt.setOrgCityName(organizationVo.getCityName());
                        teacherVoExt.setOrgAreaId(organizationVo.getAreaId());
                        teacherVoExt.setOrgAreaName(organizationVo.getAreaName());
                    }

                    OrganizationSettingVo settingVo = authOrgMap.get(userVo.getOrganizationId());
                    if (settingVo != null) {
                        try {
                            if (OrganizationAuthType.AUTH.getCode().equals(Integer.valueOf(settingVo.getRemark()))
                                && sdf.parse(sdf.format(nowDay)).compareTo(sdf.parse(settingVo.getVal())) <= 0) {
                                teacherVoExt.setAuthType(OrganizationAuthType.AUTH.getCode());
                            } else {
                                teacherVoExt.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
                            }
                            teacherVoExt.setAuthEndDay(sdf.parse(settingVo.getVal()));
                        } catch (Exception e) {
                            log.error("set org authType error : ", e);
                        }
                    } else {
                        teacherVoExt.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
                    }
                } else {
                    teacherVoExt.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
                }

                // 字典翻译
                if (teacherVoExt.getTitleType() != null && dicDataMap.containsKey(teacherVoExt.getTitleType().toString())) {
                    teacherVoExt.setTitleName(dicDataMap.get(teacherVoExt.getTitleType().toString()));
                }
            }
        }

        map.put("list", list);

        return map;
    }

    @Override
    public boolean resetPasswordByUserOid(String userOid) {
        UserConditionBo userConditionBo = new UserConditionBo();
        userConditionBo.setOid(userOid);
        userConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult ajaxResult = userApi.getUserListByCondition(userConditionBo);
        if (ajaxResult.isFail()) {
            return false;
        }
        Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
        List<UserVo> userVos = JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), UserVo.class);
        if (CollectionUtils.isEmpty(userVos)) {
            return false;
        }
        UserVo userVo = userVos.get(0);
        final AjaxResult resetResult = accountApi.resetPasswordByUserOid(userOid);
        if (!resetResult.isFail()) {
            userApi.kick(userVo.getAccountName());
            return true;
        }
        return false;
    }

    @Override
    public List<String> listUnregisterMobiles(List<String> allMobiles) {
        AccountConditionBo accountConditionBo = new AccountConditionBo();
        accountConditionBo.setPhoneList(allMobiles);
        accountConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        accountConditionBo.setPageNo(ConstantsInteger.NUM_1);
        accountConditionBo.setPageSize(ConstantsInteger.NUM_10000);
        AjaxResult ajaxResult = accountApi.getAccountListByCondition(accountConditionBo);
        if (ajaxResult.isFail()) {
            return Lists.newArrayList();
        }
        Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
        List<AccountVo> accountVos =
            JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), AccountVo.class);
        if (CollectionUtils.isEmpty(accountVos)) {
            return Lists.newArrayList();
        }
        List<String> existMobiles =
            accountVos.stream().filter(accountVo -> StringUtils.isNotBlank(accountVo.getPhone()))
                .map(AccountVo::getPhone).collect(Collectors.toList());
        return (List<String>)CollectionUtils.subtract(allMobiles, existMobiles);
    }

    @Override
    public String getAreaNameFromCache(Long id) {
        if (id == null) {
            return null;
        }
        AjaxResult<String> areaNameFromCache = areaApi.getAreaNameFromCache(id);
        return areaNameFromCache.getData();
    }

    /**
     * 新增超级管理员信息
     *
     * @param organizationBo
     * @return String
     * <AUTHOR>
     * @date 2023/7/5 16:51
     **/
    private String addSuperAdmin(final OrganizationBo organizationBo) {
        // 从返回对象获取organizationId
        Long organizationId = organizationBo.getId();
        Long type = organizationBo.getType();
        String userOid = null;
        try {
            // 1、添加账号，真实姓名就是学校名称+超级管理员。获取到添加后的account 的oid
            // 2、userBo里面放roleId 和 account的oid。调用addUser
            AccountBo accountBo = new AccountBo();
            accountBo.setPassword(RSAUtil.publicEncrypt(DEFAULT_PASSWORD, PUBLIC_KEY));
            AjaxResult result = accountApi.addAccount(accountBo);// 没有返回账户，
            Object object = result.getData();
            if (null == object) {
                return null;
            }
            String json = JSONUtil.toJsonStr(object);
            final AccountVo accountvo = JSONUtil.toBean(json, AccountVo.class);

            // 新增超级管理员
            UserBo userBo = new UserBo();
            userBo.setOrganizationId(organizationId);
            userBo.setUserIdentityType(UserIdentityType.TEACHER.getValue());
            userBo.setRealName(organizationBo.getName().concat("超级管理员"));
            userBo.setPhone(organizationBo.getContact());
            userBo.setAccountOid(accountvo.getOid());
            List<Long> roleIds = new ArrayList<>();
            roleIds.add(RoleIdentifierType.SCHOOL_SUPER_ADMIN.getRoleId());
            userBo.setRoleIds(roleIds);
            AjaxResult ajaxResultUser = userApi.addUser(userBo);
            if (ajaxResultUser.isFail()) {
                return null;
            }
            userOid = JSONObject.parseObject(JSONObject.toJSONString(ajaxResultUser.getData()), UserVo.class).getOid();
        } catch (Exception e) {
            return null;
        }
        return userOid;
    }

    /**
     * 获取超级管理员
     *
     * @param organizationId
     * @param type
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 14:27
     **/
    private AjaxResult getOrgSuperAdminDetail(Long organizationId, Long type) {
        UserRoleConditionBo userRoleConditionBo = new UserRoleConditionBo();
        userRoleConditionBo.setOrganizationId(organizationId);
        if (type.equals(OrgType.ORG.getValue())) {
            userRoleConditionBo.setRoleId(RoleIdentifierType.BUREAU_SUPER_ADMIN.getRoleId());
        }
        if (type.equals(OrgType.SCHOOL.getValue())) {
            userRoleConditionBo.setRoleId(RoleIdentifierType.SCHOOL_SUPER_ADMIN.getRoleId());
        }
        userRoleConditionBo.setPageNo(1);
        userRoleConditionBo.setPageSize(100);
        AjaxResult<Map<String, Object>> ajaxResult = userRoleApi.getUserRoleListByCondition(userRoleConditionBo);
        Map<String, Object> result = ajaxResult.getData();
        List<Map> list = (List)result.get("list");
        String userOid = "";
        if (CollectionUtil.isNotEmpty(list)) {
            userOid = (String)list.get(0).get("userOid");
        }
        if (com.light.core.utils.StringUtils.isBlank(userOid)) {
            return AjaxResult.fail("userOid为空");
        }

        AjaxResult ajaxResultAccount = accountApi.getAccountByUserOid(userOid);
        if (ajaxResultAccount.isFail()) {
            return AjaxResult.success("");
        }
        List<AccountVo> accountVoList =
            JSONObject.parseArray(JSONObject.toJSONString(ajaxResultAccount.getData()), AccountVo.class);
        if (CollectionUtil.isEmpty(accountVoList)) {
            return AjaxResult.success("");
        }
        return AjaxResult.success(accountVoList.get(0));
    }

    /**
     * 获取新时代文化校园入住学校
     *
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2023/7/21 9:26
     **/
    private List<Long> getAllYanxOrgIdList() {
        OrganizationSettingConditionBo orgSettingConditionBo = new OrganizationSettingConditionBo();
        orgSettingConditionBo.setK(ConstServiceName.PLATFORM_KEY);
        orgSettingConditionBo.setEqVal(ConstServiceName.PLATFORM_NAME);
        orgSettingConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult<List<OrganizationSettingVo>> settingResult =
            organizationSettingApi.getOrganizationSettingListByCondition(orgSettingConditionBo);
        if (settingResult.isFail()) {
            return null;
        }
        return settingResult.getData().stream().map(OrganizationSettingVo::getOrganizationId)
            .collect(Collectors.toList());
    }

    @Override
    public Integer getOrgAuthType(Long organizationId) {
        // 获取用户组织认证状态
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date nowDay = new Date();
        OrganizationSettingConditionBo conditionBo = new OrganizationSettingConditionBo();
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        conditionBo.setOrganizationId(organizationId);
        conditionBo.setK(ConstOrgAuth.ORG_AUTH);
        OrganizationSettingVo settingVo = this.getOrganizationSetting(conditionBo);
        Integer authType = OrganizationAuthType.UN_AUTH.getCode();
        try {
            if (settingVo != null && OrganizationAuthType.AUTH.getCode().equals(Integer.valueOf(settingVo.getRemark()))
                && sdf.parse(sdf.format(nowDay)).compareTo(sdf.parse(settingVo.getVal())) <= 0) {
                authType = OrganizationAuthType.AUTH.getCode();
            }
        } catch (Exception e) {
            log.error("orgAuthType error : ", e);
        }
        return authType;
    }

    @Override
    public AjaxResult addUserTransfer(UserTransferBo userTransferBo) {
        return userTransferApi.addUserTransfer(userTransferBo);
    }

    @Override
    public List<AdminVo> getAdminListByCondition(AdminConditionBo conditionBo) {
        conditionBo.setNeRoleIds(Lists.newArrayList(RoleIdentifierType.OPERATION_ADMIN.getRoleId(),
            RoleIdentifierType.OPERATION_CRM.getRoleId()));
        AjaxResult<List<AdminVo>> listByCondition = adminApi.getListByCondition(conditionBo);
        if (listByCondition.isSuccess() && CollectionUtils.isNotEmpty(listByCondition.getData())) {
            List<AdminVo> data = listByCondition.getData();
            setAdminRoleNames(data);
            return data;
        }
        return Lists.newArrayList();
    }

    @Override
    public AjaxResult getAdminPageListByCondition(AdminConditionBo conditionBo) {
        // 排除新时代运营和新时代代理商之外的角色
        RoleConditionBo roleConditionBo = new RoleConditionBo();
        roleConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult roleResult = roleApi.getRoleListByCondition(roleConditionBo);
        List<Long> neRoleIds = Lists.newArrayList();
        if (roleResult.isSuccess() && roleResult.getData() != null) {
            Map roleResultMap = JSON.parseObject(JSON.toJSONString(roleResult.getData()), Map.class);
            if (roleResultMap.containsKey("list") && roleResultMap.get("list") != null) {
                List<RoleVo> roleVos = JSONArray.parseArray(JSONArray.toJSONString(roleResultMap.get("list")), RoleVo.class);
                roleVos = roleVos.stream()
                        .filter(x -> !RoleIdentifierType.YANX_OPERATION_ADMIN.getRoleId().equals(x.getId())
                                && !RoleIdentifierType.YANX_CM_ADMIN.getRoleId().equals(x.getId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(roleVos)) {
                    neRoleIds.addAll(roleVos.stream().map(RoleVo::getId).collect(Collectors.toList()));
                }
            }
        }
        conditionBo.setNeRoleIds(neRoleIds);
        AjaxResult adminListByCondition = adminApi.getAdminListByCondition(conditionBo);
        if (adminListByCondition.isFail()) {
            return adminListByCondition;
        }
        Map<String, Object> dataMap = (Map<String, Object>)adminListByCondition.getData();
        List<AdminVo> adminVos = JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), AdminVo.class);
        if (CollectionUtils.isEmpty(adminVos)) {
            return adminListByCondition;
        }

        setAdminRoleNames(adminVos);
        Map<String, Object> map = new HashMap<>();
        map.put("total", dataMap.get("total"));
        map.put("list", adminVos);
        return AjaxResult.success(map);
    }

    @Override
    public AjaxResult addAdmin(AdminBo adminBo) {
        return adminApi.addAdmin(adminBo);
    }

    @Override
    public AjaxResult updateAdmin(AdminBo adminBo) {
        AjaxResult detail = adminApi.getDetail(adminBo.getId());
        if (detail.isFail() || detail.getData() == null) {
            return detail;
        }
        Map<String, Object> dataMap = (Map<String, Object>)detail.getData();
        AdminVo adminVo = JSONObject.parseObject(JSONObject.toJSONString(dataMap.get("adminVo")), AdminVo.class);
        AdminBo adminBoToUpdate = new AdminBo();
        BeanUtils.copyProperties(adminVo, adminBoToUpdate);
        // 底层不支持，因此这里先查询再更新指定字段
        adminBoToUpdate.setAdminName(adminBo.getAdminName());
        if (adminBo.getRoleId() != null) {
            adminBoToUpdate.setRoleId(adminBo.getRoleId());
        }
        adminBoToUpdate.setPhone(adminBo.getPhone());
        return adminApi.updateAdmin(adminBoToUpdate);
    }

    @Override
    public AjaxResult delAdmin(Long adminId) {
        return adminApi.delete(adminId);
    }

    @Override
    public AjaxResult<AdminVo> getAdminDetail(Long adminId) {
        AjaxResult detail = adminApi.getDetail(adminId);
        if (detail.isFail() || detail.getData() == null) {
            return detail;
        }
        Map<String, Object> dataMap = (Map<String, Object>)detail.getData();
        AdminVo adminVo = JSONObject.parseObject(JSONObject.toJSONString(dataMap.get("adminVo")), AdminVo.class);
        List<AdminVo> adminVos = Lists.newArrayList(adminVo);
        setAdminRoleNames(adminVos);
        return AjaxResult.success(adminVo);
    }

    @Override
    public AjaxResult enableAdmin(Long adminId, Integer status) {
        return adminApi.lock(adminId, status);
    }

    @Override
    public AjaxResult resetAdminPassword(Long adminId, String initPwd) {
        if (StringUtils.isBlank(initPwd)) {
            initPwd = DEFAULT_PASSWORD;
        }
        return adminApi.resetPasswordWithPwd(adminId, initPwd);
    }

    /**
     * 设置管理员角色名称
     * 
     * @param adminVos
     */
    private void setAdminRoleNames(List<AdminVo> adminVos) {
        if (CollectionUtils.isEmpty(adminVos)) {
            return;
        }
        for (AdminVo adminVo : adminVos) {
            AjaxResult<List<RoleVo>> byAdminOid = roleApi.getByAdminOid(adminVo.getOid());
            if (byAdminOid.isFail() || CollectionUtils.isEmpty(byAdminOid.getData())) {
                continue;
            }
            List<RoleVo> roleVos = byAdminOid.getData();
            List<String> roleNames = roleVos.stream().map(RoleVo::getName).distinct().collect(Collectors.toList());
            adminVo.setRoleNames(roleNames);
        }
    }

    @Override
    public AjaxResult addUserAndGeneratorAccount(UserBo userBo) {
        try {
            AccountBo accountBo = new AccountBo();
            accountBo.setPhone(userBo.getPhone());
            accountBo.setSex(userBo.getSex());
            accountBo.setRealName(userBo.getRealName());
            accountBo.setPassword(RSAUtil.publicEncrypt(DEFAULT_PASSWORD, PUBLIC_KEY));
            AjaxResult accountAjaxResult = accountApi.addAccount(accountBo);
            if (accountAjaxResult.isFail()) {
                return accountAjaxResult;
            }
            AccountVo accountVo =
                    JSONObject.parseObject(JSONObject.toJSONString(accountAjaxResult.getData()), AccountVo.class);
            userBo.setAccountOid(accountVo.getOid());
            userBo.setUserIdentityTypes(Stream.of(UserIdentityType.TEACHER.getValue()).collect(Collectors.toList()));
            return userApi.addUser(userBo);
        } catch (Exception e) {
            log.error("addUser error:" + e);
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult updateExpertUser(UserBo userBo) {
        AjaxResult userResult = userApi.getDetailByOid(userBo.getOid());
        if (userResult.isFail() || userResult.getData() == null) {
            return AjaxResult.fail();
        }
        UserDetailVo userVo = JSONObject.parseObject(JSONObject.toJSONString(userResult.getData()), UserDetailVo.class);
        userBo.setUserId(userVo.getId());
        // 修改手机号
        if (!userVo.getPhone().equals(userBo.getPhone())) {
            AjaxResult updatePhoneResult = updatePhoneByUserOid(userBo);
            if (updatePhoneResult.isFail()) {
                return AjaxResult.fail("手机号已存在");
            }
        }
        AjaxResult updateUserResult = userApi.updateUser(userBo);
        // 获取用户角色列表
        AjaxResult<List<UserRoleVo>> userRoleListResult = userRoleApi.getListByUserOid(userBo.getOid());
        // 判断是否修改角色
        // 新时代前台案例持有者角色
        List<Long> receptionRoleIds = Arrays.stream(ExpertRoleEnum.values()).map(ExpertRoleEnum::getRoleId)
                .collect(Collectors.toList());
        if (userRoleListResult.isSuccess()) {
            List<UserRoleVo> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(userRoleListResult.getData())) {
                list = userRoleListResult.getData().stream()
                        .filter(x -> receptionRoleIds.contains(x.getRoleId())).collect(Collectors.toList());
            }
            List<Long> userRoleIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                userRoleIds = list.stream().map(UserRoleVo::getRoleId).collect(Collectors.toList());
            }
            // 判断是否新增角色
            if (CollectionUtils.isNotEmpty(userBo.getRoleIds())) {
                Long organizationId = 0L;
                if (StringUtils.isNotBlank(userBo.getOrganizationIds())) {
                    String[] organizationIds = userBo.getOrganizationIds().split(",");
                    organizationId = Long.parseLong(organizationIds[ConstantsInteger.NUM_0]);
                }
                for (Long roleId : userBo.getRoleIds()) {
                    if (userRoleIds.contains(roleId)) {
                        continue;
                    }
                    UserRoleBo userRoleBo = new UserRoleBo();
                    userRoleBo.setRoleId(roleId);
                    userRoleBo.setUserOid(userBo.getOid());
                    userRoleBo.setOrganizationId(organizationId);
                    userRoleApi.addUserRole(userRoleBo);
                }
            }
            // 判断移除
            if (CollectionUtil.isNotEmpty(list)) {
                if (CollectionUtils.isNotEmpty(userBo.getRoleIds())) {
                    list = list.stream().filter(ur -> !userBo.getRoleIds().contains(ur.getRoleId())).collect(Collectors.toList());
                }
                for (UserRoleVo userRoleVo : list) {
                    userRoleApi.delete(userRoleVo.getId());
                }
            }
        }
        return updateUserResult;
    }

    @Override
    public AjaxResult getUserList(UserConditionBoExt conditionBo) {
        List<Long> orgIds = Lists.newArrayList();
        Map<String, Object> defaultResultMap = Maps.newHashMap();
        defaultResultMap.put("list", Lists.newArrayList());
        defaultResultMap.put("total", ConstantsInteger.NUM_0);
        if (conditionBo.getOrganizationId() != null) {
            List<Long> yanxOrgIds = getAllYanxOrgIdList();
            if (yanxOrgIds.contains(conditionBo.getOrganizationId())) {
                orgIds.add(conditionBo.getOrganizationId());
            }
        } else if (conditionBo.getOnlyQueryExpert()) {
            OrganizationExtConditionBo organizationConditionBo = new OrganizationExtConditionBo();
            // 专家组织
            organizationConditionBo.setType(3L);
            organizationConditionBo.setPageNo(SystemConstants.NO_PAGE);
            organizationConditionBo.setName(conditionBo.getOrganizationName());
            Map<String, Object> organizationMap = getOrganizationListByCondition(organizationConditionBo);
            if (organizationMap == null || !organizationMap.containsKey("list")) {
                return AjaxResult.success(defaultResultMap);
            }
            List<OrganizationVoExt> organizationVos = JSONArray.parseArray(JSONArray.toJSONString(organizationMap.get("list")), OrganizationVoExt.class);
            if (CollectionUtils.isEmpty(organizationVos)) {
                return AjaxResult.success(defaultResultMap);
            }
            orgIds = organizationVos.stream().map(OrganizationVoExt::getId).collect(Collectors.toList());
        } else {
            orgIds = getAllYanxOrgIdList();
        }

        if (CollectionUtils.isEmpty(orgIds)) {
            return AjaxResult.success(defaultResultMap);
        }
        List<Long> expertRoleIds = Arrays.stream(ExpertRoleEnum.values()).map(ExpertRoleEnum::getRoleId)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(conditionBo.getRoleIds())) {
            conditionBo.setRoleIds(expertRoleIds);
        }
        conditionBo.setOrganizationIds(orgIds);
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        AjaxResult userListResult = userApi.getUserListByCondition(conditionBo);
        if (userListResult.isFail() || userListResult.getData() == null) {
            return userListResult;
        }
        Map<String, Object> userVosMap = JSON.parseObject(JSON.toJSONString(userListResult.getData()), Map.class);
        List<UserVoExt> userVoExts = JSONArray.parseArray(userVosMap.get("list").toString(), UserVoExt.class);
        List<String> userOids = userVoExts.stream().map(UserVoExt::getUserOid).collect(Collectors.toList());
        UserRoleConditionBo userRoleConditionBo = new UserRoleConditionBo();
        userRoleConditionBo.setUserOids(userOids);
        userRoleConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult userRoleVoResult = userRoleApi.getUserRoleListByCondition(userRoleConditionBo);
        Map<String, List<UserRoleVoExt>> userRolesMap = Maps.newHashMap();
        if (userRoleVoResult.isSuccess() && userRoleVoResult.getData() != null) {
            Map<String, Object> userRoleResultMap = JSON.parseObject(JSON.toJSONString(userRoleVoResult.getData()), Map.class);
            List<UserRoleVoExt> userRoleList = JSONArray.parseArray(userRoleResultMap.get("list").toString(), UserRoleVoExt.class);
            userRolesMap = userRoleList.stream()
                    .filter(x -> expertRoleIds.contains(x.getRoleId()))
                    .map(x -> {
                        UserRoleVoExt voExt = new UserRoleVoExt();
                        BeanUtils.copyProperties(x, voExt);
                        ExpertRoleEnum roleEnum = ExpertRoleEnum.getByRoleId(x.getRoleId());
                        if (roleEnum != null) {
                            voExt.setName(roleEnum.getName());
                            voExt.setCode(roleEnum.getCode());
                        }
                        return voExt;
                    }).collect(Collectors.groupingBy(UserRoleVoExt::getUserOid));
        }

        List<OrganizationVo> organizationVos = getOrganizationVoList(orgIds);
        Map<Long, OrganizationVo> orgMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(organizationVos)) {
            orgMap = organizationVos.stream().collect(Collectors.toMap(OrganizationVo::getId, o -> o, (v1, v2) -> v1));
        }

        for (UserVoExt userVoExt : userVoExts) {
            if (userRolesMap.containsKey(userVoExt.getUserOid())) {
                userVoExt.setUserRoleList(userRolesMap.get(userVoExt.getUserOid()));
            }
            if (orgMap.containsKey(Long.parseLong(userVoExt.getOrganizationIds()))) {
                OrganizationVo organizationVo = orgMap.get(Long.parseLong(userVoExt.getOrganizationIds()));
                userVoExt.setOrganizationNames(organizationVo.getName());
            }
        }

        userVosMap.put("list", userVoExts);
        return AjaxResult.success(userVosMap);
    }

    @Override
    public AjaxResult getUserDetail(String userOid) {
        AjaxResult ajaxResult = userApi.getVoByUserOid(userOid);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return ajaxResult;
        }
        UserVoExt userVo = JSONObject.parseObject(JSONObject.toJSONString(ajaxResult.getData()), UserVoExt.class);
        // 查询用户账号，返回账号信息
        AjaxResult<List<AccountVo>> userAccountResult = accountApi.getAccountListByUserOid(userOid);
        if (userAccountResult.isSuccess() && CollectionUtils.isNotEmpty(userAccountResult.getData())) {
            AccountVo accountVo = userAccountResult.getData().get(ConstantsInteger.NUM_0);
            userVo.setAccountId(accountVo.getId());
            userVo.setAccountName(accountVo.getAccountName());
            userVo.setAccountOid(accountVo.getOid());
            userVo.setAccountPhone(accountVo.getPhone());
        }
        List<String> dictTypes = Lists.newArrayList("title_type");
        List<DictionaryDataVo> dictionaryDataVos = listValueByTypes(dictTypes);
        Map<String, String> dicDataMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(dictionaryDataVos)) {
            dicDataMap = dictionaryDataVos.stream().collect(Collectors.toMap(DictionaryDataVo::getDictValue, DictionaryDataVo::getDictLabel));
        }
        // 查询用户角色
        AjaxResult<List<UserRoleVo>> userRoleVoResult = userRoleApi.getListByUserOid(userOid);
        if (userRoleVoResult.isSuccess() && CollectionUtils.isNotEmpty(userRoleVoResult.getData())) {
            List<Long> roleIds = Arrays.stream(ExpertRoleEnum.values()).map(ExpertRoleEnum::getRoleId)
                    .collect(Collectors.toList());
            roleIds.add(SCHOOL_TEACHER);
            roleIds.add(ReceptionRoleEnum.CASE_OWNER.getRoleId());
            AtomicReference<Boolean> isTeacher = new AtomicReference<>(false);
            List<UserRoleVoExt> userRoleList = userRoleVoResult.getData().stream()
                    .filter(x -> roleIds.contains(x.getRoleId()))
                    .map(x -> {
                        if (ReceptionRoleEnum.SCHOOL_TEACHER.getRoleId().equals(x.getRoleId())) {
                            isTeacher.set(true);
                        }
                        UserRoleVoExt voExt = new UserRoleVoExt();
                        org.springframework.beans.BeanUtils.copyProperties(x, voExt);
                        ExpertRoleEnum roleEnum = ExpertRoleEnum.getByRoleId(x.getRoleId());
                        if (roleEnum != null) {
                            voExt.setName(roleEnum.getName());
                            voExt.setCode(roleEnum.getCode());
                        }
                        ReceptionRoleEnum receptionRoleEnum = ReceptionRoleEnum.getByRoleId(x.getRoleId());
                        if (receptionRoleEnum != null) {
                            voExt.setName(receptionRoleEnum.getName());
                            voExt.setCode(receptionRoleEnum.getCode());
                        }
                        return voExt;
                    }).collect(Collectors.toList());
            userVo.setUserRoleList(userRoleList);

            // 查询用户组织
            AjaxResult<List<UserOrgVo>> userOrgResult = userOrgApi.getByUserOid(userVo.getUserOid());
            if (userOrgResult.isSuccess() && CollectionUtils.isNotEmpty(userOrgResult.getData())) {
                userVo.setOrganizationIds(userOrgResult.getData().get(ConstantsInteger.NUM_0).getOrganizationId().toString());
            }

            Map<Long, OrganizationVo> orgMap = Maps.newHashMap();
            if (StringUtils.isNotBlank(userVo.getOrganizationIds())) {
                Long orgId = Long.parseLong(userVo.getOrganizationIds());
                List<OrganizationVo> organizationVos = getOrganizationVoList(Stream.of(orgId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(organizationVos)) {
                    orgMap = organizationVos.stream().collect(Collectors.toMap(OrganizationVo::getId, o -> o, (v1, v2) -> v1));
                }
            }

            if (isTeacher.get()) {
                // 设置职称信息
                AjaxResult teacherResult = teacherApi.getByUserOid(userOid);
                if (teacherResult.isFail() || teacherResult.getData() == null) {
                    return null;
                }
                TeacherVo teacherVo = JSONObject.parseObject(JSONObject.toJSONString(teacherResult.getData()), TeacherVo.class);
                userVo.setTitleType(teacherVo.getTitleType());
                if (userVo.getTitleType() != null && dicDataMap.containsKey(userVo.getTitleType().toString())) {
                    userVo.setTitleName(dicDataMap.get(userVo.getTitleType().toString()));
                }
                if (orgMap.containsKey(Long.parseLong(userVo.getOrganizationIds()))) {
                    OrganizationVo organizationVo = orgMap.get(Long.parseLong(userVo.getOrganizationIds()));
                    userVo.setOrganizationNames(organizationVo.getName());
                    userVo.setOrgProvinceIds(organizationVo.getProvinceId() == null ? "" : organizationVo.getProvinceId().toString());
                    userVo.setOrgCityIds(organizationVo.getCityId() == null ? "" : organizationVo.getCityId().toString());
                    userVo.setOrgAreaIds(organizationVo.getAreaId() == null ? "" : organizationVo.getAreaId().toString());
                }
                // 教师回显组织省市区信息
                if (StringUtils.isNotBlank(userVo.getOrgProvinceIds())) {
                    userVo.setOrgProvinceNames(areaApi.getAreaNameFromCache(Long.parseLong(userVo.getOrgProvinceIds())).getData());
                }
                if (StringUtils.isNotBlank(userVo.getOrgCityIds())) {
                    userVo.setOrgCityNames(areaApi.getAreaNameFromCache(Long.parseLong(userVo.getOrgCityIds())).getData());
                }
                if (StringUtils.isNotBlank(userVo.getOrgAreaIds())) {
                    userVo.setOrgAreaNames(areaApi.getAreaNameFromCache(Long.parseLong(userVo.getOrgAreaIds())).getData());
                }
            }
        }
        return AjaxResult.success(userVo);
    }

    @Override
    public AjaxResult delUser(String userOid) {
        AjaxResult ajaxResult = userApi.getVoByUserOid(userOid);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return ajaxResult;
        }
        UserVoExt userVo = JSONObject.parseObject(JSONObject.toJSONString(ajaxResult.getData()), UserVoExt.class);
        AjaxResult delUserResult = userApi.delete(userVo.getId());
        if (delUserResult.isSuccess()) {
            // 同步删除账号
            accountApi.delete(userVo.getAccountId());
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult delOrganization(Long organizationId) {
        // 目前只支持删除学术组织
        OrganizationExtConditionBo organizationConditionBo = new OrganizationExtConditionBo();
        // 专家组织
        organizationConditionBo.setType(3L);
        organizationConditionBo.setPageNo(SystemConstants.NO_PAGE);
        Map<String, Object> organizationMap = getOrganizationListByCondition(organizationConditionBo);
        if (organizationMap == null || !organizationMap.containsKey("list")) {
            return AjaxResult.fail();
        }
        List<OrganizationVoExt> organizationVos = JSONArray.parseArray(JSONArray.toJSONString(organizationMap.get("list")), OrganizationVoExt.class);
        if (CollectionUtils.isEmpty(organizationVos)) {
            return AjaxResult.fail();
        }
        List<Long> orgIds = organizationVos.stream().map(OrganizationVoExt::getId).collect(Collectors.toList());
        if (!orgIds.contains(organizationId)) {
            return AjaxResult.fail();
        }
        AjaxResult delOrgResult = organizationApiService.delete(organizationId);
        if (delOrgResult.isSuccess()) {
            // 异步删除组织下用户
            executor.submit(() -> {
                UserConditionBoExt conditionBoExt = new UserConditionBoExt();
                conditionBoExt.setOrganizationId(organizationId);
                conditionBoExt.setPageNo(SystemConstants.NO_PAGE);
                AjaxResult userListResult = getUserList(conditionBoExt);
                if (userListResult.isSuccess() && userListResult.getData() != null) {
                    Map userListResultMap = JSON.parseObject(JSON.toJSONString(userListResult.getData()), Map.class);
                    if (userListResultMap.containsKey("list") && userListResultMap.get("list") != null) {
                        List<UserVo> userVos = JSONArray.parseArray(JSONArray.toJSONString(userListResultMap.get("list")), UserVo.class);
                        for (UserVo userVo : userVos) {
                            userApi.delete(userVo.getUserId());
                            accountApi.delete(userVo.getAccountId());
                        }
                    }
                }
            });
        }
        return delOrgResult;
    }

    @Override
    public AjaxResult updatePhoneByUserOid(UserBo userBo) {
        userBo.setIsSyncAccountPhone(StatusEnum.YES.getCode());
        return userApi.updatePhoneByOid(userBo);
    }

    @Override
    public List<UserVoExt> queryByUserOidList(List<String> userOidList) {

        // 获取用户信息
        AjaxResult<List<UserVo>> userOidAjax = this.userApi.getByOidList(userOidList);
        if(userOidAjax.isFail()){
            log.error("【用户服务】 调用异常， 用户 OID 集合 ：{}", JSON.toJSON(userOidList));
            return Collections.emptyList();
        }
        List<UserVo> data = userOidAjax.getData();
        if (data == null) {
            log.warn("【用户服务】 为获取到用户数据， 用户 OID 集合 ：{}", JSON.toJSON(userOidList));
            return Collections.emptyList();
        }
        // 获取角色
        Map<String, List<UserRoleVoExt>> userRoleMap = this.queryUserRoleMapByUserOidList(userOidList);

        return data.stream().map(x -> {
            UserVoExt bean = BeanUtil.toBean(x, UserVoExt.class);
            List<UserOrgVo> userOrgList = x.getUserOrgList();
            if(CollUtil.isNotEmpty(userOrgList)){
                bean.setOrganizationId(userOrgList.get(0).getId());
                bean.setOrganizationNames(userOrgList.get(0).getName());
            }
            List<UserRoleVoExt> userRoleVoExts = userRoleMap.get(bean.getOid());
            bean.setUserRoleList(userRoleVoExts);
            return bean;
        }).collect(Collectors.toList());
    }


    /**
     *  根据用户 OID 获取角色 map ： key-> user oid  Value: user role vo list
     * @param userOidList the user oid list 用户 OID 集合
     * @return {@link Map }<{@link String }, {@link List }<{@link UserRoleVoExt }>>
     */
    @Override
    public Map<String, List<UserRoleVoExt>> queryUserRoleMapByUserOidList(List<String> userOidList) {
        List<UserRoleVoExt> userRoleVoExts = this.queryUserRoleListByUserOidList(userOidList);
        if(userRoleVoExts == null){
            return Maps.newHashMap();
        }
        return userRoleVoExts.stream().collect(Collectors.groupingBy(UserRoleVo::getUserOid));
    }

    @Override
    public Map<String, UserVoExt> queryMapByUserOidList(List<String> userOidList) {
        List<UserVoExt> userVoExts = this.queryByUserOidList(userOidList);
        return userVoExts.stream().collect(Collectors.toMap(UserVo::getOid, x-> x));
    }

    /**
     *  根据用户 OID 集合获取用户角色
     * @param userOidList the user oid list 用户 OID 集合
     * @return {@link List }<{@link UserRoleVoExt }>
     */
    private List<UserRoleVoExt> queryUserRoleListByUserOidList(List<String> userOidList) {

        // 查询角色数据信息
        UserRoleConditionBo userRoleConditionBo = new UserRoleConditionBo();
        userRoleConditionBo.setUserOids(userOidList);
        userRoleConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult userRoleVoResult = userRoleApi.getUserRoleListByCondition(userRoleConditionBo);
        if(userRoleVoResult.isFail()){
            log.error("【用户角色】 用户角色调用失败: {}", JSON.toJSON(userOidList));
            return Collections.emptyList();
        }
        Object data = userRoleVoResult.getData();
        if(data == null){
            log.warn("【用户角色】 用户角色未获取到数据: {}", JSON.toJSON(userOidList));
            return Collections.emptyList();
        }

        //
        Map<String, Object> userRoleResultMap = JSON.parseObject(JSON.toJSONString(data), Map.class);
        List<UserRoleVoExt> userRoleVoExts = JSONArray.parseArray(userRoleResultMap.get("list").toString(), UserRoleVoExt.class);

        // 处理角色信息
        List<Long> roleIds = Arrays.stream(ExpertRoleEnum.values()).map(ExpertRoleEnum::getRoleId)
                .collect(Collectors.toList());
        roleIds.add(SCHOOL_TEACHER);
        roleIds.add(ReceptionRoleEnum.CASE_OWNER.getRoleId());
        return userRoleVoExts.stream()
                .filter(x -> roleIds.contains(x.getRoleId()))
                .map(x -> {
                    UserRoleVoExt voExt = new UserRoleVoExt();
                    org.springframework.beans.BeanUtils.copyProperties(x, voExt);
                    ExpertRoleEnum roleEnum = ExpertRoleEnum.getByRoleId(x.getRoleId());
                    if (roleEnum != null) {
                        voExt.setName(roleEnum.getName());
                        voExt.setCode(roleEnum.getCode());
                    }
                    ReceptionRoleEnum receptionRoleEnum = ReceptionRoleEnum.getByRoleId(x.getRoleId());
                    if (receptionRoleEnum != null) {
                        voExt.setName(receptionRoleEnum.getName());
                        voExt.setCode(receptionRoleEnum.getCode());
                    }
                    return voExt;
                }).collect(Collectors.toList());
    }

    @Override
    public AjaxResult addBatchTeacher(List<TeacherBo> teacherBos) {
        return teacherApi.addBatchTeacher(teacherBos);
    }

    @Override
    public AjaxResult addUserAndGeneratorAccountBatch(List<UserBo> userBos) {
        for (UserBo userBo : userBos) {
            try {
                AccountBo accountBo = new AccountBo();
                accountBo.setPhone(userBo.getPhone());
                accountBo.setSex(userBo.getSex());
                accountBo.setRealName(userBo.getRealName());
                accountBo.setPassword(RSAUtil.publicEncrypt(DEFAULT_PASSWORD, PUBLIC_KEY));
                AjaxResult accountAjaxResult = accountApi.addAccount(accountBo);
                if (accountAjaxResult.isFail()) {
                    if (accountAjaxResult.getMsg().contains("手机号")) {
                        return AjaxResult.fail("[" + userBo.getPhone() + "]手机号已存在");
                    }
                    return accountAjaxResult;
                }
                AccountVo accountVo =
                        JSONObject.parseObject(JSONObject.toJSONString(accountAjaxResult.getData()), AccountVo.class);
                userBo.setAccountOid(accountVo.getOid());
                userBo.setUserIdentityTypes(Stream.of(UserIdentityType.TEACHER.getValue()).collect(Collectors.toList()));
                userApi.addUser(userBo);
            } catch (Exception e) {
                log.error("addUser error:" + e);
            }
        }
        return AjaxResult.success();
    }
}
