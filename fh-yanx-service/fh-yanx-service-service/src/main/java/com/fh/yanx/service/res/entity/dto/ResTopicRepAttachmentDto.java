package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 结题答辩附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_topic_rep_attachment")
public class ResTopicRepAttachmentDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 课题id
     */
    @TableField("topic_id")
    private Long topicId;

    /**
     * 研究附件名称
     */
    @TableField("rep_name")
    private String repName;

    /**
     * 论文摘要
     */
    @TableField("rep_desc")
    private String repDesc;

    /**
     * 附件文件原始名称-带后缀
     */
    @TableField("rep_file_name_ori")
    private String repFileNameOri;

    /**
     * 附件文件名称-不带后缀
     */
    @TableField("rep_file_name")
    private String repFileName;

    /**
     * 附件文件地址
     */
    @TableField("rep_file_url")
    private String repFileUrl;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 附件类型，1：结题论文，2：答辩材料
     */
    @TableField("rep_type")
    private Integer repType;

    /**
     * 论文作者，多个用顿号
     */
    @TableField("rep_author_name")
    private String repAuthorName;

    /**
     * 论文所属学校
     */
    @TableField("rep_organization_name")
    private String repOrganizationName;

    /**
     * 发表时间
     */
    @TableField("rep_pub_date")
    private Date repPubDate;

    /**
     * 论文：1校内自建，2校外导入
     */
    @TableField("rs_source")
    private Integer rsSource;

    /**
     * 论文第三方来源类型：1有方，2汇景
     */
    @TableField("rs_third_type")
    private Integer rsThirdType;

    /**
     * 首页展示：1展示
     */
    @TableField("home_show")
    private Integer homeShow;


    /**
     * 文件oid
     */
    @TableField("rep_file_oid")
    private String repFileOid;
}
