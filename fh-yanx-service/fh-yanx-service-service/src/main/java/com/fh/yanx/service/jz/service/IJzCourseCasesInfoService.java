package com.fh.yanx.service.jz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesInfoDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 金中-校本课程案例详细信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface IJzCourseCasesInfoService extends IService<JzCourseCasesInfoDto> {

    List<JzCourseCasesInfoVo> getJzCourseCasesInfoListByCondition(JzCourseCasesInfoConditionBo condition);

	AjaxResult addJzCourseCasesInfo(JzCourseCasesInfoBo jzCourseCasesInfoBo);

	AjaxResult updateJzCourseCasesInfo(JzCourseCasesInfoBo jzCourseCasesInfoBo);

	JzCourseCasesInfoVo getJzCourseCasesInfoByCondition(JzCourseCasesInfoConditionBo condition);

	/**
	 * 获取详情
	 *
	 * @param casesId the cases id
	 * @return p course cases info vo
	 * <AUTHOR>
	 * @date 2023 -08-14 14:17:01
	 */
	JzCourseCasesInfoVo infoDetail(Long casesId);
}

