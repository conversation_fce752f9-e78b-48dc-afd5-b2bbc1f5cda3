package com.fh.yanx.service.activ.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoIntroduceDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 新时代文化校园活动介绍表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
public interface IActivInfoIntroduceService extends IService<ActivInfoIntroduceDto> {

    List<ActivInfoIntroduceVo> getActivInfoIntroduceListByCondition(ActivInfoIntroduceConditionBo condition);

	AjaxResult addActivInfoIntroduce(ActivInfoIntroduceBo activInfoIntroduceBo);

	AjaxResult updateActivInfoIntroduce(ActivInfoIntroduceBo activInfoIntroduceBo);

	ActivInfoIntroduceVo getActivInfoIntroduceByCondition(ActivInfoIntroduceConditionBo condition);

	boolean deleteAndAddActivIntroduces(ActivInfoBo activInfoBo);

}

