package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课题审批意见表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_topic_approval")
public class ResTopicApprovalDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "topic_approval_id", type = IdType.AUTO)
	private Long topicApprovalId;

	/**
	 * 课题id
	 */
	@TableField("topic_id")
	private Long topicId;

	/**
	 * 审批老师的userOid
	 */
	@TableField("verify_teacher")
	private String verifyTeacher;

	/**
	 * 课题名称建议
	 */
	@TableField("topic_name_sug")
	private String topicNameSug;

	/**
	 * 课题类别建议
	 */
	@TableField("topic_type_sug")
	private String topicTypeSug;

	/**
	 * 关联学科建议
	 */
	@TableField("rel_subject_sug")
	private String relSubjectSug;

	/**
	 * 课题界定建议
	 */
	@TableField("topic_desc_sug")
	private String topicDescSug;

	/**
	 * 课题背景建议
	 */
	@TableField("topic_back_sug")
	private String topicBackSug;

	/**
	 * 课题目的建议
	 */
	@TableField("topic_goal_sug")
	private String topicGoalSug;

	/**
	 * 研究内容建议
	 */
	@TableField("topic_content_sug")
	private String topicContentSug;

	/**
	 * 研究方法建议
	 */
	@TableField("topic_method_sug")
	private String topicMethodSug;

	/**
	 * 研究条件建议
	 */
	@TableField("topic_condition_sug")
	private String topicConditionSug;

	/**
	 * 研究计划建议
	 */
	@TableField("topic_plan_sug")
	private String topicPlanSug;

	/**
	 * 研究预期成果建议
	 */
	@TableField("topic_expected_sug")
	private String topicExpectedSug;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
