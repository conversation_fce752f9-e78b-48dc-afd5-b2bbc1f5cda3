package com.fh.yanx.service.res.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.res.api.ResTopicRepAttachmentApi;
import com.fh.yanx.service.res.entity.dto.ResTopicRepAttachmentDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicRepAttachmentVo;
import com.fh.yanx.service.res.service.IResTopicRepAttachmentService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 结题答辩附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
public class ResTopicRepAttachmentController implements ResTopicRepAttachmentApi {

    @Autowired
    private IResTopicRepAttachmentService resTopicRepAttachmentService;

    /**
     * 查询结题答辩附件表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<PageInfo<ResTopicRepAttachmentVo>> getResTopicRepAttachmentPageListByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicRepAttachmentVo> pageInfo = new PageInfo<>(resTopicRepAttachmentService.getResTopicRepAttachmentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询结题答辩附件表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<List<ResTopicRepAttachmentVo>> getResTopicRepAttachmentListByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition) {
        List<ResTopicRepAttachmentVo> list = resTopicRepAttachmentService.getResTopicRepAttachmentListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增结题答辩附件表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult addResTopicRepAttachment(@RequestBody ResTopicRepAttachmentBo resTopicRepAttachmentBo) {
        if (null == resTopicRepAttachmentBo.getTopicId() || null == resTopicRepAttachmentBo.getRepName()) {
            return AjaxResult.fail("参数错误");
        }
        return resTopicRepAttachmentService.addResTopicRepAttachment(resTopicRepAttachmentBo);
    }

    /**
     * 修改结题答辩附件表
     *
     * @param resTopicRepAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult updateResTopicRepAttachment(@Validated @RequestBody ResTopicRepAttachmentBo resTopicRepAttachmentBo) {
        if (null == resTopicRepAttachmentBo.getId()) {
            return AjaxResult.fail("结题答辩附件表id不能为空");
        }
        return resTopicRepAttachmentService.updateResTopicRepAttachment(resTopicRepAttachmentBo);
    }

    /**
     * 批量修改附件表
     *
     * @param resTopicRepAttachmentBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/8 14:58
     */
    @Override
    public AjaxResult updateBatchResTopicRepAttachment(List<ResTopicRepAttachmentBo> resTopicRepAttachmentBos) {
        return resTopicRepAttachmentService.updateBatchResTopicRepAttachment(resTopicRepAttachmentBos);
    }

    /**
     * 查询结题答辩附件表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<ResTopicRepAttachmentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("结题答辩附件表id不能为空");
        }
        ResTopicRepAttachmentVo vo = resTopicRepAttachmentService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除结题答辩附件表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicRepAttachmentDto resTopicRepAttachmentDto = new ResTopicRepAttachmentDto();
        resTopicRepAttachmentDto.setId(id);
        resTopicRepAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicRepAttachmentService.updateById(resTopicRepAttachmentDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult deleteByFileOid(String fileOid) {
        if (StringUtils.isBlank(fileOid)) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        LambdaUpdateWrapper<ResTopicRepAttachmentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResTopicRepAttachmentDto::getRepFileOid, fileOid);
        updateWrapper.eq(ResTopicRepAttachmentDto::getIsDelete,StatusEnum.NOTDELETE.getCode());

        ResTopicRepAttachmentDto resTopicRepAttachmentDto = new ResTopicRepAttachmentDto();
        resTopicRepAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean success = resTopicRepAttachmentService.update(resTopicRepAttachmentDto, updateWrapper);
        if (success) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult<List<String>> getResTopicRepAttachmentYearsByCondition(ResTopicRepAttachmentConditionBo condition) {
        return AjaxResult.success(resTopicRepAttachmentService.getResTopicRepAttachmentYearsByCondition(condition));
    }
}
