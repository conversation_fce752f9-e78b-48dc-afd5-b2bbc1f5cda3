package com.fh.yanx.service.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyConditionBo;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyDto;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 组织申请表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
public interface IOrganizationApplyService extends IService<OrganizationApplyDto> {

    List<OrganizationApplyVo> getOrganizationApplyListByCondition(OrganizationApplyConditionBo condition);

	AjaxResult addOrganizationApply(OrganizationApplyBo organizationApplyBo);

	AjaxResult updateOrganizationApply(OrganizationApplyBo organizationApplyBo);

	OrganizationApplyVo getOrganizationApplyByCondition(OrganizationApplyConditionBo condition);

}

