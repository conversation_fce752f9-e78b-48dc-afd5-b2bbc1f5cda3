package com.fh.yanx.service.bm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.bm.entity.bo.BmPayAdjustRecordConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmPayAdjustRecordDto;
import com.fh.yanx.service.bm.entity.vo.BmPayAdjustRecordVo;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-05-27  16:40
 */
public interface BmPayAdjustRecordMapper extends BaseMapper<BmPayAdjustRecordDto> {

    List<BmPayAdjustRecordVo> getBmPayAdjustRecordListByCondition(BmPayAdjustRecordConditionBo condition);

    BmPayAdjustRecordVo getBmPayAdjustRecordByCondition(BmPayAdjustRecordConditionBo condition);
}
