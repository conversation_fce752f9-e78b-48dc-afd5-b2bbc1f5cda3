package com.fh.yanx.service.pub.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 融合出版书
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pub_book")
public class PubBookDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "book_id", type = IdType.AUTO)
	private Long bookId;

	/**
	 * 书籍名称
	 */
	@TableField("book_name")
	private String bookName;

	/**
	 * 标签字符串，多个则使用英文逗号分割
	 */
	@TableField("label_names")
	private String labelNames;

	/**
	 * 出版社名称
	 */
	@TableField("press_name")
	private String pressName;

	/**
	 * 书号
	 */
	@TableField("book_no")
	private String bookNo;

	/**
	 * 阅读次数
	 */
	@TableField("read_times")
	private Long readTimes;

	/**
	 * 书籍顺序
	 */
	@TableField("book_index")
	private Integer bookIndex;

	/**
	 * 封面文件oid
	 */
	@TableField("cover_file_oid")
	private String coverFileOid;

	/**
	 * 封面文件预览地址
	 */
	@TableField("cover_file_url")
	private String coverFileUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
