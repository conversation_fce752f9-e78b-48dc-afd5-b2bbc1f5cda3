package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.api.R;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.fh.yanx.service.res.service.IResTopicService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicCollectDto;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicCollectVo;
import com.fh.yanx.service.res.service.IResTopicCollectService;
import com.fh.yanx.service.res.mapper.ResTopicCollectMapper;
import com.light.core.entity.AjaxResult;
/**
 * 用户收藏的课题接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResTopicCollectServiceImpl extends ServiceImpl<ResTopicCollectMapper, ResTopicCollectDto> implements IResTopicCollectService {

	@Resource
	private ResTopicCollectMapper resTopicCollectMapper;
	@Resource
	private IResTopicService resTopicService;
	
    @Override
	public List<ResTopicCollectVo> getResTopicCollectListByCondition(ResTopicCollectConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicCollectMapper.getResTopicCollectListByCondition(condition);
	}

	@Override
	public AjaxResult addResTopicCollect(ResTopicCollectBo resTopicCollectBo) {
		ResTopicCollectDto resTopicCollect = new ResTopicCollectDto();
		BeanUtils.copyProperties(resTopicCollectBo, resTopicCollect);
		resTopicCollect.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resTopicCollect)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResTopicCollect(ResTopicCollectBo resTopicCollectBo) {
		ResTopicCollectDto resTopicCollect = new ResTopicCollectDto();
		BeanUtils.copyProperties(resTopicCollectBo, resTopicCollect);
		if(updateById(resTopicCollect)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResTopicCollectVo getDetail(Long id) {
		ResTopicCollectConditionBo condition = new ResTopicCollectConditionBo();
		condition.setCollectId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResTopicCollectVo> list = resTopicCollectMapper.getResTopicCollectListByCondition(condition);
		ResTopicCollectVo vo = new ResTopicCollectVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

	@Override
	public AjaxResult deleteByTopicAndUser(Long topicId, String userOid) {
		LambdaQueryWrapper<ResTopicCollectDto> lqw=new LambdaQueryWrapper<>();
		List<ResTopicCollectDto> list = this.list(lqw);
		if (CollectionUtils.isEmpty(list)){
			return AjaxResult.success();
		}
		LambdaUpdateWrapper<ResTopicCollectDto> uqw=new LambdaUpdateWrapper<>();
		uqw.eq(ResTopicCollectDto::getTopicId,topicId).eq(ResTopicCollectDto::getUserOid,userOid).eq(ResTopicCollectDto::getIsDelete,StatusEnum.NOTDELETE.getCode())
				.set(ResTopicCollectDto::getIsDelete,StatusEnum.ISDELETE.getCode());
		this.update(uqw);
		return AjaxResult.success();
	}

}