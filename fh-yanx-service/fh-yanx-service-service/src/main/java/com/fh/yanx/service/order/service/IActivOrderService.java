package com.fh.yanx.service.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.order.entity.dto.ActivOrderDto;
import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import com.light.core.entity.AjaxResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
public interface IActivOrderService extends IService<ActivOrderDto> {

    List<ActivOrderVo> getActivOrderListByCondition(ActivOrderConditionBo condition);

    AjaxResult addActivOrder(ActivOrderBo activOrderBo);

    AjaxResult updateActivOrder(ActivOrderBo activOrderBo);

    ActivOrderVo getActivOrderByCondition(ActivOrderConditionBo condition);

    ActivOrderVo createOrder(ActivOrderBo orderBo);

    AjaxResult changeOrderAmount(ActivOrderBo orderBo);

    /**
     * 修改订单状态
     *
     * @param orderBo the order bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -11-03 16:32:08
     */
    AjaxResult changeOrderState(ActivOrderBo orderBo);

    /**
     * 修改订单状态，如果是支付成功同步修改交易流水
     *
     * @param orderBo the order bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -09-22 15:03:56
     */
    AjaxResult completeOrder(ActivOrderBo orderBo);

    /**
     * 获取用户订单列表
     *
     * @param orderBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/25 15:24
     **/
    AjaxResult getUserOrderList(ActivOrderBo orderBo);

    /**
     * 超时取消订单
     *
     * @param orderIds
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/9/11 10:59
     **/
    Integer changeActivOrderTimeout(List<Long> orderIds);

}
