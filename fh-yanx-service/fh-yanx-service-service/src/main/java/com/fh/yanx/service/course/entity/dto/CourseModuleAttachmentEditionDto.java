package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程资源或成果样例附件表-模块附件版本表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_module_attachment_edition")
public class CourseModuleAttachmentEditionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "course_module_attachment_edition_id", type = IdType.AUTO)
	private Long courseModuleAttachmentEditionId;

	/**
	 * 课程模块id
	 */
	@TableField("course_module_edition_id")
	private Long courseModuleEditionId;

	/**
	 * 案例id，冗余
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 资源类型：1本地视频文件，2视频地址，3文本，4图片，5其他文件
	 */
	@TableField("resource_type")
	private Integer resourceType;

	/**
	 * 文件fileOid
	 */
	@TableField("attachment_media_id")
	private String attachmentMediaId;

	/**
	 * 压缩文件的文件fileOid
	 */
	@TableField("attachment_media_id_compress")
	private String attachmentMediaIdCompress;

	/**
	 * 附件类型：1文档，2图片，3视频，4音频，5压缩包，6其他
	 */
	@TableField("attachment_media_type")
	private Integer attachmentMediaType;

	/**
	 * 媒体文件后缀，小写
	 */
	@TableField("attachment_media_suffix")
	private String attachmentMediaSuffix;

	/**
	 * 文件地址
	 */
	@TableField("attachment_media_url")
	private String attachmentMediaUrl;

	/**
	 * 文档文件地址-压缩后地址
	 */
	@TableField("attachment_media_url_compress")
	private String attachmentMediaUrlCompress;

	/**
	 * 文件名称，不带后缀
	 */
	@TableField("attachment_media_name")
	private String attachmentMediaName;

	/**
	 * 文件名称，带后缀
	 */
	@TableField("attachment_media_name_ori")
	private String attachmentMediaNameOri;

	/**
	 * 文件转码状态：1未转码，2已转码，3转码失败
	 */
	@TableField("attachment_media_transfer_type")
	private Integer attachmentMediaTransferType;

	/**
	 * 文件第三方在线地址，可以支持直接填写地址展示
	 */
	@TableField("attachment_media_online_url")
	private String attachmentMediaOnlineUrl;

	/**
	 * 附件顺序：默认1
	 */
	@TableField("attachment_media_sort")
	private Long attachmentMediaSort;

	/**
	 * 资源下载类型：1不可下载，2可下载
	 */
	@TableField("download_type")
	private Integer downloadType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 前端生成提交存储，用于前端和布局里面的信息做映射
	 */
	@TableField("uuid")
	private String uuid;

	/**
	 * 用于多组文字显示
	 */
	@TableField("content")
	private String content;

}
