package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.PCourseCasesInfoApi;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesInfoDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import com.fh.yanx.service.course.service.IPCourseCasesInfoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 校本课程案例详细信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@RestController
@Validated
public class PCourseCasesInfoController implements PCourseCasesInfoApi{
	
    @Autowired
    private IPCourseCasesInfoService pCourseCasesInfoService;

    /**
     * 查询校本课程案例详细信息分页列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PageInfo<PCourseCasesInfoVo>> getPCourseCasesInfoPageListByCondition(@RequestBody PCourseCasesInfoConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<PCourseCasesInfoVo> pageInfo = new PageInfo<>(pCourseCasesInfoService.getPCourseCasesInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询校本课程案例详细信息列表
	 * <AUTHOR>
	 * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult<List<PCourseCasesInfoVo>> getPCourseCasesInfoListByCondition(@RequestBody PCourseCasesInfoConditionBo condition){
		List<PCourseCasesInfoVo> list = pCourseCasesInfoService.getPCourseCasesInfoListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增校本课程案例详细信息
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
	@Override
    public AjaxResult addPCourseCasesInfo(@Validated @RequestBody PCourseCasesInfoBo pCourseCasesInfoBo){
		return pCourseCasesInfoService.addPCourseCasesInfo(pCourseCasesInfoBo);
    }

    /**
	 * 修改校本课程案例详细信息
	 * @param pCourseCasesInfoBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult updatePCourseCasesInfo(@Validated @RequestBody PCourseCasesInfoBo pCourseCasesInfoBo) {
		if(null == pCourseCasesInfoBo.getId()) {
			return AjaxResult.fail("校本课程案例详细信息id不能为空");
		}
		return pCourseCasesInfoService.updatePCourseCasesInfo(pCourseCasesInfoBo);
	}

	/**
	 * 查询校本课程案例详细信息详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult<PCourseCasesInfoVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("校本课程案例详细信息id不能为空");
		}
		PCourseCasesInfoConditionBo condition = new PCourseCasesInfoConditionBo();
		condition.setId(id);
		PCourseCasesInfoVo vo = pCourseCasesInfoService.getPCourseCasesInfoByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除校本课程案例详细信息
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		PCourseCasesInfoDto pCourseCasesInfoDto = new PCourseCasesInfoDto();
		pCourseCasesInfoDto.setId(id);
		pCourseCasesInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(pCourseCasesInfoService.updateById(pCourseCasesInfoDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
