package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工作站具体内容
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_station_content")
public class ResStationContentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "station_content_id", type = IdType.AUTO)
	private Long stationContentId;

	/**
	 * 工作站id
	 */
	@TableField("station_id")
	private Long stationId;

	/**
	 * 内容名称
	 */
	@TableField("station_content_name")
	private String stationContentName;

	/**
	 * 内容类型：1核心能力展示，2优秀课题展示，3热点资讯，4赛事服务
	 */
	@TableField("station_content_type")
	private Integer stationContentType;

	/**
	 * 内容顺序
	 */
	@TableField("station_content_index")
	private Long stationContentIndex;

	/**
	 * 内容图片文件原始名称-带后缀，内容只有一张图片时生效
	 */
	@TableField("content_file_name_ori")
	private String contentFileNameOri;

	/**
	 * 内容图片文件名称-不带后缀
	 */
	@TableField("content_file_name")
	private String contentFileName;

	/**
	 * 内容图片文件地址
	 */
	@TableField("content_file_url")
	private String contentFileUrl;

	/**
	 * 内容图片点击链接
	 */
	@TableField("content_file_url_link")
	private String contentFileUrlLink;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
