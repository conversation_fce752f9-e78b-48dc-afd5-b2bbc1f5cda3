package com.fh.yanx.service.activ.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoIntroduceDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo;

/**
 * 新时代文化校园活动介绍表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
public interface ActivInfoIntroduceMapper extends BaseMapper<ActivInfoIntroduceDto> {

	List<ActivInfoIntroduceVo> getActivInfoIntroduceListByCondition(ActivInfoIntroduceConditionBo condition);

	ActivInfoIntroduceVo getActivInfoIntroduceByCondition(ActivInfoIntroduceConditionBo condition);

}
