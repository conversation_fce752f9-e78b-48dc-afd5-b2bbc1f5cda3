package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo;

/**
 * 课程资源或成果样例附件表-模块附件Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface CourseModuleAttachmentMapper extends BaseMapper<CourseModuleAttachmentDto> {

	List<CourseModuleAttachmentVo> getCourseModuleAttachmentListByCondition(CourseModuleAttachmentConditionBo condition);

	CourseModuleAttachmentVo getCourseModuleAttachmentByCondition(CourseModuleAttachmentConditionBo condition);

}
