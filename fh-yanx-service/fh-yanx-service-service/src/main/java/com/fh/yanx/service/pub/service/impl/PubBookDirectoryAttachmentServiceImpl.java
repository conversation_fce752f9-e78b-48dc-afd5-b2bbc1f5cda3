package com.fh.yanx.service.pub.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentConditionBo;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryAttachmentDto;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryAttachmentVo;
import com.fh.yanx.service.pub.mapper.PubBookDirectoryAttachmentMapper;
import com.fh.yanx.service.pub.service.IPubBookDirectoryAttachmentService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 融合出版书目录附件接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Service
public class PubBookDirectoryAttachmentServiceImpl
    extends ServiceImpl<PubBookDirectoryAttachmentMapper, PubBookDirectoryAttachmentDto>
    implements IPubBookDirectoryAttachmentService {

    @Resource
    private PubBookDirectoryAttachmentMapper pubBookDirectoryAttachmentMapper;

    @Override
    public List<PubBookDirectoryAttachmentVo>
        getPubBookDirectoryAttachmentListByCondition(PubBookDirectoryAttachmentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return pubBookDirectoryAttachmentMapper.getPubBookDirectoryAttachmentListByCondition(condition);
    }

    @Override
    public AjaxResult addPubBookDirectoryAttachment(PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo) {
        PubBookDirectoryAttachmentDto pubBookDirectoryAttachment = new PubBookDirectoryAttachmentDto();
        BeanUtils.copyProperties(pubBookDirectoryAttachmentBo, pubBookDirectoryAttachment);
        pubBookDirectoryAttachment.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pubBookDirectoryAttachment)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePubBookDirectoryAttachment(PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo) {
        PubBookDirectoryAttachmentDto pubBookDirectoryAttachment = new PubBookDirectoryAttachmentDto();
        BeanUtils.copyProperties(pubBookDirectoryAttachmentBo, pubBookDirectoryAttachment);
        if (updateById(pubBookDirectoryAttachment)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PubBookDirectoryAttachmentVo
        getPubBookDirectoryAttachmentByCondition(PubBookDirectoryAttachmentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PubBookDirectoryAttachmentVo vo =
            pubBookDirectoryAttachmentMapper.getPubBookDirectoryAttachmentByCondition(condition);
        return vo;
    }

}