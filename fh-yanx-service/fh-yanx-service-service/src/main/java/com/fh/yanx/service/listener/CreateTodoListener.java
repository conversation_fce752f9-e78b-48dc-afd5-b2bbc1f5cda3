package com.fh.yanx.service.listener;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.service.consts.ConstantsInteger;
import com.fh.yanx.service.dto.EventPublishDto;
import com.fh.yanx.service.enums.TodoBusinessType;
import com.fh.yanx.service.enums.TodoStatusType;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CreateTodoEvent;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTodoVo;
import com.fh.yanx.service.res.service.IResTopicTodoService;
import com.fh.yanx.service.utils.DateKit;
import com.fh.yanx.service.utils.StringKit;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 创建待办事件监听
 *
 * <AUTHOR>
 * @date 2022/5/10 16:45
 */
@Component
public class CreateTodoListener {

    @Lazy
    @Autowired
    private IResTopicTodoService iResTopicTodoService;

    @Async
    @EventListener
    public void onApplicationEvent(CreateTodoEvent event) {
        EventPublishDto eventPublishDto = (EventPublishDto)event.getSource();
        if (eventPublishDto.getEventType() == null) {
            return;
        }

        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_STASH.getType())) {
            // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
            ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
            resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoConditionBo.setTodoTypes(Lists.newArrayList(TopicTodoType.STUDENT_TOPIC_STASH.getType()));
            resTopicTodoConditionBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
            resTopicTodoConditionBo.setOrderBy("update_time desc");
            List<ResTopicTodoVo> resTopicTodoVos =
                iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
            if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_STASH.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】您暂存课题“{1}”，暂未提交",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_STASH.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_STASH.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
            } else {
                ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                // 个人操作无并发，因此可以这么更新。原db数量 +1
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_STASH.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】您暂存课题“{1}”，暂未提交",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_STASH.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_STASH.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
            }

        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_PASS.getType())) {
            // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
            ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
            resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoConditionBo.setTodoTypes(Lists.newArrayList(TopicTodoType.STUDENT_TOPIC_STASH.getType()));
            resTopicTodoConditionBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
            resTopicTodoConditionBo.setOrderBy("update_time desc");
            List<ResTopicTodoVo> resTopicTodoVos =
                iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
            if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_PASS.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】您的课题“{1}”已通过审批",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_PASS.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_PASS.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
            } else {
                ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                // 个人操作无并发，因此可以这么更新。原db数量 +1
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_PASS.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】您的课题“{1}”已通过审批",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_PASS.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_PASS.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_REJECT.getType())) {
            // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
            ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
            resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoConditionBo.setTodoTypes(Lists.newArrayList(TopicTodoType.STUDENT_TOPIC_STASH.getType()));
            resTopicTodoConditionBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
            resTopicTodoConditionBo.setOrderBy("update_time desc");
            List<ResTopicTodoVo> resTopicTodoVos =
                iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
            if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_REJECT.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】您的课题“{1}”已被驳回",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_REJECT.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_REJECT.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
            } else {
                ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                // 个人操作无并发，因此可以这么更新。原db数量 +1
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_REJECT.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】您的课题“{1}”已被驳回",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_REJECT.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_REJECT.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_TEACHER.getType())) {
            // 插入学生课题创建的待办
            ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
            resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_TEACHER.getKey());
            resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
            final String todoMsg = StringKit.getMessage("【{0}】指导老师{1}评定了您的课题“{2}”",
                DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTeacherName(),
                eventPublishDto.getTopicName());
            resTopicTodoBo.setTodoMsg(todoMsg);
            resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_TEACHER.getName());
            resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_TEACHER.getType());
            resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
            resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
            iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getType())) {
            // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
            ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
            resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoConditionBo.setTodoTypes(Lists.newArrayList(TopicTodoType.STUDENT_TOPIC_STASH.getType()));
            resTopicTodoConditionBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
            resTopicTodoConditionBo.setOrderBy("update_time desc");
            List<ResTopicTodoVo> resTopicTodoVos =
                iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
            if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了您的课题“{1}”",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
            } else {
                ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                // 个人操作无并发，因此可以这么更新。原db数量 +1
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了您的课题“{1}”",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getType())) {
            // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
            ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
            resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoConditionBo.setTodoTypes(Lists.newArrayList(TopicTodoType.STUDENT_TOPIC_STASH.getType()));
            resTopicTodoConditionBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
            resTopicTodoConditionBo.setOrderBy("update_time desc");
            List<ResTopicTodoVo> resTopicTodoVos =
                iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
            if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了您的课题“{1}”，并设置为优秀课题",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
            } else {
                ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                // 个人操作无并发，因此可以这么更新。原db数量 +1
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                resTopicTodoBo.setTodoKey(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了您的课题“{1}”，并设置为优秀课题",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.STUDENT_TOPIC_SCHOOL_PASS_YOU.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
                iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_SUBMIT.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
                ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
                resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoConditionBo.setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_SUBMIT.getType(),
                    TopicTodoType.TEACHER_TOPIC_RESUBMIT.getType()));
                resTopicTodoConditionBo.setUserOid(userOid);
                resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoConditionBo.setOrderBy("update_time desc");
                List<ResTopicTodoVo> resTopicTodoVos =
                    iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
                if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                    // 新增
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SUBMIT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】您有课题需要审批",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SUBMIT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SUBMIT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
                } else {
                    ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                    // 个人操作无并发，因此可以这么更新。原db数量 +1
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SUBMIT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    resTopicTodoBo.setTodoNum(resTopicTodoBo.getTodoNum() + ConstantsInteger.NUM_1);
                    final String todoMsg = StringKit.getMessage("【{0}】您有课题需要审批",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SUBMIT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SUBMIT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
                }
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
                ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
                resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoConditionBo.setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getType(),
                    TopicTodoType.TEACHER_TOPIC_RESUBMIT.getType()));
                resTopicTodoConditionBo.setUserOid(userOid);
                resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoConditionBo.setOrderBy("update_time desc");
                List<ResTopicTodoVo> resTopicTodoVos =
                    iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
                if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                    // 新增
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】您有课题需要审批",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
                } else {
                    ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                    // 个人操作无并发，因此可以这么更新。原db数量 +1
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    resTopicTodoBo.setTodoNum(resTopicTodoBo.getTodoNum() + ConstantsInteger.NUM_1);
                    final String todoMsg = StringKit.getMessage("【{0}】您有课题需要审批",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_RESUBMIT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
                }
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
                ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
                resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoConditionBo
                    .setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getType()));
                resTopicTodoConditionBo.setUserOid(userOid);
                resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoConditionBo.setOrderBy("update_time desc");
                List<ResTopicTodoVo> resTopicTodoVos =
                    iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
                if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                    // 新增
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】{1} 组课题组提交课题研究材料",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(ConstantsInteger.NUM_1));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
                } else {
                    ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                    // 个人操作无并发，因此可以这么更新。原db数量 +1
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    resTopicTodoBo.setTodoNum(resTopicTodoBo.getTodoNum() + ConstantsInteger.NUM_1);
                    final String todoMsg = StringKit.getMessage("【{0}】{1} 组课题组提交课题研究材料",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(resTopicTodoBo.getTodoNum()));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
                }
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
                ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
                resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoConditionBo
                    .setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getType()));
                resTopicTodoConditionBo.setUserOid(userOid);
                resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoConditionBo.setOrderBy("update_time desc");
                List<ResTopicTodoVo> resTopicTodoVos =
                    iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
                if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                    // 新增
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】{1}组课题组提交课题答辩论文",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(ConstantsInteger.NUM_1));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
                } else {
                    ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                    // 个人操作无并发，因此可以这么更新。原db数量 +1
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    resTopicTodoBo.setTodoNum(resTopicTodoBo.getTodoNum() + ConstantsInteger.NUM_1);
                    final String todoMsg = StringKit.getMessage("【{0}】{1}组课题组提交课题答辩论文",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(resTopicTodoBo.getTodoNum()));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
                }
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_EVALUATE.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
                ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
                resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoConditionBo
                    .setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_EVALUATE.getType()));
                resTopicTodoConditionBo.setUserOid(userOid);
                resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoConditionBo.setOrderBy("update_time desc");
                List<ResTopicTodoVo> resTopicTodoVos =
                    iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
                if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                    // 新增
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_EVALUATE.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】有课题组请您对课题进行评定",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_EVALUATE.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_EVALUATE.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
                } else {
                    ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                    // 个人操作无并发，因此可以这么更新。原db数量 +1
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_EVALUATE.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】有课题组请您对课题进行评定",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_EVALUATE.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_EVALUATE.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
                }
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
                ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
                resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoConditionBo
                    .setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getType()));
                resTopicTodoConditionBo.setUserOid(userOid);
                resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoConditionBo.setOrderBy("update_time desc");
                List<ResTopicTodoVo> resTopicTodoVos =
                    iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
                if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                    // 新增
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了{1}个课题",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(ConstantsInteger.NUM_1));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
                } else {
                    ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                    // 个人操作无并发，因此可以这么更新。原db数量 +1
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    resTopicTodoBo.setTodoNum(resTopicTodoBo.getTodoNum() + ConstantsInteger.NUM_1);
                    final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了{1}个课题",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(resTopicTodoBo.getTodoNum()));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
                }
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 查询符合条件（todoBusinessType,todoTypes,userOid,todoStatus）的待办
                ResTopicTodoConditionBo resTopicTodoConditionBo = new ResTopicTodoConditionBo();
                resTopicTodoConditionBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoConditionBo
                    .setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getType()));
                resTopicTodoConditionBo.setUserOid(userOid);
                resTopicTodoConditionBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoConditionBo.setOrderBy("update_time desc");
                List<ResTopicTodoVo> resTopicTodoVos =
                    iResTopicTodoService.getResTopicTodoListByCondition(resTopicTodoConditionBo);
                if (CollectionUtils.isEmpty(resTopicTodoVos)) {
                    // 新增
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了{1}课题，并设置{1}个优秀课题",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(ConstantsInteger.NUM_1));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
                } else {
                    ResTopicTodoVo resTopicTodoVo = resTopicTodoVos.get(0);
                    // 个人操作无并发，因此可以这么更新。原db数量 +1
                    ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                    BeanUtils.copyProperties(resTopicTodoVo, resTopicTodoBo);

                    resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getKey());
                    resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                    resTopicTodoBo.setTodoNum(resTopicTodoBo.getTodoNum() + ConstantsInteger.NUM_1);
                    final String todoMsg = StringKit.getMessage("【{0}】学校课题组通过了{1}课题，并设置{1}个优秀课题",
                        DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"),
                        String.valueOf(resTopicTodoBo.getTodoNum()));
                    resTopicTodoBo.setTodoMsg(todoMsg);
                    resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getName());
                    resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT.getType());
                    resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                    resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                    resTopicTodoBo.setUserOid(userOid);
                    iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
                }
            }
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_SCHOOL_REJECT.getType())) {
            for (String userOid : eventPublishDto.getUserOids()) {
                // 新增
                ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
                resTopicTodoBo.setTodoKey(TopicTodoType.TEACHER_TOPIC_SCHOOL_REJECT.getKey());
                resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
                final String todoMsg = StringKit.getMessage("【{0}】学校课题组驳回了课题“{1}”",
                    DateKit.date2String(eventPublishDto.getDate(), "yyyy-MM-dd"), eventPublishDto.getTopicName());
                resTopicTodoBo.setTodoMsg(todoMsg);
                resTopicTodoBo.setTodoName(TopicTodoType.TEACHER_TOPIC_SCHOOL_REJECT.getName());
                resTopicTodoBo.setTodoType(TopicTodoType.TEACHER_TOPIC_SCHOOL_REJECT.getType());
                resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
                resTopicTodoBo.setTodoStatus(TodoStatusType.NO_DO.getValue());
                resTopicTodoBo.setUserOid(userOid);
                iResTopicTodoService.addResTopicTodo(resTopicTodoBo);
            }
        }
    }
}
