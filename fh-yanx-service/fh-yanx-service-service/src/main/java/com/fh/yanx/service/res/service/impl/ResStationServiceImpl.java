package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResStationDto;
import com.fh.yanx.service.res.entity.bo.ResStationConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationBo;
import com.fh.yanx.service.res.entity.vo.ResStationVo;
import com.fh.yanx.service.res.service.IResStationService;
import com.fh.yanx.service.res.mapper.ResStationMapper;
import com.light.core.entity.AjaxResult;
/**
 * 工作站和学校表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResStationServiceImpl extends ServiceImpl<ResStationMapper, ResStationDto> implements IResStationService {

	@Resource
	private ResStationMapper resStationMapper;
	
    @Override
	public List<ResStationVo> getResStationListByCondition(ResStationConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resStationMapper.getResStationListByCondition(condition);
	}

	@Override
	public AjaxResult addResStation(ResStationBo resStationBo) {
		ResStationDto resStation = new ResStationDto();
		BeanUtils.copyProperties(resStationBo, resStation);
		resStation.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resStation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResStation(ResStationBo resStationBo) {
		ResStationDto resStation = new ResStationDto();
		BeanUtils.copyProperties(resStationBo, resStation);
		if(updateById(resStation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResStationVo getDetail(Long id) {
		ResStationConditionBo condition = new ResStationConditionBo();
		condition.setStationId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResStationVo> list = resStationMapper.getResStationListByCondition(condition);
		ResStationVo vo = new ResStationVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}