package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程讨论区表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_discuss_section")
public class CourseDiscussSectionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 0-一级评论 不为0为对某条评论的回复
	 */
	@TableField("parent_id")
	private Long parentId;

	/**
	 * 所有父级id英文逗号拼接
	 */
	@TableField("superior_ids")
	private String superiorIds;

	/**
	 * 评论用户oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 评论内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 回复数量
	 */
	@TableField("reply_num")
	private Integer replyNum;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
