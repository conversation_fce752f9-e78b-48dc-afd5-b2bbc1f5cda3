package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseRecommendDto;
import com.fh.yanx.service.course.entity.bo.CourseRecommendConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendBo;
import com.fh.yanx.service.course.entity.vo.CourseRecommendVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程推荐表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
public interface ICourseRecommendService extends IService<CourseRecommendDto> {

    List<CourseRecommendVo> getCourseRecommendListByCondition(CourseRecommendConditionBo condition);

	AjaxResult addCourseRecommend(CourseRecommendBo courseRecommendBo);

	AjaxResult addCourseRecommend(List<CourseRecommendBo> courseRecommendBo);

	AjaxResult updateCourseRecommend(CourseRecommendBo courseRecommendBo);

	AjaxResult updateCourseRecommend(List<CourseRecommendBo> courseRecommendBo);

	CourseRecommendVo getCourseRecommendByCondition(CourseRecommendConditionBo condition);

}

