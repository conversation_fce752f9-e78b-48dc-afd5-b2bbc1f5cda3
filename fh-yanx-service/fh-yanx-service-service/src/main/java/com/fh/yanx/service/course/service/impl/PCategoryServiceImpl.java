package com.fh.yanx.service.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.entity.bo.PCategoryConditionBo;
import com.fh.yanx.service.course.entity.bo.PCategoryBo;
import com.fh.yanx.service.course.entity.vo.PCategoryVo;
import com.fh.yanx.service.course.service.IPCategoryService;
import com.fh.yanx.service.course.mapper.PCategoryMapper;
import com.light.core.entity.AjaxResult;
/**
 * 资源类别接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Service
public class PCategoryServiceImpl extends ServiceImpl<PCategoryMapper, PCategoryDto> implements IPCategoryService {

	@Resource
	private PCategoryMapper pCategoryMapper;
	
    @Override
	public List<PCategoryVo> getPCategoryListByCondition(PCategoryConditionBo condition) {
		FuzzyQueryUtil.transferMeanBean(condition);
        return pCategoryMapper.getPCategoryListByCondition(condition);
	}

	@Override
	public AjaxResult addPCategory(PCategoryBo pCategoryBo) {
		PCategoryDto pCategory = new PCategoryDto();
		BeanUtils.copyProperties(pCategoryBo, pCategory);
		if(save(pCategory)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePCategory(PCategoryBo pCategoryBo) {
		PCategoryDto pCategory = new PCategoryDto();
		BeanUtils.copyProperties(pCategoryBo, pCategory);
		if(updateById(pCategory)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public PCategoryVo getPCategoryByCondition(PCategoryConditionBo condition) {
		PCategoryVo vo = pCategoryMapper.getPCategoryByCondition(condition);
		return vo;
	}

	@Override
	public List<PCategoryDto> getListByIdsList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        List<PCategoryDto> categoryList;
        QueryWrapper<PCategoryDto> qw = new QueryWrapper<>();
        qw.in("id", ids).ne("state", StatusEnum.ISDELETE.getCode());
        categoryList = list(qw);
        return categoryList;
	}
}