package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicApprovalDto;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicApprovalVo;

/**
 * 课题审批意见表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicApprovalMapper extends BaseMapper<ResTopicApprovalDto> {

	List<ResTopicApprovalVo> getResTopicApprovalListByCondition(ResTopicApprovalConditionBo condition);

}
