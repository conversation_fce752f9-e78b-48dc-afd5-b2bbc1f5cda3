package com.fh.yanx.service.jz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesTypeDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesTypeVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 金中-校本课程案例类型接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface IJzCourseCasesTypeService extends IService<JzCourseCasesTypeDto> {

    List<JzCourseCasesTypeVo> getJzCourseCasesTypeListByCondition(JzCourseCasesTypeConditionBo condition);

	AjaxResult addJzCourseCasesType(JzCourseCasesTypeBo jzCourseCasesTypeBo);

	AjaxResult updateJzCourseCasesType(JzCourseCasesTypeBo jzCourseCasesTypeBo);

	JzCourseCasesTypeVo getJzCourseCasesTypeByCondition(JzCourseCasesTypeConditionBo condition);

	/**
	 * 获取案例相关联类型名称
	 * @param casesId
	 * @return
	 */
	List<String> getCasesTypeNameList(Long casesId);
}

