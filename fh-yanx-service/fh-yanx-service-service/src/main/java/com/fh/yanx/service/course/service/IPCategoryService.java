package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.entity.bo.PCategoryConditionBo;
import com.fh.yanx.service.course.entity.bo.PCategoryBo;
import com.fh.yanx.service.course.entity.vo.PCategoryVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 资源类别接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface IPCategoryService extends IService<PCategoryDto> {

    List<PCategoryVo> getPCategoryListByCondition(PCategoryConditionBo condition);

	AjaxResult addPCategory(PCategoryBo pCategoryBo);

	AjaxResult updatePCategory(PCategoryBo pCategoryBo);

	PCategoryVo getPCategoryByCondition(PCategoryConditionBo condition);

	/**
	 * 通过类别ids获取类别对象
	 *
	 * @param ids
	 * @return
	 */
	List<PCategoryDto> getListByIdsList(List<Long> ids);

}

