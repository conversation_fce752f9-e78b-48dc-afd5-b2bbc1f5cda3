package com.fh.yanx.service.course.controller;

import com.fh.yanx.service.course.api.CourseCasesGradeEditionApi;
import com.fh.yanx.service.course.entity.dto.CourseCasesGradeEditionDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesGradeEditionVo;
import com.fh.yanx.service.course.service.ICourseCasesGradeEditionService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 校本课程案例年级版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:00
 */
@RestController
@Validated
public class CourseCasesGradeEditionController implements CourseCasesGradeEditionApi{
	
    @Autowired
    private ICourseCasesGradeEditionService courseCasesGradeEditionService;

    /**
     * 查询校本课程案例年级版本记录分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
    @Override
    public AjaxResult<PageInfo<CourseCasesGradeEditionVo>> getCourseCasesGradeEditionPageListByCondition(@RequestBody CourseCasesGradeEditionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseCasesGradeEditionVo> pageInfo = new PageInfo<>(courseCasesGradeEditionService.getCourseCasesGradeEditionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询校本课程案例年级版本记录列表
	 * <AUTHOR>
	 * @date 2024-12-05 10:41:00
	 */
	@Override
	public AjaxResult<List<CourseCasesGradeEditionVo>> getCourseCasesGradeEditionListByCondition(@RequestBody CourseCasesGradeEditionConditionBo condition){
		List<CourseCasesGradeEditionVo> list = courseCasesGradeEditionService.getCourseCasesGradeEditionListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增校本课程案例年级版本记录
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
	@Override
    public AjaxResult addCourseCasesGradeEdition(@Validated @RequestBody CourseCasesGradeEditionBo courseCasesGradeEditionBo){
		return courseCasesGradeEditionService.addCourseCasesGradeEdition(courseCasesGradeEditionBo);
    }

    /**
	 * 修改校本课程案例年级版本记录
	 * @param courseCasesGradeEditionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
	 */
	@Override
	public AjaxResult updateCourseCasesGradeEdition(@Validated @RequestBody CourseCasesGradeEditionBo courseCasesGradeEditionBo) {
		if(null == courseCasesGradeEditionBo.getId()) {
			return AjaxResult.fail("校本课程案例年级版本记录id不能为空");
		}
		return courseCasesGradeEditionService.updateCourseCasesGradeEdition(courseCasesGradeEditionBo);
	}

	/**
	 * 查询校本课程案例年级版本记录详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
	 */
	@Override
	public AjaxResult<CourseCasesGradeEditionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("校本课程案例年级版本记录id不能为空");
		}
		CourseCasesGradeEditionConditionBo condition = new CourseCasesGradeEditionConditionBo();
		condition.setId(id);
		CourseCasesGradeEditionVo vo = courseCasesGradeEditionService.getCourseCasesGradeEditionByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除校本课程案例年级版本记录
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseCasesGradeEditionDto courseCasesGradeEditionDto = new CourseCasesGradeEditionDto();
		courseCasesGradeEditionDto.setId(id);
		courseCasesGradeEditionDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseCasesGradeEditionService.updateById(courseCasesGradeEditionDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
