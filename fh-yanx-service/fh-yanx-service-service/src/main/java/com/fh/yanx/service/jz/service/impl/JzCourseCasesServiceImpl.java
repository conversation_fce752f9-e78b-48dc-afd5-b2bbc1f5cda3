package com.fh.yanx.service.jz.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.fh.yanx.service.jz.service.IJzCourseCasesTypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo;
import com.fh.yanx.service.jz.mapper.JzCourseCasesMapper;
import com.fh.yanx.service.jz.service.IJzCourseCasesService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 金中-校本课程案例接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@Service
public class JzCourseCasesServiceImpl extends ServiceImpl<JzCourseCasesMapper, JzCourseCasesDto>
    implements IJzCourseCasesService {

    @Resource
    private JzCourseCasesMapper jzCourseCasesMapper;
    @Resource
    private IJzCourseCasesTypeService jzCourseCasesTypeService;

    @Override
    public List<JzCourseCasesVo> getJzCourseCasesListByCondition(JzCourseCasesConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return jzCourseCasesMapper.getJzCourseCasesListByCondition(condition);
    }

    @Override
    public AjaxResult addJzCourseCases(JzCourseCasesBo jzCourseCasesBo) {
        JzCourseCasesDto jzCourseCases = new JzCourseCasesDto();
        BeanUtils.copyProperties(jzCourseCasesBo, jzCourseCases);
        jzCourseCases.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(jzCourseCases)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateJzCourseCases(JzCourseCasesBo jzCourseCasesBo) {
        JzCourseCasesDto jzCourseCases = new JzCourseCasesDto();
        BeanUtils.copyProperties(jzCourseCasesBo, jzCourseCases);
        if (updateById(jzCourseCases)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public JzCourseCasesVo getJzCourseCasesByCondition(JzCourseCasesConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        JzCourseCasesVo vo = jzCourseCasesMapper.getJzCourseCasesByCondition(condition);
        return vo;
    }

    @Override
    public List<JzCourseCasesVo> getHomeList(JzCourseCasesConditionBo condition) {
        return jzCourseCasesMapper.getHomeList(condition);
    }

    @Override
    public JzCourseCasesVo getHomeDetail(Long casesId) {
        JzCourseCasesVo jzCourseCasesVo = new JzCourseCasesVo();
        JzCourseCasesDto pCourseCasesDto = jzCourseCasesMapper.selectById(casesId);
        if (null != pCourseCasesDto) {
            pCourseCasesDto.setViews(pCourseCasesDto.getViews() + 1);
            jzCourseCasesMapper.updateById(pCourseCasesDto);
            BeanUtils.copyProperties(pCourseCasesDto, jzCourseCasesVo);
            jzCourseCasesVo.setPhone(null);
            List<String> casesTypeNameList = jzCourseCasesTypeService.getCasesTypeNameList(casesId);
            jzCourseCasesVo.setTypeNameList(casesTypeNameList);
        }
        return jzCourseCasesVo;
    }
}