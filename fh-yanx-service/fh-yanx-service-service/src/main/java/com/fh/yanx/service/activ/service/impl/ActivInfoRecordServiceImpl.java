package com.fh.yanx.service.activ.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import com.fh.yanx.service.activ.service.IActivInfoRecordService;
import com.fh.yanx.service.activ.mapper.ActivInfoRecordMapper;
import com.light.core.entity.AjaxResult;
/**
 * 新时代文化校园活动内容表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
@Service
public class ActivInfoRecordServiceImpl extends ServiceImpl<ActivInfoRecordMapper, ActivInfoRecordDto> implements IActivInfoRecordService {

	@Resource
	private ActivInfoRecordMapper activInfoRecordMapper;
	
    @Override
	public List<ActivInfoRecordVo> getActivInfoRecordListByCondition(ActivInfoRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return activInfoRecordMapper.getActivInfoRecordListByCondition(condition);
	}

	@Override
	public AjaxResult addActivInfoRecord(ActivInfoRecordBo activInfoRecordBo) {
		ActivInfoRecordDto activInfoRecord = new ActivInfoRecordDto();
		BeanUtils.copyProperties(activInfoRecordBo, activInfoRecord);
		activInfoRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(activInfoRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateActivInfoRecord(ActivInfoRecordBo activInfoRecordBo) {
		ActivInfoRecordDto activInfoRecord = new ActivInfoRecordDto();
		BeanUtils.copyProperties(activInfoRecordBo, activInfoRecord);
		if(updateById(activInfoRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ActivInfoRecordVo getActivInfoRecordByCondition(ActivInfoRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return activInfoRecordMapper.getActivInfoRecordByCondition(condition);
	}

	@Override
	public boolean deleteAndAddActivInfoRecords(ActivInfoBo activInfoBo) {
    	if (null == activInfoBo.getActivId()) {
    		return false;
		}

		// 删除
		LambdaUpdateWrapper<ActivInfoRecordDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(ActivInfoRecordDto::getActivId, activInfoBo.getActivId());
		updateWrapper.set(ActivInfoRecordDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		update(updateWrapper);

		if (CollectionUtil.isEmpty(activInfoBo.getActivInfoRecords())) {
			return true;
		}
		// 新增
		List<ActivInfoRecordDto> activInfoRecordDtos = activInfoBo.getActivInfoRecords().stream()
				.map(activInfoRecordBo -> {
					ActivInfoRecordDto recordDto = new ActivInfoRecordDto();
					BeanUtils.copyProperties(activInfoRecordBo, recordDto);
					recordDto.setActivId(activInfoBo.getActivId());
					return recordDto;
				}).collect(Collectors.toList());
		return saveBatch(activInfoRecordDtos);
	}

}