package com.fh.yanx.service.bm.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoDto;
import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportZdVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoStatisticsVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * 新时代文化校园报名活动申请表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
public interface BmInfoMapper extends BaseMapper<BmInfoDto> {

    List<BmInfoVo> getBmInfoListByCondition(BmInfoConditionBo condition);

    BmInfoVo getBmInfoByCondition(BmInfoConditionBo condition);

    /**
     * 查询导出数据
     *
     * @param condition the condition
     * @return bm info list by condition export
     */
    List<BmInfoExportVo> getBmInfoListByConditionExport(BmInfoConditionBo condition);

    /**
     * 查询导出数据-征订
     *
     * @param condition the condition
     * @return bm info list by condition export
     */
    List<BmInfoExportZdVo> getBmInfoListByConditionExportZd(BmInfoConditionBo condition);

    /**
     * 查询报名名单列表（包含订单信息）
     *
     * @param condition
     * @return
     */
    List<BmInfoVo> getBmInfoListWithOrder(BmInfoConditionBo condition);

    /**
     * 查询报名统计信息
     *
     * @param condition
     * @return
     */
    BmInfoStatisticsVo getBmInfoStatistics(BmInfoConditionBo condition);

    /**
     * 校验手机号是否可以登录
     *
     * @param phone
     * @return
     */
    String checkPhoneForLogin(String phone);

    /**
     *
     *
     * @param conditionBo
     * @return
     */
    Integer countBmInfoJoiner(BmInfoConditionBo conditionBo);

    /**
     * 批量更新支付状态
     *
     * @param infoIds
     * @return
     */
    Boolean updatePayTypeBatch(@Param("infoIds") List<Long> infoIds, @Param("payType") Integer payType);

}
