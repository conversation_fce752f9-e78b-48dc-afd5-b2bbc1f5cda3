package com.fh.yanx.service.activ.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import com.fh.yanx.service.activ.service.IActivInfoRecordService;
import com.fh.yanx.service.consts.ConstantsInteger;
import com.fh.yanx.service.enums.RecordType;
import com.light.user.organization.entity.bo.OrganizationBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.activ.api.ActivInfoRecordViewApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewConditionBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordViewDto;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo;
import com.fh.yanx.service.activ.service.IActivInfoRecordViewService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 新时代文化校园活动内容观看记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
@RestController
@Validated
public class ActivInfoRecordViewController implements ActivInfoRecordViewApi {

    @Autowired
    private IActivInfoRecordViewService activInfoRecordViewService;
    @Autowired
    private IActivInfoRecordService activInfoRecordService;

    /**
     * 查询新时代文化校园活动内容观看记录表分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @Override
    public AjaxResult<PageInfo<ActivInfoRecordViewVo>>
        getActivInfoRecordViewPageListByCondition(@RequestBody ActivInfoRecordViewConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ActivInfoRecordViewVo> pageInfo =
            new PageInfo<>(activInfoRecordViewService.getActivInfoRecordViewListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询新时代文化校园活动内容观看记录表列表
     * 
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @Override
    public AjaxResult<List<ActivInfoRecordViewVo>>
        getActivInfoRecordViewListByCondition(@RequestBody ActivInfoRecordViewConditionBo condition) {
        List<ActivInfoRecordViewVo> list = activInfoRecordViewService.getActivInfoRecordViewListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增新时代文化校园活动内容观看记录表
     * 
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @Override
    public AjaxResult addActivInfoRecordView(@Validated @RequestBody ActivInfoRecordViewBo activInfoRecordViewBo) {
        return activInfoRecordViewService.addActivInfoRecordView(activInfoRecordViewBo);
    }

    /**
     * 修改新时代文化校园活动内容观看记录表
     * 
     * @param activInfoRecordViewBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @Override
    public AjaxResult updateActivInfoRecordView(@Validated @RequestBody ActivInfoRecordViewBo activInfoRecordViewBo) {
        if (null == activInfoRecordViewBo.getId()) {
            return AjaxResult.fail("新时代文化校园活动内容观看记录表id不能为空");
        }
        return activInfoRecordViewService.updateActivInfoRecordView(activInfoRecordViewBo);
    }

    /**
     * 查询新时代文化校园活动内容观看记录表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @Override
    public AjaxResult<ActivInfoRecordViewVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("新时代文化校园活动内容观看记录表id不能为空");
        }
        ActivInfoRecordViewConditionBo condition = new ActivInfoRecordViewConditionBo();
        condition.setId(id);
        ActivInfoRecordViewVo vo = activInfoRecordViewService.getActivInfoRecordViewByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除新时代文化校园活动内容观看记录表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ActivInfoRecordViewDto activInfoRecordViewDto = new ActivInfoRecordViewDto();
        activInfoRecordViewDto.setId(id);
        activInfoRecordViewDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (activInfoRecordViewService.updateById(activInfoRecordViewDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult<List<ActivInfoRecordVo>>
        getActivInfoRecordViewStatistics(ActivInfoRecordViewConditionBo condition) {
        // 这个活动的所有活动记录信息
        ActivInfoRecordConditionBo activInfoRecordConditionBo = new ActivInfoRecordConditionBo();
        activInfoRecordConditionBo.setActivId(condition.getActivId());
        activInfoRecordConditionBo.setOrderBy("record_sort");
        activInfoRecordConditionBo.setRecordType(RecordType.CONTENT.getValue());
        List<ActivInfoRecordVo> activInfoRecordListByCondition =
            activInfoRecordService.getActivInfoRecordListByCondition(activInfoRecordConditionBo);
        if (CollectionUtils.isEmpty(activInfoRecordListByCondition)) {
            return AjaxResult.success(activInfoRecordListByCondition);
        }

        // 总统计信息封装
        List<ActivInfoRecordViewVo> activInfoRecordViewStatisticsAll =
            activInfoRecordViewService.getActivInfoRecordViewStatistics(condition);
        Map<Long, Long> allMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(activInfoRecordViewStatisticsAll)) {
            allMap = activInfoRecordViewStatisticsAll.stream().collect(Collectors.groupingBy(
                ActivInfoRecordViewVo::getRecordId, Collectors.summingLong(ActivInfoRecordViewVo::getViewCountDay)));
        }

        // 一个视频按天统计信息封装，产品只要求查询近60自然天，这里查询60天内有数据的也没有问题，包含产品的需求
        condition.setStatisticsDateTopCount(ConstantsInteger.NUM_60);
        List<ActivInfoRecordViewVo> activInfoRecordViewStatisticsTop =
            activInfoRecordViewService.getActivInfoRecordViewStatistics(condition);
        Map<Long, List<ActivInfoRecordViewVo>> topMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(activInfoRecordViewStatisticsTop)) {
            topMap = activInfoRecordViewStatisticsTop.stream()
                .collect(Collectors.groupingBy(ActivInfoRecordViewVo::getRecordId));
        }

        Map<Long, Long> allMapFinal = allMap;
        Map<Long, List<ActivInfoRecordViewVo>> topMapFinal = topMap;
        activInfoRecordListByCondition.forEach(activInfoRecordVo -> {
            activInfoRecordVo.setTotalViewCount(allMapFinal.get(activInfoRecordVo.getRecordId()));
            activInfoRecordVo.setActivInfoRecordViewVoList(topMapFinal.get(activInfoRecordVo.getRecordId()));
        });
        return AjaxResult.success(activInfoRecordListByCondition);
    }
}
