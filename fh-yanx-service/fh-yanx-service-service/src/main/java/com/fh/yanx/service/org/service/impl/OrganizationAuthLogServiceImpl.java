package com.fh.yanx.service.org.service.impl;

import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.org.entity.dto.OrganizationAuthLogDto;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogBo;
import com.fh.yanx.service.org.entity.vo.OrganizationAuthLogVo;
import com.fh.yanx.service.org.service.IOrganizationAuthLogService;
import com.fh.yanx.service.org.mapper.OrganizationAuthLogMapper;
import com.light.core.entity.AjaxResult;
/**
 * 组织认证日志记录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
@Service
public class OrganizationAuthLogServiceImpl extends ServiceImpl<OrganizationAuthLogMapper, OrganizationAuthLogDto> implements IOrganizationAuthLogService {

	@Resource
	private OrganizationAuthLogMapper organizationAuthLogMapper;
	
    @Override
	public List<OrganizationAuthLogVo> getOrganizationAuthLogListByCondition(OrganizationAuthLogConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return organizationAuthLogMapper.getOrganizationAuthLogListByCondition(condition);
	}

	@Override
	public AjaxResult addOrganizationAuthLog(OrganizationAuthLogBo organizationAuthLogBo) {
		OrganizationAuthLogDto organizationAuthLog = new OrganizationAuthLogDto();
		BeanUtils.copyProperties(organizationAuthLogBo, organizationAuthLog);
		organizationAuthLog.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(organizationAuthLog)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateOrganizationAuthLog(OrganizationAuthLogBo organizationAuthLogBo) {
		OrganizationAuthLogDto organizationAuthLog = new OrganizationAuthLogDto();
		BeanUtils.copyProperties(organizationAuthLogBo, organizationAuthLog);
		if(updateById(organizationAuthLog)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public OrganizationAuthLogVo getOrganizationAuthLogByCondition(OrganizationAuthLogConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		OrganizationAuthLogVo vo = organizationAuthLogMapper.getOrganizationAuthLogByCondition(condition);
		if(null != vo) {
			return vo;
		}
		return new OrganizationAuthLogVo();
	}

}