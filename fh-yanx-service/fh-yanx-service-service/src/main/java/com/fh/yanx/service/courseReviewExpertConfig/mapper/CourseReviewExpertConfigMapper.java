package com.fh.yanx.service.courseReviewExpertConfig.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.courseReviewExpertConfig.entity.dto.CourseReviewExpertConfigDto;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo;

/**
 * 课程审核专家配置表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
public interface CourseReviewExpertConfigMapper extends BaseMapper<CourseReviewExpertConfigDto> {

	List<CourseReviewExpertConfigVo> getCourseReviewExpertConfigListByCondition(CourseReviewExpertConfigConditionBo condition);

	CourseReviewExpertConfigVo getCourseReviewExpertConfigByCondition(CourseReviewExpertConfigConditionBo condition);

}
