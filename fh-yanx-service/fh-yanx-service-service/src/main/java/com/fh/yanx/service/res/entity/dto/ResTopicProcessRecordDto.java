package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课题流程记录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_topic_process_record")
public class ResTopicProcessRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "process_record_id", type = IdType.AUTO)
	private Long processRecordId;

	/**
	 * 课题id
	 */
	@TableField("topic_id")
	private Long topicId;

	/**
	 * 过程名称
	 */
	@TableField("process_name")
	private String processName;

	/**
	 * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定
	 */
	@TableField("topic_process")
	private Integer topicProcess;

	/**
	 * 流程提交人
	 */
	@TableField("process_submit_user")
	private String processSubmitUser;

	/**
	 * 流程审批人
	 */
	@TableField("process_verify_user")
	private String processVerifyUser;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
