package com.fh.yanx.service.org.service.impl;

import com.fh.yanx.service.enums.OrganizationApplyType;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyBo;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyDto;
import com.fh.yanx.service.org.mapper.OrganizationApplyMapper;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.org.entity.dto.OrganizationApplyCommunicateDto;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateBo;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyCommunicateVo;
import com.fh.yanx.service.org.service.IOrganizationApplyCommunicateService;
import com.fh.yanx.service.org.mapper.OrganizationApplyCommunicateMapper;
import com.light.core.entity.AjaxResult;
/**
 * 组织申请沟通记录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
@Service
public class OrganizationApplyCommunicateServiceImpl extends ServiceImpl<OrganizationApplyCommunicateMapper, OrganizationApplyCommunicateDto> implements IOrganizationApplyCommunicateService {

    @Resource
    private OrganizationApplyCommunicateMapper organizationApplyCommunicateMapper;
    @Resource
    private OrganizationApplyMapper organizationApplyMapper;

    @Override
    public List<OrganizationApplyCommunicateVo> getOrganizationApplyCommunicateListByCondition(OrganizationApplyCommunicateConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return organizationApplyCommunicateMapper.getOrganizationApplyCommunicateListByCondition(condition);
    }

    @Override
    public AjaxResult addOrganizationApplyCommunicate(OrganizationApplyCommunicateBo organizationApplyCommunicateBo) {
        OrganizationApplyCommunicateDto organizationApplyCommunicate = new OrganizationApplyCommunicateDto();
        BeanUtils.copyProperties(organizationApplyCommunicateBo, organizationApplyCommunicate);
        organizationApplyCommunicate.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(organizationApplyCommunicate)) {
            OrganizationApplyDto organizationApplyDto = new OrganizationApplyDto();
            organizationApplyDto.setOrganizationApplyId(organizationApplyCommunicate.getOrganizationApplyId());
            organizationApplyDto.setType(organizationApplyCommunicate.getType());
            organizationApplyMapper.updateById(organizationApplyDto);
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateOrganizationApplyCommunicate(OrganizationApplyCommunicateBo organizationApplyCommunicateBo) {
        OrganizationApplyCommunicateDto organizationApplyCommunicate = new OrganizationApplyCommunicateDto();
        BeanUtils.copyProperties(organizationApplyCommunicateBo, organizationApplyCommunicate);
        if (updateById(organizationApplyCommunicate)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public OrganizationApplyCommunicateVo getOrganizationApplyCommunicateByCondition(OrganizationApplyCommunicateConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return organizationApplyCommunicateMapper.getOrganizationApplyCommunicateByCondition(condition);
    }

}