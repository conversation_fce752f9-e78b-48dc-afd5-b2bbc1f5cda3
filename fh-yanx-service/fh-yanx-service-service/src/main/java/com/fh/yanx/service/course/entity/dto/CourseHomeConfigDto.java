package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 推荐展示位
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_home_config")
public class CourseHomeConfigDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "home_config_id", type = IdType.AUTO)
	private Long homeConfigId;

	/**
	 * 案例id
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 展示首页的类型，1：大首页，2：校本课程平台首页
	 */
	@TableField("home_type")
	private Integer homeType;

	/**
	 * 校本课程案例类型，冗余字段：0课程案例，1典型案例，2精品课程，3样例课程
	 */
	@TableField("is_excellent")
	private Integer isExcellent;

	/**
	 * 展示顺序
	 */
	@TableField("home_index")
	private Long homeIndex;

	/**
	 * 推荐说明
	 */
	@TableField("recommend_text")
	private String recommendText;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 课程名称
	 */
	@TableField("course_name")
	private String courseName;
}
