package com.fh.yanx.service.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesInfoDto;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseCasesInfoEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo;
import com.fh.yanx.service.course.service.ICourseCasesInfoEditionService;
import com.fh.yanx.service.course.mapper.CourseCasesInfoEditionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 校本课程案例详细信息版本记录接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
@Service
public class CourseCasesInfoEditionServiceImpl extends ServiceImpl<CourseCasesInfoEditionMapper, CourseCasesInfoEditionDto> implements ICourseCasesInfoEditionService {

	@Resource
	private CourseCasesInfoEditionMapper courseCasesInfoEditionMapper;
	
    @Override
	public List<CourseCasesInfoEditionVo> getCourseCasesInfoEditionListByCondition(CourseCasesInfoEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseCasesInfoEditionMapper.getCourseCasesInfoEditionListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseCasesInfoEdition(CourseCasesInfoEditionBo courseCasesInfoEditionBo) {
		CourseCasesInfoEditionDto courseCasesInfoEdition = new CourseCasesInfoEditionDto();
		BeanUtils.copyProperties(courseCasesInfoEditionBo, courseCasesInfoEdition);
		courseCasesInfoEdition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseCasesInfoEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseCasesInfoEdition(CourseCasesInfoEditionBo courseCasesInfoEditionBo) {
		CourseCasesInfoEditionDto courseCasesInfoEdition = new CourseCasesInfoEditionDto();
		BeanUtils.copyProperties(courseCasesInfoEditionBo, courseCasesInfoEdition);
		if(updateById(courseCasesInfoEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CourseCasesInfoEditionVo getCourseCasesInfoEditionByCondition(CourseCasesInfoEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return courseCasesInfoEditionMapper.getCourseCasesInfoEditionByCondition(condition);
	}

	@Override
	public CourseCasesInfoEditionVo infoDetail(Long casesEditionId) {
		CourseCasesInfoEditionVo courseCasesInfoEditionVo = new CourseCasesInfoEditionVo();
		QueryWrapper<CourseCasesInfoEditionDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("cases_edition_id", casesEditionId).eq("is_delete", 0).last("limit 1");
		CourseCasesInfoEditionDto courseCasesInfoEditionDto = courseCasesInfoEditionMapper.selectOne(queryWrapper);
		if (null != courseCasesInfoEditionDto) {
			BeanUtils.copyProperties(courseCasesInfoEditionDto, courseCasesInfoEditionVo);
		}
		return courseCasesInfoEditionVo;
	}

}