package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicCollectDto;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicCollectVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏的课题接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResTopicCollectService extends IService<ResTopicCollectDto> {

    List<ResTopicCollectVo> getResTopicCollectListByCondition(ResTopicCollectConditionBo condition);

	AjaxResult addResTopicCollect(ResTopicCollectBo resTopicCollectBo);

	AjaxResult updateResTopicCollect(ResTopicCollectBo resTopicCollectBo);

	ResTopicCollectVo getDetail(Long id);

	AjaxResult deleteByTopicAndUser(Long topicId,String userOid);

}

