package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicTeacherDto;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;

/**
 * 课题指导老师表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicTeacherMapper extends BaseMapper<ResTopicTeacherDto> {

	List<ResTopicTeacherVo> getResTopicTeacherListByCondition(ResTopicTeacherConditionBo condition);

}
