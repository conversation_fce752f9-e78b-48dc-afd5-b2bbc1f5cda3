package com.fh.yanx.service.pub.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryDto;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryBo;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 融合出版书目录接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface IPubBookDirectoryService extends IService<PubBookDirectoryDto> {

    List<PubBookDirectoryVo> getPubBookDirectoryListByCondition(PubBookDirectoryConditionBo condition);

	AjaxResult addPubBookDirectory(PubBookDirectoryBo pubBookDirectoryBo);

	AjaxResult updatePubBookDirectory(PubBookDirectoryBo pubBookDirectoryBo);

	PubBookDirectoryVo getPubBookDirectoryByCondition(PubBookDirectoryConditionBo condition);

}

