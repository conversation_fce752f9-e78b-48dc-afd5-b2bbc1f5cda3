package com.fh.yanx.service.org.controller;

import com.fh.yanx.service.org.api.OrganizationAuthLogApi;
import com.fh.yanx.service.org.entity.dto.OrganizationAuthLogDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogBo;
import com.fh.yanx.service.org.entity.vo.OrganizationAuthLogVo;
import com.fh.yanx.service.org.service.IOrganizationAuthLogService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 组织认证日志记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
@RestController
@Validated
public class OrganizationAuthLogController implements OrganizationAuthLogApi{
	
    @Autowired
    private IOrganizationAuthLogService organizationAuthLogService;

    /**
     * 查询组织认证日志记录表分页列表
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<OrganizationAuthLogVo>> getOrganizationAuthLogPageListByCondition(@RequestBody OrganizationAuthLogConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<OrganizationAuthLogVo> pageInfo = new PageInfo<>(organizationAuthLogService.getOrganizationAuthLogListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询组织认证日志记录表列表
	 * <AUTHOR>
	 * @date 2023-07-19 15:36:19
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<OrganizationAuthLogVo>> getOrganizationAuthLogListByCondition(@RequestBody OrganizationAuthLogConditionBo condition){
		List<OrganizationAuthLogVo> list = organizationAuthLogService.getOrganizationAuthLogListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增组织认证日志记录表
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addOrganizationAuthLog(@Validated @RequestBody OrganizationAuthLogBo organizationAuthLogBo){
		return organizationAuthLogService.addOrganizationAuthLog(organizationAuthLogBo);
    }

    /**
	 * 修改组织认证日志记录表
	 * @param organizationAuthLogBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateOrganizationAuthLog(@Validated @RequestBody OrganizationAuthLogBo organizationAuthLogBo) {
		if(null == organizationAuthLogBo.getId()) {
			return AjaxResult.fail("组织认证日志记录表id不能为空");
		}
		return organizationAuthLogService.updateOrganizationAuthLog(organizationAuthLogBo);
	}

	/**
	 * 查询组织认证日志记录表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<OrganizationAuthLogVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("组织认证日志记录表id不能为空");
		}
		OrganizationAuthLogConditionBo condition = new OrganizationAuthLogConditionBo();
		condition.setId(id);
		OrganizationAuthLogVo vo = organizationAuthLogService.getOrganizationAuthLogByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除组织认证日志记录表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		OrganizationAuthLogDto organizationAuthLogDto = new OrganizationAuthLogDto();
		organizationAuthLogDto.setId(id);
		organizationAuthLogDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(organizationAuthLogService.updateById(organizationAuthLogDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
