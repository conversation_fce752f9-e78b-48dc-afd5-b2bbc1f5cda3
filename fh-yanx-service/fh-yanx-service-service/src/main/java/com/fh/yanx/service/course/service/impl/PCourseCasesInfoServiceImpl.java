package com.fh.yanx.service.course.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesInfoDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import com.fh.yanx.service.course.mapper.PCourseCasesInfoMapper;
import com.fh.yanx.service.course.service.IPCourseCasesInfoService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 校本课程案例详细信息接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Service
public class PCourseCasesInfoServiceImpl extends ServiceImpl<PCourseCasesInfoMapper, PCourseCasesInfoDto>
    implements IPCourseCasesInfoService {

    @Resource
    private PCourseCasesInfoMapper pCourseCasesInfoMapper;

    @Override
    public List<PCourseCasesInfoVo> getPCourseCasesInfoListByCondition(PCourseCasesInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return pCourseCasesInfoMapper.getPCourseCasesInfoListByCondition(condition);
    }

    @Override
    public AjaxResult addPCourseCasesInfo(PCourseCasesInfoBo pCourseCasesInfoBo) {
        PCourseCasesInfoDto pCourseCasesInfo = new PCourseCasesInfoDto();
        BeanUtils.copyProperties(pCourseCasesInfoBo, pCourseCasesInfo);
        pCourseCasesInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pCourseCasesInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePCourseCasesInfo(PCourseCasesInfoBo pCourseCasesInfoBo) {
        PCourseCasesInfoDto pCourseCasesInfo = new PCourseCasesInfoDto();
        BeanUtils.copyProperties(pCourseCasesInfoBo, pCourseCasesInfo);
        pCourseCasesInfo.setUpdateDate(new Date());
        if (updateById(pCourseCasesInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PCourseCasesInfoVo getPCourseCasesInfoByCondition(PCourseCasesInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PCourseCasesInfoVo vo = pCourseCasesInfoMapper.getPCourseCasesInfoByCondition(condition);
        return vo;
    }

    @Override
    public PCourseCasesInfoVo infoDetail(Long casesId) {
        PCourseCasesInfoVo pCourseCasesInfoVo = new PCourseCasesInfoVo();
        QueryWrapper<PCourseCasesInfoDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("cases_id", casesId).eq("is_delete", 0).last("limit 1");
        PCourseCasesInfoDto pCourseCasesInfoDto = pCourseCasesInfoMapper.selectOne(queryWrapper);
        if (null != pCourseCasesInfoDto) {
            BeanUtils.copyProperties(pCourseCasesInfoDto, pCourseCasesInfoVo);
        }
        return pCourseCasesInfoVo;
    }
}