package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResTopicApprovalApi;
import com.fh.yanx.service.res.entity.dto.ResTopicApprovalDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicApprovalConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalBo;
import com.fh.yanx.service.res.entity.vo.ResTopicApprovalVo;
import com.fh.yanx.service.res.service.IResTopicApprovalService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 课题审批意见表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResTopicApprovalController implements ResTopicApprovalApi {

    @Autowired
    private IResTopicApprovalService resTopicApprovalService;

    /**
     * 查询课题审批意见表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResTopicApprovalVo>> getResTopicApprovalPageListByCondition(@RequestBody ResTopicApprovalConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicApprovalVo> pageInfo = new PageInfo<>(resTopicApprovalService.getResTopicApprovalListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题审批意见表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResTopicApprovalVo>> getResTopicApprovalListByCondition(@RequestBody ResTopicApprovalConditionBo condition) {
        List<ResTopicApprovalVo> list = resTopicApprovalService.getResTopicApprovalListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增课题审批意见表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResTopicApproval(@Validated @RequestBody ResTopicApprovalBo resTopicApprovalBo) {
        return resTopicApprovalService.addResTopicApproval(resTopicApprovalBo);
    }

    /**
     * 修改课题审批意见表
     *
     * @param resTopicApprovalBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResTopicApproval(@Validated @RequestBody ResTopicApprovalBo resTopicApprovalBo) {
        if (null == resTopicApprovalBo.getTopicApprovalId()) {
            return AjaxResult.fail("课题审批意见表id不能为空");
        }
        return resTopicApprovalService.updateResTopicApproval(resTopicApprovalBo);
    }

    /**
     * 查询课题审批意见表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResTopicApprovalVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题审批意见表id不能为空");
        }
        ResTopicApprovalVo vo = resTopicApprovalService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课题审批意见表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicApprovalDto resTopicApprovalDto = new ResTopicApprovalDto();
        resTopicApprovalDto.setTopicApprovalId(id);
        resTopicApprovalDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicApprovalService.updateById(resTopicApprovalDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
