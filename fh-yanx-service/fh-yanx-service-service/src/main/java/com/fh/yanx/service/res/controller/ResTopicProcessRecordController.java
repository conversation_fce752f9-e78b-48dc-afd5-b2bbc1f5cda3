package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResTopicProcessRecordApi;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordConditionBo;
import com.fh.yanx.service.res.entity.dto.ResTopicProcessRecordDto;
import com.fh.yanx.service.res.entity.vo.ResTopicProcessRecordVo;
import com.fh.yanx.service.res.service.IResTopicProcessRecordService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 课题流程记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResTopicProcessRecordController implements ResTopicProcessRecordApi {

    @Autowired
    private IResTopicProcessRecordService resTopicProcessRecordService;

    /**
     * 查询课题流程记录分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResTopicProcessRecordVo>> getResTopicProcessRecordPageListByCondition(@RequestBody ResTopicProcessRecordConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicProcessRecordVo> pageInfo = new PageInfo<>(resTopicProcessRecordService.getResTopicProcessRecordListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题流程记录列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResTopicProcessRecordVo>> getResTopicProcessRecordListByCondition(@RequestBody ResTopicProcessRecordConditionBo condition) {
        List<ResTopicProcessRecordVo> list = resTopicProcessRecordService.getResTopicProcessRecordListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增课题流程记录
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResTopicProcessRecord(@Validated @RequestBody ResTopicProcessRecordBo resTopicProcessRecordBo) {
        return resTopicProcessRecordService.addResTopicProcessRecord(resTopicProcessRecordBo);
    }

    /**
     * 修改课题流程记录
     *
     * @param resTopicProcessRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResTopicProcessRecord(@Validated @RequestBody ResTopicProcessRecordBo resTopicProcessRecordBo) {
        if (null == resTopicProcessRecordBo.getProcessRecordId()) {
            return AjaxResult.fail("课题流程记录id不能为空");
        }
        return resTopicProcessRecordService.updateResTopicProcessRecord(resTopicProcessRecordBo);
    }

    /**
     * 查询课题流程记录详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResTopicProcessRecordVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题流程记录id不能为空");
        }
        ResTopicProcessRecordVo vo = resTopicProcessRecordService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课题流程记录
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicProcessRecordDto resTopicProcessRecordDto = new ResTopicProcessRecordDto();
        resTopicProcessRecordDto.setProcessRecordId(id);
        resTopicProcessRecordDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicProcessRecordService.updateById(resTopicProcessRecordDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
