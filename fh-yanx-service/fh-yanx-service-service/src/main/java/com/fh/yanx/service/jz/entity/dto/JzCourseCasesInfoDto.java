package com.fh.yanx.service.jz.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 金中-校本课程案例详细信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("jz_course_cases_info")
public class JzCourseCasesInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 背景
	 */
	@TableField("background")
	private String background;

	/**
	 * 目标
	 */
	@TableField("goal")
	private String goal;

	/**
	 * 内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 评价
	 */
	@TableField("eval")
	private String eval;

	/**
	 * 实施信息
	 */
	@TableField("operation_info")
	private String operationInfo;

	/**
	 * 校本课程建设经验
	 */
	@TableField("exp")
	private String exp;

	/**
	 * 校本课程建设成效
	 */
	@TableField("effect")
	private String effect;

	/**
	 * 问题困难
	 */
	@TableField("problem")
	private String problem;

	/**
	 * 课程框架图
	 */
	@TableField("structure")
	private String structure;

	/**
	 * 实施案例
	 */
	@TableField("teacher_case_info")
	private String teacherCaseInfo;

	/**
	 * 
	 */
	@TableField("teacher_case_name")
	private String teacherCaseName;

	/**
	 * 0-未删除 1-已删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 
	 */
	@TableField("create_date")
	private Date createDate;

	/**
	 * 
	 */
	@TableField("update_date")
	private Date updateDate;

}
