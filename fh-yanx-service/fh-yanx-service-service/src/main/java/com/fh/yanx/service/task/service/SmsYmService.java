package com.fh.yanx.service.task.service;

import com.dtflys.forest.Forest;
import com.dtflys.forest.utils.StringUtils;
import com.fh.yanx.service.task.entity.bo.SmsYmBo;
import com.fh.yanx.service.utils.MD5Sign;
import com.light.core.entity.AjaxResult;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 短信service(通过http调用源梦的短信服务)
 * 
 * <AUTHOR>
 * @date 2023/6/20 10:48
 */
@Service
public class SmsYmService {

    /**
     * 编程式调用通过forest调用源梦的发短信接口，返回code
     *
     * @param phone the phone
     * @return ajax result
     */
    public AjaxResult<String> send(String phone) {
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.fail("手机号码不允许为空");
        }
        long timeStamp = System.currentTimeMillis();
        String appid = "os2b268vonxjy53g";
        String secret = "5HItP5SXv6JCiUOUknuFDduGiYSVTmMs";

        // sign计算
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("appid", appid);
        paramMap.put("timestamp", String.valueOf(timeStamp));
        paramMap.put("phone", phone.trim());
        final String sign = MD5Sign.sign(paramMap, secret);

        // 请求头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("appid", appid);
        headMap.put("sign", sign);
        headMap.put("timestamp", String.valueOf(timeStamp));

        // 请求体参数
        SmsYmBo smsYmBo = new SmsYmBo();
        smsYmBo.setPhone(phone);

        // 发起请求并返回
        AjaxResult execute = Forest.post("http://api.coursechina.cn/open-api/sms/sendSms").contentFormUrlEncoded()
            .addHeader(headMap).contentTypeJson().addBody(smsYmBo).execute(AjaxResult.class);
        return execute;
    }

    /**
     * 编程式调用通过forest调用源梦的发短信接口，忽略返回值，仅以成功或失败判断
     *
     * @param phone the phone
     * @return ajax result
     */
    public AjaxResult<Integer> sendContent(String phone, String content) {
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.fail("手机号码不允许为空");
        }
        if(StringUtils.isBlank(content)){
            return AjaxResult.fail("短信内容不允许为空");
        }
        long timeStamp = System.currentTimeMillis();
        String appid = "os2b268vonxjy53g";
        String secret = "5HItP5SXv6JCiUOUknuFDduGiYSVTmMs";

        // sign计算
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("appid", appid);
        paramMap.put("timestamp", String.valueOf(timeStamp));
        paramMap.put("phone", phone.trim());
        paramMap.put("content", content.trim());
        final String sign = MD5Sign.sign(paramMap, secret);

        // 请求头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("appid", appid);
        headMap.put("sign", sign);
        headMap.put("timestamp", String.valueOf(timeStamp));

        // 请求体参数
        SmsYmBo smsYmBo = new SmsYmBo();
        smsYmBo.setPhone(phone);
        smsYmBo.setContent(content);

        // 发起请求并返回
        AjaxResult execute = Forest.post("http://api.coursechina.cn/open-api/sms/sendPromotionSms").contentFormUrlEncoded()
            .addHeader(headMap).contentTypeJson().addBody(smsYmBo).execute(AjaxResult.class);
        return execute;
    }
}
