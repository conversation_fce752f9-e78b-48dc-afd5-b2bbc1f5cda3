package com.fh.yanx.service.pub.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.pub.entity.bo.PubBookBo;
import com.fh.yanx.service.pub.entity.bo.PubBookConditionBo;
import com.fh.yanx.service.pub.entity.dto.PubBookDto;
import com.fh.yanx.service.pub.entity.vo.PubBookVo;
import com.fh.yanx.service.pub.mapper.PubBookMapper;
import com.fh.yanx.service.pub.service.IPubBookService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 融合出版书接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Service
public class PubBookServiceImpl extends ServiceImpl<PubBookMapper, PubBookDto> implements IPubBookService {

    @Resource
    private PubBookMapper pubBookMapper;

    @Override
    public List<PubBookVo> getPubBookListByCondition(PubBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return pubBookMapper.getPubBookListByCondition(condition);
    }

    @Override
    public AjaxResult addPubBook(PubBookBo pubBookBo) {
        PubBookDto pubBook = new PubBookDto();
        BeanUtils.copyProperties(pubBookBo, pubBook);
        pubBook.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pubBook)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePubBook(PubBookBo pubBookBo) {
        PubBookDto pubBook = new PubBookDto();
        BeanUtils.copyProperties(pubBookBo, pubBook);
        if (updateById(pubBook)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PubBookVo getPubBookByCondition(PubBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PubBookVo vo = pubBookMapper.getPubBookByCondition(condition);
        return vo;
    }

    @Override
    public void incrPubBookReadTimes(Long bookId) {
        pubBookMapper.incrPubBookReadTimes(bookId);
    }
}