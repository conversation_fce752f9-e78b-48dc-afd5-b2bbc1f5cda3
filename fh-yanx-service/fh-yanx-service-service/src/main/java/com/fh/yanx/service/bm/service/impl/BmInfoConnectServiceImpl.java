package com.fh.yanx.service.bm.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.service.IBmInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoConnectDto;
import com.fh.yanx.service.bm.entity.vo.BmInfoConnectVo;
import com.fh.yanx.service.bm.mapper.BmInfoConnectMapper;
import com.fh.yanx.service.bm.service.IBmInfoConnectService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报名活动沟通确认表（本表只有新增记录）接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
@Service
public class BmInfoConnectServiceImpl extends ServiceImpl<BmInfoConnectMapper, BmInfoConnectDto>
    implements IBmInfoConnectService {

    @Resource
    private BmInfoConnectMapper bmInfoConnectMapper;
    @Resource
    private IBmInfoConnectService bmInfoConnectService;
    @Resource
    private IBmInfoService bmInfoService;

    @Override
    public List<BmInfoConnectVo> getBmInfoConnectListByCondition(BmInfoConnectConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return bmInfoConnectMapper.getBmInfoConnectListByCondition(condition);
    }

    @Override
    public AjaxResult addBmInfoConnect(BmInfoConnectBo bmInfoConnectBo) {
        BmInfoConnectDto bmInfoConnect = new BmInfoConnectDto();
        BeanUtils.copyProperties(bmInfoConnectBo, bmInfoConnect);
        bmInfoConnect.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(bmInfoConnect)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBmInfoConnect(BmInfoConnectBo bmInfoConnectBo) {
        BmInfoConnectDto bmInfoConnect = new BmInfoConnectDto();
        BeanUtils.copyProperties(bmInfoConnectBo, bmInfoConnect);
        if (updateById(bmInfoConnect)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public BmInfoConnectVo getBmInfoConnectByCondition(BmInfoConnectConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return bmInfoConnectMapper.getBmInfoConnectByCondition(condition);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult addBmInfoConnectWithUpdateBmInfo(BmInfoConnectBo bmInfoConnectBo) {
        // 新增BmInfoConnect
        AjaxResult ajaxResult = bmInfoConnectService.addBmInfoConnect(bmInfoConnectBo);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        Long infoId = bmInfoConnectBo.getInfoId();

        // 更新BmInfo
        BmInfoBo bmInfoBo = new BmInfoBo();
        bmInfoBo.setInfoId(infoId);
        bmInfoBo.setSubmitType(bmInfoConnectBo.getSubmitType());
        bmInfoBo.setChangeOrderState(true);
        AjaxResult ajaxResultBmInfo = bmInfoService.updateBmInfo(bmInfoBo);
        return ajaxResultBmInfo;

    }
}