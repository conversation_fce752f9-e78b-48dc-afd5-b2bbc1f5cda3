package com.fh.yanx.service.activ.controller;

import com.fh.yanx.service.activ.api.ActivInfoIntroduceApi;
import com.fh.yanx.service.activ.entity.dto.ActivInfoIntroduceDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo;
import com.fh.yanx.service.activ.service.IActivInfoIntroduceService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 新时代文化校园活动介绍表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
@RestController
@Validated
public class ActivInfoIntroduceController implements ActivInfoIntroduceApi{
	
    @Autowired
    private IActivInfoIntroduceService activInfoIntroduceService;

    /**
     * 查询新时代文化校园活动介绍表分页列表
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ActivInfoIntroduceVo>> getActivInfoIntroducePageListByCondition(@RequestBody ActivInfoIntroduceConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ActivInfoIntroduceVo> pageInfo = new PageInfo<>(activInfoIntroduceService.getActivInfoIntroduceListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询新时代文化校园活动介绍表列表
	 * <AUTHOR>
	 * @date 2023-07-04 10:33:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ActivInfoIntroduceVo>> getActivInfoIntroduceListByCondition(@RequestBody ActivInfoIntroduceConditionBo condition){
		List<ActivInfoIntroduceVo> list = activInfoIntroduceService.getActivInfoIntroduceListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增新时代文化校园活动介绍表
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addActivInfoIntroduce(@Validated @RequestBody ActivInfoIntroduceBo activInfoIntroduceBo){
		return activInfoIntroduceService.addActivInfoIntroduce(activInfoIntroduceBo);
    }

    /**
	 * 修改新时代文化校园活动介绍表
	 * @param activInfoIntroduceBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateActivInfoIntroduce(@Validated @RequestBody ActivInfoIntroduceBo activInfoIntroduceBo) {
		if(null == activInfoIntroduceBo.getIntroduceId()) {
			return AjaxResult.fail("新时代文化校园活动介绍表id不能为空");
		}
		return activInfoIntroduceService.updateActivInfoIntroduce(activInfoIntroduceBo);
	}

	/**
	 * 查询新时代文化校园活动介绍表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ActivInfoIntroduceVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("新时代文化校园活动介绍表id不能为空");
		}
		ActivInfoIntroduceConditionBo condition = new ActivInfoIntroduceConditionBo();
		condition.setIntroduceId(id);
		ActivInfoIntroduceVo vo = activInfoIntroduceService.getActivInfoIntroduceByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除新时代文化校园活动介绍表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ActivInfoIntroduceDto activInfoIntroduceDto = new ActivInfoIntroduceDto();
		activInfoIntroduceDto.setIntroduceId(id);
		activInfoIntroduceDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(activInfoIntroduceService.updateById(activInfoIntroduceDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
