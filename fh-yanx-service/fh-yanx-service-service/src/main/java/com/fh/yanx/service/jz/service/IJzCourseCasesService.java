package com.fh.yanx.service.jz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 金中-校本课程案例接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface IJzCourseCasesService extends IService<JzCourseCasesDto> {

    List<JzCourseCasesVo> getJzCourseCasesListByCondition(JzCourseCasesConditionBo condition);

	AjaxResult addJzCourseCases(JzCourseCasesBo jzCourseCasesBo);

	AjaxResult updateJzCourseCases(JzCourseCasesBo jzCourseCasesBo);

	JzCourseCasesVo getJzCourseCasesByCondition(JzCourseCasesConditionBo condition);

	/**
	 * 查询首页列表数据
	 *
	 * @param condition the condition
	 * @return home list
	 */
	List<JzCourseCasesVo> getHomeList(JzCourseCasesConditionBo condition);

	/**
	 * 查询首页课程详情
	 *
	 * @param casesId
	 * @return
	 */
	JzCourseCasesVo getHomeDetail(Long casesId);
}

