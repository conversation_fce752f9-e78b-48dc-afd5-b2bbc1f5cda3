package com.fh.yanx.service.activ.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代文化校园活动内容表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activ_info_record")
public class ActivInfoRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "record_id", type = IdType.AUTO)
	private Long recordId;

	/**
	 * FK新时代文化校园活动信息表的id
	 */
	@TableField("activ_id")
	private Long activId;

	/**
	 * 内容类型：1标题，2图文
	 */
	@TableField("record_type")
	private Integer recordType;

	/**
	 * 顺序
	 */
	@TableField("record_sort")
	private Long recordSort;

	/**
	 * 介绍标题
	 */
	@TableField("record_title")
	private String recordTitle;

	/**
	 * 内容图文
	 */
	@TableField("record_content")
	private String recordContent;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
