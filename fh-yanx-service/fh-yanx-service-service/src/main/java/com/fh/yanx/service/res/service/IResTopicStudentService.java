package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicStudentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课题组成员表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface IResTopicStudentService extends IService<ResTopicStudentDto> {

    List<ResTopicStudentVo> getResTopicStudentListByCondition(ResTopicStudentConditionBo condition);

	List<String> getResTopicStudentOidListByTopicId(Long topicId);

	AjaxResult addResTopicStudent(ResTopicStudentBo resTopicStudentBo);

	AjaxResult updateResTopicStudent(ResTopicStudentBo resTopicStudentBo);

	ResTopicStudentVo getDetail(Long id);

}

