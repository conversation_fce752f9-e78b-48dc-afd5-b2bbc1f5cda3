package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResInformationDto;
import com.fh.yanx.service.res.entity.bo.ResInformationConditionBo;
import com.fh.yanx.service.res.entity.vo.ResInformationVo;

/**
 * 资讯Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
public interface ResInformationMapper extends BaseMapper<ResInformationDto> {

	List<ResInformationVo> getResInformationListByCondition(ResInformationConditionBo condition);

}
