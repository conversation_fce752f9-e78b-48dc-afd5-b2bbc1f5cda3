package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResStationImgDto;
import com.fh.yanx.service.res.entity.bo.ResStationImgConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationImgBo;
import com.fh.yanx.service.res.entity.vo.ResStationImgVo;
import com.fh.yanx.service.res.service.IResStationImgService;
import com.fh.yanx.service.res.mapper.ResStationImgMapper;
import com.light.core.entity.AjaxResult;
/**
 * 工作站介绍图片表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResStationImgServiceImpl extends ServiceImpl<ResStationImgMapper, ResStationImgDto> implements IResStationImgService {

	@Resource
	private ResStationImgMapper resStationImgMapper;
	
    @Override
	public List<ResStationImgVo> getResStationImgListByCondition(ResStationImgConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resStationImgMapper.getResStationImgListByCondition(condition);
	}

	@Override
	public AjaxResult addResStationImg(ResStationImgBo resStationImgBo) {
		ResStationImgDto resStationImg = new ResStationImgDto();
		BeanUtils.copyProperties(resStationImgBo, resStationImg);
		resStationImg.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resStationImg)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResStationImg(ResStationImgBo resStationImgBo) {
		ResStationImgDto resStationImg = new ResStationImgDto();
		BeanUtils.copyProperties(resStationImgBo, resStationImg);
		if(updateById(resStationImg)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResStationImgVo getDetail(Long id) {
		ResStationImgConditionBo condition = new ResStationImgConditionBo();
		condition.setId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResStationImgVo> list = resStationImgMapper.getResStationImgListByCondition(condition);
		ResStationImgVo vo = new ResStationImgVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}