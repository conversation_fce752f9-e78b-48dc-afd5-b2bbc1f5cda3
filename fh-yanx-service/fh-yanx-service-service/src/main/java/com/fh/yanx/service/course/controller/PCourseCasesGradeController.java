package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.PCourseCasesGradeApi;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesGradeDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesGradeVo;
import com.fh.yanx.service.course.service.IPCourseCasesGradeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 校本课程案例年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@RestController
@Validated
public class PCourseCasesGradeController implements PCourseCasesGradeApi{
	
    @Autowired
    private IPCourseCasesGradeService pCourseCasesGradeService;

    /**
     * 查询校本课程案例年级分页列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PageInfo<PCourseCasesGradeVo>> getPCourseCasesGradePageListByCondition(@RequestBody PCourseCasesGradeConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<PCourseCasesGradeVo> pageInfo = new PageInfo<>(pCourseCasesGradeService.getPCourseCasesGradeListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询校本课程案例年级列表
	 * <AUTHOR>
	 * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult<List<PCourseCasesGradeVo>> getPCourseCasesGradeListByCondition(@RequestBody PCourseCasesGradeConditionBo condition){
		List<PCourseCasesGradeVo> list = pCourseCasesGradeService.getPCourseCasesGradeListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增校本课程案例年级
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
	@Override
    public AjaxResult addPCourseCasesGrade(@Validated @RequestBody PCourseCasesGradeBo pCourseCasesGradeBo){
		return pCourseCasesGradeService.addPCourseCasesGrade(pCourseCasesGradeBo);
    }

    /**
	 * 修改校本课程案例年级
	 * @param pCourseCasesGradeBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult updatePCourseCasesGrade(@Validated @RequestBody PCourseCasesGradeBo pCourseCasesGradeBo) {
		if(null == pCourseCasesGradeBo.getId()) {
			return AjaxResult.fail("校本课程案例年级id不能为空");
		}
		return pCourseCasesGradeService.updatePCourseCasesGrade(pCourseCasesGradeBo);
	}

	/**
	 * 查询校本课程案例年级详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult<PCourseCasesGradeVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("校本课程案例年级id不能为空");
		}
		PCourseCasesGradeConditionBo condition = new PCourseCasesGradeConditionBo();
		condition.setId(id);
		PCourseCasesGradeVo vo = pCourseCasesGradeService.getPCourseCasesGradeByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除校本课程案例年级
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		PCourseCasesGradeDto pCourseCasesGradeDto = new PCourseCasesGradeDto();
		pCourseCasesGradeDto.setId(id);
		pCourseCasesGradeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(pCourseCasesGradeService.updateById(pCourseCasesGradeDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
