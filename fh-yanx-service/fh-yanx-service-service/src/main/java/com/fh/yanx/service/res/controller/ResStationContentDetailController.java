package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResStationContentDetailApi;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailConditionBo;
import com.fh.yanx.service.res.entity.dto.ResStationContentDetailDto;
import com.fh.yanx.service.res.entity.vo.ResStationContentDetailVo;
import com.fh.yanx.service.res.service.IResStationContentDetailService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工作站具体内容详情
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResStationContentDetailController implements ResStationContentDetailApi {

    @Autowired
    private IResStationContentDetailService resStationContentDetailService;

    /**
     * 查询工作站具体内容详情分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResStationContentDetailVo>> getResStationContentDetailPageListByCondition(@RequestBody ResStationContentDetailConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResStationContentDetailVo> pageInfo = new PageInfo<>(resStationContentDetailService.getResStationContentDetailListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询工作站具体内容详情列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResStationContentDetailVo>> getResStationContentDetailListByCondition(@RequestBody ResStationContentDetailConditionBo condition) {
        List<ResStationContentDetailVo> list = resStationContentDetailService.getResStationContentDetailListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增工作站具体内容详情
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResStationContentDetail(@Validated @RequestBody ResStationContentDetailBo resStationContentDetailBo) {
        return resStationContentDetailService.addResStationContentDetail(resStationContentDetailBo);
    }

    /**
     * 修改工作站具体内容详情
     *
     * @param resStationContentDetailBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResStationContentDetail(@Validated @RequestBody ResStationContentDetailBo resStationContentDetailBo) {
        if (null == resStationContentDetailBo.getStationContentDetailId()) {
            return AjaxResult.fail("工作站具体内容详情id不能为空");
        }
        return resStationContentDetailService.updateResStationContentDetail(resStationContentDetailBo);
    }

    /**
     * 查询工作站具体内容详情详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResStationContentDetailVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("工作站具体内容详情id不能为空");
        }
        ResStationContentDetailVo vo = resStationContentDetailService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除工作站具体内容详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResStationContentDetailDto resStationContentDetailDto = new ResStationContentDetailDto();
        resStationContentDetailDto.setStationContentDetailId(id);
        resStationContentDetailDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resStationContentDetailService.updateById(resStationContentDetailDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
