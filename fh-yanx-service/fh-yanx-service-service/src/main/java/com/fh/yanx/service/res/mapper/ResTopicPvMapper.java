package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicPvDto;
import com.fh.yanx.service.res.entity.bo.ResTopicPvConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicPvVo;

/**
 * 课题pv记录Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicPvMapper extends BaseMapper<ResTopicPvDto> {

	List<ResTopicPvVo> getResTopicPvListByCondition(ResTopicPvConditionBo condition);

}
