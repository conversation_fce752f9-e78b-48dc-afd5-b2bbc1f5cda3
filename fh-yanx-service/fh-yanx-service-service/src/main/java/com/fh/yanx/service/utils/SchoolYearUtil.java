package com.fh.yanx.service.utils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 学年util
 */
public class SchoolYearUtil {
    /**
     * 学年-月
     */
    public static final int SCHOOL_YEAR_MONTH = 8;
    /**
     * 学年-日
     */
    public static final int SCHOOL_YEAR_DAY = 20;

    // 教育级别value列表
    public final static List<String> eduLevelList = Arrays.asList("01", "02", "03", "04", "05", "06", "07");

    // 学段value列表
    public final static List<String> sectionList = Arrays.asList("01", "02", "03");
    // 年级value值列表
    private final static List<String> gradeList = Arrays.asList("0", "2101", "2102", "2103", "2104", "2105", "2106",
            "3101", "3102", "3103", "4101", "4102", "4103");
    // 年级和lable的映射map
    public final static Map<String, String> gradeMap = new LinkedHashMap() {
        {
            put("2101", "一年级");
            put("2102", "二年级");
            put("2103", "三年级");
            put("2104", "四年级");
            put("2105", "五年级");
            put("2106", "六年级");
            put("3101", "七年级");
            put("3102", "八年级");
            put("3103", "九年级");
            put("4101", "高一");
            put("4102", "高二");
            put("4103", "高三");
        }
    };

    // 年级与所处index的映射
    public final static Map<String, Integer> gradeIndexMap = new HashMap<String, Integer>() {
        {
            put("2101", 1);
            put("2102", 2);
            put("2103", 3);
            put("2104", 4);
            put("2105", 5);
            put("2106", 6);
            put("3101", 7);
            put("3102", 8);
            put("3103", 9);
            put("4101", 10);
            put("4102", 11);
            put("4103", 12);
        }
    };

    // 学段和lable的映射map
    public final static Map<String, String> sectionListMap = new HashMap<String, String>() {
        {
            put("01", "小学");
            put("02", "初中");
            put("03", "高中");
        }
    };

    // 教育级别和lable的映射map
    public final static Map<String, String> eduLevelListMap = new LinkedHashMap() {
        {
            put("01", "小学");
            put("02", "初中");
            put("03", "九年一贯制学校");
            put("04", "高中");
            put("05", "十二年一贯制学校");
            put("06", "完全中学");
            put("07", "中等职业学校");
        }
    };

    // 教育级别和lable的映射map
    public final static Map<String, List<String>> eduLevelSectionListMap = new LinkedHashMap() {
        {
            put("01", new ArrayList() {
                {
                    add("01");
                }
            });
            put("02", new ArrayList() {
                {
                    add("02");
                }
            });
            put("03", new ArrayList() {
                {
                    add("01");
                    add("02");
                }
            });
            put("04", new ArrayList() {
                {
                    add("03");
                }
            });
            put("05", new ArrayList() {
                {
                    add("01");
                    add("02");
                    add("03");
                }
            });
            put("06", new ArrayList() {
                {
                    add("02");
                    add("03");
                }
            });
            put("07", new ArrayList() {
                {
                    add("02");
                }
            });
        }
    };

    /**
     * 学年转换
     *
     * @param year the year
     * @return string string
     * <AUTHOR>
     * @date 2022 -04-11 17:46:42
     */
    public static String transfomYear(String year) {
        if (StringUtils.isNotBlank(year) && -1 != year.indexOf("-")) {
            year = year.substring(0, year.indexOf("-"));
        }
        return year;
    }

    /**
     * 学年转换为年的Integer类型，举例：2016-2017返回2016
     *
     * @param year 2016-2017
     * @return 2016 integer
     * <AUTHOR>
     * @date 2022 -04-11 17:46:42
     * @create 2016 -11-2下午07:54:29
     */
    public static Integer transfomYearInteger(String year) {
        if (StringUtils.isNotBlank(year) && -1 != year.indexOf("-")) {

            year = year.substring(0, year.indexOf("-"));

        }
        return StringUtils.isBlank(year) ? null : Integer.parseInt(year);

    }

    /**
     * 将Integer学年集合转换为String学年集合
     *
     * @param years Integer学年集合
     * @return String学年集合 list
     * <AUTHOR>
     * @date 2022 -04-11 17:46:42
     */
    public static List<String> transfomYearListString(List<Integer> years) {

        List<String> result = new ArrayList<String>();

        if (CollectionUtils.isEmpty(years)) {
            return result;
        }
        for (Integer year : years) {

            result.add(getChinaSchoolYear(year));

        }

        return result;
    }

    /**
     * 根据年获取学年 year -> year-year+1
     *
     * @param year the year
     * @return china school year
     * @updateauthor sunqingbiao
     */
    public static String getChinaSchoolYear(Integer year) {

        if (year == null) {
            return "";
        }
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.YEAR, year + 1);
        return String.valueOf(year) + "-" + cal.get(Calendar.YEAR);
    }

    /**
     * 根据年获取学年 year -> year-year+1
     *
     * @param year the year
     * @return china school year
     * @updateauthor sunqingbiao
     * @updatedate 2017 -03-24 09:07:58
     */
    public static String getChinaSchoolYear(String year) {
        return getChinaSchoolYear(Integer.valueOf(year));
    }

    /**
     * 根据年获取学年 year -> year-year+1
     *
     * @param year the year
     * @return china school year
     * @updateauthor sunqingbiao
     * @updatedate 2017 -03-24 09:07:58
     */
    public static List<String> getChinaSchoolYear(List<String> year) {
        if (CollectionUtils.isEmpty(year)) {
            return new ArrayList<String>();
        }

        List<String> result = new ArrayList<String>();
        for (String s : year) {
            result.add(getChinaSchoolYear(s));
        }
        return result;
    }

    /**
     * 获取当前学年，格式为 1990-1991
     *
     * @return current school year
     */
    @SuppressWarnings("deprecation")
    public static String getCurrentSchoolYear() {
        Calendar cal = Calendar.getInstance();
        Integer year = cal.get(Calendar.YEAR);
        Integer month = cal.get(Calendar.MONTH);
        Integer date = cal.get(Calendar.DATE);
        Date now = new Date(year, month, date);
        Date school = new Date(year, SCHOOL_YEAR_MONTH - 1, SCHOOL_YEAR_DAY);
        if (now.getTime() >= school.getTime()) {
            year = year + 1;
        }
        return String.valueOf(year - 1) + "-" + (year);
    }

    /**
     * 获取当前学年 Integer型
     *
     * @return 当前学年 current integer school year
     */
    public static Integer getCurrentIntegerSchoolYear() {
        return SchoolYearUtil.transfomYearInteger(SchoolYearUtil.getCurrentSchoolYear());
    }

    /**
     * 获取当前学年 Integer型
     *
     * @return 当前学年 current integer school year
     */
    public static Integer getCurrentYear() {
        Calendar cal = Calendar.getInstance();
        Integer year = cal.get(Calendar.YEAR);
        return year;
    }

    /**
     * 按学年规则，获取 当前学年、下一学年
     *
     * @return 当前学年 、下一学年集合
     * <AUTHOR>
     * @date 2018 -09-30 09:43:21
     */
    // @SuppressWarnings("deprecation")
    //
    // public static List<String> getCurrentSchoolYearAndNext() {
    // List<String> result = new ArrayList<String>();
    // Calendar cal = Calendar.getInstance();
    // Integer year = cal.get(Calendar.YEAR);
    // Integer month = cal.get(Calendar.MONTH);
    // Integer date = cal.get(Calendar.DATE);
    // Date now = new Date(year, month, date);
    // Date school = new Date(year, ConstantsConfig.SCHOOL_YEAR_MONTH - 1, ConstantsConfig.SCHOOL_YEAR_DAY);
    // if (now.getTime() >= school.getTime()) {
    // year = year + 1;
    // }
    // result.add(String.valueOf(year - 1) + "-" + (year));
    // result.add(String.valueOf(year) + "-" + (year + 1));
    // return result;
    // }
    //

    /**
     * 大陆，获得当前年（按照学年规则）例如2014-2015学年则返回2014
     *
     * @return current year for school year
     */
    // public static String getCurrentYearForSchoolYear() {
    // return transfomYear(getCurrentSchoolYear());
    // }

    /**
     * 获取学年列表（下一学年，当前学年，下一学年）s：2014-2015 -> [2015-2016, 2014-2015, 2013-2014]
     *
     * @param currentSchoolYear 2014-2015
     * @return list years
     */
    public static List<String> getYears(String currentSchoolYear) {
        List<String> resultList = new ArrayList<String>();
        if (StringUtils.isBlank(currentSchoolYear)) {
            return resultList;
        }
        String[] yearArray = currentSchoolYear.split("-");
        if (yearArray == null || yearArray.length != 2) {
            return resultList;
        }
        try {
            resultList.add((Integer.valueOf(yearArray[0]) + 1) + "-" + (Integer.valueOf(yearArray[1]) + 1));
            resultList.add((Integer.valueOf(yearArray[0])) + "-" + (Integer.valueOf(yearArray[1])));
            // resultList.add((Integer.valueOf(yearArray[0]) - 1) + "-" + (Integer.valueOf(yearArray[1]) - 1));
            return resultList;

        } catch (Exception e) {
            return resultList;
        }
    }

    /**
     * 根据年级和学年获取入学年份
     *
     * @param grade 年级
     * @param schoolYear 学年，格式为 xxxx-yyyy
     * @return year by grade and school year
     */
    public static Integer getYearByGradeAndSchoolYear(String grade, String schoolYear) {
        if (StringUtils.isBlank(schoolYear) || StringUtils.isBlank(grade)) {
            return null;
        }

        Integer schoolYearInteger = transfomYearInteger(schoolYear);
        Integer year = (schoolYearInteger + 1) - gradeIndexMap.get(grade);
        return year;
    }

    /**
     * 根据年级和入学年份获取学年
     *
     * @param grade 年级
     * @param year 入学年份
     * @return schoolYear 学年，格式为 xxxx-yyyy
     */
    public static String getSchoolYearByGradeAndYear(String grade, Integer year) {
        if (StringUtils.isBlank(grade) || null == year) {
            return null;
        }

        int schoolYearInteger = gradeIndexMap.get(grade) + year;
        String schoolYear = String.valueOf(schoolYearInteger - 1).concat("-").concat(String.valueOf(schoolYearInteger));
        return schoolYear;
    }

    /**
     * 根据学年获取一年级对应的入学年份
     *
     * @param schoolYear 学年，格式为 xxxx-yyyy
     * @param section the section
     * @return year by grade and school year
     */
    public static Integer getYearBySchoolYearOfNext(String schoolYear, String section) {
        if (StringUtils.isBlank(schoolYear)) {
            return null;
        }

        Integer schoolYearInteger = transfomYearInteger(schoolYear);

        int index = 1;
        if (StringUtils.isBlank(section) || section.equals(sectionList.get(0))) {
            index = 1;
        } else if (section.equals(sectionList.get(1))) {
            index = 7;
        } else if (section.equals(sectionList.get(2))) {
            index = 10;
        }
        Integer year = (schoolYearInteger + 1) - gradeIndexMap.get(gradeList.get(index));
        return year;
    }

    // /**
    // * 获取一年级的grade
    // *
    // * @param section the section
    // * @return grade of one
    // */
    // public static String getGradeOfOne(String section) {
    // return (String) SchoolYearUtil.gradeList.get(ConstantsInteger.NUM_1);
    // }

}
