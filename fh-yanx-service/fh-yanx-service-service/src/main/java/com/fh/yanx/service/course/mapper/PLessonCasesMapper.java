package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.PLessonCasesDto;
import com.fh.yanx.service.course.entity.bo.PLessonCasesConditionBo;
import com.fh.yanx.service.course.entity.vo.PLessonCasesVo;

/**
 * 课例表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PLessonCasesMapper extends BaseMapper<PLessonCasesDto> {

	List<PLessonCasesVo> getPLessonCasesListByCondition(PLessonCasesConditionBo condition);

	PLessonCasesVo getPLessonCasesByCondition(PLessonCasesConditionBo condition);

}
