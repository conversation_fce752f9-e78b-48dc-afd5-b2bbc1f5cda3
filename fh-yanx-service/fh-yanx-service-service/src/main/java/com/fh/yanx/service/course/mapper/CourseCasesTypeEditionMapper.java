package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseCasesTypeEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesTypeEditionVo;

/**
 * 校本课程案例类型版本记录Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
public interface CourseCasesTypeEditionMapper extends BaseMapper<CourseCasesTypeEditionDto> {

	List<CourseCasesTypeEditionVo> getCourseCasesTypeEditionListByCondition(CourseCasesTypeEditionConditionBo condition);

	CourseCasesTypeEditionVo getCourseCasesTypeEditionByCondition(CourseCasesTypeEditionConditionBo condition);

}
