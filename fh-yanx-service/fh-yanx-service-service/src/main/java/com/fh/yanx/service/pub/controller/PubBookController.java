package com.fh.yanx.service.pub.controller;

import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.pub.api.PubBookApi;
import com.fh.yanx.service.pub.entity.bo.PubBookBo;
import com.fh.yanx.service.pub.entity.bo.PubBookConditionBo;
import com.fh.yanx.service.pub.entity.dto.PubBookDto;
import com.fh.yanx.service.pub.entity.vo.PubBookVo;
import com.fh.yanx.service.pub.service.IPubBookService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 融合出版书
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Slf4j
@RestController
@Validated
public class PubBookController implements PubBookApi {

    @Autowired
    private IPubBookService pubBookService;

    /**
     * 查询融合出版书分页列表
     * 
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult<PageInfo<PubBookVo>> getPubBookPageListByCondition(@RequestBody PubBookConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<PubBookVo> pageInfo = new PageInfo<>(pubBookService.getPubBookListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询融合出版书列表
     * 
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult<List<PubBookVo>> getPubBookListByCondition(@RequestBody PubBookConditionBo condition) {
        List<PubBookVo> list = pubBookService.getPubBookListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增融合出版书
     * 
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult addPubBook(@Validated @RequestBody PubBookBo pubBookBo) {
        return pubBookService.addPubBook(pubBookBo);
    }

    /**
     * 修改融合出版书
     * 
     * @param pubBookBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult updatePubBook(@Validated @RequestBody PubBookBo pubBookBo) {
        if (null == pubBookBo.getBookId()) {
            return AjaxResult.fail("融合出版书id不能为空");
        }
        return pubBookService.updatePubBook(pubBookBo);
    }

    /**
     * 查询融合出版书详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult<PubBookVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("融合出版书id不能为空");
        }
        PubBookConditionBo condition = new PubBookConditionBo();
        condition.setBookId(id);
        PubBookVo vo = pubBookService.getPubBookByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除融合出版书
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        PubBookDto pubBookDto = new PubBookDto();
        pubBookDto.setBookId(id);
        pubBookDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (pubBookService.updateById(pubBookDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult incrBookReadTimes(@RequestParam("bookId") Long bookId) {
        if (null == bookId) {
            return AjaxResult.fail("参数错误");
        }
        try {
            pubBookService.incrPubBookReadTimes(bookId);
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("incrBookReadTimes error：", e);
        }
        return AjaxResult.fail();
    }
}
