package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.CourseWeightedScoreApi;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreBo;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseWeightedScoreDto;
import com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo;
import com.fh.yanx.service.course.service.ICourseWeightedScoreService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 课程加权分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
@RestController
@Validated
public class CourseWeightedScoreController implements CourseWeightedScoreApi{
	
    @Autowired
    private ICourseWeightedScoreService courseWeightedScoreService;

    /**
     * 查询课程加权分表分页列表
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
    @Override
    public AjaxResult<PageInfo<CourseWeightedScoreVo>> getCourseWeightedScorePageListByCondition(@RequestBody CourseWeightedScoreConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseWeightedScoreVo> pageInfo = new PageInfo<>(courseWeightedScoreService.getCourseWeightedScoreListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程加权分表列表
	 * <AUTHOR>
	 * @date 2024-08-29 11:07:05
	 */
	@Override
	public AjaxResult<List<CourseWeightedScoreVo>> getCourseWeightedScoreListByCondition(@RequestBody CourseWeightedScoreConditionBo condition){
		List<CourseWeightedScoreVo> list = courseWeightedScoreService.getCourseWeightedScoreListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程加权分表
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
	@Override
    public AjaxResult addCourseWeightedScore(@Validated @RequestBody CourseWeightedScoreBo courseWeightedScoreBo){
		return courseWeightedScoreService.addCourseWeightedScore(courseWeightedScoreBo);
    }

    /**
	 * 修改课程加权分表
	 * @param courseWeightedScoreBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
	 */
	@Override
	public AjaxResult updateCourseWeightedScore(@Validated @RequestBody CourseWeightedScoreBo courseWeightedScoreBo) {
		if(null == courseWeightedScoreBo.getId()) {
			return AjaxResult.fail("课程加权分表id不能为空");
		}
		return courseWeightedScoreService.updateCourseWeightedScore(courseWeightedScoreBo);
	}

	/**
	 * 查询课程加权分表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
	 */
	@Override
	public AjaxResult<CourseWeightedScoreVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程加权分表id不能为空");
		}
		CourseWeightedScoreConditionBo condition = new CourseWeightedScoreConditionBo();
		condition.setId(id);
		CourseWeightedScoreVo vo = courseWeightedScoreService.getCourseWeightedScoreByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程加权分表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseWeightedScoreDto courseWeightedScoreDto = new CourseWeightedScoreDto();
		courseWeightedScoreDto.setId(id);
		courseWeightedScoreDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseWeightedScoreService.updateById(courseWeightedScoreDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
