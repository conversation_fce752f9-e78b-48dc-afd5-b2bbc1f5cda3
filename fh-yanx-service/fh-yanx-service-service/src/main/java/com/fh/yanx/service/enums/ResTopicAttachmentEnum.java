package com.fh.yanx.service.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ResTopicAttachmentEnum {
    /*
     *论文：1校内自建，2校外导入
     */
    RS_SOURCE_IN(1, "校内自建"),
    RS_SOURCE_OUT(2, "校外导入"),

    /**
     * 论文项目单独排序
     */
    DISSERTATION_NAME(0, "结题论文"),

    /**
     * 附件类型，1：结题论文，2：答辩材料
     */
    RES_TYPE_ONE(1,"结题论文"),
    RES_TYPE_TWO(2,"答辩材料");
    private Integer value;
    private String name;
}
