package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResStationDto;
import com.fh.yanx.service.res.entity.bo.ResStationConditionBo;
import com.fh.yanx.service.res.entity.vo.ResStationVo;

/**
 * 工作站和学校表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationMapper extends BaseMapper<ResStationDto> {

	List<ResStationVo> getResStationListByCondition(ResStationConditionBo condition);

}
