package com.fh.yanx.service.bm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.bm.entity.dto.BmInfoConnectDto;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectConditionBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoConnectVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 报名活动沟通确认表（本表只有新增记录）接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
public interface IBmInfoConnectService extends IService<BmInfoConnectDto> {

    List<BmInfoConnectVo> getBmInfoConnectListByCondition(BmInfoConnectConditionBo condition);

    AjaxResult addBmInfoConnect(BmInfoConnectBo bmInfoConnectBo);

    AjaxResult updateBmInfoConnect(BmInfoConnectBo bmInfoConnectBo);

    BmInfoConnectVo getBmInfoConnectByCondition(BmInfoConnectConditionBo condition);

    /**
     * 新增BmInfoConnect同时更新submitType
     *
     * @param bmInfoConnectBo the bm info connect bo
     * @return ajax result
     */
    AjaxResult addBmInfoConnectWithUpdateBmInfo(BmInfoConnectBo bmInfoConnectBo);
}
