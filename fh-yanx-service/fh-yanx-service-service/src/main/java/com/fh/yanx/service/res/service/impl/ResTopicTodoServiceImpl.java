package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fh.yanx.service.enums.TodoStatusType;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicTodoDto;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTodoVo;
import com.fh.yanx.service.res.service.IResTopicTodoService;
import com.fh.yanx.service.res.mapper.ResTopicTodoMapper;
import com.light.core.entity.AjaxResult;

/**
 * 用户待办事项接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Service
public class ResTopicTodoServiceImpl extends ServiceImpl<ResTopicTodoMapper, ResTopicTodoDto>
    implements IResTopicTodoService {

    @Resource
    private ResTopicTodoMapper resTopicTodoMapper;

    @Override
    public List<ResTopicTodoVo> getResTopicTodoListByCondition(ResTopicTodoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicTodoMapper.getResTopicTodoListByCondition(condition);
    }

    @Override
    public AjaxResult addResTopicTodo(ResTopicTodoBo resTopicTodoBo) {
        ResTopicTodoDto resTopicTodo = new ResTopicTodoDto();
        BeanUtils.copyProperties(resTopicTodoBo, resTopicTodo);
        resTopicTodo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(resTopicTodo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateResTopicTodo(ResTopicTodoBo resTopicTodoBo) {
        ResTopicTodoDto resTopicTodo = new ResTopicTodoDto();
        BeanUtils.copyProperties(resTopicTodoBo, resTopicTodo);
        if (updateById(resTopicTodo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ResTopicTodoVo getDetail(Long id) {
        ResTopicTodoConditionBo condition = new ResTopicTodoConditionBo();
        condition.setTodoId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicTodoVo> list = resTopicTodoMapper.getResTopicTodoListByCondition(condition);
        ResTopicTodoVo vo = new ResTopicTodoVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public void updateTodoStatus(ResTopicTodoBo resTopicTodoBo) {
        LambdaUpdateWrapper<ResTopicTodoDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResTopicTodoDto::getUserOid, resTopicTodoBo.getUserOid());
        updateWrapper.in(ResTopicTodoDto::getTodoType, resTopicTodoBo.getTodoTypes());
        updateWrapper.eq(ResTopicTodoDto::getTodoBusinessType, resTopicTodoBo.getTodoBusinessType());
        updateWrapper.eq(ResTopicTodoDto::getTodoBusinessId, resTopicTodoBo.getTodoBusinessId());

        ResTopicTodoDto resTopicTodoDto = new ResTopicTodoDto();
        resTopicTodoDto.setTodoStatus(TodoStatusType.HAS_DO.getValue());
        update(resTopicTodoDto, updateWrapper);
    }

    @Override
    public void saveOrUpdate(ResTopicTodoBo resTopicTodoBo) {
        ResTopicTodoDto resTopicTodo = new ResTopicTodoDto();
        BeanUtils.copyProperties(resTopicTodoBo, resTopicTodo);
        resTopicTodo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        saveOrUpdate(resTopicTodo);
    }
}