package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseModuleDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;

/**
 * 课程模块Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface CourseModuleMapper extends BaseMapper<CourseModuleDto> {

	List<CourseModuleVo> getCourseModuleListByCondition(CourseModuleConditionBo condition);

	CourseModuleVo getCourseModuleByCondition(CourseModuleConditionBo condition);

}
