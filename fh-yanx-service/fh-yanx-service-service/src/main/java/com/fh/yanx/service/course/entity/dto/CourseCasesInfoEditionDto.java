package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 校本课程案例详细信息版本记录
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_course_cases_info_edition")
public class CourseCasesInfoEditionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 校本课程案例版本表ID
	 */
	@TableField("cases_edition_id")
	private Long casesEditionId;

	/**
	 * 背景
	 */
	@TableField("background")
	private String background;

	/**
	 * 目标
	 */
	@TableField("goal")
	private String goal;

	/**
	 * 内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 评价
	 */
	@TableField("eval")
	private String eval;

	/**
	 * 实施信息
	 */
	@TableField("operation_info")
	private String operationInfo;

	/**
	 * 校本课程建设经验
	 */
	@TableField("exp")
	private String exp;

	/**
	 * 校本课程建设成效
	 */
	@TableField("effect")
	private String effect;

	/**
	 * 校本课程建设成效
	 */
	@TableField("problem")
	private String problem;

	/**
	 * 校本课程建设成效
	 */
	@TableField("structure")
	private String structure;

	/**
	 * 实施案例
	 */
	@TableField("teacher_case_info")
	private String teacherCaseInfo;

	/**
	 * 
	 */
	@TableField("teacher_case_name")
	private String teacherCaseName;

	/**
	 * 0-未删除 1-已删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 
	 */
	@TableField("create_date")
	private Date createDate;

	/**
	 * 
	 */
	@TableField("update_date")
	private Date updateDate;

}
