package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseModuleDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程模块接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface ICourseModuleService extends IService<CourseModuleDto> {

    List<CourseModuleVo> getCourseModuleListByCondition(CourseModuleConditionBo condition);

    AjaxResult addCourseModule(CourseModuleBo courseModuleBo);

    AjaxResult updateCourseModule(CourseModuleBo courseModuleBo);

    CourseModuleVo getCourseModuleByCondition(CourseModuleConditionBo condition);

    /**
     * 根据条件查询课程模块内容，可选是否查询附件
     *
     * @param condition the condition
     * @param isQueryAttachment 是否查询附件
     * @return course module list by condition with attachment
     */
    List<CourseModuleVo> getCourseModuleListByConditionWithAttachment(CourseModuleConditionBo condition,
        boolean isQueryAttachment);

    /**
     * 保存课程模块，保存后设置id到Bo里面
     *
     * @param casesId the cases id
     * @param courseModuleType the course module type
     * @param courseModuleBoList the course module bo list
     * <AUTHOR>
     * @date 2023 -08-18 16:52:05
     */
    void saveCourseModule(Long casesId, Integer courseModuleType, List<CourseModuleBo> courseModuleBoList);
}
