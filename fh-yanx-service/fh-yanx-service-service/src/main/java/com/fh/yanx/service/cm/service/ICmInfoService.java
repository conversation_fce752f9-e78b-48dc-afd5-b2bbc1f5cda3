package com.fh.yanx.service.cm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.cm.entity.dto.CmInfoDto;
import com.fh.yanx.service.cm.entity.bo.CmInfoConditionBo;
import com.fh.yanx.service.cm.entity.bo.CmInfoBo;
import com.fh.yanx.service.cm.entity.vo.CmInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 代理商信息表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
public interface ICmInfoService extends IService<CmInfoDto> {

    List<CmInfoVo> getCmInfoListByCondition(CmInfoConditionBo condition);

	AjaxResult addCmInfo(CmInfoBo cmInfoBo);

	AjaxResult updateCmInfo(CmInfoBo cmInfoBo);

	CmInfoVo getCmInfoByCondition(CmInfoConditionBo condition);

	AjaxResult addCmInfoBatch(List<CmInfoBo> cmInfoBo);

	/**
	 * 校验代理商参数在同一个活动下是否重复，如果没有传活动id，则全局校验
	 * @param cmInfoBo：id（主键，非必传，更新时候必传），activId，cm
	 * @return
	 */
	boolean checkRepeat(CmInfoBo cmInfoBo);

	/**
	 * cm改变的时候（如果没有改变cm则直接返回false），检验代理商渠道参数是否有订单参数，如果有订单产生则返回true
	 * @param cmInfoBo：id（主键，必传），activId，cm
	 * @return
	 */
	boolean checkOrderWhenUpdateCm(CmInfoBo cmInfoBo);

	/**
	 * 根据代理商oid更新代理商信息里面的代理商姓名和手机号
	 *
	 * @param cmInfoBo ：adminOid，realName，phone
	 * <AUTHOR>
	 * @date 2024 -07-17 10:03:57
	 */
	void updateCmInfoByAdmin(CmInfoBo cmInfoBo);
}

