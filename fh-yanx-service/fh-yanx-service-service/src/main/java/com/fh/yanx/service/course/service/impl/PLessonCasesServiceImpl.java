package com.fh.yanx.service.course.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.PLessonCasesBo;
import com.fh.yanx.service.course.entity.bo.PLessonCasesConditionBo;
import com.fh.yanx.service.course.entity.dto.PLessonCasesDto;
import com.fh.yanx.service.course.entity.vo.PLessonCasesVo;
import com.fh.yanx.service.course.mapper.PLessonCasesMapper;
import com.fh.yanx.service.course.service.IPLessonCasesService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 课例表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Service
public class PLessonCasesServiceImpl extends ServiceImpl<PLessonCasesMapper, PLessonCasesDto>
    implements IPLessonCasesService {

    @Resource
    private PLessonCasesMapper pLessonCasesMapper;

    @Override
    public List<PLessonCasesVo> getPLessonCasesListByCondition(PLessonCasesConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return pLessonCasesMapper.getPLessonCasesListByCondition(condition);
    }

    @Override
    public AjaxResult addPLessonCases(PLessonCasesBo pLessonCasesBo) {
        PLessonCasesDto pLessonCases = new PLessonCasesDto();
        BeanUtils.copyProperties(pLessonCasesBo, pLessonCases);
        pLessonCases.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pLessonCases)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePLessonCases(PLessonCasesBo pLessonCasesBo) {
        PLessonCasesDto pLessonCases = new PLessonCasesDto();
        BeanUtils.copyProperties(pLessonCasesBo, pLessonCases);
        if (updateById(pLessonCases)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PLessonCasesVo getPLessonCasesByCondition(PLessonCasesConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PLessonCasesVo vo = pLessonCasesMapper.getPLessonCasesByCondition(condition);
        return vo;
    }

}