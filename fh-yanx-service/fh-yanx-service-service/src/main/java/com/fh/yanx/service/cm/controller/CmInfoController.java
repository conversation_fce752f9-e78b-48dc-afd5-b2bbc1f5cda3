package com.fh.yanx.service.cm.controller;

import java.util.List;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.cm.api.CmInfoApi;
import com.fh.yanx.service.cm.entity.bo.CmInfoBo;
import com.fh.yanx.service.cm.entity.bo.CmInfoConditionBo;
import com.fh.yanx.service.cm.entity.dto.CmInfoDto;
import com.fh.yanx.service.cm.entity.vo.CmInfoVo;
import com.fh.yanx.service.cm.service.ICmInfoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 代理商信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
@Slf4j
@RestController
@Validated
public class CmInfoController implements CmInfoApi {

    @Autowired
    private ICmInfoService cmInfoService;

    /**
     * 查询代理商信息表分页列表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @Override
    public AjaxResult<PageInfo<CmInfoVo>> getCmInfoPageListByCondition(@RequestBody CmInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<CmInfoVo> pageInfo = new PageInfo<>(cmInfoService.getCmInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询代理商信息表列表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @Override
    public AjaxResult<List<CmInfoVo>> getCmInfoListByCondition(@RequestBody CmInfoConditionBo condition) {
        List<CmInfoVo> list = cmInfoService.getCmInfoListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增代理商信息表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @Override
    public AjaxResult addCmInfo(@Validated @RequestBody CmInfoBo cmInfoBo) {
        // 校验代理商参数同一个活动下是否重复
        if (cmInfoService.checkRepeat(cmInfoBo)) {
            return AjaxResult.fail("代理商参数不允许重复");
        }
        return cmInfoService.addCmInfo(cmInfoBo);
    }

    /**
     * 批量新增代理商信息表
     * 
     * @param cmInfoBos
     * @return
     */
    @Override
    public AjaxResult addCmInfoBatch(List<CmInfoBo> cmInfoBos) {
        for (CmInfoBo cmInfoBo : cmInfoBos) {
            // 校验代理商参数同一个活动下是否重复
            if (cmInfoService.checkRepeat(cmInfoBo)) {
                return AjaxResult.fail("代理商参数不允许重复");
            }
        }
        return cmInfoService.addCmInfoBatch(cmInfoBos);
    }

    /**
     * 修改代理商信息表
     * 
     * @param cmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @Override
    public AjaxResult updateCmInfo(@Validated @RequestBody CmInfoBo cmInfoBo) {
        if (null == cmInfoBo.getId()) {
            return AjaxResult.fail("代理商信息表id不能为空");
        }
        // 校验代理商渠道参数同一个活动下是否重复
        if (cmInfoService.checkRepeat(cmInfoBo)) {
            return AjaxResult.fail("代理商渠道参数不允许重复");
        }
        // cm改变的时候，检验该代理商渠道参数是否有订单，如果有订单则不允许修改此代理商参数
        if (cmInfoService.checkOrderWhenUpdateCm(cmInfoBo)) {
            return AjaxResult.fail("该代理商渠道参数有订单，不允许修改");
        }
        return cmInfoService.updateCmInfo(cmInfoBo);
    }

    /**
     * 查询代理商信息表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @Override
    public AjaxResult<CmInfoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("代理商信息表id不能为空");
        }
        CmInfoConditionBo condition = new CmInfoConditionBo();
        condition.setId(id);
        CmInfoVo vo = cmInfoService.getCmInfoByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除代理商信息表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        CmInfoDto cmInfoDto = new CmInfoDto();
        cmInfoDto.setId(id);
        cmInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (cmInfoService.updateById(cmInfoDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult updateCmInfoByAdmin(CmInfoBo cmInfoBo) {
        if (StringUtils.isBlank(cmInfoBo.getAdminOid())) {
            return AjaxResult.fail("管理员oid不能为空");
        }
        try {
            cmInfoService.updateCmInfoByAdmin(cmInfoBo);
        } catch (Exception e) {
            log.error("updateCmInfoByAdmin error:", e);
            return AjaxResult.fail("更新代理商信息失败");
        }
        return AjaxResult.success("更新代理商信息成功");
    }
}
