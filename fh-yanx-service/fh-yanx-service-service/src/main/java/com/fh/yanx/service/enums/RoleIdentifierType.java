package com.fh.yanx.service.enums;

/**
 * 注意这里角色是新时代角色，除1，2，其他会被放到后台账户管理
 * <AUTHOR>
 * @date 2023-07-17 18:00
 */
public enum RoleIdentifierType {
    BUREAU_SUPER_ADMIN("教育局超级管理员", "bureau_super_admin", 1L),
    SCHOOL_SUPER_ADMIN("学校超级管理员", "school_super_admin", 2L),
    OPERATION_ADMIN("运营管理员", "operation_crm", 12L),
    OPERATION_CRM("运营商讯查看员", "operation_crm", 21L),

    // 新时代的角色
    YANX_OPERATION_ADMIN("新时代运营管理员", "yanx_operation_admin", 17L),
//    YANX_AUDIT_EXPERT("新时代校本课程审核终审专家", "yanx_audit_expert", 22L),
//    YANX_AUDIT_EXPERT_PRE("新时代校本课程审核初审专家", "yanx_audit_expert_pre", 23L),
//    YANX_SENIOR_OPERATION_AUDIT("新时代高级运营审核员", "yanx_senior_operation_audit", 26L),
    YANX_CM_ADMIN("代理商", "yanx_cm_admin", 27L),
//    YANX_EXPERT_ASSISTANT("专家助理", "yanx_expert_assistant", 28L),
//    YANX_COMPREHENSIVE_AUDIT("综评专家", "yanx_comprehensive_audit", 29L),
//
//    YANX_REVIEW_EXPERT("评审专家", "yanx_review_expert", 32L),
    ;

    private String name;
    private String code;
    private Long roleId;

    private RoleIdentifierType(String name, String code, Long roleId) {
        this.name = name;
        this.code = code;
        this.roleId = roleId;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public Long getRoleId() {
        return this.roleId;
    }
}
