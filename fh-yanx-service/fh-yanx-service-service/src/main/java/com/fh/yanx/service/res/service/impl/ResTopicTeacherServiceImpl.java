package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.service.enums.ResTopicProcessEnum;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CompleteTodoEvent;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.service.IResTopicService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicTeacherDto;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;
import com.fh.yanx.service.res.service.IResTopicTeacherService;
import com.fh.yanx.service.res.mapper.ResTopicTeacherMapper;
import com.light.core.entity.AjaxResult;

/**
 * 课题指导老师表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Service
public class ResTopicTeacherServiceImpl extends ServiceImpl<ResTopicTeacherMapper, ResTopicTeacherDto>
    implements IResTopicTeacherService {

    @Resource
    private ResTopicTeacherMapper resTopicTeacherMapper;
    @Resource
    private IResTopicService resTopicService;
    @Resource
    private IResTopicTeacherService resTopicTeacherService;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public List<ResTopicTeacherVo> getResTopicTeacherListByCondition(ResTopicTeacherConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicTeacherMapper.getResTopicTeacherListByCondition(condition);
    }

    @Override
    public List<String> getResTopicTeacherOidListByTopicId(Long topicId) {
        List<String> oids = new ArrayList<>();
        ResTopicTeacherConditionBo condition = new ResTopicTeacherConditionBo();
        condition.setTopicId(topicId);
        List<ResTopicTeacherVo> resTopicTeacherVos = getResTopicTeacherListByCondition(condition);
        if (CollectionUtils.isNotEmpty(resTopicTeacherVos)) {
            oids = resTopicTeacherVos.stream().map(ResTopicTeacherVo::getUserOid).collect(Collectors.toList());
        }
        return oids;
    }

    @Override
    public AjaxResult addResTopicTeacher(ResTopicTeacherBo resTopicTeacherBo) {
        ResTopicTeacherDto resTopicTeacher = new ResTopicTeacherDto();
        BeanUtils.copyProperties(resTopicTeacherBo, resTopicTeacher);
        resTopicTeacher.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(resTopicTeacher)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateResTopicTeacher(ResTopicTeacherBo resTopicTeacherBo) {
        ResTopicTeacherDto resTopicTeacher = new ResTopicTeacherDto();
        BeanUtils.copyProperties(resTopicTeacherBo, resTopicTeacher);
        updateById(resTopicTeacher);
        if (StatusEnum.YES.getCode().equals(resTopicTeacherBo.getIsAddEvaluate())) {
            ResTopicTeacherDto teacherDto = this.getById(resTopicTeacherBo.getId());
            ResTopicDto byId = resTopicService.getById(teacherDto.getTopicId());
            if (null == byId) {
                return AjaxResult.fail("课题不存在");
            }
            // 修改课题状态
            ResTopicBo resTopicBo=new ResTopicBo();
            resTopicBo.setTopicId(byId.getTopicId());
            resTopicBo.setTopicProcess(ResTopicProcessEnum.TEACHER_EVALUATE.getCode());
            resTopicService.updateResTopicStatus(resTopicBo);
            // 生成学生的，教师以评定代办事件及流转记录
            List<String> oids = resTopicTeacherService.getResTopicTeacherOidListByTopicId(byId.getTopicId());
            resTopicService.publishEvent(byId.getTopicId(), ResTopicProcessEnum.TEACHER_EVALUATE.getCode(),
                    byId.getTopicName(), oids, resTopicTeacherBo.getCurrentUserOid(), byId.getSubmitUser());

            //取消教师待评定代办
            for (String userOid : oids) {
                applicationContext.publishEvent(CompleteTodoEvent.produceTopicEvent(TopicTodoType.TEACHER_TOPIC_EVALUATE_COMPLETE.getType(), byId.getTopicId(),  new Date(), "", userOid,null));
            }
        }
        return AjaxResult.success("保存成功");

    }

    @Override
    public ResTopicTeacherVo getDetail(Long id) {
        ResTopicTeacherConditionBo condition = new ResTopicTeacherConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicTeacherVo> list = resTopicTeacherMapper.getResTopicTeacherListByCondition(condition);
        ResTopicTeacherVo vo = new ResTopicTeacherVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

}