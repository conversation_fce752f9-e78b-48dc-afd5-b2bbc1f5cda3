package com.fh.yanx.service.jz.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesGradeDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeConditionBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesGradeVo;

/**
 * 金中-校本课程案例年级Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesGradeMapper extends BaseMapper<JzCourseCasesGradeDto> {

	List<JzCourseCasesGradeVo> getJzCourseCasesGradeListByCondition(JzCourseCasesGradeConditionBo condition);

	JzCourseCasesGradeVo getJzCourseCasesGradeByCondition(JzCourseCasesGradeConditionBo condition);

}
