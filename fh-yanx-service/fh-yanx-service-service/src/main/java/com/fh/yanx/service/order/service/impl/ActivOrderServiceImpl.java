package com.fh.yanx.service.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.yanx.service.bm.entity.dto.BmInfoDto;
import com.fh.yanx.service.bm.mapper.BmInfoMapper;
import com.fh.yanx.service.enums.BmInfoPayType;
import com.fh.yanx.service.enums.GoodsType;
import com.fh.yanx.service.enums.OrderEnum;
import com.fh.yanx.service.utils.UUIDUtil;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.order.entity.dto.ActivOrderDto;
import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import com.fh.yanx.service.order.service.IActivOrderService;
import com.fh.yanx.service.order.mapper.ActivOrderMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
@Service
public class ActivOrderServiceImpl extends ServiceImpl<ActivOrderMapper, ActivOrderDto> implements IActivOrderService {

    @Resource
    private ActivOrderMapper activOrderMapper;
    @Resource
    private BmInfoMapper bmInfoMapper;

    @Override
    public List<ActivOrderVo> getActivOrderListByCondition(ActivOrderConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return activOrderMapper.getActivOrderListByCondition(condition);
    }

    @Override
    public AjaxResult addActivOrder(ActivOrderBo activOrderBo) {
        ActivOrderDto activOrder = new ActivOrderDto();
        BeanUtils.copyProperties(activOrderBo, activOrder);
        activOrder.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(activOrder)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateActivOrder(ActivOrderBo activOrderBo) {
        ActivOrderDto activOrder = new ActivOrderDto();
        BeanUtils.copyProperties(activOrderBo, activOrder);
        if (updateById(activOrder)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ActivOrderVo getActivOrderByCondition(ActivOrderConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return activOrderMapper.getActivOrderByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ActivOrderVo createOrder(ActivOrderBo orderBo) {
        ActivOrderDto orderDto = new ActivOrderDto();
        BeanUtils.copyProperties(orderBo, orderDto);
        orderDto.setOrderNumber(UUIDUtil.getOrderId());
        // 订单类型：消费
        orderDto.setOrderType(OrderEnum.ORDER_TYPE_CONSUMPTION_ENUM.getCode());
        // 支付状态 待支付
        orderDto.setOrderState(OrderEnum.ORDER_STATE_PENDING_PAY.getCode());
        orderDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
        boolean save = save(orderDto);
        if (!save) {
            return null;
        }
        ActivOrderVo orderVo = new ActivOrderVo();
        BeanUtils.copyProperties(orderDto, orderVo);
        return orderVo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult changeOrderAmount(ActivOrderBo orderBo) {
        ActivOrderDto orderDto = getOne(new LambdaQueryWrapper<ActivOrderDto>()
            .eq(ActivOrderDto::getGoodsId, orderBo.getGoodsId()).eq(ActivOrderDto::getGoodsType, orderBo.getGoodsType())
            .eq(ActivOrderDto::getIsDelete, StatusEnum.NOTDELETE.getCode()).orderByDesc(ActivOrderDto::getOrderId)
            .last("limit 1"));
        if (orderDto == null) {
            return AjaxResult.fail("未找到订单信息");
        }
        if (!OrderEnum.ORDER_STATE_PENDING_PAY.getCode().equals(orderDto.getOrderState())) {
            return AjaxResult.fail("非未付款订单，无法修改金额");
        }
        // 改价格生成新订单
        ActivOrderDto entity = new ActivOrderDto();
        BeanUtils.copyProperties(orderDto, entity);
        entity.setOrderId(null);
        entity.setOrderNumber(UUIDUtil.getOrderId());
        entity.setOrderAmount(orderBo.getOrderAmount());
        // 旧订单置为已删除
        orderDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (save(entity) && updateById(orderDto)) {
            return AjaxResult.success("修改订单金额成功");
        }
        return AjaxResult.fail("修改订单金额失败");
    }

    @Override
    public AjaxResult changeOrderState(ActivOrderBo orderBo) {
        ActivOrderDto orderDto = getOne(new LambdaQueryWrapper<ActivOrderDto>()
            .eq(ActivOrderDto::getGoodsId, orderBo.getGoodsId()).eq(ActivOrderDto::getGoodsType, orderBo.getGoodsType())
            .eq(ActivOrderDto::getIsDelete, StatusEnum.NOTDELETE.getCode()).orderByDesc(ActivOrderDto::getOrderId)
            .last("limit 1"));
        if (orderDto == null) {
            return AjaxResult.fail("未找到订单信息");
        }
        orderDto.setOrderState(orderBo.getOrderState());
        if (orderBo.getOrderType() != null) {
            orderDto.setOrderType(orderBo.getOrderType());
        }
        if (orderBo.getPayMode() != null) {
            orderDto.setPayMode(orderBo.getPayMode());
        }
        if (updateById(orderDto)) {
            return AjaxResult.success("修改订单状态成功");
        }
        return AjaxResult.fail("修改订单状态失败");
    }

    @Override
    public AjaxResult completeOrder(ActivOrderBo orderBo) {
        // 查询出订单信息
        ActivOrderDto orderDto =
            getOne(new LambdaQueryWrapper<ActivOrderDto>().eq(ActivOrderDto::getOrderNumber, orderBo.getOrderNumber())
                .eq(ActivOrderDto::getIsDelete, StatusEnum.NOTDELETE.getCode()).last("limit 1"));
        if (orderDto == null) {
            return AjaxResult.fail("未查询到订单信息，订单已被删除");
        }
        if (orderDto.getOrderState() != null && orderDto.getOrderState().equals(OrderEnum.ORDER_STATE_PAY.getCode())) {
            return AjaxResult.fail("已付款，请勿重新付款");
        }

        orderDto.setTransactionId(orderBo.getTransactionId());
        orderDto.setPayMode(orderBo.getPayMode());
        orderDto.setTerminalType(orderBo.getTerminalType());
        orderDto.setPayTime(orderBo.getPayTime());
        orderDto.setFirstPay(orderBo.getFirstPay());
        orderDto.setOrderState(orderBo.getOrderState());
        updateById(orderDto);
        // 已付款，更新业务表付款状态
        if (OrderEnum.ORDER_STATE_PAY.getCode().equals(orderDto.getOrderState())) {
            changePayType(orderDto);
        }
        // 判断如果未支付且修改为已支付状态则同步修改交易流水
        return AjaxResult.success("修改订单状态成功");
    }

    /**
     * 更新业务表支付状态
     *
     * @param activOrderDto
     * @return void
     * <AUTHOR>
     * @date 2023/10/31 17:15
     **/
    public void changePayType(ActivOrderDto activOrderDto) {
        // 活动
        if (GoodsType.ACTIV_BM.getCode().equals(activOrderDto.getGoodsType())) {
            BmInfoDto bmInfoDto = new BmInfoDto();
            bmInfoDto.setInfoId(activOrderDto.getGoodsId());
            bmInfoDto.setPayType(BmInfoPayType.ALREADY_PAY.getCode());
            bmInfoDto.setPayTime(new Date());
            bmInfoMapper.updateById(bmInfoDto);
        }
    }

    @Override
    public AjaxResult getUserOrderList(ActivOrderBo orderBo) {
        return null;
    }

    @Override
    public Integer changeActivOrderTimeout(List<Long> orderIds) {
        if (CollectionUtil.isEmpty(orderIds)) {
            return null;
        }
        return baseMapper.changeActivOrderTimeout(orderIds);
    }
}