package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程加权分表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_weighted_score")
public class CourseWeightedScoreDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 校本或精品审核的加权分（暂时这么设计）：1、校本，2精品
	 */
	@TableField("normal_best_type")
	private Integer normalBestType;

	/**
	 * 加权分1
	 */
	@TableField("weighted_score_one")
	private BigDecimal weightedScoreOne;

	/**
	 * 加权分2
	 */
	@TableField("weighted_score_two")
	private BigDecimal weightedScoreTwo;

	/**
	 * 加权分3
	 */
	@TableField("weighted_score_three")
	private BigDecimal weightedScoreThree;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
