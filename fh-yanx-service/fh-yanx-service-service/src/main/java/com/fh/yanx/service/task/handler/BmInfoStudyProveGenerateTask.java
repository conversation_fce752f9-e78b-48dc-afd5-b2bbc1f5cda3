package com.fh.yanx.service.task.handler;

import com.fh.yanx.service.bm.service.IBmInfoJoinerService;
import com.fh.yanx.service.bm.service.IBmInfoService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 学时证明生产task
 *
 * <AUTHOR>
 * @date 2024-03-27 14:27
 */
@Slf4j
@Component
public class BmInfoStudyProveGenerateTask {
    @Resource
    private IBmInfoJoinerService bmInfoJoinerService;

    /**
     * 学时证明生成task
     */
    @XxlJob("task-study-prove-generate")
    public void studyProveGenerate() {
        log.info("study-prove-generate start");
        bmInfoJoinerService.studyProveGenerate();
        log.info("study-prove-generate end");
    }
}
