package com.fh.yanx.service.course.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseRecommendDto;
import com.fh.yanx.service.course.entity.bo.CourseRecommendConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendBo;
import com.fh.yanx.service.course.entity.vo.CourseRecommendVo;
import com.fh.yanx.service.course.service.ICourseRecommendService;
import com.fh.yanx.service.course.mapper.CourseRecommendMapper;
import com.light.core.entity.AjaxResult;
/**
 * 课程推荐表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
@Service
public class CourseRecommendServiceImpl extends ServiceImpl<CourseRecommendMapper, CourseRecommendDto> implements ICourseRecommendService {

	@Resource
	private CourseRecommendMapper courseRecommendMapper;
	
    @Override
	public List<CourseRecommendVo> getCourseRecommendListByCondition(CourseRecommendConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseRecommendMapper.getCourseRecommendListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseRecommend(CourseRecommendBo courseRecommendBo) {
		CourseRecommendDto courseRecommend = new CourseRecommendDto();
		BeanUtils.copyProperties(courseRecommendBo, courseRecommend);
		courseRecommend.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseRecommend)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseRecommend(CourseRecommendBo courseRecommendBo) {
		CourseRecommendDto courseRecommend = new CourseRecommendDto();
		BeanUtils.copyProperties(courseRecommendBo, courseRecommend);
		if(updateById(courseRecommend)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult addCourseRecommend(List<CourseRecommendBo> courseRecommendBo) {
		List<CourseRecommendDto> courseRecommendDtos = BeanUtil.copyToList(courseRecommendBo, CourseRecommendDto.class);
		courseRecommendDtos.forEach(item->item.setIsDelete(StatusEnum.NOTDELETE.getCode()));
		saveBatch(courseRecommendDtos);
		return AjaxResult.success("保存成功");
	}

	@Override
	public AjaxResult updateCourseRecommend(List<CourseRecommendBo> courseRecommendBo) {
		List<CourseRecommendDto> courseRecommendDtos = BeanUtil.copyToList(courseRecommendBo, CourseRecommendDto.class);
		updateBatchById(courseRecommendDtos);
		return AjaxResult.success("保存成功");
	}

	@Override
	public CourseRecommendVo getCourseRecommendByCondition(CourseRecommendConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		CourseRecommendVo vo = courseRecommendMapper.getCourseRecommendByCondition(condition);
		if(null != vo) {
			return vo;
		}
		return new CourseRecommendVo();
	}

}