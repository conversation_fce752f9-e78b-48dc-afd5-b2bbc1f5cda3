package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;

/**
 * 校本课程案例Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesMapper extends BaseMapper<PCourseCasesDto> {

    List<PCourseCasesVo> getPCourseCasesListByCondition(PCourseCasesConditionBo condition);

    PCourseCasesVo getPCourseCasesByCondition(PCourseCasesConditionBo condition);

    /**
     * 查询首页列表数据
     *
     * @param condition the condition
     * @return home list
     */
    List<PCourseCasesVo> getHomeList(PCourseCasesConditionBo condition);

    /**
     * 查询首页推荐列表数据
     *
     * @param condition the condition
     * @return home list
     */
    List<PCourseCasesVo> topList(PCourseCasesConditionBo condition);

}
