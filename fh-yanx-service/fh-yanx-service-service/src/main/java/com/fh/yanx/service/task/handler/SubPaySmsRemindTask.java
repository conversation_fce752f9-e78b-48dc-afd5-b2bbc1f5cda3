package com.fh.yanx.service.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.fh.yanx.service.bm.service.IBmInfoService;
import com.fh.yanx.service.consts.ConstantsInteger;
import com.fh.yanx.service.enums.ActivBizType;
import com.fh.yanx.service.enums.BmInfoPayType;
import com.fh.yanx.service.enums.OrderEnum;
import com.fh.yanx.service.order.service.IActivOrderService;
import com.fh.yanx.service.order.service.IActivOrderSmsRemindRecordService;
import com.fh.yanx.service.task.service.SmsYmService;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 征订支付提醒定时器
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-09-11  09:23
 */
@Slf4j
@Component
public class SubPaySmsRemindTask {
    @Resource
    IBmInfoService bmInfoService;
    @Resource
    IActivOrderService activOrderService;
    @Resource
    IActivOrderSmsRemindRecordService activOrderSmsRemindRecordService;

    @Value("subOrderPayRemindContent:您有%d笔关于%s订单未完成，点连接http://wwwineoschool.fhsljy.com:8000/xsd-h5/order-list?type=zhengding&activId=%d查看详情")
    private String smsContent;

    @Resource
    SmsYmService smsYmService;

    /**
     * 征订支付短信提醒（每小时扫一次）
     * 超过48小时未支付提醒
     *
     * @param
     * @return void
     * <AUTHOR>
     * @date 2024/9/11 9:26
     **/
    @XxlJob("task-sub-pay-sms-remind")
    public void subPaySmsRemind() {
        // 查询未支付且未发送短信提醒的订单
        BmInfoConditionBo conditionBo = new BmInfoConditionBo();
        conditionBo.setQueryNotSmsRemindRecord(true);
        conditionBo.setActivBizType(ActivBizType.SUB.getValue());
        conditionBo.setOrderState(OrderEnum.ORDER_STATE_PENDING_PAY.getCode());
        List<BmInfoVo> bmInfoVos = bmInfoService.getBmInfoListWithOrder(conditionBo);
        if (CollectionUtil.isEmpty(bmInfoVos)) {
            return;
        }
        Date now = new Date();
        bmInfoVos = bmInfoVos.stream()
                .filter(x -> DateUtil.between(x.getCreateTime(), now, DateUnit.HOUR) >= OrderEnum.REMIND_HOURS.getCode())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(bmInfoVos)) {
            return;
        }
        // 短信提醒
        List<Long> orderIds = Lists.newArrayList();
        Map<Long, List<BmInfoVo>> groupByActivIdMap = bmInfoVos.stream().collect(Collectors.groupingBy(BmInfoVo::getActivId));
        for (Long activId : groupByActivIdMap.keySet()) {
            List<BmInfoVo> activBmInfoList = groupByActivIdMap.get(activId);
            Map<String, List<BmInfoVo>> groupByPhoneMap = activBmInfoList.stream().collect(Collectors.groupingBy(BmInfoVo::getContactMobile));
            for (String phone : groupByPhoneMap.keySet()) {
                List<BmInfoVo> userBmInfoList = groupByPhoneMap.get(phone);
                List<Long> userOrderIds = userBmInfoList.stream().map(BmInfoVo::getOrderId).collect(Collectors.toList());
                orderIds.addAll(userOrderIds);
                BmInfoVo userBmInfo = userBmInfoList.get(ConstantsInteger.NUM_0);
                String content = String.format(smsContent, userOrderIds.size(), userBmInfo.getActivName(), userBmInfo.getActivId());
                smsYmService.sendContent(phone, content);
            }
        }
        activOrderSmsRemindRecordService.addActivOrderSmsRemindRecordBatchByOrderIds(orderIds);
    }

    /**
     * 征订订单超时取消
     *
     * @param
     * @return void
     * <AUTHOR>
     * @date 2024/9/11 10:57
     **/
    @XxlJob("task-sub-pay-timeout-cancel")
    public void subPayTimeout() {
        // 查询未支付的订单
        BmInfoConditionBo conditionBo = new BmInfoConditionBo();
        conditionBo.setActivBizType(ActivBizType.SUB.getValue());
        conditionBo.setOrderState(OrderEnum.ORDER_STATE_PENDING_PAY.getCode());
        List<BmInfoVo> bmInfoVos = bmInfoService.getBmInfoListWithOrder(conditionBo);
        if (CollectionUtil.isEmpty(bmInfoVos)) {
            return;
        }
        Date now = new Date();
        bmInfoVos = bmInfoVos.stream()
                .filter(x -> DateUtil.between(x.getOrderCreateTime(), now, DateUnit.HOUR) >= OrderEnum.TIMEOUT_HOURS.getCode())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(bmInfoVos)) {
            return;
        }
        // 取消订单
        List<Long> orderIds = bmInfoVos.stream().map(BmInfoVo::getOrderId).collect(Collectors.toList());
        activOrderService.changeActivOrderTimeout(orderIds);
        // 设置payType=4已取消
        List<Long> infoIds = bmInfoVos.stream().map(BmInfoVo::getInfoId).collect(Collectors.toList());
        bmInfoService.updatePayTypeBatch(infoIds, BmInfoPayType.CANCEL.getCode());
    }

}
