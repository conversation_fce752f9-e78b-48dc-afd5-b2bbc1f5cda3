package com.fh.yanx.service.activ.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;

/**
 * 新时代文化校园活动内容表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
public interface ActivInfoRecordMapper extends BaseMapper<ActivInfoRecordDto> {

	List<ActivInfoRecordVo> getActivInfoRecordListByCondition(ActivInfoRecordConditionBo condition);

	ActivInfoRecordVo getActivInfoRecordByCondition(ActivInfoRecordConditionBo condition);

}
