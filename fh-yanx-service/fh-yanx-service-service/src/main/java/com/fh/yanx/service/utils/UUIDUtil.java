package com.fh.yanx.service.utils;

import cn.hutool.core.util.RandomUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

public class UUIDUtil {

	public static String getUUID(){
		UUID uuid = UUID.randomUUID();
		return uuid.toString().replace("-", "");
	}
	public static String getOrderId(){
		String orderId = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		return orderId.replace("-", "") + String.valueOf(System.currentTimeMillis())+ getRandomNumber(5);
	}

	private static String getRandomNumber(int length) {
		StringBuffer buffer = new StringBuffer(
				"0123456789");

		StringBuffer sb = new StringBuffer();
		Random random = new Random();

		int range = buffer.length();
		for (int i = 0; i < length; i++) {
			sb.append(buffer.charAt(random.nextInt(range)));
		}

		return sb.toString();
	}

	public static void main(String[] args) {
		System.out.println(RandomUtil.randomString("0123456789", 4));
	}
	
}
