package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 公开课表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_open_course")
public class ResOpenCourseDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "open_course_id", type = IdType.AUTO)
	private Long openCourseId;

	/**
	 * 研究附件名称
	 */
	@TableField("open_course_name")
	private String openCourseName;

	/**
	 * 论文摘要
	 */
	@TableField("open_course_desc")
	private String openCourseDesc;

	/**
	 * pv学习数，多少个人点开过
	 */
	@TableField("open_course_view")
	private Long openCourseView;

	/**
	 * 公开课顺序
	 */
	@TableField("open_course_index")
	private Long openCourseIndex;

	/**
	 * 学校id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 学校名称
	 */
	@TableField("organization_name")
	private String organizationName;

	/**
	 * 上传人userOid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 上传人姓名
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 视频文件原始名称-带后缀
	 */
	@TableField("media_file_name_ori")
	private String mediaFileNameOri;

	/**
	 * 视频文件名称-不带后缀
	 */
	@TableField("media_file_name")
	private String mediaFileName;

	/**
	 * 视频文件地址
	 */
	@TableField("media_file_url")
	private String mediaFileUrl;

	/**
	 * 图片文件原始名称-带后缀
	 */
	@TableField("img_file_name_ori")
	private String imgFileNameOri;

	/**
	 * 图片文件名称-不带后缀
	 */
	@TableField("img_file_name")
	private String imgFileName;

	/**
	 * 图片文件地址
	 */
	@TableField("img_file_url")
	private String imgFileUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
