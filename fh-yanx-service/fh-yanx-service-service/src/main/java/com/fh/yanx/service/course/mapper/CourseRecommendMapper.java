package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseRecommendDto;
import com.fh.yanx.service.course.entity.bo.CourseRecommendConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseRecommendVo;

/**
 * 课程推荐表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
public interface CourseRecommendMapper extends BaseMapper<CourseRecommendDto> {

	List<CourseRecommendVo> getCourseRecommendListByCondition(CourseRecommendConditionBo condition);

	CourseRecommendVo getCourseRecommendByCondition(CourseRecommendConditionBo condition);

}
