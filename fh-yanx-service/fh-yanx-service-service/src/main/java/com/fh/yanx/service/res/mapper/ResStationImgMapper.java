package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResStationImgDto;
import com.fh.yanx.service.res.entity.bo.ResStationImgConditionBo;
import com.fh.yanx.service.res.entity.vo.ResStationImgVo;

/**
 * 工作站介绍图片表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationImgMapper extends BaseMapper<ResStationImgDto> {

	List<ResStationImgVo> getResStationImgListByCondition(ResStationImgConditionBo condition);

}
