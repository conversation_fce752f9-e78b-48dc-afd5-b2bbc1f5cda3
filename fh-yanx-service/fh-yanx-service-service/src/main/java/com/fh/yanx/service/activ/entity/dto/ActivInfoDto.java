package com.fh.yanx.service.activ.entity.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代文化校园活动信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activ_info")
public class ActivInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "activ_id", type = IdType.AUTO)
	private Long activId;

	/**
	 * 学校id,0表示是全网的活动
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 活动名称
	 */
	@TableField("activ_name")
	private String activName;

	/**
	 * 活动描述
	 */
	@TableField("activ_description")
	private String activDescription;

	/**
	 * 活动封面文件oid
	 */
	@TableField("activ_cover_id")
	private String activCoverId;

	/**
	 * 活动封面文件url
	 */
	@TableField("activ_cover_url")
	private String activCoverUrl;

	/**
	 * 活动开始时间：yyyy-MM-dd
	 */
	@TableField("activ_start_time")
	private Date activStartTime;

	/**
	 * 活动结束时间：yyyy-MM-dd
	 */
	@TableField("activ_end_time")
	private Date activEndTime;

	/**
	 * 活动形式：1线上，2线下，3线上线下
	 */
	@TableField("activ_form_type")
	private Integer activFormType;

	/**
	 * 活动上下架：1上架，2下架
	 */
	@TableField("activ_type")
	private Integer activType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 活动展示类型：1默认无特殊含义，2在教师发展页展示
	 */
	@TableField("show_type")
	private Integer showType;

	/**
	 * 展示顺序
	 */
	@TableField("show_index")
	private Long showIndex;

	/**
	 * 活动业务类型：1普通活动，2征订活动
	 */
	@TableField("activ_biz_type")
	private Integer activBizType;
	/**
	 * 征订书籍名称
	 */
	@TableField("sub_book_name")
	private String subBookName;
	/**
	 * 征订最大可征订数量
	 */
	@TableField("sub_max_number")
	private Long subMaxNumber;

	/**
	 * 报名名称
	 */
	@TableField("bm_name")
	private String bmName;

	/**
	 * 报名说明
	 */
	@TableField("bm_content")
	private String bmContent;

	/**
	 * 是否缴费 1-无需缴费 2-需要缴费
	 */
	@TableField("charge_type")
	private Integer chargeType;

	/**
	 * 缴费金额 元/人
	 */
	@TableField("charge_amount")
	private BigDecimal chargeAmount;

	/**
	 * web报名入口展示开始时间
	 */
	@TableField("web_bm_start_time")
	private Date webBmStartTime;

	/**
	 * web报名入口展示结束时间
	 */
	@TableField("web_bm_end_time")
	private Date webBmEndTime;

	/**
	 * 报名开始时间
	 */
	@TableField("bm_start_time")
	private Date bmStartTime;

	/**
	 * 报名结束时间
	 */
	@TableField("bm_end_time")
	private Date bmEndTime;

	/**
	 * 线下活动地点
	 */
	@TableField("activ_address")
	private String activAddress;
}
