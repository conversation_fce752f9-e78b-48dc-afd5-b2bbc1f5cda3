package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicPvDto;
import com.fh.yanx.service.res.entity.bo.ResTopicPvConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicPvBo;
import com.fh.yanx.service.res.entity.vo.ResTopicPvVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课题pv记录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResTopicPvService extends IService<ResTopicPvDto> {

    List<ResTopicPvVo> getResTopicPvListByCondition(ResTopicPvConditionBo condition);

	AjaxResult addResTopicPv(ResTopicPvBo resTopicPvBo);

	AjaxResult updateResTopicPv(ResTopicPvBo resTopicPvBo);

	ResTopicPvVo getDetail(Long id);

	 Integer getPVByCache(Long topicId);

}

