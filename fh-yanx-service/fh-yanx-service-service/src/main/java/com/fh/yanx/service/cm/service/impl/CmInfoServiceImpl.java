package com.fh.yanx.service.cm.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.fh.yanx.service.bm.service.IBmInfoService;
import com.light.core.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.cm.entity.bo.CmInfoBo;
import com.fh.yanx.service.cm.entity.bo.CmInfoConditionBo;
import com.fh.yanx.service.cm.entity.dto.CmInfoDto;
import com.fh.yanx.service.cm.entity.vo.CmInfoVo;
import com.fh.yanx.service.cm.mapper.CmInfoMapper;
import com.fh.yanx.service.cm.service.ICmInfoService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 代理商信息表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
@Service
public class CmInfoServiceImpl extends ServiceImpl<CmInfoMapper, CmInfoDto> implements ICmInfoService {

    @Resource
    private CmInfoMapper cmInfoMapper;
    @Resource
    private IBmInfoService bmInfoService;

    @Override
    public List<CmInfoVo> getCmInfoListByCondition(CmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return cmInfoMapper.getCmInfoListByCondition(condition);
    }

    @Override
    public AjaxResult addCmInfo(CmInfoBo cmInfoBo) {
        CmInfoDto cmInfo = new CmInfoDto();
        BeanUtils.copyProperties(cmInfoBo, cmInfo);
        cmInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(cmInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCmInfo(CmInfoBo cmInfoBo) {
        CmInfoDto cmInfo = new CmInfoDto();
        BeanUtils.copyProperties(cmInfoBo, cmInfo);
        if (updateById(cmInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public CmInfoVo getCmInfoByCondition(CmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        CmInfoVo vo = cmInfoMapper.getCmInfoByCondition(condition);
        return vo;
    }

    @Override
    public AjaxResult addCmInfoBatch(List<CmInfoBo> cmInfoBos) {
        if (CollectionUtils.isEmpty(cmInfoBos)) {
            return AjaxResult.fail();
        }
        List<CmInfoDto> cmInfos = cmInfoBos.stream().map(bo -> {
            CmInfoDto cmInfo = new CmInfoDto();
            BeanUtils.copyProperties(bo, cmInfo);
            cmInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            return cmInfo;
        }).collect(Collectors.toList());
        if (saveBatch(cmInfos)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public boolean checkRepeat(CmInfoBo cmInfoBo) {
        LambdaQueryWrapper<CmInfoDto> wrapper = new LambdaQueryWrapper<>();
        if (cmInfoBo.getActivId() != null) {
            wrapper.eq(CmInfoDto::getActivId, cmInfoBo.getActivId());
        }
        wrapper.eq(CmInfoDto::getCm, cmInfoBo.getCm());
        if (null != cmInfoBo.getId()) {
            wrapper.ne(CmInfoDto::getId, cmInfoBo.getId());
        }
        List<CmInfoDto> list = list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkOrderWhenUpdateCm(CmInfoBo cmInfoBo) {
        if(cmInfoBo.getId() == null || StringUtil.isBlank(cmInfoBo.getCm()) || cmInfoBo.getActivId() == null ){
            return false;
        }
        // 如果没有改变cm则直接返回false
        CmInfoDto cmInfo = getById(cmInfoBo.getId());
        if(cmInfo == null || cmInfo.getCm().equals(cmInfoBo.getCm())){
            return false;
        }
        // 检验代理商渠道参数是否有订单参数，如果有订单产生则返回true
        BmInfoConditionBo bmInfoConditionBo = new BmInfoConditionBo();
        bmInfoConditionBo.setActivId(cmInfo.getActivId());
        bmInfoConditionBo.setCm(cmInfo.getCm());
        List<BmInfoVo> bmInfoVoList = bmInfoService.getBmInfoListByCondition(bmInfoConditionBo);
        if(CollectionUtils.isNotEmpty(bmInfoVoList)){
            return true;
        }
        return false;
    }

    @Override
    public void updateCmInfoByAdmin(CmInfoBo cmInfoBo) {
        if(StringUtils.isBlank(cmInfoBo.getAdminOid())){
            return;
        }
        LambdaUpdateWrapper<CmInfoDto> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CmInfoDto::getAdminOid, cmInfoBo.getAdminOid());
        wrapper.set(CmInfoDto::getRealName, cmInfoBo.getRealName());
        wrapper.set(CmInfoDto::getPhone, cmInfoBo.getPhone());
        wrapper.set(CmInfoDto::getUpdateTime, new Date());
        update(wrapper);
    }
}