package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.PCourseCasesDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 校本课程案例接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface IPCourseCasesService extends IService<PCourseCasesDto> {

    List<PCourseCasesVo> getPCourseCasesListByCondition(PCourseCasesConditionBo condition);

    AjaxResult addPCourseCases(PCourseCasesBo pCourseCasesBo);

    AjaxResult updatePCourseCases(PCourseCasesBo pCourseCasesBo);

    PCourseCasesVo getPCourseCasesByCondition(PCourseCasesConditionBo condition);

    /**
     *  根据 ID 获取用户 OID
     * @param id the cases id
     * @return {@link String }
     */
    String getCourseCasesUserOidById(Long id);

    /**
     * 查询首页列表数据
     *
     * @param condition the condition
     * @return home list
     */
    List<PCourseCasesVo> getHomeList(PCourseCasesConditionBo condition);

    /**
     * 查询首页课程详情
     * 
     * @param casesId
     * @return
     */
    PCourseCasesVo getHomeDetail(Long casesId);

    /**
     * 查询首页推荐列表数据
     *
     * @param condition the condition
     * @return home list
     */
    List<PCourseCasesVo> topList(PCourseCasesConditionBo condition);

    /**
     * 查询内容管理详情
     *
     * @param condition the condition
     * @return p course cases vo
     * <AUTHOR>
     * @date 2023 -08-18 14:31:58
     */
    PCourseCasesVo getContentModule(PCourseCasesConditionBo condition);

    /**
     * 保存内容管理和课程模块
     *
     * @param pCourseCasesBo the p course cases bo
     * <AUTHOR>
     * @date 2023 -08-18 15:24:14
     */
    void saveContentModule(PCourseCasesBo pCourseCasesBo);
}
