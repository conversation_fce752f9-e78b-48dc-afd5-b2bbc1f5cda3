package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程审核流水表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_verify_log")
public class CourseVerifyLogDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@TableField("cases_id")
	private Long casesId;

	/**
	 * 审核进度类型，默认空：1初审，2二审，3运营审核。4综合评语，5派单评语
	 */
	@TableField("verify_process_type")
	private Integer verifyProcessType;

	/**
	 * 精品审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过
	 */
	@TableField("best_verify_type")
	private Integer bestVerifyType;

	/**
	 * 普通课程审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过
	 */
	@TableField("normal_verify_type")
	private Integer normalVerifyType;

	/**
	 * 课程流水来源：1校本课程审核，2精品课程审核
	 */
	@TableField("course_verify_source")
	private Integer courseVerifySource;

	/**
	 * 审核备注
	 */
	@TableField("verify_remark")
	private String verifyRemark;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人【审核人】
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 审核打分逐项-前端记录显示
	 */
	@TableField("verify_score_detail_json")
	private String verifyScoreDetailJson;

	/**
	 * 审核打分总分
	 */
	@TableField("verify_score_total")
	private String verifyScoreTotal;

	/**
	 * 审核人
	 */
	@TableField("user_oid")
	private String userOid;


	/**
	 * 审核人姓名
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 审核人角色
	 */
	@TableField("role_id")
	private Long roleId;

	/**
	 * 审核人角色名称
	 */
	@TableField("role_name")
	private String roleName;

	/**
	 * 采纳意见 1-建议采纳 2-建议不采纳
	 */
	@TableField("adopt_type")
	private Integer adoptType;

	/**
	 * 是否展示到前台，1展示，2不展示
	 */
	@TableField("show_type")
	private Integer showType;
}
