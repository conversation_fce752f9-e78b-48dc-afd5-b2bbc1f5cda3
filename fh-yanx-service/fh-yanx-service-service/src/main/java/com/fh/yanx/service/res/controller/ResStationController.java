package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResStationApi;
import com.fh.yanx.service.res.entity.bo.ResStationBo;
import com.fh.yanx.service.res.entity.bo.ResStationConditionBo;
import com.fh.yanx.service.res.entity.dto.ResStationDto;
import com.fh.yanx.service.res.entity.vo.ResStationVo;
import com.fh.yanx.service.res.service.IResStationService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工作站和学校表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResStationController implements ResStationApi {

    @Autowired
    private IResStationService resStationService;

    /**
     * 查询工作站和学校表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResStationVo>> getResStationPageListByCondition(@RequestBody ResStationConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResStationVo> pageInfo = new PageInfo<>(resStationService.getResStationListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询工作站和学校表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResStationVo>> getResStationListByCondition(@RequestBody ResStationConditionBo condition) {
        List<ResStationVo> list = resStationService.getResStationListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增工作站和学校表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResStation(@Validated @RequestBody ResStationBo resStationBo) {
        return resStationService.addResStation(resStationBo);
    }

    /**
     * 修改工作站和学校表
     *
     * @param resStationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResStation(@Validated @RequestBody ResStationBo resStationBo) {
        if (null == resStationBo.getStationId()) {
            return AjaxResult.fail("工作站和学校表id不能为空");
        }
        return resStationService.updateResStation(resStationBo);
    }

    /**
     * 查询工作站和学校表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResStationVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("工作站和学校表id不能为空");
        }
        ResStationVo vo = resStationService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除工作站和学校表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResStationDto resStationDto = new ResStationDto();
        resStationDto.setStationId(id);
        resStationDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resStationService.updateById(resStationDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
