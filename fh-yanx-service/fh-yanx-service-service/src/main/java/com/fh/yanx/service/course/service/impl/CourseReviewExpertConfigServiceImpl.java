package com.fh.yanx.service.course.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.fh.yanx.service.consts.ConstantsRedis;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.redis.component.RedisComponent;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.courseReviewExpertConfig.entity.dto.CourseReviewExpertConfigDto;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo;
import com.fh.yanx.service.courseReviewExpertConfig.service.ICourseReviewExpertConfigService;
import com.fh.yanx.service.courseReviewExpertConfig.mapper.CourseReviewExpertConfigMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程审核专家配置表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@Service
@Slf4j
public class CourseReviewExpertConfigServiceImpl extends ServiceImpl<CourseReviewExpertConfigMapper, CourseReviewExpertConfigDto> implements ICourseReviewExpertConfigService {

	@Resource
	private CourseReviewExpertConfigMapper courseReviewExpertConfigMapper;
	
    @Override
	public List<CourseReviewExpertConfigVo> getCourseReviewExpertConfigListByCondition(CourseReviewExpertConfigConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseReviewExpertConfigMapper.getCourseReviewExpertConfigListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseReviewExpertConfig(CourseReviewExpertConfigBo courseReviewExpertConfigBo) {
		CourseReviewExpertConfigDto courseReviewExpertConfig = new CourseReviewExpertConfigDto();
		BeanUtils.copyProperties(courseReviewExpertConfigBo, courseReviewExpertConfig);
		courseReviewExpertConfig.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseReviewExpertConfig)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult addCourseReviewExpertConfig(List<CourseReviewExpertConfigBo> courseReviewExpertConfigBoList) {
		//提取课程id
		List<Long> casesIdList = courseReviewExpertConfigBoList.stream()
				.map(CourseReviewExpertConfigBo::getCasesId)
				.distinct()
				.collect(Collectors.toList());
		//提取专家类型
		Set<Integer> expertTypeSet = courseReviewExpertConfigBoList.stream()
				.map(CourseReviewExpertConfigBo::getExpertType)
				.collect(Collectors.toSet());
		if(expertTypeSet.size()!=1){
			return AjaxResult.fail("expertType参数错误");
		}
		Integer	expertType=expertTypeSet.stream().findFirst().get();
		//查询课程下的配置表信息
		CourseReviewExpertConfigConditionBo courseReviewExpertConfigConditionBo = new CourseReviewExpertConfigConditionBo();
		courseReviewExpertConfigConditionBo.setExpertType(expertType);
		courseReviewExpertConfigConditionBo.setCasesIds(casesIdList);
		List<CourseReviewExpertConfigVo> courseReviewExpertConfigVoList = getCourseReviewExpertConfigListByCondition(courseReviewExpertConfigConditionBo);
		//数据处理
		processCourseReviewExpertData(courseReviewExpertConfigBoList,courseReviewExpertConfigVoList);
		return AjaxResult.success("保存成功");
	}

	public void processCourseReviewExpertData(List<CourseReviewExpertConfigBo> newDataList,
											  List<CourseReviewExpertConfigVo> existingDataList) {
		// 转换现有数据集合为目标对象类型
		List<CourseReviewExpertConfigBo> existingDataBoList = BeanUtil.copyToList(existingDataList, CourseReviewExpertConfigBo.class);

		// 转换现有数据集合为 Map，键为 (casesId + userOid + expertType)
		Map<String, CourseReviewExpertConfigBo> existingDataMap = existingDataBoList.stream()
				.collect(Collectors.toMap(
						d -> d.getCasesId() + "_" + d.getUserOid() + "_" + d.getExpertType(),
						d -> d,
						(v1, v2) -> v1
				));

		// 初始化新增和删除列表
		List<CourseReviewExpertConfigBo> toAddList = new ArrayList<>();
		List<CourseReviewExpertConfigBo> toDeleteList = new ArrayList<>(existingDataBoList);

		// 遍历新数据，判断是否需要新增或恢复
		for (CourseReviewExpertConfigBo newData : newDataList) {
			String key = newData.getCasesId() + "_" + newData.getUserOid() + "_" + newData.getExpertType();
			CourseReviewExpertConfigBo existingData = existingDataMap.get(key);

			if (existingData == null) {
				// 如果现有数据中不存在，加入新增列表
				toAddList.add(newData);
			} else {
				// 如果现有数据存在但标记为删除，恢复为未删除状态
				if (existingData.getIsDelete() == 1) {
					existingData.setIsDelete(0);
				}
				// 从待删除列表中移除该数据，避免被误标记删除
				toDeleteList.remove(existingData);
			}
		}

		// 输出新增和标记删除的数量
		log.info("专家配置表新增的数据个数: {}", toAddList.size());
		log.info("标记删除的数据个数: {}", toDeleteList.size());

		// 执行删除（状态修改为已删除）
		if (CollUtil.isNotEmpty(toDeleteList)) {
			deleteCourseReviewExpertConfig(toDeleteList);
		}

		// 执行添加
		List<CourseReviewExpertConfigDto> courseReviewExpertConfigDtos = BeanUtil.copyToList(toAddList, CourseReviewExpertConfigDto.class);
		if (CollUtil.isNotEmpty(courseReviewExpertConfigDtos)) {
			saveBatch(courseReviewExpertConfigDtos);
		}
	}


	// 构建唯一标识符的方法
	private static String buildKey(Long casesId, String userOid, Integer expertType) {
		return casesId + "|" + userOid + "|" + expertType;
	}


	@Override
	public AjaxResult updateCourseReviewExpertConfig(CourseReviewExpertConfigBo courseReviewExpertConfigBo) {
		CourseReviewExpertConfigDto courseReviewExpertConfig = new CourseReviewExpertConfigDto();
		BeanUtils.copyProperties(courseReviewExpertConfigBo, courseReviewExpertConfig);
		if(updateById(courseReviewExpertConfig)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseReviewExpertConfig(List<CourseReviewExpertConfigBo> courseReviewExpertConfigBo) {
		CourseReviewExpertConfigDto courseReviewExpertConfig = new CourseReviewExpertConfigDto();
		BeanUtils.copyProperties(courseReviewExpertConfigBo, courseReviewExpertConfig);
		if(updateById(courseReviewExpertConfig)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult deleteCourseReviewExpertConfig(List<CourseReviewExpertConfigBo> courseReviewExpertConfigBo) {
		List<CourseReviewExpertConfigDto> courseReviewExpertConfigDtos = BeanUtil
				.copyToList(courseReviewExpertConfigBo, CourseReviewExpertConfigDto.class);
		courseReviewExpertConfigDtos.forEach(dto -> dto.setIsDelete(StatusEnum.ISDELETE.getCode()));
		updateBatchById(courseReviewExpertConfigDtos);
		return AjaxResult.success("删除成功");
	}

	@Override
	public CourseReviewExpertConfigVo getCourseReviewExpertConfigByCondition(CourseReviewExpertConfigConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		CourseReviewExpertConfigVo vo = courseReviewExpertConfigMapper.getCourseReviewExpertConfigByCondition(condition);
		return vo;
	}

}