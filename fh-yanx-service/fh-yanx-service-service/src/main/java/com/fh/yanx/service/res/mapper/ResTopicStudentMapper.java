package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicStudentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;

/**
 * 课题组成员表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicStudentMapper extends BaseMapper<ResTopicStudentDto> {

	List<ResTopicStudentVo> getResTopicStudentListByCondition(ResTopicStudentConditionBo condition);

}
