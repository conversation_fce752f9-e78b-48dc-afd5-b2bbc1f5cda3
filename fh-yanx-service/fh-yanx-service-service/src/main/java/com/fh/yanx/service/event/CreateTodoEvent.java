package com.fh.yanx.service.event;

import java.util.Date;
import java.util.List;

import org.springframework.context.ApplicationEvent;

import com.fh.yanx.service.dto.EventPublishDto;

/**
 * 创建待办事件
 *
 * <AUTHOR>
 * @date 2022/5/10 16:42
 */
public class CreateTodoEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    public CreateTodoEvent(EventPublishDto source) {
        super(source);
    }

    /**
     * 构造一个topicEvent，课题事件
     *
     * @param eventType the event type
     * @param topicId the topic id
     * @param topicName the topic name
     * @param date date 日期，如果空会取当前事件
     * @param msg the msg
     * @param teacherName the teacher name
     * @param userOid the user oids
     * @return publish event
     */
    public static CreateTodoEvent produceTopicEvent(Integer eventType, Long topicId, String topicName, Date date,
        String msg, String teacherName, List<String> userOids) {
        if (date == null) {
            date = new Date();
        }
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setEventType(eventType);
        eventPublishDto.setTopicId(topicId);
        eventPublishDto.setTopicName(topicName);
        eventPublishDto.setDate(date);
        eventPublishDto.setMsg(msg);
        eventPublishDto.setTeacherName(teacherName);
        eventPublishDto.setUserOids(userOids);
        return new CreateTodoEvent(eventPublishDto);
    }

    // TODO 根据消息类型，各个业务重载实现不同的produce方法

}
