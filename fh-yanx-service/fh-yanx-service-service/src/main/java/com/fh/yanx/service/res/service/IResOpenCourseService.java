package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResOpenCourseDto;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseConditionBo;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseBo;
import com.fh.yanx.service.res.entity.vo.ResOpenCourseVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 公开课表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
public interface IResOpenCourseService extends IService<ResOpenCourseDto> {

    List<ResOpenCourseVo> getResOpenCourseListByCondition(ResOpenCourseConditionBo condition);

	AjaxResult addResOpenCourse(ResOpenCourseBo resOpenCourseBo);

	AjaxResult updateResOpenCourse(ResOpenCourseBo resOpenCourseBo);

	ResOpenCourseVo getDetail(Long id);

}

