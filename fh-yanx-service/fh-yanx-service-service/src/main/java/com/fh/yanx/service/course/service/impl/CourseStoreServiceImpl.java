package com.fh.yanx.service.course.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.fh.yanx.service.consts.ConstantsRedis;
import com.light.redis.component.RedisComponent;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseStoreDto;
import com.fh.yanx.service.course.entity.bo.CourseStoreConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseStoreBo;
import com.fh.yanx.service.course.entity.vo.CourseStoreVo;
import com.fh.yanx.service.course.service.ICourseStoreService;
import com.fh.yanx.service.course.mapper.CourseStoreMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程收藏表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-04 18:33:34
 */
@Service
public class CourseStoreServiceImpl extends ServiceImpl<CourseStoreMapper, CourseStoreDto> implements ICourseStoreService {

	@Resource
	private CourseStoreMapper courseStoreMapper;

	@Resource
	private RedisComponent redisComponent;
	
    @Override
	public List<CourseStoreVo> getCourseStoreListByCondition(CourseStoreConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseStoreMapper.getCourseStoreListByCondition(condition);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult addCourseStore(CourseStoreBo courseStoreBo) {
		CourseStoreDto courseStore = new CourseStoreDto();
		BeanUtils.copyProperties(courseStoreBo, courseStore);
		courseStore.setIsDelete(StatusEnum.NOTDELETE.getCode());
		save(courseStore);
		redisComponent.incr(ConstantsRedis.COURSE_STORE_PREFIX+courseStoreBo.getCasesId(),1);
		return AjaxResult.success("保存成功");
	}

	@Override
	public AjaxResult updateCourseStore(CourseStoreBo courseStoreBo) {
		CourseStoreDto courseStore = new CourseStoreDto();
		BeanUtils.copyProperties(courseStoreBo, courseStore);
		if(updateById(courseStore)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult deleteCourseStore(CourseStoreBo courseStoreBo) {
		//更具课程Id和用户Id修改
		CourseStoreConditionBo courseStoreConditionBo = new CourseStoreConditionBo();
		courseStoreConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		courseStoreConditionBo.setCasesId(courseStoreBo.getCasesId());
		courseStoreConditionBo.setUserOid(courseStoreBo.getUserOid());
		CourseStoreVo courseStoreByCondition = courseStoreMapper.getCourseStoreByCondition(courseStoreConditionBo);
		if (ObjectUtil.isNull(courseStoreByCondition)){
			return AjaxResult.success("保存成功");
		}
		courseStoreByCondition.setIsDelete(StatusEnum.ISDELETE.getCode());
		CourseStoreDto courseStoreDto = new CourseStoreDto();
		BeanUtil.copyProperties(courseStoreByCondition,courseStoreDto);
		updateById(courseStoreDto);
		redisComponent.decr(ConstantsRedis.COURSE_STORE_PREFIX+courseStoreBo.getCasesId(),1);
		return AjaxResult.success("保存成功");
	}

	@Override
	public CourseStoreVo getCourseStoreByCondition(CourseStoreConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		CourseStoreVo vo = courseStoreMapper.getCourseStoreByCondition(condition);
		if(null != vo) {
			return vo;
		}
		return new CourseStoreVo();
	}

}