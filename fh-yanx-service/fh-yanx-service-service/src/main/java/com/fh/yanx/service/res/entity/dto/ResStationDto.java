package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工作站和学校表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_station")
public class ResStationDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "station_id", type = IdType.AUTO)
	private Long stationId;

	/**
	 * 工作站名称
	 */
	@TableField("station_name")
	private String stationName;

	/**
	 * 工作站介绍
	 */
	@TableField("station_desc")
	private String stationDesc;

	/**
	 * logo图片文件原始名称-带后缀
	 */
	@TableField("logo_file_name_ori")
	private String logoFileNameOri;

	/**
	 * logo图片文件名称-不带后缀
	 */
	@TableField("logo_file_name")
	private String logoFileName;

	/**
	 * logo图片文件地址
	 */
	@TableField("logo_file_url")
	private String logoFileUrl;

	/**
	 * 工作站类型：1工作站，2学校
	 */
	@TableField("station_type")
	private Integer stationType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
