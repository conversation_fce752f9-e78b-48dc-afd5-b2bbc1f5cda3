package com.fh.yanx.service.course.controller;

import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.course.api.CourseCasesEditionApi;
import com.fh.yanx.service.course.entity.bo.*;
import com.fh.yanx.service.course.entity.dto.CourseCasesEditionDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.entity.vo.*;
import com.fh.yanx.service.course.service.*;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.user.account.entity.vo.LoginAccountVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.ArrayList;
import java.util.List;
/**
 * 课程版本表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
@RestController
@Validated
public class CourseCasesEditionController implements CourseCasesEditionApi{
	
    @Autowired
    private ICourseCasesEditionService courseCasesEditionService;
	@Autowired
	private BaseDataService baseDataService;
	@Autowired
	private ICourseCasesTypeEditionService courseCasesTypeEditionService;
	@Autowired
	private IPCategoryService pCategoryService;
	@Autowired
	private ICourseCasesInfoEditionService courseCasesInfoEditionService;
	@Autowired
	private ICourseModuleEditionService courseModuleEditionService;

    /**
     * 查询课程版本表分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
    @Override
    public AjaxResult<PageInfo<CourseCasesEditionVo>> getCourseCasesEditionPageListByCondition(@RequestBody CourseCasesEditionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseCasesEditionVo> pageInfo = new PageInfo<>(courseCasesEditionService.getCourseCasesEditionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程版本表列表
	 * <AUTHOR>
	 * @date 2024-12-05 10:40:52
	 */
	@Override
	public AjaxResult<List<CourseCasesEditionVo>> getCourseCasesEditionListByCondition(@RequestBody CourseCasesEditionConditionBo condition){
		List<CourseCasesEditionVo> list = courseCasesEditionService.getCourseCasesEditionListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程版本表
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
	@Override
    public AjaxResult addCourseCasesEdition(@Validated @RequestBody CourseCasesEditionBo courseCasesEditionBo){
		return courseCasesEditionService.addCourseCasesEdition(courseCasesEditionBo);
    }

    /**
	 * 修改课程版本表
	 * @param courseCasesEditionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
	 */
	@Override
	public AjaxResult updateCourseCasesEdition(@Validated @RequestBody CourseCasesEditionBo courseCasesEditionBo) {
		if(null == courseCasesEditionBo.getId()) {
			return AjaxResult.fail("课程版本表id不能为空");
		}
		return courseCasesEditionService.updateCourseCasesEdition(courseCasesEditionBo);
	}

	/**
	 * 查询课程版本表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
	 */
	@Override
	public AjaxResult<CourseCasesEditionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程版本表id不能为空");
		}
		CourseCasesEditionConditionBo condition = new CourseCasesEditionConditionBo();
		condition.setId(id);
		CourseCasesEditionVo vo = courseCasesEditionService.getCourseCasesEditionByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程版本表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseCasesEditionDto courseCasesEditionDto = new CourseCasesEditionDto();
		courseCasesEditionDto.setId(id);
		courseCasesEditionDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseCasesEditionService.updateById(courseCasesEditionDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	@Override
	public AjaxResult<CourseCasesEditionVo> getContentModuleEdition(@RequestBody CourseCasesEditionConditionBo condition) {
		if (condition.getId() == null) {
			return AjaxResult.fail("参数错误");
		}
		// 仅查询个人版本记录
		String currentUserOid = baseDataService.getCurrentUserOid();
		condition.setUserOid(currentUserOid);
		CourseCasesEditionVo contentModuleEdition = courseCasesEditionService.getContentModule(condition);
		// 封装原始返回信息（字典类型等）
		convertHomeResultOne(contentModuleEdition);
		return AjaxResult.success(contentModuleEdition);
	}

	/**
	 * 处理返回结果的数据
	 *
	 * @param courseCasesEditionVo the p course cases vos
	 * <AUTHOR>
	 * @date 2023 -08-14 13:52:20
	 */
	private void convertHomeResultOne(CourseCasesEditionVo courseCasesEditionVo) {
		if (courseCasesEditionVo == null) {
			return;
		}

		// 案例类型名称集合
		List<String> casesTypeNameList = courseCasesTypeEditionService.getCasesTypeNameList(courseCasesEditionVo.getId());
		courseCasesEditionVo.setTypeNameList(casesTypeNameList);
		// 学段翻译
		if (null != courseCasesEditionVo.getPhase()) {
			PCategoryDto pCategoryDto = pCategoryService.getById(courseCasesEditionVo.getPhase());
			courseCasesEditionVo.setPhaseName(pCategoryDto.getName());
		}
		// 年份翻译
		if (null != courseCasesEditionVo.getYear()) {
			PCategoryDto pCategoryDto = pCategoryService.getById(courseCasesEditionVo.getYear());
			courseCasesEditionVo.setYearName(pCategoryDto.getName());
		}

		CourseCasesInfoEditionVo courseCasesInfoEditionVo = courseCasesInfoEditionService.infoDetail(courseCasesEditionVo.getId());
		courseCasesEditionVo.setCourseCasesInfoVo(courseCasesInfoEditionVo);
		CourseModuleEditionConditionBo condition = new CourseModuleEditionConditionBo();
		condition.setCasesEditionId(courseCasesInfoEditionVo.getId());
		condition.setPageNo(SystemConstants.NO_PAGE);
		courseCasesEditionVo.setCourseModuleVos(
				courseModuleEditionService.getCourseModuleListByConditionWithAttachment(condition, true));

	}
}
