package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResStationDto;
import com.fh.yanx.service.res.entity.bo.ResStationConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationBo;
import com.fh.yanx.service.res.entity.vo.ResStationVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 工作站和学校表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResStationService extends IService<ResStationDto> {

    List<ResStationVo> getResStationListByCondition(ResStationConditionBo condition);

	AjaxResult addResStation(ResStationBo resStationBo);

	AjaxResult updateResStation(ResStationBo resStationBo);

	ResStationVo getDetail(Long id);

}

