package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseHomeConfigDto;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigBo;
import com.fh.yanx.service.course.entity.vo.CourseHomeConfigVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 推荐展示位接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface ICourseHomeConfigService extends IService<CourseHomeConfigDto> {

    List<CourseHomeConfigVo> getCourseHomeConfigListByCondition(CourseHomeConfigConditionBo condition);

	AjaxResult addCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo);

	AjaxResult updateCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo);

	CourseHomeConfigVo getCourseHomeConfigByCondition(CourseHomeConfigConditionBo condition);

	/**
	 * 保存首页展示配置
	 *
	 * @param courseHomeConfigBo the course home config bo
	 * @return long long
	 * <AUTHOR>
	 * @date 2023 -08-18 11:56:35
	 */
	Long saveCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo);

}

