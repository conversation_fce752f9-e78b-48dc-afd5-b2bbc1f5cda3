package com.fh.yanx.service.org.controller;

import com.fh.yanx.service.org.api.OrganizationApplyCommunicateApi;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyCommunicateDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateBo;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyCommunicateVo;
import com.fh.yanx.service.org.service.IOrganizationApplyCommunicateService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;

/**
 * 组织申请沟通记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
@RestController
@Validated
public class OrganizationApplyCommunicateController implements OrganizationApplyCommunicateApi {

    @Autowired
    private IOrganizationApplyCommunicateService organizationApplyCommunicateService;

    /**
     * 查询组织申请沟通记录表分页列表
     *
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<PageInfo<OrganizationApplyCommunicateVo>> getOrganizationApplyCommunicatePageListByCondition(@RequestBody OrganizationApplyCommunicateConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<OrganizationApplyCommunicateVo> pageInfo = new PageInfo<>(organizationApplyCommunicateService.getOrganizationApplyCommunicateListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询组织申请沟通记录表列表
     *
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<List<OrganizationApplyCommunicateVo>> getOrganizationApplyCommunicateListByCondition(@RequestBody OrganizationApplyCommunicateConditionBo condition) {
        List<OrganizationApplyCommunicateVo> list = organizationApplyCommunicateService.getOrganizationApplyCommunicateListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增组织申请沟通记录表
     *
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult addOrganizationApplyCommunicate(@Validated @RequestBody OrganizationApplyCommunicateBo organizationApplyCommunicateBo) {
        return organizationApplyCommunicateService.addOrganizationApplyCommunicate(organizationApplyCommunicateBo);
    }

    /**
     * 修改组织申请沟通记录表
     *
     * @param organizationApplyCommunicateBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult updateOrganizationApplyCommunicate(@Validated @RequestBody OrganizationApplyCommunicateBo organizationApplyCommunicateBo) {
        if (null == organizationApplyCommunicateBo.getId()) {
            return AjaxResult.fail("组织申请沟通记录表id不能为空");
        }
        return organizationApplyCommunicateService.updateOrganizationApplyCommunicate(organizationApplyCommunicateBo);
    }

    /**
     * 查询组织申请沟通记录表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<OrganizationApplyCommunicateVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("组织申请沟通记录表id不能为空");
        }
        OrganizationApplyCommunicateConditionBo condition = new OrganizationApplyCommunicateConditionBo();
        condition.setId(id);
        OrganizationApplyCommunicateVo vo = organizationApplyCommunicateService.getOrganizationApplyCommunicateByCondition(condition);
        return AjaxResult.success(vo);
    }


    /**
     * 删除组织申请沟通记录表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        OrganizationApplyCommunicateDto organizationApplyCommunicateDto = new OrganizationApplyCommunicateDto();
        organizationApplyCommunicateDto.setId(id);
        organizationApplyCommunicateDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (organizationApplyCommunicateService.updateById(organizationApplyCommunicateDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
