package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesGradeDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeConditionBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesGradeVo;

/**
 * 校本课程案例年级Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesGradeMapper extends BaseMapper<PCourseCasesGradeDto> {

	List<PCourseCasesGradeVo> getPCourseCasesGradeListByCondition(PCourseCasesGradeConditionBo condition);

	PCourseCasesGradeVo getPCourseCasesGradeByCondition(PCourseCasesGradeConditionBo condition);

}
