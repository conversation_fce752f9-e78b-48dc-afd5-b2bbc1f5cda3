package com.fh.yanx.service.activ.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoViewPermissionDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 活动内容观看权限表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
public interface IActivInfoViewPermissionService extends IService<ActivInfoViewPermissionDto> {

    List<ActivInfoViewPermissionVo> getActivInfoViewPermissionListByCondition(ActivInfoViewPermissionConditionBo condition);

	AjaxResult addActivInfoViewPermission(ActivInfoViewPermissionBo activInfoViewPermissionBo);

	AjaxResult updateActivInfoViewPermission(ActivInfoViewPermissionBo activInfoViewPermissionBo);

	ActivInfoViewPermissionVo getActivInfoViewPermissionByCondition(ActivInfoViewPermissionConditionBo condition);

	boolean deleteAndAddActivInfoViewPermission(ActivInfoBo activInfoBo);
}

