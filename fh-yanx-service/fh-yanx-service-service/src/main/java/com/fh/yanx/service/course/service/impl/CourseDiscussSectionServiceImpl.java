package com.fh.yanx.service.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionBo;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseDiscussSectionDto;
import com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo;
import com.fh.yanx.service.course.mapper.CourseDiscussSectionMapper;
import com.fh.yanx.service.course.service.ICourseDiscussSectionService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.UnifiedException;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 课程讨论区表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
@Service
public class CourseDiscussSectionServiceImpl extends ServiceImpl<CourseDiscussSectionMapper, CourseDiscussSectionDto> implements ICourseDiscussSectionService {

	@Resource
	private CourseDiscussSectionMapper courseDiscussSectionMapper;
	
    @Override
	public List<CourseDiscussSectionVo> getCourseDiscussSectionListByCondition(CourseDiscussSectionConditionBo condition) {
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseDiscussSectionMapper.getCourseDiscussSectionListByCondition(condition);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult addCourseDiscussSection(CourseDiscussSectionBo courseDiscussSectionBo) {
		Long parentId = courseDiscussSectionBo.getParentId();
		if(parentId == null){
			parentId = 0L;
		}
		courseDiscussSectionBo.setSuperiorIds("0");
		if(!parentId.equals(0L)) {
			CourseDiscussSectionVo parentDiscussSection = this.queryById(parentId);
			if(parentDiscussSection == null){
				return AjaxResult.fail("父级评论不存在");
			}
			courseDiscussSectionBo.setSuperiorIds(parentDiscussSection.getSuperiorIds() + "," + parentId);
			// 增加回复数量
			this.baseMapper.addReplyNumById(parentId, 1);
		}

		CourseDiscussSectionDto courseDiscussSection = new CourseDiscussSectionDto();
		BeanUtils.copyProperties(courseDiscussSectionBo, courseDiscussSection);
		courseDiscussSection.setIsDelete(StatusEnum.NOTDELETE.getCode());
		this.save(courseDiscussSection);
		return AjaxResult.success();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult updateCourseDiscussSection(CourseDiscussSectionBo courseDiscussSectionBo) {
		CourseDiscussSectionDto courseDiscussSection = new CourseDiscussSectionDto();
		BeanUtils.copyProperties(courseDiscussSectionBo, courseDiscussSection);
		this.updateById(courseDiscussSection);
		return AjaxResult.success("保存成功");
	}

	@Override
	public CourseDiscussSectionVo queryById(Long id) {
		CourseDiscussSectionConditionBo condition = new CourseDiscussSectionConditionBo();
		condition.setId(id);
		List<CourseDiscussSectionVo> list = courseDiscussSectionMapper.getCourseDiscussSectionListByCondition(condition);
		if(CollUtil.isEmpty(list)){
			return null;
		}
		return list.get(0);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIdAndUserOid(Long id, String userOid) {
		CourseDiscussSectionVo courseDiscussSectionVo = this.queryById(id);
		if(courseDiscussSectionVo == null){
			return true;
		}
        if (!courseDiscussSectionVo.getUserOid().equals(userOid)) {
			throw new UnifiedException("用户信息不匹配，无法进行删除");
        }

		// 减少回复数量
		Long parentId = courseDiscussSectionVo.getParentId();
		if(parentId != null && !parentId.equals(0L)){
			this.baseMapper.addReplyNumById(parentId, -1);
		}

		// 删除所有子集
		this.baseMapper.deleteByParentId(id);

		// 删除信息
		UpdateWrapper<CourseDiscussSectionDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(CourseDiscussSectionDto::getId, id);
		updateWrapper.lambda().set(CourseDiscussSectionDto::getIsDelete, StatusEnum.YES.getCode());
		return this.update(updateWrapper);
	}

	@Override
	public boolean deleteById(Long id) {
		CourseDiscussSectionVo courseDiscussSectionVo = this.queryById(id);
		if(courseDiscussSectionVo == null){
			return true;
		}
		// 减少回复数量
		Long parentId = courseDiscussSectionVo.getParentId();
		if(parentId != null && !parentId.equals(0L)){
			this.baseMapper.addReplyNumById(parentId, -1);
		}

		// 删除所有子集
		this.baseMapper.deleteByParentId(id);

		// 删除信息
		UpdateWrapper<CourseDiscussSectionDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(CourseDiscussSectionDto::getId, id);
		updateWrapper.lambda().set(CourseDiscussSectionDto::getIsDelete, StatusEnum.YES.getCode());
		return this.update(updateWrapper);
	}

	@Override
	public Long queryCountByCasesId(Long casesId) {
		QueryWrapper<CourseDiscussSectionDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(CourseDiscussSectionDto::getCasesId, casesId);
		queryWrapper.lambda().eq(CourseDiscussSectionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		return this.baseMapper.selectCount(queryWrapper).longValue();
	}

	@Override
	public Map<Long, Long> queryCountMapByCasesIdList(List<Long> casesIdList) {
		List<CourseDiscussSectionDto> list = this.queryByCasesIdList(casesIdList);
		if(CollUtil.isEmpty(list)){
			return Collections.emptyMap();
		}
		return list.stream().collect(Collectors.groupingBy(CourseDiscussSectionDto::getCasesId, Collectors.counting()));
	}

	public List<CourseDiscussSectionDto> queryByCasesIdList(List<Long> casesIdList) {
		QueryWrapper<CourseDiscussSectionDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(CourseDiscussSectionDto::getCasesId, casesIdList);
		queryWrapper.lambda().eq(CourseDiscussSectionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		return this.baseMapper.selectList(queryWrapper);
	}
}