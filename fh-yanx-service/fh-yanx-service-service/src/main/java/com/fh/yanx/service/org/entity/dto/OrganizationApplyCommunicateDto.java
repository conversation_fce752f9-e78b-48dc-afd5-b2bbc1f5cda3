package com.fh.yanx.service.org.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 组织申请沟通记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("organization_apply_communicate")
public class OrganizationApplyCommunicateDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 组织申请id
	 */
	@TableField("organization_apply_id")
	private Long organizationApplyId;

	/**
	 * 沟通内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 处理状态：1：处理中，2：已处理
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 是否删除：0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
