package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.CourseHomeConfigApi;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigBo;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseHomeConfigDto;
import com.fh.yanx.service.course.entity.vo.CourseHomeConfigVo;
import com.fh.yanx.service.course.service.ICourseHomeConfigService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 推荐展示位
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@RestController
@Validated
public class CourseHomeConfigController implements CourseHomeConfigApi{
	
    @Autowired
    private ICourseHomeConfigService courseHomeConfigService;

    /**
     * 查询推荐展示位分页列表
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @Override
    public AjaxResult<PageInfo<CourseHomeConfigVo>> getCourseHomeConfigPageListByCondition(@RequestBody CourseHomeConfigConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseHomeConfigVo> pageInfo = new PageInfo<>(courseHomeConfigService.getCourseHomeConfigListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询推荐展示位列表
	 * <AUTHOR>
	 * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult<List<CourseHomeConfigVo>> getCourseHomeConfigListByCondition(@RequestBody CourseHomeConfigConditionBo condition){
		List<CourseHomeConfigVo> list = courseHomeConfigService.getCourseHomeConfigListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增推荐展示位
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
	@Override
    public AjaxResult addCourseHomeConfig(@Validated @RequestBody CourseHomeConfigBo courseHomeConfigBo){
		return courseHomeConfigService.addCourseHomeConfig(courseHomeConfigBo);
    }

    /**
	 * 修改推荐展示位
	 * @param courseHomeConfigBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult updateCourseHomeConfig(@Validated @RequestBody CourseHomeConfigBo courseHomeConfigBo) {
		if(null == courseHomeConfigBo.getHomeConfigId()) {
			return AjaxResult.fail("推荐展示位id不能为空");
		}
		return courseHomeConfigService.updateCourseHomeConfig(courseHomeConfigBo);
	}

	/**
	 * 查询推荐展示位详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult<CourseHomeConfigVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("推荐展示位id不能为空");
		}
		CourseHomeConfigConditionBo condition = new CourseHomeConfigConditionBo();
		condition.setHomeConfigId(id);
		CourseHomeConfigVo vo = courseHomeConfigService.getCourseHomeConfigByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除推荐展示位
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseHomeConfigDto courseHomeConfigDto = new CourseHomeConfigDto();
		courseHomeConfigDto.setHomeConfigId(id);
		courseHomeConfigDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseHomeConfigService.updateById(courseHomeConfigDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	@Override
	public AjaxResult saveCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo) {
		courseHomeConfigService.saveCourseHomeConfig(courseHomeConfigBo);
		return AjaxResult.success();
	}
}
