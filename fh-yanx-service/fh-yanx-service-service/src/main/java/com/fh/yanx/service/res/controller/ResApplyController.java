package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResApplyApi;
import com.fh.yanx.service.res.entity.dto.ResApplyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResApplyConditionBo;
import com.fh.yanx.service.res.entity.bo.ResApplyBo;
import com.fh.yanx.service.res.entity.vo.ResApplyVo;
import com.fh.yanx.service.res.service.IResApplyService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 合作意向申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-27 16:19:35
 */
@RestController
@Validated
public class ResApplyController implements ResApplyApi{
	
    @Autowired
    private IResApplyService resApplyService;

    /**
     * 查询合作意向申请表分页列表
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
    @Override
    public AjaxResult<PageInfo<ResApplyVo>> getResApplyPageListByCondition(@RequestBody ResApplyConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ResApplyVo> pageInfo = new PageInfo<>(resApplyService.getResApplyListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询合作意向申请表列表
	 * <AUTHOR>
	 * @date 2022-10-27 16:19:35
	 */
	@Override
	public AjaxResult<List<ResApplyVo>> getResApplyListByCondition(@RequestBody ResApplyConditionBo condition){
		List<ResApplyVo> list = resApplyService.getResApplyListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增合作意向申请表
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
	@Override
    public AjaxResult addResApply(@Validated @RequestBody ResApplyBo resApplyBo){
		return resApplyService.addResApply(resApplyBo);
    }

    /**
	 * 修改合作意向申请表
	 * @param resApplyBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
	 */
	@Override
	public AjaxResult updateResApply(@Validated @RequestBody ResApplyBo resApplyBo) {
		if(null == resApplyBo.getApplyId()) {
			return AjaxResult.fail("合作意向申请表id不能为空");
		}
		return resApplyService.updateResApply(resApplyBo);
	}

	/**
	 * 查询合作意向申请表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
	 */
	@Override
	public AjaxResult<ResApplyVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("合作意向申请表id不能为空");
		}
		ResApplyVo vo = resApplyService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除合作意向申请表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ResApplyDto resApplyDto = new ResApplyDto();
		resApplyDto.setApplyId(id);
		resApplyDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(resApplyService.updateById(resApplyDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
