package com.fh.yanx.service.jz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesGradeDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesGradeVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 金中-校本课程案例年级接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface IJzCourseCasesGradeService extends IService<JzCourseCasesGradeDto> {

    List<JzCourseCasesGradeVo> getJzCourseCasesGradeListByCondition(JzCourseCasesGradeConditionBo condition);

	AjaxResult addJzCourseCasesGrade(JzCourseCasesGradeBo jzCourseCasesGradeBo);

	AjaxResult updateJzCourseCasesGrade(JzCourseCasesGradeBo jzCourseCasesGradeBo);

	JzCourseCasesGradeVo getJzCourseCasesGradeByCondition(JzCourseCasesGradeConditionBo condition);

}

