package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.PCourseCasesBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseCasesEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesEditionVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程版本表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
public interface ICourseCasesEditionService extends IService<CourseCasesEditionDto> {

    List<CourseCasesEditionVo> getCourseCasesEditionListByCondition(CourseCasesEditionConditionBo condition);

	AjaxResult addCourseCasesEdition(CourseCasesEditionBo courseCasesEditionBo);

	AjaxResult updateCourseCasesEdition(CourseCasesEditionBo courseCasesEditionBo);

	CourseCasesEditionVo getCourseCasesEditionByCondition(CourseCasesEditionConditionBo condition);

	/**
	 * 保存课程版本记录
	 *
	 * @param pCourseCasesBo
	 * @return void
	 * <AUTHOR>
	 * @date 2024/12/5 11:30
	 **/
	void saveContentModuleEdition(PCourseCasesBo pCourseCasesBo);

	/**
	 * 查询内容管理详情
	 *
	 * @param condition the condition
	 * @return p course cases vo
	 * <AUTHOR>
	 * @date 2023 -08-18 14:31:58
	 */
	CourseCasesEditionVo getContentModule(CourseCasesEditionConditionBo condition);

}

