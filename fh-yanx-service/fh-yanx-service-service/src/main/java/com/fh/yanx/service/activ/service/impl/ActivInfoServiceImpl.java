package com.fh.yanx.service.activ.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoShowBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo;
import com.fh.yanx.service.activ.service.IActivInfoIntroduceService;
import com.fh.yanx.service.consts.ConstantsLong;
import com.fh.yanx.service.enums.ActivInfoShowType;
import com.fh.yanx.service.enums.ActivType;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.activ.entity.dto.ActivInfoDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;
import com.fh.yanx.service.activ.service.IActivInfoService;
import com.fh.yanx.service.activ.mapper.ActivInfoMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 新时代文化校园活动信息表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
@Service
public class ActivInfoServiceImpl extends ServiceImpl<ActivInfoMapper, ActivInfoDto> implements IActivInfoService {

	@Resource
	private ActivInfoMapper activInfoMapper;
	@Resource
	IActivInfoService activInfoService;
	@Resource
	IActivInfoIntroduceService activInfoIntroduceService;
	
    @Override
	public List<ActivInfoVo> getActivInfoListByCondition(ActivInfoConditionBo condition) {
    	condition.setNowTime(new Date());
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return activInfoMapper.getActivInfoListByCondition(condition);
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public AjaxResult addActivInfo(ActivInfoBo activInfoBo) {
		ActivInfoDto activInfo = new ActivInfoDto();
		BeanUtils.copyProperties(activInfoBo, activInfo);
		activInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(activInfoService.save(activInfo)){
			activInfoBo.setActivId(activInfo.getActivId());
			activInfoIntroduceService.deleteAndAddActivIntroduces(activInfoBo);
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public AjaxResult updateActivInfo(ActivInfoBo activInfoBo) {
		ActivInfoDto activInfo = new ActivInfoDto();
		BeanUtils.copyProperties(activInfoBo, activInfo);
		if(activInfoService.updateById(activInfo)){
			activInfoIntroduceService.deleteAndAddActivIntroduces(activInfoBo);
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ActivInfoVo getActivInfoByCondition(ActivInfoConditionBo condition) {
    	condition.setNowTime(new Date());
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		ActivInfoVo activInfoVo = activInfoMapper.getActivInfoByCondition(condition);
		if (activInfoVo == null) {
			return null;
		}

		// 查询活动介绍
		ActivInfoIntroduceConditionBo infoIntroduceConditionBo = new ActivInfoIntroduceConditionBo();
		infoIntroduceConditionBo.setActivId(activInfoVo.getActivId());
		List<ActivInfoIntroduceVo> infoIntroduceVos =
				activInfoIntroduceService.getActivInfoIntroduceListByCondition(infoIntroduceConditionBo);
		if (CollectionUtil.isNotEmpty(infoIntroduceVos)) {
			activInfoVo.setActivInfoIntroduces(infoIntroduceVos);
		}
		return activInfoVo;
	}

	@Override
	public AjaxResult updateActivType(ActivInfoBo activInfoBo) {
    	ActivInfoDto activInfoDto = baseMapper
				.selectOne(new LambdaQueryWrapper<ActivInfoDto>()
						.eq(ActivInfoDto::getActivId, activInfoBo.getActivId())
						.eq(ActivInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
    	if (activInfoDto == null) {
    		return AjaxResult.fail("当前活动不存在");
		}
    	activInfoDto.setActivType(activInfoBo.getActivType());
    	if (updateById(activInfoDto)) {
    		return AjaxResult.success("上下架成功");
		}
		return AjaxResult.fail("上下架失败");
	}

	@Override
	public AjaxResult setActivInfoShow(ActivInfoShowBo activInfoShowBo) {
    	List<ActivInfoDto> activInfoDtos = baseMapper.selectList(new LambdaQueryWrapper<ActivInfoDto>()
				.eq(ActivInfoDto::getShowType, activInfoShowBo.getShowType())
				.eq(ActivInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
    	if (CollectionUtil.isNotEmpty(activInfoDtos)) {
    		activInfoDtos.forEach(activInfoDto -> {
    			activInfoDto.setShowType(ActivInfoShowType.DEFAULT.getCode());
    			activInfoDto.setShowIndex(ConstantsLong.NUM_0);
			});
    		updateBatchById(activInfoDtos);
		}
    	if (CollectionUtil.isEmpty(activInfoShowBo.getActivInfoList())) {
    		return AjaxResult.success("设置活动展示成功");
		}
    	List<ActivInfoDto> entities = new ArrayList<>();
    	for (ActivInfoBo activInfoBo : activInfoShowBo.getActivInfoList()) {
    		if (null == activInfoBo.getActivId()) {
    			continue;
			}
    		ActivInfoDto entity = new ActivInfoDto();
    		entity.setActivId(activInfoBo.getActivId());
    		entity.setShowType(activInfoShowBo.getShowType());
    		entity.setShowIndex(activInfoBo.getShowIndex());
    		entities.add(entity);
		}
    	if (updateBatchById(entities)) {
			return AjaxResult.success("设置活动展示成功");
		}
    	return AjaxResult.fail("设置活动展示失败");
	}

	@Override
	public List<ActivInfoVo> getUserActivList(ActivInfoConditionBo conditionBo) {
    	conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
    	conditionBo.setActivType(ActivType.ACTIV_TYPE_UP.getCode());
		return baseMapper.getUserActivList(conditionBo);
	}

}