package com.fh.yanx.service.jz.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesDto;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesConditionBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo;

/**
 * 金中-校本课程案例Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesMapper extends BaseMapper<JzCourseCasesDto> {

	List<JzCourseCasesVo> getJzCourseCasesListByCondition(JzCourseCasesConditionBo condition);

	JzCourseCasesVo getJzCourseCasesByCondition(JzCourseCasesConditionBo condition);

	/**
	 * 查询首页列表数据
	 *
	 * @param condition the condition
	 * @return home list
	 */
	List<JzCourseCasesVo> getHomeList(JzCourseCasesConditionBo condition);
}
