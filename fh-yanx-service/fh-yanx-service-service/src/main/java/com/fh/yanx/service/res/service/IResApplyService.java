package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResApplyDto;
import com.fh.yanx.service.res.entity.bo.ResApplyConditionBo;
import com.fh.yanx.service.res.entity.bo.ResApplyBo;
import com.fh.yanx.service.res.entity.vo.ResApplyVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 合作意向申请表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-27 16:19:35
 */
public interface IResApplyService extends IService<ResApplyDto> {

    List<ResApplyVo> getResApplyListByCondition(ResApplyConditionBo condition);

	AjaxResult addResApply(ResApplyBo resApplyBo);

	AjaxResult updateResApply(ResApplyBo resApplyBo);

	ResApplyVo getDetail(Long id);

}

