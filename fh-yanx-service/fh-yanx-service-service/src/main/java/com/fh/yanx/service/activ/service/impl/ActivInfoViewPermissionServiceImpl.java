package com.fh.yanx.service.activ.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.activ.entity.dto.ActivInfoViewPermissionDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo;
import com.fh.yanx.service.activ.service.IActivInfoViewPermissionService;
import com.fh.yanx.service.activ.mapper.ActivInfoViewPermissionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 活动内容观看权限表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
@Service
public class ActivInfoViewPermissionServiceImpl extends ServiceImpl<ActivInfoViewPermissionMapper, ActivInfoViewPermissionDto> implements IActivInfoViewPermissionService {

	@Resource
	private ActivInfoViewPermissionMapper activInfoViewPermissionMapper;
	
    @Override
	public List<ActivInfoViewPermissionVo> getActivInfoViewPermissionListByCondition(ActivInfoViewPermissionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return activInfoViewPermissionMapper.getActivInfoViewPermissionListByCondition(condition);
	}

	@Override
	public AjaxResult addActivInfoViewPermission(ActivInfoViewPermissionBo activInfoViewPermissionBo) {
		ActivInfoViewPermissionDto activInfoViewPermission = new ActivInfoViewPermissionDto();
		BeanUtils.copyProperties(activInfoViewPermissionBo, activInfoViewPermission);
		activInfoViewPermission.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(activInfoViewPermission)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateActivInfoViewPermission(ActivInfoViewPermissionBo activInfoViewPermissionBo) {
		ActivInfoViewPermissionDto activInfoViewPermission = new ActivInfoViewPermissionDto();
		BeanUtils.copyProperties(activInfoViewPermissionBo, activInfoViewPermission);
		if(updateById(activInfoViewPermission)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ActivInfoViewPermissionVo getActivInfoViewPermissionByCondition(ActivInfoViewPermissionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return activInfoViewPermissionMapper.getActivInfoViewPermissionByCondition(condition);
	}

	@Override
	public boolean deleteAndAddActivInfoViewPermission(ActivInfoBo activInfoBo) {
    	if (null == activInfoBo.getActivId()) {
    		return false;
		}
    	// 删除
		LambdaUpdateWrapper<ActivInfoViewPermissionDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(ActivInfoViewPermissionDto::getActivId, activInfoBo.getActivId());
		updateWrapper.eq(ActivInfoViewPermissionDto::getViewType, activInfoBo.getViewType());
		updateWrapper.set(ActivInfoViewPermissionDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		update(updateWrapper);

		// 新增
		if (CollectionUtil.isEmpty(activInfoBo.getViewPermissions())) {
			return true;
		}
		List<ActivInfoViewPermissionDto> viewPermissionDtos = activInfoBo.getViewPermissions().stream()
				.map(viewPermissionBo -> {
					ActivInfoViewPermissionDto entity = new ActivInfoViewPermissionDto();
					BeanUtils.copyProperties(viewPermissionBo, entity);
					entity.setViewType(activInfoBo.getViewType());
					entity.setActivId(activInfoBo.getActivId());
					return entity;
				}).collect(Collectors.toList());
		return saveBatch(viewPermissionDtos);
	}

}