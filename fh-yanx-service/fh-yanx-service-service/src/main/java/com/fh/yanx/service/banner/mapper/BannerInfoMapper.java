package com.fh.yanx.service.banner.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.fh.yanx.service.banner.entity.dto.BannerInfoDto;
import com.fh.yanx.service.banner.entity.vo.BannerInfoVo;

/**
 * 新时代文化校园banner信息表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
public interface BannerInfoMapper extends BaseMapper<BannerInfoDto> {

	List<BannerInfoVo> getBannerListByCondition(BannerInfoConditionBo condition);

	BannerInfoVo getBannerByCondition(BannerInfoConditionBo condition);

}
