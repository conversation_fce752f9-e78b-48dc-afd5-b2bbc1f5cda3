package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资讯
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_information")
public class ResInformationDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "information_id", type = IdType.AUTO)
	private Long informationId;

	/**
	 * 资讯名称
	 */
	@TableField("information_name")
	private String informationName;

	/**
	 * 资讯内容
	 */
	@TableField("information_desc")
	private String informationDesc;

	/**
	 * 资讯顺序
	 */
	@TableField("information_index")
	private Long informationIndex;

	/**
	 * 资讯图片文件原始名称-带后缀
	 */
	@TableField("img_file_name_ori")
	private String imgFileNameOri;

	/**
	 * 资讯图片文件名称-不带后缀
	 */
	@TableField("img_file_name")
	private String imgFileName;

	/**
	 * 资讯图片文件地址
	 */
	@TableField("img_file_url")
	private String imgFileUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
