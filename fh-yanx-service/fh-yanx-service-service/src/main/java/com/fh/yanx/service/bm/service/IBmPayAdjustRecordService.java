package com.fh.yanx.service.bm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.bm.entity.bo.BmPayAdjustRecordBo;
import com.fh.yanx.service.bm.entity.bo.BmPayAdjustRecordConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmPayAdjustRecordDto;
import com.fh.yanx.service.bm.entity.vo.BmPayAdjustRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-05-27  17:11
 */
public interface IBmPayAdjustRecordService extends IService<BmPayAdjustRecordDto> {

    List<BmPayAdjustRecordVo> getBmPayAdjustRecordListByCondition(BmPayAdjustRecordConditionBo condition);

    AjaxResult addBmPayAdjustRecord(BmPayAdjustRecordBo bmPayAdjustRecordBo);

    AjaxResult updateBmPayAdjustRecord(BmPayAdjustRecordBo bmPayAdjustRecordBo);

    BmPayAdjustRecordVo getBmPayAdjustRecordDetail(BmPayAdjustRecordConditionBo condition);

}

