package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课题表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResTopicService extends IService<ResTopicDto> {

    List<ResTopicVo> getResTopicListByCondition(ResTopicConditionBo condition);

    AjaxResult addResTopic(ResTopicBo resTopicBo);

    /**
     * 修改课题 状态
     * 
     * @param resTopicBo
     * @return
     */
    AjaxResult updateResTopicStatus(ResTopicBo resTopicBo);

    ResTopicVo getDetail(Long id, Long organizationId);

    /**
     * 待办流水处理
     *
     * @param topicId 主题
     * @param topicProcess the topic process
     * @param topicName the topic name
     * @param teacherOids 审批老师集合
     * @param currentUserOid 当前登陆人，可以是老师或者学生
     * @param submitUser the submit user，课题提交人-学生
     */
    void publishEvent(Long topicId, Integer topicProcess, String topicName, List<String> teacherOids,
        String currentUserOid, String submitUser);

}
