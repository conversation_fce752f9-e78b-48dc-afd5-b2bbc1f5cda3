package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课例表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_lesson_cases")
public class PLessonCasesDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 标题
	 */
	@TableField("title")
	private String title;

	/**
	 * 描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 封面
	 */
	@TableField("cover")
	private String cover;

	/**
	 * 课例实验区id
	 */
	@TableField("lc_zone")
	private Long lcZone;

	/**
	 * 课例科目
	 */
	@TableField("lc_subject")
	private Long lcSubject;

	/**
	 * 媒体id
	 */
	@TableField("media_id")
	private String mediaId;

	/**
	 * 数据来源：1汇景数据
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 
	 */
	@TableField("create_user_oid")
	private String createUserOid;

	/**
	 * 
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 删除状态
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
