package com.fh.yanx.service.banner.controller;

import com.fh.yanx.service.banner.api.BannerInfoApi;
import com.fh.yanx.service.banner.service.IBannerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.banner.entity.bo.BannerInfoBo;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.fh.yanx.service.banner.entity.dto.BannerInfoDto;
import com.fh.yanx.service.banner.entity.vo.BannerInfoVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 新时代文化校园banner信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
@RestController
@Validated
public class BannerInfoController implements BannerInfoApi {
	
    @Autowired
    private IBannerInfoService bannerInfoService;

    /**
     * 查询新时代文化校园banner信息表分页列表
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<BannerInfoVo>> getBannerPageListByCondition(@RequestBody BannerInfoConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<BannerInfoVo> pageInfo = new PageInfo<>(bannerInfoService.getBannerListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询新时代文化校园banner信息表列表
	 * <AUTHOR>
	 * @date 2023-10-24 14:47:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<BannerInfoVo>> getBannerListByCondition(@RequestBody BannerInfoConditionBo condition){
		List<BannerInfoVo> list = bannerInfoService.getBannerListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增新时代文化校园banner信息表
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addBanner(@Validated @RequestBody BannerInfoBo bannerInfoBo){
		return bannerInfoService.addBanner(bannerInfoBo);
    }

    /**
	 * 修改新时代文化校园banner信息表
	 * @param bannerInfoBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateBanner(@Validated @RequestBody BannerInfoBo bannerInfoBo) {
		if(null == bannerInfoBo.getBannerInfoId()) {
			return AjaxResult.fail("新时代文化校园banner信息表id不能为空");
		}
		return bannerInfoService.updateBanner(bannerInfoBo);
	}

	/**
	 * 查询新时代文化校园banner信息表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<BannerInfoVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("新时代文化校园banner信息表id不能为空");
		}
		BannerInfoConditionBo condition = new BannerInfoConditionBo();
		condition.setBannerInfoId(id);
		BannerInfoVo vo = bannerInfoService.getBannerByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除新时代文化校园banner信息表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		BannerInfoDto bannerInfoDto = new BannerInfoDto();
		bannerInfoDto.setBannerInfoId(id);
		bannerInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(bannerInfoService.updateById(bannerInfoDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	/**
	 * 修改banner状态
	 *
	 * @param bannerInfoBo
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/10/25 10:09
	 **/
	@Override
	public AjaxResult changeState(@RequestBody BannerInfoBo bannerInfoBo) {
		if (null == bannerInfoBo.getBannerInfoId()) {
			return AjaxResult.fail("请选择banner");
		}
		BannerInfoDto entity = bannerInfoService.getById(bannerInfoBo.getBannerInfoId());
		entity.setState(bannerInfoBo.getState());
		if (bannerInfoService.updateById(entity)) {
			return AjaxResult.success("修改状态成功");
		}
		return AjaxResult.fail("修改状态失败");
	}
}
