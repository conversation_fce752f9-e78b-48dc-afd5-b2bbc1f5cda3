package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.api.R;
import com.fh.yanx.service.enums.ResTopicProcessEnum;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CompleteTodoEvent;
import com.fh.yanx.service.event.CreateTodoEvent;
import com.fh.yanx.service.res.entity.bo.*;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.fh.yanx.service.res.service.IResTopicService;
import com.fh.yanx.service.res.service.IResTopicStudentService;
import com.fh.yanx.service.res.service.IResTopicTeacherService;
import com.google.common.collect.Lists;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicApprovalDto;
import com.fh.yanx.service.res.entity.vo.ResTopicApprovalVo;
import com.fh.yanx.service.res.service.IResTopicApprovalService;
import com.fh.yanx.service.res.mapper.ResTopicApprovalMapper;
import com.light.core.entity.AjaxResult;

/**
 * 课题审批意见表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResTopicApprovalServiceImpl extends ServiceImpl<ResTopicApprovalMapper, ResTopicApprovalDto>
        implements IResTopicApprovalService {

    @Resource
    private ResTopicApprovalMapper resTopicApprovalMapper;
    @Resource
    private IResTopicService resTopicService;
    @Resource
    private IResTopicTeacherService resTopicTeacherService;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public List<ResTopicApprovalVo> getResTopicApprovalListByCondition(ResTopicApprovalConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicApprovalMapper.getResTopicApprovalListByCondition(condition);
    }

    @Override
    public AjaxResult addResTopicApproval(ResTopicApprovalBo resTopicApprovalBo) {
        ResTopicDto resTopicDto = resTopicService.getById(resTopicApprovalBo.getTopicId());
        if (resTopicDto.getTopicProcess().equals(resTopicApprovalBo.getTopicProcess())){
          return AjaxResult.fail("教师以审批");
        }
        //审核接口，判断有无审核意见。是否生成审核意见记录
        if (resTopicApprovalBo.isHasValue()) {
            ResTopicApprovalDto resTopicApproval = new ResTopicApprovalDto();
            BeanUtils.copyProperties(resTopicApprovalBo, resTopicApproval);
            resTopicApproval.setIsDelete(StatusEnum.NOTDELETE.getCode());
            resTopicApproval.setVerifyTeacher(resTopicApprovalBo.getCurrentUserOid());
            save(resTopicApproval);
        }
        // 同步更新课题状态
        ResTopicBo resTopicBo = new ResTopicBo();
        resTopicBo.setTopicId(resTopicApprovalBo.getTopicId());
        resTopicBo.setTopicProcess(resTopicApprovalBo.getTopicProcess());
        resTopicBo.setCurrentUserOid(resTopicApprovalBo.getCurrentUserOid());
        resTopicService.updateResTopicStatus(resTopicBo);
        //判断是否全部审核通过，生成取消教师代办事件
//        ResTopicConditionBo conditionBo = new ResTopicConditionBo();
//        conditionBo.setTeacherOid(resTopicApprovalBo.getCurrentUserOid());
//        conditionBo.setTopicProcess(ResTopicProcessEnum.SUBMIT_EXAMINE.getCode());
//        List<ResTopicVo> topicVos = resTopicService.getResTopicListByCondition(conditionBo);
//        if (CollectionUtils.isEmpty(topicVos)) {
            // 取消教师代办
            List<String> oidListByTopicId = resTopicTeacherService.getResTopicTeacherOidListByTopicId(resTopicBo.getTopicId());
            for (String userOid : oidListByTopicId) {
                applicationContext.publishEvent(CompleteTodoEvent.produceTopicEvent(TopicTodoType.TEACHER_TOPIC_JUDGE.getType(),
                        resTopicApprovalBo.getTopicId(), new Date(), "",userOid,null));
            }

//        }
        return AjaxResult.success("保存成功");
    }

    @Override
    public AjaxResult updateResTopicApproval(ResTopicApprovalBo resTopicApprovalBo) {
        ResTopicApprovalDto resTopicApproval = new ResTopicApprovalDto();
        BeanUtils.copyProperties(resTopicApprovalBo, resTopicApproval);
        if (updateById(resTopicApproval)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ResTopicApprovalVo getDetail(Long id) {
        ResTopicApprovalConditionBo condition = new ResTopicApprovalConditionBo();
        condition.setTopicApprovalId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicApprovalVo> list = resTopicApprovalMapper.getResTopicApprovalListByCondition(condition);
        ResTopicApprovalVo vo = new ResTopicApprovalVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

}