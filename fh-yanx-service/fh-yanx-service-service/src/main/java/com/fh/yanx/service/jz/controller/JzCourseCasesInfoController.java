package com.fh.yanx.service.jz.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.jz.api.JzCourseCasesInfoApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesInfoDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesInfoVo;
import com.fh.yanx.service.jz.service.IJzCourseCasesInfoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 金中-校本课程案例详细信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@RestController
@Validated
public class JzCourseCasesInfoController implements JzCourseCasesInfoApi{
	
    @Autowired
    private IJzCourseCasesInfoService jzCourseCasesInfoService;

    /**
     * 查询金中-校本课程案例详细信息分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @Override
    public AjaxResult<PageInfo<JzCourseCasesInfoVo>> getJzCourseCasesInfoPageListByCondition(@RequestBody JzCourseCasesInfoConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<JzCourseCasesInfoVo> pageInfo = new PageInfo<>(jzCourseCasesInfoService.getJzCourseCasesInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询金中-校本课程案例详细信息列表
	 * <AUTHOR>
	 * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<List<JzCourseCasesInfoVo>> getJzCourseCasesInfoListByCondition(@RequestBody JzCourseCasesInfoConditionBo condition){
		List<JzCourseCasesInfoVo> list = jzCourseCasesInfoService.getJzCourseCasesInfoListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增金中-校本课程案例详细信息
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
	@Override
    public AjaxResult addJzCourseCasesInfo(@Validated @RequestBody JzCourseCasesInfoBo jzCourseCasesInfoBo){
		return jzCourseCasesInfoService.addJzCourseCasesInfo(jzCourseCasesInfoBo);
    }

    /**
	 * 修改金中-校本课程案例详细信息
	 * @param jzCourseCasesInfoBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult updateJzCourseCasesInfo(@Validated @RequestBody JzCourseCasesInfoBo jzCourseCasesInfoBo) {
		if(null == jzCourseCasesInfoBo.getId()) {
			return AjaxResult.fail("金中-校本课程案例详细信息id不能为空");
		}
		return jzCourseCasesInfoService.updateJzCourseCasesInfo(jzCourseCasesInfoBo);
	}

	/**
	 * 查询金中-校本课程案例详细信息详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<JzCourseCasesInfoVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("金中-校本课程案例详细信息id不能为空");
		}
		JzCourseCasesInfoConditionBo condition = new JzCourseCasesInfoConditionBo();
		condition.setId(id);
		JzCourseCasesInfoVo vo = jzCourseCasesInfoService.getJzCourseCasesInfoByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除金中-校本课程案例详细信息
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		JzCourseCasesInfoDto jzCourseCasesInfoDto = new JzCourseCasesInfoDto();
		jzCourseCasesInfoDto.setId(id);
		jzCourseCasesInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(jzCourseCasesInfoService.updateById(jzCourseCasesInfoDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
