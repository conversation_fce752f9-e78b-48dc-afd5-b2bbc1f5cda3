package com.fh.yanx.service.activ.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 活动内容观看权限表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activ_info_view_permission")
public class ActivInfoViewPermissionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 活动id
	 */
	@TableField("activ_id")
	private Long activId;

	/**
	 * 活动内容 1-活动日程（直播） 2-内容回看
	 */
	@TableField("view_type")
	private Integer viewType;

	/**
	 * 观看权限 1-活动报名人员 2-未认证学校用户 3-已认证学校用户
	 */
	@TableField("view_permission")
	private Integer viewPermission;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
