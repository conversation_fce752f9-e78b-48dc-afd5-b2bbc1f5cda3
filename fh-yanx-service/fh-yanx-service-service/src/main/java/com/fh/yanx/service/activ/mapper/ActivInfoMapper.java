package com.fh.yanx.service.activ.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;

/**
 * 新时代文化校园活动信息表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
public interface ActivInfoMapper extends BaseMapper<ActivInfoDto> {

	List<ActivInfoVo> getActivInfoListByCondition(ActivInfoConditionBo condition);

	ActivInfoVo getActivInfoByCondition(ActivInfoConditionBo condition);

	List<ActivInfoVo> getUserActivList(ActivInfoConditionBo conditionBo);

}
