package com.fh.yanx.service.activ.controller;

import com.fh.yanx.service.activ.api.ActivInfoApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoShowBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;
import com.fh.yanx.service.activ.service.IActivInfoService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 新时代文化校园活动信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
@RestController
@Validated
public class ActivInfoController implements ActivInfoApi{
	
    @Autowired
    private IActivInfoService activInfoService;

    /**
     * 查询新时代文化校园活动信息表分页列表
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ActivInfoVo>> getActivInfoPageListByCondition(@RequestBody ActivInfoConditionBo condition){
		// 前台后台都需要根据创建时间倒序排序，因此写在底层的方法里面
		if(StringUtils.isBlank(condition.getOrderBy())){
			condition.setOrderBy("create_time desc");
		}
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ActivInfoVo> pageInfo = new PageInfo<>(activInfoService.getActivInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询新时代文化校园活动信息表列表
	 * <AUTHOR>
	 * @date 2023-07-04 10:34:04
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ActivInfoVo>> getActivInfoListByCondition(@RequestBody ActivInfoConditionBo condition){
		// 前台后台都需要根据创建时间倒序排序，因此写在底层的方法里面
		if(StringUtils.isBlank(condition.getOrderBy())){
			condition.setOrderBy("create_time desc");
		}
		List<ActivInfoVo> list = activInfoService.getActivInfoListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增新时代文化校园活动信息表
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addActivInfo(@Validated @RequestBody ActivInfoBo activInfoBo){
		return activInfoService.addActivInfo(activInfoBo);
    }

    /**
	 * 修改新时代文化校园活动信息表
	 * @param activInfoBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateActivInfo(@Validated @RequestBody ActivInfoBo activInfoBo) {
		if(null == activInfoBo.getActivId()) {
			return AjaxResult.fail("新时代文化校园活动信息表id不能为空");
		}
		return activInfoService.updateActivInfo(activInfoBo);
	}

	/**
	 * 查询新时代文化校园活动信息表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ActivInfoVo> getDetail(@RequestParam("id") Long id,
											 @RequestParam(value = "activType", required = false) Integer activType) {
		if(null == id) {
			return AjaxResult.fail("新时代文化校园活动信息表id不能为空");
		}
		ActivInfoConditionBo condition = new ActivInfoConditionBo();
		condition.setActivId(id);
		if (null != activType) {
			condition.setActivType(activType);
		}
		ActivInfoVo vo = activInfoService.getActivInfoByCondition(condition);
		if (vo != null) {
			return AjaxResult.success(vo);
		}
		return AjaxResult.fail("获取活动详情失败");
	}

    
    /**
	 * 删除新时代文化校园活动信息表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ActivInfoDto activInfoDto = new ActivInfoDto();
		activInfoDto.setActivId(id);
		activInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(activInfoService.updateById(activInfoDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	@Override
	public AjaxResult updateActivType(@RequestBody ActivInfoBo activInfoBo) {
		return activInfoService.updateActivType(activInfoBo);
	}

	@Override
	public AjaxResult setActivInfoShow(@RequestBody ActivInfoShowBo activInfoShowBo) {
		return activInfoService.setActivInfoShow(activInfoShowBo);
	}

	@Override
	public AjaxResult<PageInfo<ActivInfoVo>> getUserActivList(@RequestBody ActivInfoConditionBo conditionBo) {
		PageHelper.startPage(conditionBo.getPageNo(), conditionBo.getPageSize(), conditionBo.getOrderBy());
		PageInfo<ActivInfoVo> pageInfo
				= new PageInfo<>(activInfoService.getUserActivList(conditionBo));
		return AjaxResult.success(pageInfo);
	}

}
