package com.fh.yanx.service.event;

import com.fh.yanx.service.dto.EventPublishDto;
import com.google.common.collect.Lists;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * The type Complete todo event.
 *
 * <AUTHOR>
 * @date 2023 /2/6 13:38
 */
public class CompleteTodoEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    public CompleteTodoEvent(EventPublishDto source) {
        super(source);
    }

    /**
     * 构造一个topicEvent，课题事件
     *
     * @param eventType the event type
     * @param topicId the topic id
     * @param date 日期，如果空会取当前事件
     * @param userOid the user oid
     * @param todoId 待办id
     * @return publish event
     */
    public static CompleteTodoEvent produceTopicEvent(Integer eventType, Long topicId, Date date, String msg,
        String userOid, Long todoId) {
        if (date == null) {
            date = new Date();
        }
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setEventType(eventType);
        eventPublishDto.setTopicId(topicId);
        eventPublishDto.setDate(date);
        eventPublishDto.setMsg(msg);
        eventPublishDto.setUserOids(Lists.newArrayList(userOid));
        eventPublishDto.setTodoId(todoId);
        return new CompleteTodoEvent(eventPublishDto);
    }

}
