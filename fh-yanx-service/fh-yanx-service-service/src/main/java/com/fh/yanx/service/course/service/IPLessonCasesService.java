package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.PLessonCasesDto;
import com.fh.yanx.service.course.entity.bo.PLessonCasesConditionBo;
import com.fh.yanx.service.course.entity.bo.PLessonCasesBo;
import com.fh.yanx.service.course.entity.vo.PLessonCasesVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课例表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface IPLessonCasesService extends IService<PLessonCasesDto> {

    List<PLessonCasesVo> getPLessonCasesListByCondition(PLessonCasesConditionBo condition);

	AjaxResult addPLessonCases(PLessonCasesBo pLessonCasesBo);

	AjaxResult updatePLessonCases(PLessonCasesBo pLessonCasesBo);

	PLessonCasesVo getPLessonCasesByCondition(PLessonCasesConditionBo condition);

}

