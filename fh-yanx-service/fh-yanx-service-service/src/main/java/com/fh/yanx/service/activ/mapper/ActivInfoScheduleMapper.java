package com.fh.yanx.service.activ.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoScheduleDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo;

/**
 * 新时代活动日程表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
public interface ActivInfoScheduleMapper extends BaseMapper<ActivInfoScheduleDto> {

	List<ActivInfoScheduleVo> getActivInfoScheduleListByCondition(ActivInfoScheduleConditionBo condition);

	ActivInfoScheduleVo getActivInfoScheduleByCondition(ActivInfoScheduleConditionBo condition);

}
