package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionBo;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseDiscussSectionDto;
import com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 课程讨论区表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
public interface ICourseDiscussSectionService extends IService<CourseDiscussSectionDto> {

	/**
	 *  根据条件查询数据
	 * @param condition 查询条件
	 * @return {@link List }<{@link CourseDiscussSectionVo }>
	 */
	List<CourseDiscussSectionVo> getCourseDiscussSectionListByCondition(CourseDiscussSectionConditionBo condition);

	/**
	 *  添加数据
	 * @param courseDiscussSectionBo the course discuss section 评论数据
	 * @return {@link AjaxResult }
	 */
	AjaxResult addCourseDiscussSection(CourseDiscussSectionBo courseDiscussSectionBo);

	/**
	 *  更新数据
	 * @param courseDiscussSectionBo
	 * @return {@link AjaxResult }
	 */
	AjaxResult updateCourseDiscussSection(CourseDiscussSectionBo courseDiscussSectionBo);

	/**
	 * 查询
	 *
	 * @param id the id
	 * @return {@link CourseDiscussSectionVo }
	 */
	CourseDiscussSectionVo  queryById(Long id);

	/**
	 *  根据 ID 和用户 OID 删除评论
	 * @param id the id 评论 ID
	 * @param userOid the user oid 用户 OID
	 * @return boolean
	 */
	boolean deleteByIdAndUserOid(Long id, String userOid);

	/**
	 * 根据 ID 删除信息
	 * @param id the id
	 * @return boolean
	 */
	boolean deleteById(Long id);

	/**
	 *  根据 课程案例 ID 获取评论数量
	 * @param casesId the course case id
	 * @return {@link Long }
	 */
	Long queryCountByCasesId(Long casesId);

	/**
	 *  根据 课程案例 ID 获取评论数量
	 * @param casesIdList cases id list 案例 ID 集合
	 * @return {@link Map }<{@link Long }, {@link Long }>
	 */
	Map<Long, Long> queryCountMapByCasesIdList(List<Long> casesIdList);
}

