package com.fh.yanx.service.course.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.course.api.CourseRecommendApi;
import com.fh.yanx.service.course.entity.bo.CourseRecommendAddBo;
import com.fh.yanx.service.course.entity.dto.CourseRecommendDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.entity.bo.CourseRecommendConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendBo;
import com.fh.yanx.service.course.entity.vo.CourseRecommendVo;
import com.fh.yanx.service.course.service.ICourseRecommendService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 课程推荐表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
@RestController
@Validated
public class CourseRecommendController implements CourseRecommendApi{
	
    @Autowired
    private ICourseRecommendService courseRecommendService;

	@Autowired
	private BaseDataService baseDataService;

    /**
     * 查询课程推荐表分页列表
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @Override
    public AjaxResult<PageInfo<CourseRecommendVo>> getCourseRecommendPageListByCondition(@RequestBody CourseRecommendConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseRecommendVo> pageInfo = new PageInfo<>(courseRecommendService.getCourseRecommendListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程推荐表列表
	 * <AUTHOR>
	 * @date 2024-12-06 10:47:11
	 */
	@Override
	public AjaxResult<List<CourseRecommendVo>> getCourseRecommendListByCondition(@RequestBody CourseRecommendConditionBo condition){
		List<CourseRecommendVo> list = courseRecommendService.getCourseRecommendListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程推荐表
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
	@Override
    public AjaxResult addCourseRecommend(@Validated @RequestBody CourseRecommendBo courseRecommendBo){
		return courseRecommendService.addCourseRecommend(courseRecommendBo);
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult addCourseRecommend(CourseRecommendAddBo conditionBo) {
		String currentUserOid = baseDataService.getCurrentUserOid();

		if (ObjectUtil.isNull(conditionBo.getCasesId())){
			return AjaxResult.fail("请选择需要添加的课程");
		}
		CourseRecommendConditionBo courseRecommendConditionBo = new CourseRecommendConditionBo();
		courseRecommendConditionBo.setCasesId(conditionBo.getCasesId());
		List<CourseRecommendVo> courseRecommendListByCondition = courseRecommendService
				.getCourseRecommendListByCondition(courseRecommendConditionBo);

		ArrayList<CourseRecommendBo> delCourseRecommendBoList = new ArrayList<>();
		//删除这一批数据
		courseRecommendListByCondition.forEach(item->{
			CourseRecommendBo delCourseRecommendBo = new CourseRecommendBo();
			item.setIsDelete(StatusEnum.ISDELETE.getCode());
			BeanUtil.copyProperties(item, delCourseRecommendBo);
			delCourseRecommendBoList.add(delCourseRecommendBo);
		});
		if (CollUtil.isNotEmpty(delCourseRecommendBoList)){
			courseRecommendService.updateCourseRecommend(delCourseRecommendBoList);
		}
		ArrayList<CourseRecommendBo> courseRecommendAddBo = new ArrayList<>();
		if (CollUtil.isNotEmpty(conditionBo.getRecommendTypeList())){
			for (Integer companyRecommend : conditionBo.getRecommendTypeList()) {
				CourseRecommendBo courseRecommendBo = new CourseRecommendBo();
				courseRecommendBo.setCasesId(conditionBo.getCasesId());
				courseRecommendBo.setUserOid(currentUserOid);
				courseRecommendBo.setRecommendType(companyRecommend);
				courseRecommendAddBo.add(courseRecommendBo);
			}
		}
		if (CollUtil.isNotEmpty(courseRecommendAddBo)) {
			courseRecommendService.addCourseRecommend(courseRecommendAddBo);
		}
		return AjaxResult.success("保存成功");
	}

	/**
	 * 修改课程推荐表
	 * @param courseRecommendBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
	 */
	@Override
	public AjaxResult updateCourseRecommend(@Validated @RequestBody CourseRecommendBo courseRecommendBo) {
		if(null == courseRecommendBo.getId()) {
			return AjaxResult.fail("课程推荐表id不能为空");
		}
		return courseRecommendService.updateCourseRecommend(courseRecommendBo);
	}

	/**
	 * 查询课程推荐表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
	 */
	@Override
	public AjaxResult<CourseRecommendVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程推荐表id不能为空");
		}
		CourseRecommendConditionBo condition = new CourseRecommendConditionBo();
		condition.setId(id);
		CourseRecommendVo vo = courseRecommendService.getCourseRecommendByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程推荐表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseRecommendDto courseRecommendDto = new CourseRecommendDto();
		courseRecommendDto.setId(id);
		courseRecommendDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseRecommendService.updateById(courseRecommendDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
