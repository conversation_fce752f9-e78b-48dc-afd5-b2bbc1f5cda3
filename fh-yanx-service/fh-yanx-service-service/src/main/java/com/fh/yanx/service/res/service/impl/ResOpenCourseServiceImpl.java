package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResOpenCourseDto;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseConditionBo;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseBo;
import com.fh.yanx.service.res.entity.vo.ResOpenCourseVo;
import com.fh.yanx.service.res.service.IResOpenCourseService;
import com.fh.yanx.service.res.mapper.ResOpenCourseMapper;
import com.light.core.entity.AjaxResult;
/**
 * 公开课表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
@Service
public class ResOpenCourseServiceImpl extends ServiceImpl<ResOpenCourseMapper, ResOpenCourseDto> implements IResOpenCourseService {

	@Resource
	private ResOpenCourseMapper resOpenCourseMapper;
	
    @Override
	public List<ResOpenCourseVo> getResOpenCourseListByCondition(ResOpenCourseConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resOpenCourseMapper.getResOpenCourseListByCondition(condition);
	}

	@Override
	public AjaxResult addResOpenCourse(ResOpenCourseBo resOpenCourseBo) {
		ResOpenCourseDto resOpenCourse = new ResOpenCourseDto();
		BeanUtils.copyProperties(resOpenCourseBo, resOpenCourse);
		resOpenCourse.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resOpenCourse)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResOpenCourse(ResOpenCourseBo resOpenCourseBo) {
		ResOpenCourseDto resOpenCourse = new ResOpenCourseDto();
		BeanUtils.copyProperties(resOpenCourseBo, resOpenCourse);
		if(updateById(resOpenCourse)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResOpenCourseVo getDetail(Long id) {
		ResOpenCourseConditionBo condition = new ResOpenCourseConditionBo();
		condition.setOpenCourseId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResOpenCourseVo> list = resOpenCourseMapper.getResOpenCourseListByCondition(condition);
		ResOpenCourseVo vo = new ResOpenCourseVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}