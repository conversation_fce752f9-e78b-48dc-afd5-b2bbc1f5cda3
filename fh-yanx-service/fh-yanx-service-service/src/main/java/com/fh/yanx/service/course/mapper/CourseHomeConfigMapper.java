package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseHomeConfigDto;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseHomeConfigVo;

/**
 * 推荐展示位Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface CourseHomeConfigMapper extends BaseMapper<CourseHomeConfigDto> {

	List<CourseHomeConfigVo> getCourseHomeConfigListByCondition(CourseHomeConfigConditionBo condition);

	CourseHomeConfigVo getCourseHomeConfigByCondition(CourseHomeConfigConditionBo condition);

}
