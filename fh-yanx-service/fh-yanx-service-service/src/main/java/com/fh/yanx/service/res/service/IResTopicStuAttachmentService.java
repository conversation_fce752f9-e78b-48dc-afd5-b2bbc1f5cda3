package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicStuAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStuAttachmentVo;
import com.light.core.entity.AjaxResult;

import java.io.IOException;
import java.util.List;

/**
 * 课题研究附件表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface IResTopicStuAttachmentService extends IService<ResTopicStuAttachmentDto> {

    List<ResTopicStuAttachmentVo> getResTopicStuAttachmentListByCondition(ResTopicStuAttachmentConditionBo condition);

	AjaxResult addResTopicStuAttachment(ResTopicStuAttachmentBo resTopicStuAttachmentBo);

	AjaxResult updateResTopicStuAttachment(ResTopicStuAttachmentBo resTopicStuAttachmentBo);

	ResTopicStuAttachmentVo getDetail(Long id);

}

