package com.fh.yanx.service.bm.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 支付信息调整记录表
 * 
 * <AUTHOR>
 * @CreateTime 2025-05-27 15:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bm_pay_adjust_record")
public class BmPayAdjustRecordDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动报名记录id
     */
    @TableField("info_id")
    private Long infoId;

    /**
     * 缴费凭证文件oid
     */
    @TableField("pay_id")
    private String payId;

    /**
     * 缴费凭证文件地址
     */
    @TableField("pay_url")
    private String payUrl;

    /**
     * 付款凭证上传时间
     */
    @TableField("pay_time")
    private Date payTime;

    /**
     * 付款凭证上传人oid
     */
    @TableField("pay_user_oid")
    private String payUserOid;

    /**
     * 付款凭证上传人
     */
    @TableField("pay_user_name")
    private String payUserName;

    /**
     * 支付方式类型：1微信，2对公账户支付
     */
    @TableField("pay_way_type")
    private Integer payWayType;

    /**
     * 转账记录
     */
    @TableField("pay_record")
    private String payRecord;

    /**
     * 交易流水号
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 调整原因
     */
    @TableField("pay_adjust_reason")
    private String payAdjustReason;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;
}