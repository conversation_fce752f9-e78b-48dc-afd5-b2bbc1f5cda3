package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 校本课程案例类型版本记录
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_course_cases_type_edition")
public class CourseCasesTypeEditionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 校本课程案例版本表ID
	 */
	@TableField("cases_edition_id")
	private Long casesEditionId;

	/**
	 * 类型名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 类型
	 */
	@TableField("type")
	private Long type;

	/**
	 * 0-未删除 1-已删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 
	 */
	@TableField("create_date")
	private Date createDate;

	/**
	 * 
	 */
	@TableField("update_date")
	private Date updateDate;

}
