package com.fh.yanx.service.activ.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.activ.entity.dto.ActivInfoScheduleDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo;
import com.fh.yanx.service.activ.service.IActivInfoScheduleService;
import com.fh.yanx.service.activ.mapper.ActivInfoScheduleMapper;
import com.light.core.entity.AjaxResult;
/**
 * 新时代活动日程表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
@Service
public class ActivInfoScheduleServiceImpl extends ServiceImpl<ActivInfoScheduleMapper, ActivInfoScheduleDto> implements IActivInfoScheduleService {

	@Resource
	private ActivInfoScheduleMapper activInfoScheduleMapper;
	
    @Override
	public List<ActivInfoScheduleVo> getActivInfoScheduleListByCondition(ActivInfoScheduleConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return activInfoScheduleMapper.getActivInfoScheduleListByCondition(condition);
	}

	@Override
	public AjaxResult addActivInfoSchedule(ActivInfoScheduleBo activInfoScheduleBo) {
		ActivInfoScheduleDto activInfoSchedule = new ActivInfoScheduleDto();
		BeanUtils.copyProperties(activInfoScheduleBo, activInfoSchedule);
		activInfoSchedule.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(activInfoSchedule)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateActivInfoSchedule(ActivInfoScheduleBo activInfoScheduleBo) {
		ActivInfoScheduleDto activInfoSchedule = new ActivInfoScheduleDto();
		BeanUtils.copyProperties(activInfoScheduleBo, activInfoSchedule);
		if(updateById(activInfoSchedule)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ActivInfoScheduleVo getActivInfoScheduleByCondition(ActivInfoScheduleConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return activInfoScheduleMapper.getActivInfoScheduleByCondition(condition);
	}

	@Override
	public boolean deleteAndAddActivInfoSchedules(ActivInfoBo activInfoBo) {
    	if (null == activInfoBo.getActivId()) {
    		return false;
		}
    	// 删除
		LambdaUpdateWrapper<ActivInfoScheduleDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(ActivInfoScheduleDto::getActivId, activInfoBo.getActivId());
		updateWrapper.set(ActivInfoScheduleDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		update(updateWrapper);

		// 新增
		if (CollectionUtil.isEmpty(activInfoBo.getSchedules())) {
			return true;
		}
		List<ActivInfoScheduleDto> scheduleDtos = activInfoBo.getSchedules().stream()
				.map(scheduleBo -> {
					ActivInfoScheduleDto entity = new ActivInfoScheduleDto();
					BeanUtils.copyProperties(scheduleBo, entity);
					entity.setActivId(activInfoBo.getActivId());
					return entity;
				}).collect(Collectors.toList());
		return saveBatch(scheduleDtos);
	}

}