package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.entity.bo.PCategoryConditionBo;
import com.fh.yanx.service.course.entity.vo.PCategoryVo;

/**
 * 资源类别Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCategoryMapper extends BaseMapper<PCategoryDto> {

	List<PCategoryVo> getPCategoryListByCondition(PCategoryConditionBo condition);

	PCategoryVo getPCategoryByCondition(PCategoryConditionBo condition);

}
