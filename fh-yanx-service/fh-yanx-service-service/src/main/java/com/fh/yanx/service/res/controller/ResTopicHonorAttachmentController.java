package com.fh.yanx.service.res.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.service.res.api.ResTopicHonorAttachmentApi;
import com.fh.yanx.service.res.entity.dto.ResTopicHonorAttachmentDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicHonorAttachmentVo;
import com.fh.yanx.service.res.service.IResTopicHonorAttachmentService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 课题荣誉表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResTopicHonorAttachmentController implements ResTopicHonorAttachmentApi {

    @Autowired
    private IResTopicHonorAttachmentService resTopicHonorAttachmentService;

    /**
     * 查询课题荣誉表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResTopicHonorAttachmentVo>>
        getResTopicHonorAttachmentPageListByCondition(@RequestBody ResTopicHonorAttachmentConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicHonorAttachmentVo> pageInfo =
            new PageInfo<>(resTopicHonorAttachmentService.getResTopicHonorAttachmentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题荣誉表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResTopicHonorAttachmentVo>>
        getResTopicHonorAttachmentListByCondition(@RequestBody ResTopicHonorAttachmentConditionBo condition) {
        List<ResTopicHonorAttachmentVo> list =
            resTopicHonorAttachmentService.getResTopicHonorAttachmentListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增课题荣誉表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult
        addResTopicHonorAttachment(@Validated @RequestBody ResTopicHonorAttachmentBo resTopicHonorAttachmentBo) {
        return resTopicHonorAttachmentService.addResTopicHonorAttachment(resTopicHonorAttachmentBo);
    }

    /**
     * 修改课题荣誉表
     *
     * @param resTopicHonorAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult
        updateResTopicHonorAttachment(@Validated @RequestBody ResTopicHonorAttachmentBo resTopicHonorAttachmentBo) {
        if (null == resTopicHonorAttachmentBo.getId()) {
            return AjaxResult.fail("课题荣誉表id不能为空");
        }
        return resTopicHonorAttachmentService.updateResTopicHonorAttachment(resTopicHonorAttachmentBo);
    }

    /**
     * 查询课题荣誉表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResTopicHonorAttachmentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题荣誉表id不能为空");
        }
        ResTopicHonorAttachmentVo vo = resTopicHonorAttachmentService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课题荣誉表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicHonorAttachmentDto resTopicHonorAttachmentDto = new ResTopicHonorAttachmentDto();
        resTopicHonorAttachmentDto.setId(id);
        resTopicHonorAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicHonorAttachmentService.updateById(resTopicHonorAttachmentDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult
        deleteAndSaveTopicHonorAttachmentBatch(List<ResTopicHonorAttachmentBo> resTopicHonorAttachmentBos) {
        if (CollectionUtils.isEmpty(resTopicHonorAttachmentBos)) {
            return AjaxResult.success();
        }
        Long topicId = resTopicHonorAttachmentBos.get(0).getTopicId();
        boolean success =
            resTopicHonorAttachmentService.deleteAndSaveTopicHonorAttachmentBatch(topicId, resTopicHonorAttachmentBos);
        if (success) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }
}
