package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseCasesInfoEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo;

/**
 * 校本课程案例详细信息版本记录Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
public interface CourseCasesInfoEditionMapper extends BaseMapper<CourseCasesInfoEditionDto> {

	List<CourseCasesInfoEditionVo> getCourseCasesInfoEditionListByCondition(CourseCasesInfoEditionConditionBo condition);

	CourseCasesInfoEditionVo getCourseCasesInfoEditionByCondition(CourseCasesInfoEditionConditionBo condition);

}
