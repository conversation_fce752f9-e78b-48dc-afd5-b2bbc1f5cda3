package com.fh.yanx.service.activ.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.activ.entity.bo.ActivInfoShowBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 新时代文化校园活动信息表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
public interface IActivInfoService extends IService<ActivInfoDto> {

    List<ActivInfoVo> getActivInfoListByCondition(ActivInfoConditionBo condition);

	AjaxResult addActivInfo(ActivInfoBo activInfoBo);

	AjaxResult updateActivInfo(ActivInfoBo activInfoBo);

	ActivInfoVo getActivInfoByCondition(ActivInfoConditionBo condition);

	AjaxResult updateActivType(ActivInfoBo activInfoBo);

	AjaxResult setActivInfoShow(ActivInfoShowBo activInfoShowBo);

	/**
	 * 获取用户活动列表
	 *
	 * @param conditionBo
	 * @return java.util.List<com.fh.yanx.service.activ.entity.vo.ActivInfoVo>
	 * <AUTHOR>
	 * @date 2023/8/8 14:26
	 **/
	List<ActivInfoVo> getUserActivList(ActivInfoConditionBo conditionBo);

}

