package com.fh.yanx.service.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyCommunicateDto;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateBo;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyCommunicateVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 组织申请沟通记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
public interface IOrganizationApplyCommunicateService extends IService<OrganizationApplyCommunicateDto> {

    List<OrganizationApplyCommunicateVo> getOrganizationApplyCommunicateListByCondition(OrganizationApplyCommunicateConditionBo condition);

	AjaxResult addOrganizationApplyCommunicate(OrganizationApplyCommunicateBo organizationApplyCommunicateBo);

	AjaxResult updateOrganizationApplyCommunicate(OrganizationApplyCommunicateBo organizationApplyCommunicateBo);

	OrganizationApplyCommunicateVo getOrganizationApplyCommunicateByCondition(OrganizationApplyCommunicateConditionBo condition);

}

