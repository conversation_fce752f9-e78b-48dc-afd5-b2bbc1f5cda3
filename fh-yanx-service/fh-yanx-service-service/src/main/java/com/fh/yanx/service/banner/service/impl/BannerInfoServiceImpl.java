package com.fh.yanx.service.banner.service.impl;

import com.fh.yanx.service.banner.entity.bo.BannerInfoBo;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.fh.yanx.service.banner.entity.dto.BannerInfoDto;
import com.fh.yanx.service.banner.entity.vo.BannerInfoVo;
import com.fh.yanx.service.banner.mapper.BannerInfoMapper;
import com.fh.yanx.service.banner.service.IBannerInfoService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;


import com.light.core.entity.AjaxResult;
/**
 * 新时代文化校园banner信息表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
@Service
public class BannerInfoServiceImpl extends ServiceImpl<BannerInfoMapper, BannerInfoDto> implements IBannerInfoService {

	@Resource
	private BannerInfoMapper bannerMapper;
	
    @Override
	public List<BannerInfoVo> getBannerListByCondition(BannerInfoConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return bannerMapper.getBannerListByCondition(condition);
	}

	@Override
	public AjaxResult addBanner(BannerInfoBo bannerBo) {
		BannerInfoDto banner = new BannerInfoDto();
		BeanUtils.copyProperties(bannerBo, banner);
		banner.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(banner)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateBanner(BannerInfoBo bannerBo) {
		BannerInfoDto banner = new BannerInfoDto();
		BeanUtils.copyProperties(bannerBo, banner);
		if(updateById(banner)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public BannerInfoVo getBannerByCondition(BannerInfoConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return bannerMapper.getBannerByCondition(condition);
	}

}