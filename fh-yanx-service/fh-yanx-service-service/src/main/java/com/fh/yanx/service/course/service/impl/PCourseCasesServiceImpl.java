package com.fh.yanx.service.course.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fh.yanx.service.course.entity.bo.*;
import cn.hutool.core.convert.Convert;
import com.fh.yanx.service.consts.ConstantsRedis;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesGradeVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesTypeVo;
import com.fh.yanx.service.course.service.*;
import com.fh.yanx.service.enums.CourseBestVerifyType;
import com.fh.yanx.service.enums.CourseContentOperateType;
import com.fh.yanx.service.enums.CourseNormalVerifyType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.redis.component.RedisComponent;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.dto.PCourseCasesDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.fh.yanx.service.course.mapper.PCourseCasesMapper;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 校本课程案例接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Service
public class PCourseCasesServiceImpl extends ServiceImpl<PCourseCasesMapper, PCourseCasesDto>
    implements IPCourseCasesService {

    @Resource
    private PCourseCasesMapper pCourseCasesMapper;
    @Resource
    private IPCourseCasesTypeService pCourseCasesTypeService;
    @Resource
    private IPCourseCasesGradeService pCourseCasesGradeService;
    @Resource
    private ICourseModuleService courseModuleService;
    @Resource
    private IPCourseCasesService courseCasesService;
    @Resource
    private ICourseModuleAttachmentService courseModuleAttachmentService;
    @Resource
    private IPCourseCasesInfoService courseCasesInfoService;
    @Resource
    private ICourseVerifyLogService courseVerifyLogService;
    @Resource
    private ICourseWeightedScoreService courseWeightedScoreService;
    @Resource
    private ICourseCasesEditionService courseCasesEditionService;
    @Resource
    private RedisComponent redisComponent;

    @Override
    public List<PCourseCasesVo> getPCourseCasesListByCondition(PCourseCasesConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<PCourseCasesVo> pCourseCasesListByCondition = pCourseCasesMapper.getPCourseCasesListByCondition(condition);
        if (condition.getQueryStoreType()){
            pCourseCasesListByCondition.forEach(vo -> {
                Long store = Optional
                        .ofNullable(redisComponent.get(ConstantsRedis.COURSE_STORE_PREFIX + vo.getId()))
                        .map(Convert::toLong)
                        .orElse(0L);
                vo.setStore(store);
            });
        }
        if (condition.getQueryCourseCasesModule()) {
            convertResult(pCourseCasesListByCondition);
        }
        return pCourseCasesListByCondition;
    }

    @Override
    public AjaxResult addPCourseCases(PCourseCasesBo pCourseCasesBo) {
        PCourseCasesDto pCourseCases = new PCourseCasesDto();
        BeanUtils.copyProperties(pCourseCasesBo, pCourseCases);
        pCourseCases.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pCourseCases)) {
            pCourseCasesBo.setId(pCourseCases.getId());
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePCourseCases(PCourseCasesBo pCourseCasesBo) {
        PCourseCasesDto pCourseCases = new PCourseCasesDto();
        BeanUtils.copyProperties(pCourseCasesBo, pCourseCases);
        pCourseCases.setUpdateDate(new Date());
        updateById(pCourseCases);
        return AjaxResult.success("保存成功");
    }

    @Override
    public PCourseCasesVo getPCourseCasesByCondition(PCourseCasesConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PCourseCasesVo vo = pCourseCasesMapper.getPCourseCasesByCondition(condition);
        return vo;
    }

    @Override
    public String getCourseCasesUserOidById(Long id) {
        if(id == null){
            return null;
        }
        QueryWrapper<PCourseCasesDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PCourseCasesDto::getId, id);
        queryWrapper.lambda().eq(PCourseCasesDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        PCourseCasesDto pCourseCasesDto = this.baseMapper.selectOne(queryWrapper);
        if(pCourseCasesDto == null){
            return null;
        }
        return pCourseCasesDto.getUserOid();
    }

    @Override
    public List<PCourseCasesVo> getHomeList(PCourseCasesConditionBo condition) {
        return pCourseCasesMapper.getHomeList(condition);
    }

    @Override
    public PCourseCasesVo getHomeDetail(Long casesId) {
        PCourseCasesVo pCourseCasesVo = new PCourseCasesVo();
        PCourseCasesDto pCourseCasesDto = pCourseCasesMapper.selectById(casesId);
        if (null != pCourseCasesDto) {
            pCourseCasesDto.setViews(pCourseCasesDto.getViews() + 1);
            pCourseCasesMapper.updateById(pCourseCasesDto);
            BeanUtils.copyProperties(pCourseCasesDto, pCourseCasesVo);
            pCourseCasesVo.setPhone(null);
            List<String> casesTypeNameList = pCourseCasesTypeService.getCasesTypeNameList(casesId);
            pCourseCasesVo.setTypeNameList(casesTypeNameList);
        }
        return pCourseCasesVo;
    }

    @Override
    public List<PCourseCasesVo> topList(PCourseCasesConditionBo condition) {
        return pCourseCasesMapper.topList(condition);
    }

    @Override
    public PCourseCasesVo getContentModule(PCourseCasesConditionBo condition) {
        // 课例信息
        PCourseCasesVo pCourseCasesVo = getPCourseCasesByCondition(condition);
        if (pCourseCasesVo == null) {
            return null;
        }
        List<PCourseCasesVo> pCourseCasesVos = Lists.newArrayList(pCourseCasesVo);
        // 封装返回信息
        convertResult(pCourseCasesVos);
        return pCourseCasesVo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void saveContentModule(PCourseCasesBo pCourseCasesBo) {
        pCourseCasesBo.setUpdateDate(new Date());
        if (pCourseCasesBo.getId() == null) {
            pCourseCasesBo.setCreateDate(new Date());
            // 保存后id更新到bo里面
            courseCasesService.addPCourseCases(pCourseCasesBo);
            // 保存详情
            PCourseCasesInfoBo courseCasesInfoBo = pCourseCasesBo.getCourseCasesInfoBo();
            courseCasesInfoBo.setCasesId(pCourseCasesBo.getId());
            courseCasesInfoService.addPCourseCasesInfo(courseCasesInfoBo);
        } else {
            // 更新课例表信息
            courseCasesService.updatePCourseCases(pCourseCasesBo);
            // 更新详情
            if (pCourseCasesBo.getCourseCasesInfoBo() != null) {
                courseCasesInfoService.updatePCourseCasesInfo(pCourseCasesBo.getCourseCasesInfoBo());
            }
        }

        // 课程详情为空，查询数据，后续保存版本使用
        if (pCourseCasesBo.getCourseCasesInfoBo() == null) {
            PCourseCasesInfoConditionBo courseCasesInfoConditionBo = new PCourseCasesInfoConditionBo();
            courseCasesInfoConditionBo.setCasesId(pCourseCasesBo.getId());
            PCourseCasesInfoVo courseCasesInfoVo = courseCasesInfoService.getPCourseCasesInfoByCondition(courseCasesInfoConditionBo);
            if (courseCasesInfoVo != null) {
                PCourseCasesInfoBo pCourseCasesInfoBo = new PCourseCasesInfoBo();
                BeanUtils.copyProperties(courseCasesInfoVo, pCourseCasesInfoBo);
                pCourseCasesBo.setCourseCasesInfoBo(pCourseCasesInfoBo);
            }
        }

        // 更新年级
        List<PCourseCasesGradeBo> courseCasesGradeBoList = pCourseCasesBo.getCourseCasesGradeBoList();
        pCourseCasesGradeService.saveCourseGrade(pCourseCasesBo.getId(), courseCasesGradeBoList);

        // 更新类型
        List<PCourseCasesTypeBo> courseCasesTypeBoList = pCourseCasesBo.getCourseCasesTypeBoList();
        pCourseCasesTypeService.saveCourseType(pCourseCasesBo.getId(), courseCasesTypeBoList);

        // 如果只保存基础信息则直接返回(空和其他会保存精品的内容)
        if (pCourseCasesBo.getCourseContentOperateType() != null
            && pCourseCasesBo.getCourseContentOperateType().equals(CourseContentOperateType.NORMAL.getValue())) {
            if (CourseNormalVerifyType.DOING.getValue().equals(pCourseCasesBo.getNormalVerifyType())
                    || CourseBestVerifyType.DOING.getValue().equals(pCourseCasesBo.getBestVerifyType())) {
                // 保存版本记录
                courseCasesEditionService.saveContentModuleEdition(pCourseCasesBo);
            }
            return;
        }

        // 更新模块
        List<CourseModuleBo> courseModuleBoList = pCourseCasesBo.getCourseModuleBoList();
        courseModuleService.saveCourseModule(pCourseCasesBo.getId(), pCourseCasesBo.getCourseModuleType(),
            courseModuleBoList);
        List<Long> courseModuleIds =
            courseModuleBoList.stream().filter(courseModuleBo -> courseModuleBo.getCourseModuleId() != null)
                .map(CourseModuleBo::getCourseModuleId).distinct().collect(Collectors.toList());

        // 更新附件信息
        List<CourseModuleAttachmentBo> courseModuleAttachmentBoListAll = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(courseModuleBoList)) {
            courseModuleBoList.stream()
                .filter(courseModuleBo -> CollectionUtils.isNotEmpty(courseModuleBo.getCourseModuleAttachmentBoList()))
                .forEach(courseModuleBo -> {
                    courseModuleBo.getCourseModuleAttachmentBoList().forEach(courseModuleAttachmentBo -> {
                        courseModuleAttachmentBo.setCourseModuleId(courseModuleBo.getCourseModuleId());
                    });
                    courseModuleAttachmentBoListAll.addAll(courseModuleBo.getCourseModuleAttachmentBoList());
                });
        }
        courseModuleAttachmentService.saveCourseModuleAttachment(courseModuleIds, courseModuleAttachmentBoListAll);

        if (CourseNormalVerifyType.DOING.getValue().equals(pCourseCasesBo.getNormalVerifyType())
                || CourseBestVerifyType.DOING.getValue().equals(pCourseCasesBo.getBestVerifyType())) {
            // 保存版本记录
            courseCasesEditionService.saveContentModuleEdition(pCourseCasesBo);
        }
    }

    /**
     * 封装详细信息返回
     *
     * @param pCourseCasesVos the p course cases vos
     * <AUTHOR>
     * @date 2024 -08-30 10:40:34
     */
    private void convertResult(List<PCourseCasesVo> pCourseCasesVos) {
        if (CollectionUtils.isEmpty(pCourseCasesVos)) {
            return;
        }
        for (PCourseCasesVo pCourseCasesVo : pCourseCasesVos) {
            // 课例详情
            PCourseCasesInfoVo pCourseCasesInfoVo = courseCasesInfoService.infoDetail(pCourseCasesVo.getId());
            pCourseCasesVo.setCourseCasesInfoVo(pCourseCasesInfoVo);

            // 年级
            PCourseCasesGradeConditionBo casesGradeConditionBo = new PCourseCasesGradeConditionBo();
            casesGradeConditionBo.setCasesId(pCourseCasesVo.getId());
            List<PCourseCasesGradeVo> courseCasesGradeVoList =
                pCourseCasesGradeService.getPCourseCasesGradeListByCondition(casesGradeConditionBo);
            pCourseCasesVo.setCourseCasesGradeVoList(courseCasesGradeVoList);

            // 类型
            PCourseCasesTypeConditionBo courseCasesTypeConditionBo = new PCourseCasesTypeConditionBo();
            courseCasesTypeConditionBo.setCasesId(pCourseCasesVo.getId());
            List<PCourseCasesTypeVo> courseCasesTypeVoList =
                pCourseCasesTypeService.getPCourseCasesTypeListByCondition(courseCasesTypeConditionBo);
            pCourseCasesVo.setCourseCasesTypeVoList(courseCasesTypeVoList);

            // 模块+附件信息（可选）
            CourseModuleConditionBo courseModuleConditionBo = new CourseModuleConditionBo();
            courseModuleConditionBo.setCasesId(pCourseCasesVo.getId());
            List<CourseModuleVo> courseModuleListByConditionWithAttachment =
                courseModuleService.getCourseModuleListByConditionWithAttachment(courseModuleConditionBo, true);
            pCourseCasesVo.setCourseModuleVos(courseModuleListByConditionWithAttachment);
        }
    }
}