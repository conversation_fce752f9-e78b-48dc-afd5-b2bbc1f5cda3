package com.fh.yanx.service.course.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesGradeDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesTypeDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesTypeVo;
import com.fh.yanx.service.course.mapper.PCourseCasesTypeMapper;
import com.fh.yanx.service.course.service.IPCourseCasesTypeService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 校本课程案例类型接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Service
public class PCourseCasesTypeServiceImpl extends ServiceImpl<PCourseCasesTypeMapper, PCourseCasesTypeDto>
    implements IPCourseCasesTypeService {

    @Resource
    private PCourseCasesTypeMapper pCourseCasesTypeMapper;

    @Override
    public List<PCourseCasesTypeVo> getPCourseCasesTypeListByCondition(PCourseCasesTypeConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return pCourseCasesTypeMapper.getPCourseCasesTypeListByCondition(condition);
    }

    @Override
    public AjaxResult addPCourseCasesType(PCourseCasesTypeBo pCourseCasesTypeBo) {
        PCourseCasesTypeDto pCourseCasesType = new PCourseCasesTypeDto();
        BeanUtils.copyProperties(pCourseCasesTypeBo, pCourseCasesType);
        pCourseCasesType.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pCourseCasesType)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePCourseCasesType(PCourseCasesTypeBo pCourseCasesTypeBo) {
        PCourseCasesTypeDto pCourseCasesType = new PCourseCasesTypeDto();
        BeanUtils.copyProperties(pCourseCasesTypeBo, pCourseCasesType);
        if (updateById(pCourseCasesType)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PCourseCasesTypeVo getPCourseCasesTypeByCondition(PCourseCasesTypeConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PCourseCasesTypeVo vo = pCourseCasesTypeMapper.getPCourseCasesTypeByCondition(condition);
        return vo;
    }

    @Override
    public List<String> getCasesTypeNameList(Long casesId) {
        List<String> casesTypeNameList = new ArrayList<>();
        QueryWrapper<PCourseCasesTypeDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("cases_id", casesId).eq("is_delete", 0);
        List<PCourseCasesTypeDto> list = pCourseCasesTypeMapper.selectList(queryWrapper);
        for (PCourseCasesTypeDto item : list) {
            casesTypeNameList.add(item.getName());
        }
        return casesTypeNameList;
    }

    @Override
    public void saveCourseType(Long casesId, List<PCourseCasesTypeBo> courseCasesTypeBoList) {
        // 删除
        LambdaUpdateWrapper<PCourseCasesTypeDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PCourseCasesTypeDto::getCasesId, casesId);
        updateWrapper.set(PCourseCasesTypeDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        update(updateWrapper);
        if (CollectionUtils.isEmpty(courseCasesTypeBoList)) {
            return;
        }

        // 新增或更新
        List<PCourseCasesTypeDto> courseCasesTypeDtos = courseCasesTypeBoList.stream().map(courseCasesTypeBo -> {
            PCourseCasesTypeDto courseCasesTypeDto = new PCourseCasesTypeDto();
            BeanUtils.copyProperties(courseCasesTypeBo, courseCasesTypeDto);
            courseCasesTypeDto.setCasesId(casesId);
            return courseCasesTypeDto;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(courseCasesTypeDtos)) {
            saveOrUpdateBatch(courseCasesTypeDtos);
        }
    }
}