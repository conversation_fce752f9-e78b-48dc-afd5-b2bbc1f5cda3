package com.fh.yanx.service.course.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.course.api.CourseDiscussSectionApi;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionBo;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesDto;
import com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo;
import com.fh.yanx.service.course.service.ICourseDiscussSectionService;
import com.fh.yanx.service.course.service.IPCourseCasesService;
import com.fh.yanx.service.org.entity.vo.UserRoleVoExt;
import com.fh.yanx.service.org.entity.vo.UserVoExt;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 课程讨论区表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
@RestController
@Validated
public class CourseDiscussSectionController implements CourseDiscussSectionApi {
	
    @Autowired
    private ICourseDiscussSectionService courseDiscussSectionService;

	@Resource
	private IPCourseCasesService ipCourseCasesService;


	@Resource
	private BaseDataService baseDataService;

    /**
     * 查询课程讨论区表分页列表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<CourseDiscussSectionVo>> getCourseDiscussSectionPageListByCondition(@RequestBody CourseDiscussSectionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize());
    	PageInfo<CourseDiscussSectionVo> pageInfo = new PageInfo<>(courseDiscussSectionService.getCourseDiscussSectionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程讨论区表列表
	 * <AUTHOR>
	 * @date 2024-12-05 17:42:00
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<CourseDiscussSectionVo>> getCourseDiscussSectionListByCondition(@RequestBody CourseDiscussSectionConditionBo condition){
		List<CourseDiscussSectionVo> list = courseDiscussSectionService.getCourseDiscussSectionListByCondition(condition);
		if(CollUtil.isNotEmpty(list)){

			String casesUserOid = this.ipCourseCasesService.getCourseCasesUserOidById(condition.getCasesId());
			List<String> userOidList = list.stream().map(CourseDiscussSectionVo::getUserOid).distinct().collect(Collectors.toList());
			Map<String, UserVoExt> userOidVoMap = this.baseDataService.queryMapByUserOidList(userOidList);
			list.forEach(x-> {
				String userOid = x.getUserOid();
				x.setIsCasesUser(StatusEnum.NO.getCode());
				if(StrUtil.isNotEmpty(userOid) && userOid.equals(casesUserOid)){
					x.setIsCasesUser(StatusEnum.YES.getCode());
				}
				UserVoExt userVoExt = userOidVoMap.get(userOid);
				x.setUser(userVoExt);
			});

			//

		}
		return AjaxResult.success(list);
	}



    /**
     * 新增课程讨论区表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addCourseDiscussSection(@Validated @RequestBody CourseDiscussSectionBo courseDiscussSectionBo){
		return courseDiscussSectionService.addCourseDiscussSection(courseDiscussSectionBo);
    }

    /**
	 * 修改课程讨论区表
	 * @param courseDiscussSectionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateCourseDiscussSection(@Validated @RequestBody CourseDiscussSectionBo courseDiscussSectionBo) {
		if(null == courseDiscussSectionBo.getId()) {
			return AjaxResult.fail("课程讨论区表id不能为空");
		}
		return courseDiscussSectionService.updateCourseDiscussSection(courseDiscussSectionBo);
	}

	/**
	 * 查询课程讨论区表详情
	 *
	 * @param id
	 * @return
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<CourseDiscussSectionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程讨论区表id不能为空");
		}
		return AjaxResult.success(courseDiscussSectionService.queryById(id));
	}


	/**
	 * 删除课程讨论区表
	 *
	 * @param id
	 * @return
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		return AjaxResult.success(this.courseDiscussSectionService.deleteById(id));
	}

	@Override
	public AjaxResult<Boolean> deleteByIdAndUserOid(@RequestParam("id") Long id, @RequestParam("userOid") String userOid) {
		return AjaxResult.success(this.courseDiscussSectionService.deleteByIdAndUserOid(id, userOid));
	}
}
