package com.fh.yanx.service.res.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.res.api.ResTopicStuAttachmentApi;
import com.fh.yanx.service.res.entity.dto.ResTopicRepAttachmentDto;
import com.fh.yanx.service.res.entity.dto.ResTopicStuAttachmentDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStuAttachmentVo;
import com.fh.yanx.service.res.service.IResTopicStuAttachmentService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.io.IOException;
import java.util.List;

/**
 * 课题研究附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
public class ResTopicStuAttachmentController implements ResTopicStuAttachmentApi {

    @Autowired
    private IResTopicStuAttachmentService resTopicStuAttachmentService;

    /**
     * 查询课题研究附件表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<PageInfo<ResTopicStuAttachmentVo>>
        getResTopicStuAttachmentPageListByCondition(@RequestBody ResTopicStuAttachmentConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicStuAttachmentVo> pageInfo =
            new PageInfo<>(resTopicStuAttachmentService.getResTopicStuAttachmentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题研究附件表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<List<ResTopicStuAttachmentVo>>
        getResTopicStuAttachmentListByCondition(@RequestBody ResTopicStuAttachmentConditionBo condition) {
        List<ResTopicStuAttachmentVo> list =
            resTopicStuAttachmentService.getResTopicStuAttachmentListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增课题研究附件表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult addResTopicStuAttachment(@RequestBody ResTopicStuAttachmentBo resTopicStuAttachmentBo) {
        if (null == resTopicStuAttachmentBo.getTopicId()) {
            return AjaxResult.fail("参数错误");
        }
        return resTopicStuAttachmentService.addResTopicStuAttachment(resTopicStuAttachmentBo);
    }

    /**
     * 修改课题研究附件表
     *
     * @param resTopicStuAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult
        updateResTopicStuAttachment(@Validated @RequestBody ResTopicStuAttachmentBo resTopicStuAttachmentBo) {
        if (null == resTopicStuAttachmentBo.getId()) {
            return AjaxResult.fail("课题研究附件表id不能为空");
        }
        return resTopicStuAttachmentService.updateResTopicStuAttachment(resTopicStuAttachmentBo);
    }

    /**
     * 批量修改附件
     *
     * @param resTopicStuAttachmentBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/8 14:54
     */
    @Override
    public AjaxResult updateBatchResTopicStuAttachment(List<ResTopicStuAttachmentBo> resTopicStuAttachmentBos) {
        for (ResTopicStuAttachmentBo resTopicStuAttachmentBo : resTopicStuAttachmentBos) {
            resTopicStuAttachmentService.updateResTopicStuAttachment(resTopicStuAttachmentBo);
        }
        return AjaxResult.success("修改成功");
    }

    /**
     * 查询课题研究附件表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<ResTopicStuAttachmentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题研究附件表id不能为空");
        }
        ResTopicStuAttachmentVo vo = resTopicStuAttachmentService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课题研究附件表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicStuAttachmentDto resTopicStuAttachmentDto = new ResTopicStuAttachmentDto();
        resTopicStuAttachmentDto.setId(id);
        resTopicStuAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicStuAttachmentService.updateById(resTopicStuAttachmentDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult deleteByFileOid(String fileOid) {
        if (StringUtils.isBlank(fileOid)) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        LambdaUpdateWrapper<ResTopicStuAttachmentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResTopicStuAttachmentDto::getStuFileOid, fileOid);
        updateWrapper.eq(ResTopicStuAttachmentDto::getIsDelete, StatusEnum.NOTDELETE.getCode());

        ResTopicStuAttachmentDto resTopicStuAttachmentDto = new ResTopicStuAttachmentDto();
        resTopicStuAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean success = resTopicStuAttachmentService.update(resTopicStuAttachmentDto, updateWrapper);
        if (success) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
