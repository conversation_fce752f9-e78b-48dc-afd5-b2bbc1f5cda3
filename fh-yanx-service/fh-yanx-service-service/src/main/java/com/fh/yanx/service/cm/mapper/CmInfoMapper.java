package com.fh.yanx.service.cm.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.cm.entity.dto.CmInfoDto;
import com.fh.yanx.service.cm.entity.bo.CmInfoConditionBo;
import com.fh.yanx.service.cm.entity.vo.CmInfoVo;

/**
 * 代理商信息表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
public interface CmInfoMapper extends BaseMapper<CmInfoDto> {

	List<CmInfoVo> getCmInfoListByCondition(CmInfoConditionBo condition);

	CmInfoVo getCmInfoByCondition(CmInfoConditionBo condition);

}
