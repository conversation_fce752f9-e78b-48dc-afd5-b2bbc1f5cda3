package com.fh.yanx.service.activ.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoScheduleDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 新时代活动日程表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
public interface IActivInfoScheduleService extends IService<ActivInfoScheduleDto> {

    List<ActivInfoScheduleVo> getActivInfoScheduleListByCondition(ActivInfoScheduleConditionBo condition);

	AjaxResult addActivInfoSchedule(ActivInfoScheduleBo activInfoScheduleBo);

	AjaxResult updateActivInfoSchedule(ActivInfoScheduleBo activInfoScheduleBo);

	ActivInfoScheduleVo getActivInfoScheduleByCondition(ActivInfoScheduleConditionBo condition);

	boolean deleteAndAddActivInfoSchedules(ActivInfoBo activInfoBo);
}

