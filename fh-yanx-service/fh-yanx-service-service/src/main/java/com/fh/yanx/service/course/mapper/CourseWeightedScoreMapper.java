package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseWeightedScoreDto;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo;

/**
 * 课程加权分表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
public interface CourseWeightedScoreMapper extends BaseMapper<CourseWeightedScoreDto> {

	List<CourseWeightedScoreVo> getCourseWeightedScoreListByCondition(CourseWeightedScoreConditionBo condition);

	CourseWeightedScoreVo getCourseWeightedScoreByCondition(CourseWeightedScoreConditionBo condition);

}
