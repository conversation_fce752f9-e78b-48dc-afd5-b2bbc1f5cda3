package com.fh.yanx.service.course.service.impl;

import com.fh.yanx.service.course.entity.bo.*;
import com.fh.yanx.service.course.entity.vo.*;
import com.fh.yanx.service.course.service.*;
import com.fh.yanx.service.enums.CourseBestVerifyType;
import com.fh.yanx.service.enums.CourseContentOperateType;
import com.fh.yanx.service.enums.CourseNormalVerifyType;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseCasesEditionDto;
import com.fh.yanx.service.course.mapper.CourseCasesEditionMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程版本表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
@Service
public class CourseCasesEditionServiceImpl extends ServiceImpl<CourseCasesEditionMapper, CourseCasesEditionDto> implements ICourseCasesEditionService {

	@Resource
	private CourseCasesEditionMapper courseCasesEditionMapper;
	@Resource
	private ICourseCasesGradeEditionService courseCasesGradeEditionService;
	@Resource
	private ICourseCasesInfoEditionService courseCasesInfoEditionService;
	@Resource
	private ICourseCasesTypeEditionService courseCasesTypeEditionService;
	@Resource
	private ICourseModuleEditionService courseModuleEditionService;
	@Resource
	private ICourseModuleAttachmentEditionService courseModuleAttachmentEditionService;
	
    @Override
	public List<CourseCasesEditionVo> getCourseCasesEditionListByCondition(CourseCasesEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseCasesEditionMapper.getCourseCasesEditionListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseCasesEdition(CourseCasesEditionBo courseCasesEditionBo) {
		CourseCasesEditionDto courseCasesEdition = new CourseCasesEditionDto();
		BeanUtils.copyProperties(courseCasesEditionBo, courseCasesEdition);
		courseCasesEdition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseCasesEdition)){
			courseCasesEditionBo.setId(courseCasesEdition.getId());
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseCasesEdition(CourseCasesEditionBo courseCasesEditionBo) {
		CourseCasesEditionDto courseCasesEdition = new CourseCasesEditionDto();
		BeanUtils.copyProperties(courseCasesEditionBo, courseCasesEdition);
		if(updateById(courseCasesEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CourseCasesEditionVo getCourseCasesEditionByCondition(CourseCasesEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return courseCasesEditionMapper.getCourseCasesEditionByCondition(condition);
	}

	@Override
	public void saveContentModuleEdition(PCourseCasesBo pCourseCasesBo) {
		// 版本记录仅保存，不做更新
		pCourseCasesBo.setUpdateDate(new Date());
		pCourseCasesBo.setCreateDate(new Date());
		// 保存后id更新到bo里面
		CourseCasesEditionBo courseCasesEditionBo = new CourseCasesEditionBo();
		BeanUtils.copyProperties(pCourseCasesBo, courseCasesEditionBo);
		courseCasesEditionBo.setId(null);
		courseCasesEditionBo.setCasesId(pCourseCasesBo.getId());
		this.addCourseCasesEdition(courseCasesEditionBo);
		// 保存详情
		PCourseCasesInfoBo courseCasesInfoBo = pCourseCasesBo.getCourseCasesInfoBo();
		CourseCasesInfoEditionBo courseCasesInfoEditionBo = new CourseCasesInfoEditionBo();
		BeanUtils.copyProperties(courseCasesInfoBo, courseCasesInfoEditionBo);
		courseCasesInfoEditionBo.setId(null);
		courseCasesInfoEditionBo.setCasesEditionId(courseCasesEditionBo.getId());
		courseCasesInfoEditionService.addCourseCasesInfoEdition(courseCasesInfoEditionBo);

		// 更新年级
		if (CollectionUtils.isNotEmpty(pCourseCasesBo.getCourseCasesGradeBoList())) {
			List<CourseCasesGradeEditionBo> courseCasesGradeBoList = pCourseCasesBo.getCourseCasesGradeBoList().stream().map(x -> {
				CourseCasesGradeEditionBo courseCasesGradeEditionBo = new CourseCasesGradeEditionBo();
				BeanUtils.copyProperties(x, courseCasesGradeEditionBo);
				courseCasesGradeEditionBo.setId(null);
				return courseCasesGradeEditionBo;
			}).collect(Collectors.toList());
			courseCasesGradeEditionService.saveCourseGradeEdition(courseCasesEditionBo.getId(), courseCasesGradeBoList);
		}

		// 更新类型
		if (CollectionUtils.isNotEmpty(pCourseCasesBo.getCourseCasesTypeBoList())) {
			List<CourseCasesTypeEditionBo> courseCasesTypeEditionBos = pCourseCasesBo.getCourseCasesTypeBoList().stream()
					.map(x -> {
						CourseCasesTypeEditionBo courseCasesTypeEditionBo = new CourseCasesTypeEditionBo();
						BeanUtils.copyProperties(x, courseCasesTypeEditionBo);
						courseCasesTypeEditionBo.setId(null);
						return courseCasesTypeEditionBo;
					})
					.collect(Collectors.toList());
			courseCasesTypeEditionService.saveCourseTypeEdition(courseCasesEditionBo.getId(), courseCasesTypeEditionBos);
		}

		// 如果只保存基础信息则直接返回(空和其他会保存精品的内容)
		if (pCourseCasesBo.getCourseContentOperateType() != null
				&& pCourseCasesBo.getCourseContentOperateType().equals(CourseContentOperateType.NORMAL.getValue())) {
			return;
		}

		// 更新模块
		if (CollectionUtils.isNotEmpty(pCourseCasesBo.getCourseModuleBoList())) {
			List<CourseModuleBo> courseModuleBoList = pCourseCasesBo.getCourseModuleBoList();
			courseModuleEditionService.saveCourseModuleEdition(courseCasesEditionBo.getId(), pCourseCasesBo.getCourseModuleType(),
					courseModuleBoList);
		}
	}

	@Override
	public CourseCasesEditionVo getContentModule(CourseCasesEditionConditionBo condition) {
		// 课例信息
		CourseCasesEditionVo courseCasesEditionVo = getCourseCasesEditionByCondition(condition);
		if (courseCasesEditionVo == null) {
			return null;
		}
		List<CourseCasesEditionVo> courseCasesEditionVos = Lists.newArrayList(courseCasesEditionVo);
		// 封装返回信息
		convertResult(courseCasesEditionVos);
		return courseCasesEditionVo;
	}

	/**
	 * 封装详细信息返回
	 *
	 * @param courseCasesEditionVos the p course cases vos
	 * <AUTHOR>
	 * @date 2024 -08-30 10:40:34
	 */
	private void convertResult(List<CourseCasesEditionVo> courseCasesEditionVos) {
		if (CollectionUtils.isEmpty(courseCasesEditionVos)) {
			return;
		}
		for (CourseCasesEditionVo courseCasesEditionVo : courseCasesEditionVos) {
			// 课例详情
			CourseCasesInfoEditionVo courseCasesInfoEditionVo = courseCasesInfoEditionService.infoDetail(courseCasesEditionVo.getId());
			courseCasesEditionVo.setCourseCasesInfoVo(courseCasesInfoEditionVo);

			// 年级
			CourseCasesGradeEditionConditionBo casesGradeEditionConditionBo = new CourseCasesGradeEditionConditionBo();
			casesGradeEditionConditionBo.setCasesEditionId(courseCasesEditionVo.getId());
			List<CourseCasesGradeEditionVo> courseCasesGradeVoList =
					courseCasesGradeEditionService.getCourseCasesGradeEditionListByCondition(casesGradeEditionConditionBo);
			courseCasesEditionVo.setCourseCasesGradeVoList(courseCasesGradeVoList);

			// 类型
			CourseCasesTypeEditionConditionBo casesTypeEditionConditionBo = new CourseCasesTypeEditionConditionBo();
			casesTypeEditionConditionBo.setCasesEditionId(courseCasesEditionVo.getId());
			List<CourseCasesTypeEditionVo> courseCasesTypeEditionVos =
					courseCasesTypeEditionService.getCourseCasesTypeEditionListByCondition(casesTypeEditionConditionBo);
			courseCasesEditionVo.setCourseCasesTypeVoList(courseCasesTypeEditionVos);

			// 模块+附件信息（可选）
			CourseModuleEditionConditionBo courseModuleEditionConditionBo = new CourseModuleEditionConditionBo();
			courseModuleEditionConditionBo.setCasesEditionId(courseCasesEditionVo.getId());
			List<CourseModuleEditionVo> courseModuleEditionListByConditionWithAttachment =
					courseModuleEditionService.getCourseModuleListByConditionWithAttachment(courseModuleEditionConditionBo, true);
			courseCasesEditionVo.setCourseModuleVos(courseModuleEditionListByConditionWithAttachment);
		}
	}
}