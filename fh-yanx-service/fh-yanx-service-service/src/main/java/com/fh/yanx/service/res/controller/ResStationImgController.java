package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResStationImgApi;
import com.fh.yanx.service.res.entity.dto.ResStationImgDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResStationImgConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationImgBo;
import com.fh.yanx.service.res.entity.vo.ResStationImgVo;
import com.fh.yanx.service.res.service.IResStationImgService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 工作站介绍图片表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResStationImgController implements ResStationImgApi{
	
    @Autowired
    private IResStationImgService resStationImgService;

    /**
     * 查询工作站介绍图片表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResStationImgVo>> getResStationImgPageListByCondition(@RequestBody ResStationImgConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ResStationImgVo> pageInfo = new PageInfo<>(resStationImgService.getResStationImgListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询工作站介绍图片表列表
	 * <AUTHOR>
	 * @date 2022-09-02 15:00:23
	 */
	@Override
	public AjaxResult<List<ResStationImgVo>> getResStationImgListByCondition(@RequestBody ResStationImgConditionBo condition){
		List<ResStationImgVo> list = resStationImgService.getResStationImgListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增工作站介绍图片表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
	@Override
    public AjaxResult addResStationImg(@Validated @RequestBody ResStationImgBo resStationImgBo){
		return resStationImgService.addResStationImg(resStationImgBo);
    }

    /**
	 * 修改工作站介绍图片表
	 * @param resStationImgBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
	 */
	@Override
	public AjaxResult updateResStationImg(@Validated @RequestBody ResStationImgBo resStationImgBo) {
		if(null == resStationImgBo.getId()) {
			return AjaxResult.fail("工作站介绍图片表id不能为空");
		}
		return resStationImgService.updateResStationImg(resStationImgBo);
	}

	/**
	 * 查询工作站介绍图片表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
	 */
	@Override
	public AjaxResult<ResStationImgVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("工作站介绍图片表id不能为空");
		}
		ResStationImgVo vo = resStationImgService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除工作站介绍图片表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ResStationImgDto resStationImgDto = new ResStationImgDto();
		resStationImgDto.setId(id);
		resStationImgDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(resStationImgService.updateById(resStationImgDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
