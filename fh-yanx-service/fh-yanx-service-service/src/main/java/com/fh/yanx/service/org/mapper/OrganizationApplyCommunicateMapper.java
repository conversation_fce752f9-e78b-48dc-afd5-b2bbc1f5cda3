package com.fh.yanx.service.org.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyCommunicateDto;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateConditionBo;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyCommunicateVo;

/**
 * 组织申请沟通记录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
public interface OrganizationApplyCommunicateMapper extends BaseMapper<OrganizationApplyCommunicateDto> {

	List<OrganizationApplyCommunicateVo> getOrganizationApplyCommunicateListByCondition(OrganizationApplyCommunicateConditionBo condition);

	OrganizationApplyCommunicateVo getOrganizationApplyCommunicateByCondition(OrganizationApplyCommunicateConditionBo condition);

}
