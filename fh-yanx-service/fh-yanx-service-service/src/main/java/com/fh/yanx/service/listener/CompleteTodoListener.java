package com.fh.yanx.service.listener;

import com.fh.yanx.service.dto.EventPublishDto;
import com.fh.yanx.service.enums.TodoBusinessType;
import com.fh.yanx.service.enums.TodoStatusType;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CompleteTodoEvent;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.service.IResTopicTodoService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 完成待办事件监听
 *
 * <AUTHOR>
 * @date 2022/5/10 16:45
 */
@Component
public class CompleteTodoListener {

    @Lazy
    @Autowired
    private IResTopicTodoService iResTopicTodoService;

    @Async
    @EventListener
    public void onApplicationEvent(CompleteTodoEvent event) {
        EventPublishDto eventPublishDto = (EventPublishDto)event.getSource();
        if (eventPublishDto.getEventType() == null) {
            return;
        }

        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_EDIT.getType())) {
            ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
            resTopicTodoBo.setTodoTypes(Lists.newArrayList(TopicTodoType.STUDENT_TOPIC_STASH.getType(),
                TopicTodoType.STUDENT_TOPIC_REJECT.getType()));
            resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
            resTopicTodoBo.setTodoStatus(TodoStatusType.HAS_DO.getValue());
            iResTopicTodoService.updateTodoStatus(resTopicTodoBo);
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.STUDENT_TOPIC_VIEW.getType())) {
            ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
            resTopicTodoBo.setTodoId(eventPublishDto.getTodoId());
            resTopicTodoBo.setTodoStatus(TodoStatusType.HAS_DO.getValue());
            iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_JUDGE.getType())) {
            // 查询条件
            ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
            resTopicTodoBo.setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_SUBMIT.getType(),
                TopicTodoType.TEACHER_TOPIC_RESUBMIT.getType()));
            resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
            // 更新条件
            resTopicTodoBo.setTodoStatus(TodoStatusType.HAS_DO.getValue());
            iResTopicTodoService.updateTodoStatus(resTopicTodoBo);
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_EVALUATE_COMPLETE.getType())) {
            // 查询条件
            ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
            resTopicTodoBo.setTodoTypes(Lists.newArrayList(TopicTodoType.TEACHER_TOPIC_EVALUATE.getType()));
            resTopicTodoBo.setTodoBusinessType(TodoBusinessType.TOPIC.getValue());
            resTopicTodoBo.setUserOid(eventPublishDto.getUserOids().get(0));
            resTopicTodoBo.setTodoBusinessId(eventPublishDto.getTopicId());
            // 更新条件
            resTopicTodoBo.setTodoStatus(TodoStatusType.HAS_DO.getValue());
            iResTopicTodoService.updateTodoStatus(resTopicTodoBo);
        }
        if (eventPublishDto.getEventType().equals(TopicTodoType.TEACHER_TOPIC_VIEW.getType())) {
            ResTopicTodoBo resTopicTodoBo = new ResTopicTodoBo();
            resTopicTodoBo.setTodoId(eventPublishDto.getTodoId());
            resTopicTodoBo.setTodoStatus(TodoStatusType.HAS_DO.getValue());
            iResTopicTodoService.updateResTopicTodo(resTopicTodoBo);
        }

    }
}
