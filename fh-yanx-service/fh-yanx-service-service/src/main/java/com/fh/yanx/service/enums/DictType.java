package com.fh.yanx.service.enums;

/**
 * 机构类型
 *
 * <AUTHOR>
 */
public enum DictType {
    /**
     * 是否
     */
    YES_NO("yes_no"),
    /**
     * 教育级别
     */
    EDUCATION_LEVEL("education-level"),
    /**
     * 年级
     */
    GRADE("grade"),
    USERIDENTITYTYPE("userIdentityType"),
    SECTION("section"),
    SEX("sex"),
    IDENTITY_CARD_TYPE("identity_card_type"),
    HEALTH_TYPE("health_type"),
    RELATION_TYPE("relation_type"),
    NATION_TYPE("nation_type"),
    NATIONALITY_TYPE("nationality_type"),
    REG<PERSON>TRATION_TYPE("registration_type"),
    POLITICAL_OUTLOOK_TYPE("political_outlook_type"),
    MARITAL_TYPE("marital_type"),
    QUALIFICATION_TYPE("qualification_type"),
    EMPLOYEE_TYPE("employee_type"),
    IS_QUOTAS("is_quotas"),
    QUOTAS_TYPE("quotas_type"),
    TITLE_TYPE("title_type"),
    SKILL_TYPE("skill_type"),
    EDUCATION_SYSTEM("education_system"),

    /**
     * 科目学科
     */
    SUBJECT_SECTION("subject_section"),
    /**
     * 研学学科（科目）
     */
    YANX_SUBJECT("yanx_subject");

    private String value;

    DictType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}