package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseDiscussSectionDto;
import com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 课程讨论区表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
public interface CourseDiscussSectionMapper extends BaseMapper<CourseDiscussSectionDto> {

	/**
	 * 根据条件查询数据
	 * @param condition
	 * @return {@link List }<{@link CourseDiscussSectionVo }>
	 */
	List<CourseDiscussSectionVo> getCourseDiscussSectionListByCondition(CourseDiscussSectionConditionBo condition);

	/**
	 *  删除所有子集
	 * @param parentId the parent id  父级 ID
	 * @return int
	 */
	int deleteByParentId(Long parentId);

	/**
	 *  增加回复数量
	 * @param id the id
	 * @param num the reply num 增加的回复数量
	 */
	int addReplyNumById(@Param("id") Long id, @Param("num") int num);
}
