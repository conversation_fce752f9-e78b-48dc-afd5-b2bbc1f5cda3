package com.fh.yanx.service.jz.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.jz.api.JzCourseCasesTypeApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesTypeDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesTypeVo;
import com.fh.yanx.service.jz.service.IJzCourseCasesTypeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 金中-校本课程案例类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@RestController
@Validated
public class JzCourseCasesTypeController implements JzCourseCasesTypeApi{
	
    @Autowired
    private IJzCourseCasesTypeService jzCourseCasesTypeService;

    /**
     * 查询金中-校本课程案例类型分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @Override
    public AjaxResult<PageInfo<JzCourseCasesTypeVo>> getJzCourseCasesTypePageListByCondition(@RequestBody JzCourseCasesTypeConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<JzCourseCasesTypeVo> pageInfo = new PageInfo<>(jzCourseCasesTypeService.getJzCourseCasesTypeListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询金中-校本课程案例类型列表
	 * <AUTHOR>
	 * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<List<JzCourseCasesTypeVo>> getJzCourseCasesTypeListByCondition(@RequestBody JzCourseCasesTypeConditionBo condition){
		List<JzCourseCasesTypeVo> list = jzCourseCasesTypeService.getJzCourseCasesTypeListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增金中-校本课程案例类型
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
	@Override
    public AjaxResult addJzCourseCasesType(@Validated @RequestBody JzCourseCasesTypeBo jzCourseCasesTypeBo){
		return jzCourseCasesTypeService.addJzCourseCasesType(jzCourseCasesTypeBo);
    }

    /**
	 * 修改金中-校本课程案例类型
	 * @param jzCourseCasesTypeBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult updateJzCourseCasesType(@Validated @RequestBody JzCourseCasesTypeBo jzCourseCasesTypeBo) {
		if(null == jzCourseCasesTypeBo.getId()) {
			return AjaxResult.fail("金中-校本课程案例类型id不能为空");
		}
		return jzCourseCasesTypeService.updateJzCourseCasesType(jzCourseCasesTypeBo);
	}

	/**
	 * 查询金中-校本课程案例类型详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<JzCourseCasesTypeVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("金中-校本课程案例类型id不能为空");
		}
		JzCourseCasesTypeConditionBo condition = new JzCourseCasesTypeConditionBo();
		condition.setId(id);
		JzCourseCasesTypeVo vo = jzCourseCasesTypeService.getJzCourseCasesTypeByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除金中-校本课程案例类型
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		JzCourseCasesTypeDto jzCourseCasesTypeDto = new JzCourseCasesTypeDto();
		jzCourseCasesTypeDto.setId(id);
		jzCourseCasesTypeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(jzCourseCasesTypeService.updateById(jzCourseCasesTypeDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
