package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工作站具体内容详情
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_station_content_detail")
public class ResStationContentDetailDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "station_content_detail_id", type = IdType.AUTO)
	private Long stationContentDetailId;

	/**
	 * 工作站具体内容id
	 */
	@TableField("station_content_id")
	private Long stationContentId;

	/**
	 * 工作站id
	 */
	@TableField("station_id")
	private Long stationId;

	/**
	 * 内容详情名称
	 */
	@TableField("station_content_detail_name")
	private String stationContentDetailName;

	/**
	 * 详细内容分组：1丘成桐赛事，2ISEF赛事（可能从字典获取）
	 */
	@TableField("station_content_detail_group")
	private Integer stationContentDetailGroup;

	/**
	 * 内容详情顺序
	 */
	@TableField("station_content_detail_index")
	private Long stationContentDetailIndex;

	/**
	 * 内容详情图片文件原始名称-带后缀，内容只有一张图片时生效
	 */
	@TableField("content_detail_file_name_ori")
	private String contentDetailFileNameOri;

	/**
	 * 内容详情图片文件名称-不带后缀
	 */
	@TableField("content_detail_file_name")
	private String contentDetailFileName;

	/**
	 * 内容详情图片文件地址
	 */
	@TableField("content_detail_file_url")
	private String contentDetailFileUrl;

	/**
	 * 内容详情图片点击链接
	 */
	@TableField("content_detail_file_url_link")
	private String contentDetailFileUrlLink;

	/**
	 * 内容详情副名称
	 */
	@TableField("content_detail_subname")
	private String contentDetailSubname;

	/**
	 * 内容详情标签，比如：环境学、统计学
	 */
	@TableField("content_detail_label")
	private String contentDetailLabel;

	/**
	 * 内容详情成员，比如：张晓琴、冒小冒、曹小明
	 */
	@TableField("content_detail_member")
	private String contentDetailMember;

	/**
	 * 收藏数
	 */
	@TableField("content_detail_collect")
	private Long contentDetailCollect;

	/**
	 * 查看数
	 */
	@TableField("content_detail_pv")
	private Long contentDetailPv;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
