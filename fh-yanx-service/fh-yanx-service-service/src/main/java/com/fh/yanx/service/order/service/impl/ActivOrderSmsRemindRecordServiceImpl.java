package com.fh.yanx.service.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.bm.entity.dto.BmInfoDto;
import com.fh.yanx.service.bm.mapper.BmInfoMapper;
import com.fh.yanx.service.enums.BmInfoPayType;
import com.fh.yanx.service.enums.GoodsType;
import com.fh.yanx.service.enums.OrderEnum;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderSmsRemindRecordBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderSmsRemindRecordConditionBo;
import com.fh.yanx.service.order.entity.dto.ActivOrderDto;
import com.fh.yanx.service.order.entity.dto.ActivOrderSmsRemindRecordDto;
import com.fh.yanx.service.order.entity.vo.ActivOrderSmsRemindRecordVo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import com.fh.yanx.service.order.mapper.ActivOrderMapper;
import com.fh.yanx.service.order.mapper.ActivOrderSmsRemindRecordMapper;
import com.fh.yanx.service.order.service.IActivOrderService;
import com.fh.yanx.service.order.service.IActivOrderSmsRemindRecordService;
import com.fh.yanx.service.utils.UUIDUtil;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
@Service
public class ActivOrderSmsRemindRecordServiceImpl extends ServiceImpl<ActivOrderSmsRemindRecordMapper, ActivOrderSmsRemindRecordDto> implements IActivOrderSmsRemindRecordService {

    @Resource
    private ActivOrderSmsRemindRecordMapper activOrderSmsRemindRecordMapper;

    @Override
    public List<ActivOrderSmsRemindRecordVo> getActivOrderSmsRemindRecordListByCondition(ActivOrderSmsRemindRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return activOrderSmsRemindRecordMapper.getActivOrderSmsRemindRecordListByCondition(condition);
    }

    @Override
    public AjaxResult addActivOrderSmsRemindRecord(ActivOrderSmsRemindRecordBo recordBo) {
        ActivOrderSmsRemindRecordDto remindRecordDto = new ActivOrderSmsRemindRecordDto();
        BeanUtils.copyProperties(recordBo, remindRecordDto);
        remindRecordDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(remindRecordDto)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateActivOrderSmsRemindRecord(ActivOrderSmsRemindRecordBo recordBo) {
        ActivOrderSmsRemindRecordDto remindRecordDto = new ActivOrderSmsRemindRecordDto();
        BeanUtils.copyProperties(recordBo, remindRecordDto);
        if (updateById(remindRecordDto)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ActivOrderSmsRemindRecordVo getActivOrderSmsRemindRecordByCondition(ActivOrderSmsRemindRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return activOrderSmsRemindRecordMapper.getActivOrderSmsRemindRecordByCondition(condition);
    }

    @Override
    public boolean addActivOrderSmsRemindRecordBatchByOrderIds(List<Long> orderIds) {
        if (CollectionUtil.isEmpty(orderIds)) {
            return false;
        }
        List<ActivOrderSmsRemindRecordDto> addList = new ArrayList<>();
        for (Long orderId : orderIds) {
            ActivOrderSmsRemindRecordDto dto = new ActivOrderSmsRemindRecordDto();
            dto.setOrderId(orderId);
            dto.setIsDelete(StatusEnum.NOTDELETE.getCode());
            addList.add(dto);
        }
        return saveBatch(addList);
    }

}