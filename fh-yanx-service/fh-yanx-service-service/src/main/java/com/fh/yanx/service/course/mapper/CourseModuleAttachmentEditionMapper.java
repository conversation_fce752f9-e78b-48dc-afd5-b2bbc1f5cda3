package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentEditionVo;

/**
 * 课程资源或成果样例附件表-模块附件版本表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:25
 */
public interface CourseModuleAttachmentEditionMapper extends BaseMapper<CourseModuleAttachmentEditionDto> {

	List<CourseModuleAttachmentEditionVo> getCourseModuleAttachmentEditionListByCondition(CourseModuleAttachmentEditionConditionBo condition);

	CourseModuleAttachmentEditionVo getCourseModuleAttachmentEditionByCondition(CourseModuleAttachmentEditionConditionBo condition);

}
