package com.fh.yanx.service.jz.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesInfoDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesInfoDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesInfoVo;
import com.fh.yanx.service.jz.mapper.JzCourseCasesInfoMapper;
import com.fh.yanx.service.jz.service.IJzCourseCasesInfoService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * 金中-校本课程案例详细信息接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@Service
public class JzCourseCasesInfoServiceImpl extends ServiceImpl<JzCourseCasesInfoMapper, JzCourseCasesInfoDto> implements IJzCourseCasesInfoService {

	@Resource
	private JzCourseCasesInfoMapper jzCourseCasesInfoMapper;
	
    @Override
	public List<JzCourseCasesInfoVo> getJzCourseCasesInfoListByCondition(JzCourseCasesInfoConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return jzCourseCasesInfoMapper.getJzCourseCasesInfoListByCondition(condition);
	}

	@Override
	public AjaxResult addJzCourseCasesInfo(JzCourseCasesInfoBo jzCourseCasesInfoBo) {
		JzCourseCasesInfoDto jzCourseCasesInfo = new JzCourseCasesInfoDto();
		BeanUtils.copyProperties(jzCourseCasesInfoBo, jzCourseCasesInfo);
		jzCourseCasesInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(jzCourseCasesInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateJzCourseCasesInfo(JzCourseCasesInfoBo jzCourseCasesInfoBo) {
		JzCourseCasesInfoDto jzCourseCasesInfo = new JzCourseCasesInfoDto();
		BeanUtils.copyProperties(jzCourseCasesInfoBo, jzCourseCasesInfo);
		if(updateById(jzCourseCasesInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public JzCourseCasesInfoVo getJzCourseCasesInfoByCondition(JzCourseCasesInfoConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		JzCourseCasesInfoVo vo = jzCourseCasesInfoMapper.getJzCourseCasesInfoByCondition(condition);
		return vo;
	}

	@Override
	public JzCourseCasesInfoVo infoDetail(Long casesId) {
		JzCourseCasesInfoVo jzCourseCasesInfoVo = new JzCourseCasesInfoVo();
		QueryWrapper<JzCourseCasesInfoDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("cases_id", casesId).eq("is_delete", 0).last("limit 1");
		JzCourseCasesInfoDto jzCourseCasesInfoDto = jzCourseCasesInfoMapper.selectOne(queryWrapper);
		if (null != jzCourseCasesInfoDto) {
			BeanUtils.copyProperties(jzCourseCasesInfoDto, jzCourseCasesInfoVo);
		}
		return jzCourseCasesInfoVo;
	}
}