package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResApplyDto;
import com.fh.yanx.service.res.entity.bo.ResApplyConditionBo;
import com.fh.yanx.service.res.entity.bo.ResApplyBo;
import com.fh.yanx.service.res.entity.vo.ResApplyVo;
import com.fh.yanx.service.res.service.IResApplyService;
import com.fh.yanx.service.res.mapper.ResApplyMapper;
import com.light.core.entity.AjaxResult;
/**
 * 合作意向申请表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-27 16:19:35
 */
@Service
public class ResApplyServiceImpl extends ServiceImpl<ResApplyMapper, ResApplyDto> implements IResApplyService {

	@Resource
	private ResApplyMapper resApplyMapper;
	
    @Override
	public List<ResApplyVo> getResApplyListByCondition(ResApplyConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resApplyMapper.getResApplyListByCondition(condition);
	}

	@Override
	public AjaxResult addResApply(ResApplyBo resApplyBo) {
		ResApplyDto resApply = new ResApplyDto();
		BeanUtils.copyProperties(resApplyBo, resApply);
		resApply.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resApply)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResApply(ResApplyBo resApplyBo) {
		ResApplyDto resApply = new ResApplyDto();
		BeanUtils.copyProperties(resApplyBo, resApply);
		if(updateById(resApply)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResApplyVo getDetail(Long id) {
		ResApplyConditionBo condition = new ResApplyConditionBo();
		condition.setApplyId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResApplyVo> list = resApplyMapper.getResApplyListByCondition(condition);
		ResApplyVo vo = new ResApplyVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}