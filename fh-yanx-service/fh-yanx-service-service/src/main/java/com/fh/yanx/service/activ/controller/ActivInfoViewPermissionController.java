package com.fh.yanx.service.activ.controller;

import com.fh.yanx.service.activ.api.ActivInfoViewPermissionApi;
import com.fh.yanx.service.activ.entity.dto.ActivInfoViewPermissionDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo;
import com.fh.yanx.service.activ.service.IActivInfoViewPermissionService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 活动内容观看权限表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
@RestController
@Validated
public class ActivInfoViewPermissionController implements ActivInfoViewPermissionApi{
	
    @Autowired
    private IActivInfoViewPermissionService activInfoViewPermissionService;

    /**
     * 查询活动内容观看权限表分页列表
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ActivInfoViewPermissionVo>> getActivInfoViewPermissionPageListByCondition(@RequestBody ActivInfoViewPermissionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ActivInfoViewPermissionVo> pageInfo = new PageInfo<>(activInfoViewPermissionService.getActivInfoViewPermissionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询活动内容观看权限表列表
	 * <AUTHOR>
	 * @date 2023-07-27 17:28:46
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ActivInfoViewPermissionVo>> getActivInfoViewPermissionListByCondition(@RequestBody ActivInfoViewPermissionConditionBo condition){
		List<ActivInfoViewPermissionVo> list = activInfoViewPermissionService.getActivInfoViewPermissionListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增活动内容观看权限表
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addActivInfoViewPermission(@Validated @RequestBody ActivInfoViewPermissionBo activInfoViewPermissionBo){
		return activInfoViewPermissionService.addActivInfoViewPermission(activInfoViewPermissionBo);
    }

    /**
	 * 修改活动内容观看权限表
	 * @param activInfoViewPermissionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateActivInfoViewPermission(@Validated @RequestBody ActivInfoViewPermissionBo activInfoViewPermissionBo) {
		if(null == activInfoViewPermissionBo.getId()) {
			return AjaxResult.fail("活动内容观看权限表id不能为空");
		}
		return activInfoViewPermissionService.updateActivInfoViewPermission(activInfoViewPermissionBo);
	}

	/**
	 * 查询活动内容观看权限表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ActivInfoViewPermissionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("活动内容观看权限表id不能为空");
		}
		ActivInfoViewPermissionConditionBo condition = new ActivInfoViewPermissionConditionBo();
		condition.setId(id);
		ActivInfoViewPermissionVo vo = activInfoViewPermissionService.getActivInfoViewPermissionByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除活动内容观看权限表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ActivInfoViewPermissionDto activInfoViewPermissionDto = new ActivInfoViewPermissionDto();
		activInfoViewPermissionDto.setId(id);
		activInfoViewPermissionDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(activInfoViewPermissionService.updateById(activInfoViewPermissionDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
