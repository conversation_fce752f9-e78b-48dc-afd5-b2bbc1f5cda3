package com.fh.yanx.service.config;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fh.yanx.service.baseinfo.BaseDataProcessors;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.baseinfo.BaseDataType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 基础数据配置类
 *
 * <AUTHOR>
 * @date 2022/6/1 11:42
 */
@Configuration
public class BaseDataConfig {

    /**
     * 服务类型，从配置获取
     */
    @Value("${base.data.service.type.name:}")
    private String baseDataType;

    /**
     * 将配置的类给spring管理
     *
     * @return
     */
    @Bean
    public BaseDataService getBaseDataService() {
        if (StringUtil.isBlank(baseDataType)) {
            baseDataType = BaseDataType.CLOUD_BASE.getName();
        }
        return BaseDataProcessors.get(baseDataType);
    }
}
