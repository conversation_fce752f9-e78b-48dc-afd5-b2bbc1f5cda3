package com.fh.yanx.service.course.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.course.entity.dto.CourseModuleDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeConditionBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesGradeDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesGradeVo;
import com.fh.yanx.service.course.mapper.PCourseCasesGradeMapper;
import com.fh.yanx.service.course.service.IPCourseCasesGradeService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 校本课程案例年级接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Service
public class PCourseCasesGradeServiceImpl extends ServiceImpl<PCourseCasesGradeMapper, PCourseCasesGradeDto>
    implements IPCourseCasesGradeService {

    @Resource
    private PCourseCasesGradeMapper pCourseCasesGradeMapper;

    @Override
    public List<PCourseCasesGradeVo> getPCourseCasesGradeListByCondition(PCourseCasesGradeConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return pCourseCasesGradeMapper.getPCourseCasesGradeListByCondition(condition);
    }

    @Override
    public AjaxResult addPCourseCasesGrade(PCourseCasesGradeBo pCourseCasesGradeBo) {
        PCourseCasesGradeDto pCourseCasesGrade = new PCourseCasesGradeDto();
        BeanUtils.copyProperties(pCourseCasesGradeBo, pCourseCasesGrade);
        pCourseCasesGrade.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(pCourseCasesGrade)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePCourseCasesGrade(PCourseCasesGradeBo pCourseCasesGradeBo) {
        PCourseCasesGradeDto pCourseCasesGrade = new PCourseCasesGradeDto();
        BeanUtils.copyProperties(pCourseCasesGradeBo, pCourseCasesGrade);
        if (updateById(pCourseCasesGrade)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PCourseCasesGradeVo getPCourseCasesGradeByCondition(PCourseCasesGradeConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PCourseCasesGradeVo vo = pCourseCasesGradeMapper.getPCourseCasesGradeByCondition(condition);
        return vo;
    }

    @Override
    public void saveCourseGrade(Long casesId, List<PCourseCasesGradeBo> courseCasesGradeBoList) {
        // 删除
        LambdaUpdateWrapper<PCourseCasesGradeDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PCourseCasesGradeDto::getCasesId, casesId);
        updateWrapper.set(PCourseCasesGradeDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        update(updateWrapper);
        if (CollectionUtils.isEmpty(courseCasesGradeBoList)) {
            return;
        }

        // 新增或更新
        List<PCourseCasesGradeDto> courseCasesGradeDtos = courseCasesGradeBoList.stream().map(courseCasesGradeBo -> {
            PCourseCasesGradeDto courseCasesGradeDto = new PCourseCasesGradeDto();
            BeanUtils.copyProperties(courseCasesGradeBo, courseCasesGradeDto);
            courseCasesGradeDto.setCasesId(casesId);
            return courseCasesGradeDto;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(courseCasesGradeDtos)) {
            saveOrUpdateBatch(courseCasesGradeDtos);
        }
    }
}