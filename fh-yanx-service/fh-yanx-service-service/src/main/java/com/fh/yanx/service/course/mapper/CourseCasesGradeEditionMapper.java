package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseCasesGradeEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesGradeEditionVo;

/**
 * 校本课程案例年级版本记录Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:00
 */
public interface CourseCasesGradeEditionMapper extends BaseMapper<CourseCasesGradeEditionDto> {

	List<CourseCasesGradeEditionVo> getCourseCasesGradeEditionListByCondition(CourseCasesGradeEditionConditionBo condition);

	CourseCasesGradeEditionVo getCourseCasesGradeEditionByCondition(CourseCasesGradeEditionConditionBo condition);

}
