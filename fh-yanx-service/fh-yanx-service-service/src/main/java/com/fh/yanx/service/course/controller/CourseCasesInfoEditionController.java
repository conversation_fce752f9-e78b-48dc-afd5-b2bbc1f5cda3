package com.fh.yanx.service.course.controller;

import com.fh.yanx.service.course.api.CourseCasesInfoEditionApi;
import com.fh.yanx.service.course.entity.dto.CourseCasesInfoEditionDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo;
import com.fh.yanx.service.course.service.ICourseCasesInfoEditionService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 校本课程案例详细信息版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
@RestController
@Validated
public class CourseCasesInfoEditionController implements CourseCasesInfoEditionApi{
	
    @Autowired
    private ICourseCasesInfoEditionService courseCasesInfoEditionService;

    /**
     * 查询校本课程案例详细信息版本记录分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
    @Override
    public AjaxResult<PageInfo<CourseCasesInfoEditionVo>> getCourseCasesInfoEditionPageListByCondition(@RequestBody CourseCasesInfoEditionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseCasesInfoEditionVo> pageInfo = new PageInfo<>(courseCasesInfoEditionService.getCourseCasesInfoEditionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询校本课程案例详细信息版本记录列表
	 * <AUTHOR>
	 * @date 2024-12-05 10:41:09
	 */
	@Override
	public AjaxResult<List<CourseCasesInfoEditionVo>> getCourseCasesInfoEditionListByCondition(@RequestBody CourseCasesInfoEditionConditionBo condition){
		List<CourseCasesInfoEditionVo> list = courseCasesInfoEditionService.getCourseCasesInfoEditionListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增校本课程案例详细信息版本记录
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
	@Override
    public AjaxResult addCourseCasesInfoEdition(@Validated @RequestBody CourseCasesInfoEditionBo courseCasesInfoEditionBo){
		return courseCasesInfoEditionService.addCourseCasesInfoEdition(courseCasesInfoEditionBo);
    }

    /**
	 * 修改校本课程案例详细信息版本记录
	 * @param courseCasesInfoEditionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
	 */
	@Override
	public AjaxResult updateCourseCasesInfoEdition(@Validated @RequestBody CourseCasesInfoEditionBo courseCasesInfoEditionBo) {
		if(null == courseCasesInfoEditionBo.getId()) {
			return AjaxResult.fail("校本课程案例详细信息版本记录id不能为空");
		}
		return courseCasesInfoEditionService.updateCourseCasesInfoEdition(courseCasesInfoEditionBo);
	}

	/**
	 * 查询校本课程案例详细信息版本记录详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
	 */
	@Override
	public AjaxResult<CourseCasesInfoEditionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("校本课程案例详细信息版本记录id不能为空");
		}
		CourseCasesInfoEditionConditionBo condition = new CourseCasesInfoEditionConditionBo();
		condition.setId(id);
		CourseCasesInfoEditionVo vo = courseCasesInfoEditionService.getCourseCasesInfoEditionByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除校本课程案例详细信息版本记录
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseCasesInfoEditionDto courseCasesInfoEditionDto = new CourseCasesInfoEditionDto();
		courseCasesInfoEditionDto.setId(id);
		courseCasesInfoEditionDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseCasesInfoEditionService.updateById(courseCasesInfoEditionDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
