package com.fh.yanx.service.bm.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.bm.entity.dto.BmInfoConnectDto;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectConditionBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoConnectVo;

/**
 * 报名活动沟通确认表（本表只有新增记录）Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
public interface BmInfoConnectMapper extends BaseMapper<BmInfoConnectDto> {

	List<BmInfoConnectVo> getBmInfoConnectListByCondition(BmInfoConnectConditionBo condition);

	BmInfoConnectVo getBmInfoConnectByCondition(BmInfoConnectConditionBo condition);

}
