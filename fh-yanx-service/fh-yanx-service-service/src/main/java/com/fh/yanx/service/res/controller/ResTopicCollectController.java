package com.fh.yanx.service.res.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.res.api.ResTopicApi;
import com.fh.yanx.service.res.api.ResTopicCollectApi;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.dto.ResTopicCollectDto;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.fh.yanx.service.res.service.IResTopicService;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicCollectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicCollectVo;
import com.fh.yanx.service.res.service.IResTopicCollectService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户收藏的课题
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResTopicCollectController implements ResTopicCollectApi {

    @Autowired
    private IResTopicCollectService resTopicCollectService;

    @Resource
    private IResTopicService resTopicService;

    @Resource
    private BaseDataService baseDataService;

    /**
     * 查询用户收藏的课题分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult getResTopicCollectPageListByCondition(@RequestBody ResTopicCollectConditionBo condition) {
        condition.setUserOid(condition.getCurrentUserOid());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicCollectVo> page = new PageInfo<>(resTopicCollectService.getResTopicCollectListByCondition(condition));
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        List<ResTopicCollectVo> resTopicCollectVos = page.getList();
        if (CollectionUtils.isEmpty(resTopicCollectVos)){
            return AjaxResult.success(map);
        }

        List<Long> topicIds = resTopicCollectVos.stream().map(ResTopicCollectVo::getTopicId).collect(Collectors.toList());
        ResTopicConditionBo resTopicConditionBo=new ResTopicConditionBo();
        resTopicConditionBo.setTopicIds(topicIds);
        resTopicConditionBo.setOrganizationId(condition.getOrganizationId());
        List<ResTopicVo> data = resTopicService.getResTopicListByCondition(resTopicConditionBo);
        map.put("list",data);
        if (CollectionUtils.isEmpty(data)){
            return AjaxResult.success(map);
        }
        //封装学校名称
        List<Long> organizationIds = data.stream().filter(x -> x.getOrganizationId() != null).map(ResTopicVo::getOrganizationId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(organizationIds)){
            return AjaxResult.success(map);
        }
        List<OrganizationVo> organizationVoList = baseDataService.getOrganizationVoList(organizationIds);
        Map<Long, List<OrganizationVo>> orgMap = organizationVoList.stream().collect(Collectors.groupingBy(OrganizationVo::getId));
        for (ResTopicVo resTopicVo : data) {
            if (resTopicVo.getOrganizationId()!=null){
                resTopicVo.setOrganizationName(orgMap.get(resTopicVo.getOrganizationId()).get(0).getName());
            }
        }
        return AjaxResult.success(map);
    }

    /**
     * 查询用户收藏的课题列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResTopicCollectVo>> getResTopicCollectListByCondition(@RequestBody ResTopicCollectConditionBo condition) {
        List<ResTopicCollectVo> list = resTopicCollectService.getResTopicCollectListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增用户收藏的课题
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResTopicCollect(@Validated @RequestBody ResTopicCollectBo resTopicCollectBo) {
        return resTopicCollectService.addResTopicCollect(resTopicCollectBo);
    }

    /**
     * 修改用户收藏的课题
     *
     * @param resTopicCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResTopicCollect(@Validated @RequestBody ResTopicCollectBo resTopicCollectBo) {
        if (null == resTopicCollectBo.getCollectId()) {
            return AjaxResult.fail("用户收藏的课题id不能为空");
        }
        return resTopicCollectService.updateResTopicCollect(resTopicCollectBo);
    }

    /**
     * 查询用户收藏的课题详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResTopicCollectVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("用户收藏的课题id不能为空");
        }
        ResTopicCollectVo vo = resTopicCollectService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除用户收藏的课题
     *
     * @param topicId,userOid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult deleteByTopicAndUser(@RequestParam("topicId") Long topicId,@RequestParam("userOid") String userOid) {
        if (null == topicId || StringUtil.isBlank(userOid)) {
            return AjaxResult.fail("参数错误");
        }
        return resTopicCollectService.deleteByTopicAndUser(topicId,userOid);
    }
}
