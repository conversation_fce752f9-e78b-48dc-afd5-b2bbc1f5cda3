package com.fh.yanx.service.res.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.C;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.res.api.ResTopicApi;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;
import com.fh.yanx.service.res.service.IResTopicStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.fh.yanx.service.res.service.IResTopicService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;
import java.util.List;

/**
 * 课题表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResTopicController implements ResTopicApi {

    @Autowired
    private IResTopicService resTopicService;
    @Resource
    private BaseDataService baseDataService;

    @Resource
    private IResTopicStudentService resTopicStudentService;

    /**
     * 查询课题表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResTopicVo>> getResTopicPageListByCondition(@RequestBody ResTopicConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicVo> pageInfo = new PageInfo<>(resTopicService.getResTopicListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResTopicVo>> getResTopicListByCondition(@RequestBody ResTopicConditionBo condition) {
        List<ResTopicVo> list = resTopicService.getResTopicListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增课题表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResTopic(@Validated @RequestBody ResTopicBo resTopicBo) {
        return resTopicService.addResTopic(resTopicBo);
    }

    /**
     * 修改课题表进程状态
     *
     * @param resTopicBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResTopicStatus(@RequestBody ResTopicBo resTopicBo) {
        if (null == resTopicBo.getTopicId()) {
            return AjaxResult.fail("课题表id不能为空");
        }
        return resTopicService.updateResTopicStatus(resTopicBo);
    }

    /**
     * 查询课题表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResTopicVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题表id不能为空");
        }
        ResTopicVo vo = resTopicService.getDetail(id,null);
        return AjaxResult.success(vo);
    }

    /**
     * 获取当前用户的课题详情
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/1 17:41
     */
    @Override
    public AjaxResult getCurrentUserTopicDetail(@RequestParam("organizationId")Long organizationId,@RequestParam("currentUserOid")String currentUserOid) {
        ResTopicStudentConditionBo conditionBo=new ResTopicStudentConditionBo();
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setUserOid(currentUserOid);
        List<ResTopicStudentVo> list = resTopicStudentService.getResTopicStudentListByCondition(conditionBo);
        if (CollectionUtils.isEmpty(list)){
            return AjaxResult.success();
        }
        Long topicId = list.get(0).getTopicId();
        ResTopicVo detail = resTopicService.getDetail(topicId,organizationId);
        if (detail.getTopicId()==null){
            return AjaxResult.success();
        }
        return  AjaxResult.success(detail);
    }

    /**
     * 删除课题表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicDto resTopicDto = new ResTopicDto();
        resTopicDto.setTopicId(id);
        resTopicDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicService.updateById(resTopicDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
