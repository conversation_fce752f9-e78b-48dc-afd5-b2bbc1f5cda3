package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicTodoDto;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTodoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 用户待办事项接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface IResTopicTodoService extends IService<ResTopicTodoDto> {

    List<ResTopicTodoVo> getResTopicTodoListByCondition(ResTopicTodoConditionBo condition);

    AjaxResult addResTopicTodo(ResTopicTodoBo resTopicTodoBo);

    AjaxResult updateResTopicTodo(ResTopicTodoBo resTopicTodoBo);

    ResTopicTodoVo getDetail(Long id);

    /**
     * 根据userOid、todoTypes、todoBusinessType、todoBusinessId 更新todoStatus状态
     * 
     * @param resTopicTodoBo
     */
    void updateTodoStatus(ResTopicTodoBo resTopicTodoBo);

    /**
     * 新增或者更新
     *
     * @param resTopicTodoBo the res topic todo bo
     */
    void saveOrUpdate(ResTopicTodoBo resTopicTodoBo);

}
