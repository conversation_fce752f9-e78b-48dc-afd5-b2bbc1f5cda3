package com.fh.yanx.service.course.controller;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.consts.ConstantsRedis;
import com.fh.yanx.service.course.entity.bo.*;
import com.fh.yanx.service.course.entity.dto.CourseVerifyLogDto;
import com.fh.yanx.service.course.entity.vo.*;
import com.fh.yanx.service.course.service.*;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo;
import com.fh.yanx.service.courseReviewExpertConfig.service.ICourseReviewExpertConfigService;
import com.fh.yanx.service.enums.*;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.service.org.entity.vo.UserVoExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.light.redis.component.RedisComponent;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.user.entity.vo.UserRoleVo;
import com.light.user.user.entity.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.consts.ConstantsInteger;
import com.fh.yanx.service.course.api.PCourseCasesApi;
import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.entity.dto.PCourseCasesDto;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;

/**
 * 校本课程案例
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Slf4j
@RestController
@Validated
public class PCourseCasesController implements PCourseCasesApi {

    @Autowired
    private IPCourseCasesService pCourseCasesService;
    @Autowired
    private IPCourseCasesTypeService pCourseCasesTypeService;
    @Autowired
    private IPCategoryService pCategoryService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private IPCourseCasesInfoService pCourseCasesInfoService;
    @Autowired
    private ICourseModuleService courseModuleService;
    @Autowired
    private ICourseVerifyLogService courseVerifyLogService;
    @Autowired
    private ICourseReviewExpertConfigService courseReviewExpertConfigService;
    @Autowired
    private ICourseStoreService courseStoreService;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private ICourseCasesEditionService courseCasesEditionService;
    @Resource
    private ICourseRecommendService courseRecommendService;

    @Resource
    private ICourseDiscussSectionService iCourseDiscussSectionService;

    @Value("${ineoschool.organization.id:1}")
    private Long ineoschoolOrganizationId;

    /**
     * 查询校本课程案例分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PageInfo<PCourseCasesVo>>
        getPCourseCasesPageListByCondition(@RequestBody PCourseCasesConditionBo condition) {
        // 设置当前登录人查询
        String currentUserOid = baseDataService.getCurrentUserOid();
        condition.setCurrentUserOid(currentUserOid);
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<PCourseCasesVo> pCourseCasesListByCondition =
            pCourseCasesService.getPCourseCasesListByCondition(condition);
        // 封装返回字段
        convertHomeResultList(pCourseCasesListByCondition);
        // 回显专家
        if (condition.getShowExpertUsers()) {
            setExpertUsers(pCourseCasesListByCondition);
        }
        if (ObjectUtil.isNotNull(condition.getRecommendType()) || condition.getQueryIsRecommend()) {
            //封装综评专家推荐状态
            convertRecommendResultList(pCourseCasesListByCondition,condition);
        }
        // 封装分数返回
        if (condition.isCalculateScore()) {
            convertScoreResultList(condition.getCurrentUserOid(), pCourseCasesListByCondition, condition.getAssistantVerifyProcessType());
        }
        // 封装操作状态
        if (StringUtils.isNotBlank(currentUserOid)) {
            convertVerifyOperateType(condition, pCourseCasesListByCondition);
        }
        // 封装版本记录返回
        if (condition.getQueryEdition()) {
            convertCourseCasesEdition(pCourseCasesListByCondition);
        }
        // 审核记录和版本提交记录合并返回
        if (condition.getHandleVerifyLogAndEdition()) {
            handleVerifyLogAndEdition(pCourseCasesListByCondition);
        }
        // 评论数量处理
        this.fillDiscussNum(pCourseCasesListByCondition);

        PageInfo<PCourseCasesVo> pageInfo = new PageInfo<>(pCourseCasesListByCondition);
        return AjaxResult.success(pageInfo);
    }


    /**
     *  填充评论数量
     * @param list the course list
     */
    private void fillDiscussNum(List<PCourseCasesVo> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<Long> courseIdList = list.stream().map(PCourseCasesVo::getId).collect(Collectors.toList());
        Map<Long, Long> courseIdDiscussNumMap = this.iCourseDiscussSectionService.queryCountMapByCasesIdList(courseIdList);
        list.forEach(x-> x.setDiscussNum(courseIdDiscussNumMap.getOrDefault(x.getId(), 0L)));
    }

    /**
     * 转换推荐结果列表
     * <p>
     * 传入参数: @param pCourseCasesListByCondition 按条件列出 P 课程个案列表
     *
     * @param condition 条件
     *                  返回值:
     *                  创建人: 杨圣君
     *                  创建时间: 2024/12/06
     */

    public void convertRecommendResultList(List<PCourseCasesVo> pCourseCasesListByCondition,
                                           PCourseCasesConditionBo condition){
        List<Long> casesIdList = pCourseCasesListByCondition.stream()
                .map(PCourseCasesVo::getId)
                .distinct()
                .collect(Collectors.toList());
        CourseRecommendConditionBo courseRecommendConditionBo = new CourseRecommendConditionBo();
        courseRecommendConditionBo.setCasesIdList(casesIdList);
        courseRecommendConditionBo.setRecommendType(condition.getRecommendType());
        List<CourseRecommendVo> courseRecommendListByCondition = courseRecommendService.getCourseRecommendListByCondition(courseRecommendConditionBo);
        Map<Long, List<CourseRecommendVo>> courseRecommendMap = courseRecommendListByCondition
                .stream()
                .collect(Collectors.groupingBy(CourseRecommendVo::getCasesId));
        pCourseCasesListByCondition.forEach(item->{
            List<CourseRecommendVo> courseRecommendVos = courseRecommendMap.getOrDefault(item.getId(),new ArrayList<CourseRecommendVo>());
            item.setIsRecommend(CollUtil.isNotEmpty(courseRecommendVos));
            List<Integer> recommendTypeList = courseRecommendVos.stream()
                    .map(CourseRecommendVo::getRecommendType)
                    .collect(Collectors.toList());
            item.setRecommendTypeList(recommendTypeList);
        });
    }

    @Override
    public AjaxResult<PageInfo<PCourseCasesVo>> getStoreCourseCasesPageListByCondition(PCourseCasesConditionBo condition) {
        // 设置只查询教师个人申报课程列表
        condition.setStoreUserOid(baseDataService.getCurrentUserOid());
        condition.setCalculateScore(true);
        condition.setQueryStoreType(true);
        condition.setCourseSortType(9);
        return getPCourseCasesPageListByCondition(condition);
    }

    @Autowired
    private ICourseWeightedScoreService courseWeightedScoreService;

    /**
     * 查询校本课程案例列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<List<PCourseCasesVo>>
        getPCourseCasesListByCondition(@RequestBody PCourseCasesConditionBo condition) {
        List<PCourseCasesVo> list = pCourseCasesService.getPCourseCasesListByCondition(condition);
        if (!condition.getSkipConvertHomeResult()) {
            convertHomeResultList(list);
        }
        // 封装版本记录返回
        if (condition.getQueryEdition()) {
            convertCourseCasesEdition(list);
        }
        // 审核记录和版本提交记录合并返回
        if (condition.getHandleVerifyLogAndEdition()) {
            handleVerifyLogAndEdition(list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 新增校本课程案例
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult addPCourseCases(@Validated @RequestBody PCourseCasesBo pCourseCasesBo) {
        return pCourseCasesService.addPCourseCases(pCourseCasesBo);
    }

    /**
     * 修改校本课程案例
     * 
     * @param pCourseCasesBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult updatePCourseCases(@Validated @RequestBody PCourseCasesBo pCourseCasesBo) {
        if (null == pCourseCasesBo.getId()) {
            return AjaxResult.fail("校本课程案例id不能为空");
        }
        return pCourseCasesService.updatePCourseCases(pCourseCasesBo);
    }

    /**
     * 查询校本课程案例详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult<PCourseCasesVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("校本课程案例id不能为空");
        }
        PCourseCasesConditionBo condition = new PCourseCasesConditionBo();
        condition.setId(id);
        PCourseCasesVo vo = pCourseCasesService.getPCourseCasesByCondition(condition);
        convertHomeResultOne(vo, null);
        return AjaxResult.success(vo);
    }

    /**
     * 删除校本课程案例
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        PCourseCasesDto pCourseCasesDto = new PCourseCasesDto();
        pCourseCasesDto.setId(id);
        pCourseCasesDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (pCourseCasesService.updateById(pCourseCasesDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 查询首页列表并返回
     * 
     * @param conditionBo
     * @return
     */
    @Override
    public AjaxResult getHomeList(PCourseCasesConditionBo conditionBo) {
        // 分页和不分页都支持
        if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())
            || Integer.valueOf(ConstantsInteger.NUM_0).equals(conditionBo.getPageNo())) {
            List<PCourseCasesVo> pCourseCasesVos = pCourseCasesService.getHomeList(conditionBo);
            // 处理返回结果
            convertHomeResultList(pCourseCasesVos);
            Map<String, Object> map = new HashMap<>();
            map.put("list", pCourseCasesVos);
            map.put("total", pCourseCasesVos.size());
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(conditionBo.getPageNo(), conditionBo.getPageSize(), conditionBo.getOrderBy());
            List<PCourseCasesVo> pCourseCasesVos = pCourseCasesService.getHomeList(conditionBo);
            // 处理返回结果
            convertHomeResultList(pCourseCasesVos);
            PageInfo<PCourseCasesVo> pageInfo = new PageInfo<>(pCourseCasesVos);
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), conditionBo.getPageNo(),
                conditionBo.getPageSize());
        }
    }

    /**
     * 查询课程详情并返回
     * 
     * @param casesId
     * @return
     */
    @Override
    public AjaxResult homeDetail(Long casesId, Boolean queryEdition) {
        if (casesId == null) {
            return AjaxResult.fail("参数错误！");
        }
        PCourseCasesVo detail = pCourseCasesService.getHomeDetail(casesId);
        convertHomeResultOne(detail,Boolean.TRUE);
        // 封装版本记录返回
        if (queryEdition != null && queryEdition) {
            List<PCourseCasesVo> pCourseCasesVos = Lists.newArrayList(detail);
            convertCourseCasesEdition(pCourseCasesVos);
        }
        //过滤专家评语
        List<CourseVerifyLogVo> courseVerifyLogList = detail.getCourseVerifyLogList();
        if (CollectionUtil.isNotEmpty(courseVerifyLogList)){
            List<CourseVerifyLogVo> courseVerifyLogVoList = courseVerifyLogList.stream()
                    .filter(item ->
                            ObjectUtil.equals(item.getVerifyProcessType(), VerifyProcessType.COMPREHENSIVE.getValue()) ||
                                    ObjectUtil.equals(item.getVerifyProcessType(), VerifyProcessType.VERIFY_COMMENT.getValue()) ||
                                    ObjectUtil.equals(item.getVerifyProcessType(), VerifyProcessType.GUIDE_COMMENT.getValue()))
                    .collect(Collectors.toList());
            detail.setCourseVerifyLogList(courseVerifyLogVoList);
        }
        return AjaxResult.success(detail);
    }

    @Override
    public AjaxResult topList(PCourseCasesConditionBo conditionBo) {
        // 分页和不分页都支持
        if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())
            || Integer.valueOf(ConstantsInteger.NUM_0).equals(conditionBo.getPageNo())) {
            List<PCourseCasesVo> pCourseCasesVos = pCourseCasesService.topList(conditionBo);
            // 处理返回结果
            convertHomeResultList(pCourseCasesVos);
            Map<String, Object> map = new HashMap<>();
            map.put("list", pCourseCasesVos);
            map.put("total", pCourseCasesVos.size());
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(conditionBo.getPageNo(), conditionBo.getPageSize(), conditionBo.getOrderBy());
            List<PCourseCasesVo> pCourseCasesVos = pCourseCasesService.topList(conditionBo);
            // 处理返回结果
            convertHomeResultList(pCourseCasesVos);
            PageInfo<PCourseCasesVo> pageInfo = new PageInfo<>(pCourseCasesVos);
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), conditionBo.getPageNo(),
                conditionBo.getPageSize());
        }
    }

    /**
     * 处理返回结果的数据
     *
     * @param pCourseCasesVos the p course cases vos
     * <AUTHOR>
     * @date 2023 -08-14 13:52:20
     */
    private void convertHomeResultList(List<PCourseCasesVo> pCourseCasesVos) {
        if (CollectionUtils.isEmpty(pCourseCasesVos)) {
            return;
        }
        List<Long> casesIds = pCourseCasesVos.stream().map(PCourseCasesVo::getId).collect(Collectors.toList());
        // 专家评语
        Map<Long, List<CourseVerifyLogVo>> verifyLogMap = Maps.newHashMap();
        CourseVerifyLogConditionBo conditionBo = new CourseVerifyLogConditionBo();
        conditionBo.setCasesIds(casesIds);
        List<CourseVerifyLogVo> courseVerifyLogVos =
                courseVerifyLogService.getCourseVerifyLogListByCondition(conditionBo);
        if (CollectionUtils.isNotEmpty(courseVerifyLogVos)) {
            verifyLogMap = courseVerifyLogVos.stream().collect(Collectors.groupingBy(CourseVerifyLogVo::getCasesId));
        }
        // 课程案例名称
        Map<Long, List<PCourseCasesTypeVo>> courseCasesTypeMap = Maps.newHashMap();
        PCourseCasesTypeConditionBo courseCasesTypeConditionBo = new PCourseCasesTypeConditionBo();
        courseCasesTypeConditionBo.setCasesIds(casesIds);
        List<PCourseCasesTypeVo> casesTypeVos = pCourseCasesTypeService.getPCourseCasesTypeListByCondition(courseCasesTypeConditionBo);
        if (CollectionUtils.isNotEmpty(casesTypeVos)) {
            courseCasesTypeMap = casesTypeVos.stream().collect(Collectors.groupingBy(PCourseCasesTypeVo::getCasesId));
        }
        // 学段、年份
        Map<Long, PCategoryVo> categoryVoMap = Maps.newHashMap();
        Set<Long> categoryIds = Sets.newHashSet();
        for (PCourseCasesVo pCourseCasesVo : pCourseCasesVos) {
            if (null != pCourseCasesVo.getPhase()) {
                categoryIds.add(pCourseCasesVo.getPhase());
            }
            // 年份翻译
            if (null != pCourseCasesVo.getYear()) {
                categoryIds.add(pCourseCasesVo.getYear());
            }
        }
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            PCategoryConditionBo categoryConditionBo = new PCategoryConditionBo();
            categoryConditionBo.setIds(new ArrayList<>(categoryIds));
            List<PCategoryVo> categoryVos = pCategoryService.getPCategoryListByCondition(categoryConditionBo);
            if (CollectionUtils.isNotEmpty(categoryVos)) {
                categoryVoMap = categoryVos.stream().collect(Collectors.toMap(PCategoryVo::getId, c -> c, (v1, v2) -> v1));
            }
        }

        for (PCourseCasesVo pCourseCasesVo : pCourseCasesVos) {
            //收藏量

            // 案例类型名称集合
            if (courseCasesTypeMap.containsKey(pCourseCasesVo.getId())) {
                List<String> typeNames = courseCasesTypeMap.get(pCourseCasesVo.getId()).stream()
                        .map(PCourseCasesTypeVo::getName).collect(Collectors.toList());
                pCourseCasesVo.setTypeNameList(typeNames);
            }
            // 学段翻译
            if (null != pCourseCasesVo.getPhase() && categoryVoMap.containsKey(pCourseCasesVo.getPhase())) {
                pCourseCasesVo.setPhaseName(categoryVoMap.get(pCourseCasesVo.getPhase()).getName());
            }
            // 年份翻译
            if (null != pCourseCasesVo.getYear() && categoryVoMap.containsKey(pCourseCasesVo.getYear())) {
                pCourseCasesVo.setYearName(categoryVoMap.get(pCourseCasesVo.getYear()).getName());
            }
            // 浏览量
            if (null != pCourseCasesVo.getViews()) {
                pCourseCasesVo.setViewsStr(pCourseCasesVo.getViews().toString());
                if (10000 <= pCourseCasesVo.getViews()) {
                    Double result = pCourseCasesVo.getViews().doubleValue() / 10000;
                    pCourseCasesVo.setViewsStr(String.format("%.1f", result) + "w");
                }
            }
            // 若简介长度大于45，则后面部分展示...
            if (StringUtils.isNotEmpty(pCourseCasesVo.getIntroduction())
                && 45 < pCourseCasesVo.getIntroduction().length()) {
                String str = pCourseCasesVo.getIntroduction().substring(0, 45);
                pCourseCasesVo.setIntroduction(str + "...");
            }
            // 省市区
            if (null != pCourseCasesVo.getProvinceId()) {
                pCourseCasesVo.setProvinceName(baseDataService.getAreaNameFromCache(pCourseCasesVo.getProvinceId()));
            }
            if (null != pCourseCasesVo.getCityId()) {
                pCourseCasesVo.setCityName(baseDataService.getAreaNameFromCache(pCourseCasesVo.getCityId()));
            }
            if (null != pCourseCasesVo.getCountyId()) {
                pCourseCasesVo.setCountyName(baseDataService.getAreaNameFromCache(pCourseCasesVo.getCountyId()));
            }
            // 专家评语
            if (verifyLogMap.containsKey(pCourseCasesVo.getId())) {
                pCourseCasesVo.setCourseVerifyLogList(verifyLogMap.get(pCourseCasesVo.getId()));
            }
        }
    }

    @Override
    public AjaxResult<PCourseCasesVo> getContentModule(PCourseCasesConditionBo condition) {
        if (condition.getId() == null) {
            return AjaxResult.fail("参数错误");
        }

        PCourseCasesVo contentModule = pCourseCasesService.getContentModule(condition);
        // 封装原始返回信息（字典类型等）
        convertHomeResultOne(contentModule,null);
        // 封装分数返回信息
        ArrayList<PCourseCasesVo> pCourseCasesVos = Lists.newArrayList(contentModule);
        convertScoreResultList(condition.getCurrentUserOid(),pCourseCasesVos, condition.getAssistantVerifyProcessType());
        setExpertUsers(pCourseCasesVos);
        // 封装版本记录返回
        if (condition.getQueryEdition()) {
            convertCourseCasesEdition(pCourseCasesVos);
        }
        //过滤专家评语
        List<CourseVerifyLogVo> courseVerifyLogList = contentModule.getCourseVerifyLogList();
        if (CollectionUtil.isNotEmpty(courseVerifyLogList)){
            List<CourseVerifyLogVo> courseVerifyLogVoList = courseVerifyLogList.stream()
                    .filter(item ->
                            ObjectUtil.equals(item.getVerifyProcessType(), VerifyProcessType.COMPREHENSIVE.getValue()) ||
                                    ObjectUtil.equals(item.getVerifyProcessType(), VerifyProcessType.VERIFY_COMMENT.getValue()) ||
                                    ObjectUtil.equals(item.getVerifyProcessType(), VerifyProcessType.GUIDE_COMMENT.getValue()))
                    .collect(Collectors.toList());
            contentModule.setCourseVerifyLogList(courseVerifyLogVoList);
        }
        return AjaxResult.success(contentModule);
    }

    @Override
    public AjaxResult saveContentModule(PCourseCasesBo pCourseCasesBo) {
        try {
            pCourseCasesService.saveContentModule(pCourseCasesBo);
        } catch (Exception e) {
            log.error("saveContentModule error:", e);
            return AjaxResult.fail();
        }
        return AjaxResult.success();
    }

    /**
     * 处理返回结果的数据
     *
     * @param pCourseCasesVos the p course cases vos
     * <AUTHOR>
     * @date 2023 -08-14 13:52:20
     */
    private void convertHomeResultOne(PCourseCasesVo pCourseCasesVo,Boolean queryReviews) {
        if (pCourseCasesVo == null) {
            return;
        }
        LoginAccountVo currentUser = baseDataService.getCurrentUser();
        // 设置当前登录人查询
        String currentUserOid = baseDataService.getCurrentUserOid();
        //是否收藏
        setIsCollected(pCourseCasesVo, currentUserOid);

        // 案例类型名称集合
        List<String> casesTypeNameList = pCourseCasesTypeService.getCasesTypeNameList(pCourseCasesVo.getId());
        pCourseCasesVo.setTypeNameList(casesTypeNameList);
        // 学段翻译
        if (null != pCourseCasesVo.getPhase()) {
            PCategoryDto pCategoryDto = pCategoryService.getById(pCourseCasesVo.getPhase());
            pCourseCasesVo.setPhaseName(pCategoryDto.getName());
        }
        // 年份翻译
        if (null != pCourseCasesVo.getYear()) {
            PCategoryDto pCategoryDto = pCategoryService.getById(pCourseCasesVo.getYear());
            pCourseCasesVo.setYearName(pCategoryDto.getName());
        }
        // 浏览量
        if (null != pCourseCasesVo.getViews()) {
            pCourseCasesVo.setViewsStr(pCourseCasesVo.getViews().toString());
            if (10000 <= pCourseCasesVo.getViews()) {
                Double result = pCourseCasesVo.getViews().doubleValue() / 10000;
                pCourseCasesVo.setViewsStr(String.format("%.1f", result) + "w");
            }
        }

        // 评论数量
        Long discussNum = this.iCourseDiscussSectionService.queryCountByCasesId(pCourseCasesVo.getId());
        pCourseCasesVo.setDiscussNum(discussNum);

        // 专家评语
        CourseVerifyLogConditionBo conditionBo = new CourseVerifyLogConditionBo();
        conditionBo.setCasesId(pCourseCasesVo.getId());
        if (ObjectUtil.equals(queryReviews, Boolean.TRUE)){
            conditionBo.setShowType(1);
        }
        List<CourseVerifyLogVo> courseVerifyLogVos =
            courseVerifyLogService.getCourseVerifyLogListByCondition(conditionBo);
        //获取用户ID
        List<String> userOidList = courseVerifyLogVos.stream()
                .map(CourseVerifyLogVo::getUserOid)
                .distinct()
                .collect(Collectors.toList());

        UserConditionBoExt userCondition = new UserConditionBoExt();
        userCondition.setOids(new ArrayList<>(userOidList));
        userCondition.setPageNo(SystemConstants.NO_PAGE);
        List<UserVoExt> userVoExtList = baseDataService.getUserList(userCondition).getList(UserVoExt.class, "list");

        Map<String, UserVoExt> userVoExtMap = userVoExtList
                .stream()
                .collect(Collectors.toMap(UserVoExt::getOid, Function.identity(), (v1, v2) -> v1));

        courseVerifyLogVos.forEach(vo -> {
            vo.setUserVoExt(userVoExtMap.get(vo.getUserOid()));
        });

        // 省市区
        if (null != pCourseCasesVo.getProvinceId()) {
            pCourseCasesVo.setProvinceName(baseDataService.getAreaNameFromCache(pCourseCasesVo.getProvinceId()));
        }
        if (null != pCourseCasesVo.getCityId()) {
            pCourseCasesVo.setCityName(baseDataService.getAreaNameFromCache(pCourseCasesVo.getCityId()));
        }
        if (null != pCourseCasesVo.getCountyId()) {
            pCourseCasesVo.setCountyName(baseDataService.getAreaNameFromCache(pCourseCasesVo.getCountyId()));
        }

        pCourseCasesVo.setCourseVerifyLogList(courseVerifyLogVos);
        // 默认不需要评分
        pCourseCasesVo.setNeedScore(NeedScore.NO.getValue());
        // 若已登录状态，则展示案例详细信息
        if (null != currentUser) {
            pCourseCasesVo.setLoginStatus(true);
            // 查看权限改动：后台设置 2-未认证学校用户 3-已认证学校用户 4-案例持有者
            if (checkAuth(pCourseCasesVo, currentUser)) {
                pCourseCasesVo.setViewAuth(true);
                PCourseCasesInfoVo pCourseCasesInfoVo = pCourseCasesInfoService.infoDetail(pCourseCasesVo.getId());
                pCourseCasesVo.setCourseCasesInfoVo(pCourseCasesInfoVo);
                CourseModuleConditionBo condition = new CourseModuleConditionBo();
                condition.setCasesId(pCourseCasesVo.getId());
                condition.setPageNo(SystemConstants.NO_PAGE);
                pCourseCasesVo.setCourseModuleVos(
                    courseModuleService.getCourseModuleListByConditionWithAttachment(condition, true));
            } else {
                pCourseCasesVo.setViewAuth(false);
            }
            // 审核专家是否需要打分
            CourseReviewExpertConfigConditionBo configConditionBo = new CourseReviewExpertConfigConditionBo();
            configConditionBo.setUserOid(currentUser.getCurrentUser().getUserOid());
            configConditionBo.setCasesId(pCourseCasesVo.getId());
            configConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            List<CourseReviewExpertConfigVo> configVos = courseReviewExpertConfigService.getCourseReviewExpertConfigListByCondition(configConditionBo);
            if (CollectionUtils.isNotEmpty(configVos)) {
                CourseReviewExpertConfigVo configVo = configVos.get(ConstantsInteger.NUM_0);
                if (configVo.getNeedScore() != null) {
                    pCourseCasesVo.setNeedScore(configVo.getNeedScore());
                }
            }
        }
    }

    private void setIsCollected(PCourseCasesVo pCourseCasesVo, String currentUserOid) {
        CourseStoreConditionBo courseStoreConditionBo = new CourseStoreConditionBo();
        courseStoreConditionBo.setCasesId(pCourseCasesVo.getId());
        courseStoreConditionBo.setUserOid(currentUserOid);
        List<CourseStoreVo> courseStoreListByCondition = courseStoreService.getCourseStoreListByCondition(courseStoreConditionBo);
        pCourseCasesVo.setIsCollected(CollectionUtils.isNotEmpty(courseStoreListByCondition));
        Long store = Optional
                .ofNullable(redisComponent.get(ConstantsRedis.COURSE_STORE_PREFIX + pCourseCasesVo.getId()))
                .map(Convert::toLong)
                .orElse(0L);
        pCourseCasesVo.setStore(store);
    }

    /**
     * 校验课程案例观看权限
     *
     * @param casesVo
     * @param currentUser
     * @return boolean
     * <AUTHOR>
     * @date 2024/9/9 14:19
     **/
    private boolean checkAuth(PCourseCasesVo casesVo, LoginAccountVo currentUser) {
        // 如果是审核专家、指导专家、综评专家、专家助理，返回true
        List<Long> viewRoleIds = Arrays.stream(ExpertRoleEnum.values()).map(ExpertRoleEnum::getRoleId).collect(Collectors.toList());
        List<UserRoleVo> currentUserRoles = currentUser.getCurrentUser().getLoginUserRoles();
        if (CollectionUtils.isNotEmpty(currentUserRoles)) {
            List<UserRoleVo> currentExpertRoles = currentUserRoles.stream()
                    .filter(x -> viewRoleIds.contains(x.getRoleId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(currentExpertRoles)) {
                return true;
            }
        }
        Long userOrganizationId = currentUser.getCurrentUser().getUserOrg().getId();
        Integer authType = baseDataService.getOrgAuthType(userOrganizationId);
        Integer userViewPermission;
        if (authType.equals(OrganizationAuthType.AUTH.getCode())) {
            userViewPermission = ViewPermission.AUTH_PEOPLE.getCode();
        } else {
            userViewPermission = ViewPermission.NO_AUTH_PEOPLE.getCode();
        }
        String viewPermission = casesVo.getViewPermission();
        if (StringUtils.isBlank(viewPermission)) {
            return false;
        }
        List<String> permissions = Lists.newArrayList(viewPermission.split(","));
        // 未认证、已认证观看权限校验
        for (String permission : permissions) {
            if (userViewPermission.toString().equals(permission)) {
                return true;
            }
        }
        // 案例持有者校验
        if (permissions.contains(ViewPermission.COURSE_CASE_OWNER.getCode().toString())) {
            List<Long> userRoleIds = currentUser.getCurrentUser().getLoginUserRoles().stream()
                    .map(UserRoleVo::getRoleId).collect(Collectors.toList());
            if (userRoleIds.contains(ReceptionRoleEnum.CASE_OWNER.getRoleId())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 封装分数返回（只有校本可以打分，所以这里不用区分是校本还是精品，都汇总显示），注意后期如果校本和精品都有打分的话，这里需要修改，查询分页列表的排序也要修改
     *
     * @param condition
     * @param pCourseCasesListByCondition
     */
    private void convertScoreResultList(String currentUserOid, List<PCourseCasesVo> pCourseCasesListByCondition, Integer assistantVerifyProcessType) {
        if (CollectionUtils.isNotEmpty(pCourseCasesListByCondition)) {
            List<Long> courseIds =
                pCourseCasesListByCondition.stream().map(PCourseCasesVo::getId).collect(Collectors.toList());
            CourseVerifyLogConditionBo courseVerifyLogConditionBo = new CourseVerifyLogConditionBo();
            courseVerifyLogConditionBo.setCasesIds(courseIds);

            // 计算总分和平均分
            List<CourseVerifyLogVo> courseVerifyLogVos =
                courseVerifyLogService.getCourseVerifyLogListByCondition(courseVerifyLogConditionBo);
            // 总分
            Map<Long, String> courseVerifyLogScoreMap = Maps.newHashMap();
            // 平均分
            Map<Long, String> courseVerifyLogAvgScoreMap = Maps.newHashMap();
            // 当前用户打分
            Map<Long, String> courseVerifyLogUserScoreMap = Maps.newHashMap();
            // 课程宗评状态
            Map<Long, Integer> comprehensiveOperateTypesMap = Maps.newHashMap();
            // 当前用户采纳意见
            Map<Long, Integer> adoptMap = Maps.newHashMap();
            // 课程派单状态
            Map<Long, Integer> dispatchOperateTypeMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(courseVerifyLogVos)) {
                Map<Long, List<CourseVerifyLogVo>> courseVerifyLogMap =
                    courseVerifyLogVos.stream().collect(Collectors.groupingBy(CourseVerifyLogVo::getCasesId));
                courseVerifyLogMap.forEach((courseId, courseVerifyLogVosTemp) -> {
                    List<CourseVerifyLogVo> scoreList = courseVerifyLogVosTemp.stream()
                        .filter(v -> v != null && NeedScore.YES.getValue().equals(v.getNeedScore()) && StringUtils.isNotBlank(v.getVerifyScoreTotal()))
                        .collect(Collectors.toList());
                    Long scoreLong =
                            scoreList.stream().mapToLong(v -> Long.parseLong(v.getVerifyScoreTotal())).sum();
                    Long userScoreLong = scoreList.stream()
                        .filter(x -> StringUtils.isNotBlank(currentUserOid) && currentUserOid.equals(x.getUserOid()))
                        .mapToLong(x -> Long.parseLong(x.getVerifyScoreTotal())).sum();
                    courseVerifyLogScoreMap.put(courseId, scoreLong == null ? "" : scoreLong.toString());
                    String avgScore = scoreLong.toString();
                    if (scoreLong != null && scoreList.size() > 0) {
                        avgScore = (new BigDecimal(scoreLong).divide(new BigDecimal(scoreList.size()), 2,
                            BigDecimal.ROUND_HALF_UP)).toString();
                    }
                    courseVerifyLogAvgScoreMap.put(courseId, avgScore);
                    courseVerifyLogUserScoreMap.put(courseId, userScoreLong == null ? "0" : userScoreLong.toString());
                    boolean comprehensiveOperateTypes =false;
                    boolean dispatchOperateType =false;
                    if (CollUtil.isNotEmpty(courseVerifyLogVosTemp)) {
                        //courseVerifyLogVosTemp里有一条verifyProcessType为4这一条，说明已经总评
                        comprehensiveOperateTypes=courseVerifyLogVosTemp
                                .stream()
                                .anyMatch(item -> VerifyProcessType.COMPREHENSIVE.getValue().equals(item.getVerifyProcessType()));
                        //courseVerifyLogVosTemp里有一条verifyProcessType为4这一条，说明已经总评
                        dispatchOperateType=courseVerifyLogVosTemp
                                .stream()
                                .anyMatch(item -> VerifyProcessType.DISPATCH.getValue().equals(item.getVerifyProcessType()));
                    }
                    comprehensiveOperateTypesMap.put(courseId, comprehensiveOperateTypes?OperateType.YES.getValue():OperateType.NO.getValue());
                    dispatchOperateTypeMap.put(courseId, dispatchOperateType?OperateType.YES.getValue():OperateType.NO.getValue());
                    Integer userAdoptType = null;
                    if (StringUtils.isNotBlank(currentUserOid)) {
                        List<CourseVerifyLogVo> adoptList = courseVerifyLogVosTemp.stream()
                                .filter(x -> VerifyProcessType.ADOPT.getValue().equals(x.getVerifyProcessType())
                                        && x.getAdoptType() != null
                                        && currentUserOid.equals(x.getUserOid()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(adoptList)) {
                            userAdoptType = adoptList.get(ConstantsInteger.NUM_0).getAdoptType();
                            adoptMap.put(courseId, userAdoptType);
                        }
                    }
                });
                pCourseCasesListByCondition.forEach(v -> {
                    // 总分
                    v.setCourseVerifyScoreTotal(courseVerifyLogScoreMap.get(v.getId()));
                    // 平均分
                    v.setCourseVerifyScoreAvg(courseVerifyLogAvgScoreMap.get(v.getId()));
                    // 当前用户打分
                    v.setCurrentUserVerifyScore(courseVerifyLogUserScoreMap.get(v.getId()));
                    // 设置综评状态
                    v.setComprehensiveOperateTypes(comprehensiveOperateTypesMap.getOrDefault(v.getId(),OperateType.NO.getValue()));
                    // 设置派单状态
                    v.setDispatchOperateType(dispatchOperateTypeMap.getOrDefault(v.getId(),OperateType.NO.getValue()));
                    // 当前用户采纳意见
                    v.setCurrentUserAdoptType(adoptMap.get(v.getId()));
                });
                //设置采纳建议数量和不采纳建议数量
                processAdoptionCounts(pCourseCasesListByCondition, courseVerifyLogMap);
            } else {
                pCourseCasesListByCondition.forEach(v -> {
                    // 设置综评状态
                    v.setComprehensiveOperateTypes(OperateType.NO.getValue());
                    // 设置派单状态
                    v.setDispatchOperateType(OperateType.NO.getValue());
                });
            }

            // 查询加权分并赋值
            CourseWeightedScoreConditionBo courseWeightedScoreConditionBo = new CourseWeightedScoreConditionBo();
            courseWeightedScoreConditionBo.setCasesIds(courseIds);
            List<CourseWeightedScoreVo> courseWeightedScoreVos =
                courseWeightedScoreService.getCourseWeightedScoreListByCondition(courseWeightedScoreConditionBo);
            if (CollectionUtils.isNotEmpty(courseWeightedScoreVos)) {
                Map<Long, CourseWeightedScoreVo> courseWeightedScoreMap = courseWeightedScoreVos.stream()
                    .collect(Collectors.toMap(CourseWeightedScoreVo::getCasesId, v -> v));
                pCourseCasesListByCondition.forEach(v -> {
                    if (!courseWeightedScoreMap.containsKey(v.getId())) {
                        return;
                    }
                    CourseWeightedScoreVo courseWeightedScoreVo = courseWeightedScoreMap.get(v.getId());
                    if (courseWeightedScoreVo != null) {
                        v.setCourseWeightedScoreVo(courseWeightedScoreVo);
                    }
                });

            }
        }
    }

    private void setExpertUsers(List<PCourseCasesVo> pCourseCasesListByCondition) {
        if (CollectionUtils.isEmpty(pCourseCasesListByCondition)) {
            return;
        }
        List<Long> courseIds =
                pCourseCasesListByCondition.stream().map(PCourseCasesVo::getId).collect(Collectors.toList());
        // 获取专家助理
        CourseReviewExpertConfigConditionBo configConditionBo = new CourseReviewExpertConfigConditionBo();
        configConditionBo.setCasesIds(courseIds);
        configConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<CourseReviewExpertConfigVo> configVoList
                = courseReviewExpertConfigService.getCourseReviewExpertConfigListByCondition(configConditionBo);
        if (CollectionUtils.isNotEmpty(configVoList)) {
            Map<Long, List<CourseReviewExpertConfigVo>> configVoMap
                    = configVoList.stream().collect(Collectors.groupingBy(CourseReviewExpertConfigVo::getCasesId));
            Set<String> adminOids = configVoList.stream().map(CourseReviewExpertConfigVo::getUserOid)
                    .collect(Collectors.toSet());
            UserConditionBoExt userCondition = new UserConditionBoExt();
            userCondition.setOids(new ArrayList<>(adminOids));
            userCondition.setPageNo(SystemConstants.NO_PAGE);
//                userCondition.setOrganizationId(ineoschoolOrganizationId);
            List<UserVoExt> userVoExtList = baseDataService.getUserList(userCondition).getList(UserVoExt.class, "list");
            if (CollectionUtils.isNotEmpty(userVoExtList)) {
                for (PCourseCasesVo pCourseCasesVo : pCourseCasesListByCondition) {
                    if (!configVoMap.containsKey(pCourseCasesVo.getId())) {
                        continue;
                    }
                    List<CourseReviewExpertConfigVo> configVos = configVoMap.get(pCourseCasesVo.getId());
                    //根据专家类型分组
                    Map<Integer, List<CourseReviewExpertConfigVo>> expertTypeConfigVoMap = configVos.stream()
                            .collect(Collectors.groupingBy(CourseReviewExpertConfigVo::getExpertType));
                    expertTypeConfigVoMap.forEach((k, v) -> {
                        // 根据用户oid查询用户信息
                        List<String> userOids = v.stream().map(CourseReviewExpertConfigVo::getUserOid)
                                .collect(Collectors.toList());
                        List<UserVoExt> expertAssistantList = userVoExtList.stream().filter(x -> userOids.contains(x.getUserOid()))
                                .collect(Collectors.toList());
                        if (ObjectUtil.equals(k,CourseReviewExpertType.AUDIT.getValue())){
                            // 指导专家
                            pCourseCasesVo.setAuditExpertsList(expertAssistantList);
                        }
                        if (ObjectUtil.equals(k,CourseReviewExpertType.GUIDANCE.getValue())){
                            // 审核专家
                            pCourseCasesVo.setGuidanceSpecialistsList(expertAssistantList);
                        }
                        if (ObjectUtil.equals(k,CourseReviewExpertType.ASSISTANT.getValue())){
                            // 专家助理
                            pCourseCasesVo.setExpertAssistantList(expertAssistantList);
                        }
                    });

                }
            }
        }
    }


    /**
     * 根据课程ID获取对应的审核日志列表
     * 
     * @param courseVerifyLogMap 一个映射，其键是课程ID，值是与该课程ID关联的审核日志列表
     * @param id 课程ID，用于从映射中获取相应的审核日志列表
     * @return 返回与指定课程ID对应的审核日志列表如果指定的课程ID在映射中没有对应的列表，则返回一个空的列表
     */
    private List<CourseVerifyLogVo> getCourseCasesLogList(Map<Long, List<CourseVerifyLogVo>> courseVerifyLogMap, Long id) {
        // 使用Optional来处理可能的空值情况，以避免空指针异常
        // 如果courseVerifyLogMap中包含id指定的键，则返回对应的审核日志列表
        // 否则，返回一个新的空列表
        return Optional.ofNullable(courseVerifyLogMap.get(id))
                .orElse(new ArrayList<>());
    }

    /**
     * 处理采纳统计信息
     * 该方法用于计算每个课程案例被建议采纳和不建议采纳的数量，并将这些数量设置到课程案例对象中
     * 
     * @param pCourseCasesListByCondition 课程案例列表，应包含需要处理的课程案例
     * @param courseVerifyLogMap 课程审核日志映射，键为课程案例ID，值为该课程案例对应的审核日志列表
     */
    public void processAdoptionCounts(List<PCourseCasesVo> pCourseCasesListByCondition, Map<Long, List<CourseVerifyLogVo>> courseVerifyLogMap) {
        // 检查输入参数是否为null，如果任一参数为null，则直接返回，不做任何处理
        // 这里也可以选择抛出异常，而不是简单地返回
        if (ObjectUtil.isNull(pCourseCasesListByCondition)  || ObjectUtil.isNull(courseVerifyLogMap)) {
            return; // 或者抛出异常
        }
    
        // 遍历课程案例列表，为每个课程案例计算采纳和不采纳的数量
        for (PCourseCasesVo pCourseCasesVo : pCourseCasesListByCondition) {
            // 获取当前课程案例的所有审核日志
            List<CourseVerifyLogVo> courseCasesLogList = getCourseCasesLogList(courseVerifyLogMap, pCourseCasesVo.getId());
            // 如果当前课程案例没有审核日志，则跳过，继续处理下一个课程案例
            if (CollectionUtils.isEmpty(courseCasesLogList)) {
                continue;
            }
            // 计算建议和不建议采纳数量
            int rejectCount = 0;
            int adoptCount = 0;
            // 遍历当前课程案例的审核日志，统计采纳和不采纳的数量
            for (CourseVerifyLogVo courseVerifyLogVo : courseCasesLogList) {
                // 检查审核日志的采纳类型是否已设置
                if (ObjectUtil.isNotNull(courseVerifyLogVo.getAdoptType())) {
                    // 根据采纳类型增加相应的计数器
                    if (courseVerifyLogVo.getAdoptType() == 1) {
                        adoptCount++;
                    } else if (courseVerifyLogVo.getAdoptType() == 2) {
                        rejectCount++;
                    }
                }
            }
            // 将计算出的采纳和不采纳数量设置到课程案例对象中
            if (adoptCount > ConstantsInteger.NUM_0) {
                pCourseCasesVo.setAdoptCount(adoptCount);
            }
            if (rejectCount > ConstantsInteger.NUM_0) {
                pCourseCasesVo.setRejectCount(rejectCount);
            }
        }
    }

    /**
     * 封装当前登录用户的课程审核操作状态
     *
     * @param condition
     * @param pCourseCasesListByCondition
     */
    private void convertVerifyOperateType(PCourseCasesConditionBo condition,
        List<PCourseCasesVo> pCourseCasesListByCondition) {
        List<CourseVerifyLogDto> courseVerifyLogDtos =
            courseVerifyLogService.getCurrentUserCourseVerifyLogList(condition);
        List<Integer> verifyProcessTypes = Lists.newArrayList(VerifyProcessType.COMPREHENSIVE.getValue(),
                VerifyProcessType.VERIFY_COMMENT.getValue(), VerifyProcessType.GUIDE_COMMENT.getValue(),
                VerifyProcessType.FEEDBACK.getValue());
        Map<Long, List<CourseVerifyLogDto>> map =
            courseVerifyLogDtos.stream().filter(x -> verifyProcessTypes.contains(x.getVerifyProcessType()))
                    .collect(Collectors.groupingBy(CourseVerifyLogDto::getCasesId));
        for (PCourseCasesVo vo : pCourseCasesListByCondition) {
            if (map.containsKey(vo.getId())) {
                vo.setVerifyOperateType(CourseVerifyOperateType.ALREADY_VERITY.getValue());
            } else {
                vo.setVerifyOperateType(CourseVerifyOperateType.NO_VERITY.getValue());
            }
        }
    }

    /**
     * 查询版本记录
     *
     * @param pCourseCasesListByCondition
     * @return void
     * <AUTHOR>
     * @date 2024/12/5 15:56
     **/
    private void convertCourseCasesEdition(List<PCourseCasesVo> pCourseCasesListByCondition) {
        List<Long> casesIds = pCourseCasesListByCondition.stream().map(PCourseCasesVo::getId).collect(Collectors.toList());
        CourseCasesEditionConditionBo courseCasesEditionConditionBo = new CourseCasesEditionConditionBo();
        courseCasesEditionConditionBo.setCasesIds(casesIds);
        List<CourseCasesEditionVo> courseCasesEditionVos = courseCasesEditionService.getCourseCasesEditionListByCondition(courseCasesEditionConditionBo);
        if (CollectionUtils.isEmpty(courseCasesEditionVos)) {
            return;
        }
        Map<Long, List<CourseCasesEditionVo>> courseCasesEditionMap
                = courseCasesEditionVos.stream().collect(Collectors.groupingBy(CourseCasesEditionVo::getCasesId));
        for (PCourseCasesVo pCourseCasesVo : pCourseCasesListByCondition) {
            if (courseCasesEditionMap.containsKey(pCourseCasesVo.getId())) {
                List<CourseCasesEditionVo> editionVos = courseCasesEditionMap.get(pCourseCasesVo.getId()).stream()
                        .sorted(Comparator.comparing(CourseCasesEditionVo::getCreateDate).reversed()).collect(Collectors.toList());
                pCourseCasesVo.setCourseCasesEditionList(editionVos);
            }
        }
    }

    /**
     * 审核记录和版本记录合并返回
     *
     * @param pCourseCasesVos
     * @return void
     * <AUTHOR>
     * @date 2024/12/5 15:56
     **/
    private void handleVerifyLogAndEdition(List<PCourseCasesVo> pCourseCasesVos) {
        if (CollectionUtils.isEmpty(pCourseCasesVos)) {
            return;
        }
        Set<String> userOidSet = new HashSet<>();
        pCourseCasesVos.stream().filter(x -> CollectionUtils.isNotEmpty(x.getCourseCasesEditionList())).forEach(x -> {
            List<String> userOids = x.getCourseCasesEditionList().stream().map(CourseCasesEditionVo::getUserOid).collect(Collectors.toList());
            userOidSet.addAll(userOids);
        });
        Map<String, UserVoExt> userVoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userOidSet)) {
            List<UserVoExt> userVoExtList = baseDataService.queryByUserOidList(new ArrayList<>(userOidSet));
            userVoMap = userVoExtList.stream().collect(Collectors.toMap(UserVoExt::getUserOid, u -> u, (v1, v2) -> v1));
        }
        for (PCourseCasesVo pCourseCasesVo : pCourseCasesVos) {
            List<CourseCasesVerifyLogAndEditionVo> allList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(pCourseCasesVo.getCourseVerifyLogList())) {
                List<Integer> courseVerifyProcessTypes = Lists.newArrayList(VerifyProcessType.VERIFY.getValue(),
                        VerifyProcessType.VERIFY_COMMENT.getValue(), VerifyProcessType.GUIDE_COMMENT.getValue(),
                        VerifyProcessType.COMPREHENSIVE.getValue());
                List<CourseCasesVerifyLogAndEditionVo> courseCasesVerifyLogAndEditionVos
                        = pCourseCasesVo.getCourseVerifyLogList().stream()
                        .filter(x -> courseVerifyProcessTypes.contains(x.getVerifyProcessType()))
                        .map(x -> {
                            CourseCasesVerifyLogAndEditionVo verifyLogAndEditionVo = new CourseCasesVerifyLogAndEditionVo();
                            verifyLogAndEditionVo.setBusinessType(1);
                            verifyLogAndEditionVo.setBusinessId(x.getId());
                            verifyLogAndEditionVo.setCreateTime(x.getCreateTime());
                            verifyLogAndEditionVo.setExpertUserName(x.getRealName());
                            verifyLogAndEditionVo.setExpertUserOid(x.getUserOid());
                            verifyLogAndEditionVo.setVerifyProcessType(x.getVerifyProcessType());
                            verifyLogAndEditionVo.setNormalVerifyType(x.getNormalVerifyType());
                            verifyLogAndEditionVo.setBestVerifyType(x.getBestVerifyType());
                            verifyLogAndEditionVo.setCourseVerifySource(x.getCourseVerifySource());
                            return verifyLogAndEditionVo;
                        }).collect(Collectors.toList());
                allList.addAll(courseCasesVerifyLogAndEditionVos);
            }
            if (CollectionUtils.isNotEmpty(pCourseCasesVo.getCourseCasesEditionList())) {
                Map<String, UserVoExt> finalUserVoMap = userVoMap;
                List<CourseCasesVerifyLogAndEditionVo> courseCasesVerifyLogAndEditionVos
                        = pCourseCasesVo.getCourseCasesEditionList().stream()
                        .map(x -> {
                            CourseCasesVerifyLogAndEditionVo verifyLogAndEditionVo = new CourseCasesVerifyLogAndEditionVo();
                            verifyLogAndEditionVo.setBusinessType(2);
                            verifyLogAndEditionVo.setBusinessId(x.getId());
                            verifyLogAndEditionVo.setCreateTime(x.getCreateDate());
                            verifyLogAndEditionVo.setApplyUserOid(x.getUserOid());
                            if (finalUserVoMap.containsKey(x.getUserOid())) {
                                verifyLogAndEditionVo.setApplyUserName(finalUserVoMap.get(x.getUserOid()).getRealName());
                            }
                            return verifyLogAndEditionVo;
                        }).collect(Collectors.toList());
                allList.addAll(courseCasesVerifyLogAndEditionVos);
            }
            if (CollectionUtils.isNotEmpty(allList)) {
                allList = allList.stream().sorted(Comparator.comparing(CourseCasesVerifyLogAndEditionVo::getCreateTime).reversed())
                        .collect(Collectors.toList());
                pCourseCasesVo.setCourseCasesVerifyLogAndEditionList(allList);
            }
        }
    }

    @Override
    public AjaxResult getTeacherApplyCourseList(PCourseCasesConditionBo condition) {
        // 设置只查询教师个人申报课程列表
        condition.setUserOid(baseDataService.getCurrentUserOid());
        condition.setQueryEdition(true);
        condition.setHandleVerifyLogAndEdition(true);
        AjaxResult<List<PCourseCasesVo>> courseCasesListResult = getPCourseCasesListByCondition(condition);
        if (courseCasesListResult.isSuccess()) {
            Map<String, Object> resultMap = Maps.newHashMap();
            List<PCourseCasesVo> courseCasesVos = courseCasesListResult.getData();
            // 过滤掉没有节点的数据
            courseCasesVos = courseCasesVos.stream()
                    .filter(x -> CollectionUtils.isNotEmpty(x.getCourseCasesVerifyLogAndEditionList())).collect(Collectors.toList());
            resultMap.put("total", courseCasesVos.size());
            resultMap.put("list", courseCasesVos);
            return AjaxResult.success(resultMap);
        }
        return courseCasesListResult;
    }

    @Override
    public AjaxResult updateExpertCommentaryShowcase(List<CourseVerifyLogBo> courseVerifyLogList) {
        ArrayList<CourseVerifyLogBo> courseVerifyLogBos = new ArrayList<>();
        courseVerifyLogList.forEach(item->{
            CourseVerifyLogBo courseVerifyLogBo = new CourseVerifyLogBo();
            courseVerifyLogBo.setId(item.getId());
            courseVerifyLogBo.setShowType(item.getShowType());
            courseVerifyLogBos.add(courseVerifyLogBo);
        });

        return courseVerifyLogService.updateCourseVerifyLog(courseVerifyLogBos);
    }

    @Override
    public AjaxResult updateSupplementState(PCourseCasesBo pCourseCasesBo) {
        PCourseCasesBo pCourseCasesBoNew = new PCourseCasesBo();
        pCourseCasesBoNew.setId(pCourseCasesBo.getId());
        pCourseCasesBoNew.setIsSupplement(pCourseCasesBo.getIsSupplement());
        return pCourseCasesService.updatePCourseCases(pCourseCasesBo);
    }

    @Override
    public AjaxResult<PageInfo<PCourseCasesVo>> getExpertVerifyCourseList(PCourseCasesConditionBo condition) {
        // 审核专家、指导专家，查询分配课程数据
        String currentUserOid = baseDataService.getCurrentUserOid();
        condition.setVerifyUserOid(currentUserOid);
        condition.setCalculateScore(true);
        condition.setShowExpertUsers(true);
        return getPCourseCasesPageListByCondition(condition);
    }

    @Override
    public AjaxResult<PageInfo<PCourseCasesVo>> getComprehensiveCourseList(PCourseCasesConditionBo condition) {
        LoginAccountVo currentUser = baseDataService.getCurrentUser();
        List<UserRoleVo> userRoleVos = currentUser.getCurrentUser().getLoginUserRoles();
        if (CollectionUtils.isEmpty(userRoleVos)) {
            return AjaxResult.fail();
        }
        List<Long> userRoleIds = userRoleVos.stream().map(UserRoleVo::getRoleId).collect(Collectors.toList());
        if (!userRoleIds.contains(ExpertRoleEnum.COMPREHENSIVE_AUDIT.getRoleId())) {
            return AjaxResult.fail();
        }
        // 综评专家，查询全量课程
        condition.setCalculateScore(true);
        condition.setShowExpertUsers(true);
        condition.setQueryIsRecommend(true);
        return getPCourseCasesPageListByCondition(condition);
    }

    @Override
    public AjaxResult<PageInfo<PCourseCasesVo>> getExpertAssistantCourseList(PCourseCasesConditionBo condition) {
        LoginAccountVo currentUser = baseDataService.getCurrentUser();
        List<UserRoleVo> userRoleVos = currentUser.getCurrentUser().getLoginUserRoles();
        if (CollectionUtils.isEmpty(userRoleVos)) {
            return AjaxResult.fail();
        }
        List<Long> userRoleIds = userRoleVos.stream().map(UserRoleVo::getRoleId).collect(Collectors.toList());
        if (!userRoleIds.contains(ExpertRoleEnum.EXPERT_ASSISTANT.getRoleId())) {
            return AjaxResult.fail();
        }
        // 专家助理，查询全量课程
        condition.setCalculateScore(true);
        condition.setShowExpertUsers(true);
        // 专家助理查询已处理、待处理，只查询分配给本人的课程
        if (condition.getVerifyOperateType() != null) {
            condition.setVerifyUserOid(currentUser.getCurrentUser().getUserOid());
        }
        return getPCourseCasesPageListByCondition(condition);
    }

}
