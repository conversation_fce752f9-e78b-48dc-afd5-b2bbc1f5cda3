package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesGradeDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesGradeVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本课程案例年级接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface IPCourseCasesGradeService extends IService<PCourseCasesGradeDto> {

    List<PCourseCasesGradeVo> getPCourseCasesGradeListByCondition(PCourseCasesGradeConditionBo condition);

	AjaxResult addPCourseCasesGrade(PCourseCasesGradeBo pCourseCasesGradeBo);

	AjaxResult updatePCourseCasesGrade(PCourseCasesGradeBo pCourseCasesGradeBo);

	PCourseCasesGradeVo getPCourseCasesGradeByCondition(PCourseCasesGradeConditionBo condition);

	/**
	 * 保存年级信息，保存后设置id到Bo里面
	 *
	 * @param casesId the cases id
	 * @param courseCasesGradeBoList the course cases grade bo list
	 * <AUTHOR>
	 * @date 2023 -08-18 16:52:05
	 */
	void saveCourseGrade(Long casesId, List<PCourseCasesGradeBo> courseCasesGradeBoList);
}

