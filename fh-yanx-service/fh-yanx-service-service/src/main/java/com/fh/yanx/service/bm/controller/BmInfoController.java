package com.fh.yanx.service.bm.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;
import com.fh.yanx.service.activ.service.IActivInfoService;
import com.fh.yanx.service.aspose.AsposeBizService;
import com.fh.yanx.service.bm.entity.vo.BmInfoCheckVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportZdVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoStatisticsVo;
import com.fh.yanx.service.enums.*;
import com.fh.yanx.service.order.mapper.ActivOrderMapper;
import com.fh.yanx.service.order.service.IActivOrderService;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.bm.api.BmInfoApi;
import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoDto;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.fh.yanx.service.bm.service.IBmInfoJoinerService;
import com.fh.yanx.service.bm.service.IBmInfoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;

/**
 * 新时代文化校园报名活动申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Slf4j
@RestController
@Validated
public class BmInfoController implements BmInfoApi {

    @Autowired
    private IBmInfoService bmInfoService;
    @Autowired
    private IBmInfoJoinerService bmInfoJoinerService;
    @Autowired
    private IActivInfoService activInfoService;
    @Autowired
    private IActivOrderService activOrderService;

    @Resource
    private AsposeBizService asposeBizService;

    /**
     * 查询新时代文化校园报名活动申请表分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult<PageInfo<BmInfoVo>> getBmInfoPageListByCondition(@RequestBody BmInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<BmInfoVo> bmInfoListByCondition = bmInfoService.getBmInfoListWithOrder(condition);
        if (CollectionUtils.isEmpty(bmInfoListByCondition)) {
            return AjaxResult.success(new PageInfo<>(bmInfoListByCondition));
        }
        // 处理返回数据（封装参与人数）
        convertResult(bmInfoListByCondition);
        PageInfo<BmInfoVo> pageInfo = new PageInfo<>(bmInfoListByCondition);
        return AjaxResult.success(pageInfo);
    }

    @Override
    public AjaxResult<BmInfoStatisticsVo> getBmInfoStatistics(BmInfoConditionBo condition) {
        BmInfoStatisticsVo bmInfoStatisticsVo = bmInfoService.getBmInfoStatistics(condition);
        return AjaxResult.success(bmInfoStatisticsVo);
    }

    @Override
    public AjaxResult<Integer> countBmInfoJoiner(BmInfoConditionBo conditionBo) {
        return AjaxResult.success(bmInfoService.countBmInfoJoiner(conditionBo));
    }

    /**
     * 返回数据处理
     * 
     * <AUTHOR>
     * @param bmInfoVoList the bm info vo list
     */
    private void convertResult(List<BmInfoVo> bmInfoVoList) {
        // 返回结果处理
        // 封装代理商类型
        bmInfoVoList.stream().forEach(bmInfoVo -> {
            bmInfoVo.setCmType(CmType.OFFICIAL.getValue());
            if (StringUtils.isNotBlank(bmInfoVo.getCm())) {
                bmInfoVo.setCmType(CmType.AGENT.getValue());
            }
        });

        // 封装参与人
        List<Long> infoIds = bmInfoVoList.stream().map(BmInfoVo::getInfoId).collect(Collectors.toList());
        List<BmInfoVo> bmInfoVoOfCounts = bmInfoJoinerService.getJoinerCountByInfoId(infoIds);
        if (CollectionUtils.isEmpty(bmInfoVoOfCounts)) {
            return;
        }
        Map<Long, Long> joinerCountMap = bmInfoVoOfCounts.stream()
            .collect(Collectors.toMap(BmInfoVo::getInfoId, a -> a.getJoinerCount(), (k1, k2) -> k1));
        bmInfoVoList.forEach(bmInfoVo -> bmInfoVo.setJoinerCount(joinerCountMap.get(bmInfoVo.getInfoId())));
    }

    /**
     * 查询新时代文化校园报名活动申请表列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult<List<BmInfoVo>> getBmInfoListByCondition(@RequestBody BmInfoConditionBo condition) {
        List<BmInfoVo> list = bmInfoService.getBmInfoListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增新时代文化校园报名活动申请表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult addBmInfo(@Validated @RequestBody BmInfoBo bmInfoBo) {
        return bmInfoService.addBmInfo(bmInfoBo);
    }

    /**
     * 修改新时代文化校园报名活动申请表
     * 
     * @param bmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult updateBmInfo(@Validated @RequestBody BmInfoBo bmInfoBo) {
        if (null == bmInfoBo.getInfoId()) {
            return AjaxResult.fail("新时代文化校园报名活动申请表id不能为空");
        }

        // 校验手机号
        if (!bmInfoBo.isNotValidateMobile()) {
            // 手机号存在返回
            BmInfoConditionBo bmInfoConditionBo = new BmInfoConditionBo();
            bmInfoConditionBo.setContactMobile(bmInfoBo.getContactMobile());
            bmInfoConditionBo.setNotEqInfoId(bmInfoBo.getInfoId());
            bmInfoConditionBo.setActivId(bmInfoBo.getActivId());
            BmInfoVo bmInfoVoExist = bmInfoService.getBmInfoByCondition(bmInfoConditionBo);
            if (bmInfoVoExist != null) {
                // 手机号码已存在
                return AjaxResult.fail("手机号码已存在，不可重复提交");
            }
        }
        // 如果是需要生成签到码且更新为已缴费则生成签到码
        if (bmInfoBo.isGenerateSignCode() && bmInfoBo.getPayType() != null
            && bmInfoBo.getPayType().equals(BmInfoPayType.ALREADY_PAY.getCode())) {
            bmInfoBo.setSignInCode(bmInfoService.generateNotRepeatSignInCode(bmInfoBo, false));
        }
        // 需要改价格
        bmInfoBo.setChangeOrderPrice(true);
        // 需要更新订单状态
        bmInfoBo.setChangeOrderState(true);
        return bmInfoService.updateBmInfo(bmInfoBo);
    }

    /**
     * 更新的时候同步更新参与人信息，参与人是先删除再新增
     * 
     * @param bmInfoBo
     * @return
     */
    @Override
    public AjaxResult updateBmInfoWbij(@RequestBody BmInfoBo bmInfoBo) {
        if (null == bmInfoBo.getInfoId()) {
            return AjaxResult.fail("新时代文化校园报名活动申请表id不能为空");
        }
        BmInfoDto bmInfoDto = bmInfoService.getById(bmInfoBo.getInfoId());
        // 联系人手机号未修改，不校验手机号是否重复
        if (!bmInfoBo.isNotValidateMobile() || !bmInfoDto.getContactMobile().equals(bmInfoBo.getContactMobile())) {
            // 手机号存在返回
            BmInfoConditionBo bmInfoConditionBo = new BmInfoConditionBo();
            bmInfoConditionBo.setContactMobile(bmInfoBo.getContactMobile());
            bmInfoConditionBo.setNotEqInfoId(bmInfoBo.getInfoId());
            bmInfoConditionBo.setActivId(bmInfoBo.getActivId());
            BmInfoVo bmInfoVoExist = bmInfoService.getBmInfoByCondition(bmInfoConditionBo);
            if (bmInfoVoExist != null) {
                // 手机号码已存在
                return AjaxResult.fail("手机号码已存在，不可重复提交");
            }
        }

        return bmInfoService.updateBmInfoWbij(bmInfoBo);
    }

    /**
     * 查询新时代文化校园报名活动申请表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult<BmInfoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("新时代文化校园报名活动申请表id不能为空");
        }
        BmInfoConditionBo condition = new BmInfoConditionBo();
        condition.setInfoId(id);
        BmInfoVo vo = bmInfoService.getBmInfoByCondition(condition);
        return AjaxResult.success(vo);
    }

    @Override
    public AjaxResult<BmInfoVo> getDetailWithJoiner(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("新时代文化校园报名活动申请表id不能为空");
        }

        return AjaxResult.success(bmInfoService.getBmInfoWithJoiner(id));
    }

    /**
     * 删除新时代文化校园报名活动申请表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        BmInfoDto bmInfoDto = new BmInfoDto();
        bmInfoDto.setInfoId(id);
        bmInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (bmInfoService.updateById(bmInfoDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult addBmInfoUg(@RequestBody BmInfoBo bmInfoBo) {
        // 参数校验
        if (StringUtils.isBlank(bmInfoBo.getContactMobile())) {
            // 参数错误，手机号码不允许为空
            return AjaxResult.fail("手机号码不允许为空");
        }
        if (CollectionUtils.isEmpty(bmInfoBo.getBmInfoJoinerBoList()) && (bmInfoBo.getActivBizType() == null
            || !bmInfoBo.getActivBizType().equals(ActivBizType.SUB.getValue()))) {
            // 参数错误，参与人不允许为空
            return AjaxResult.success("参与人不允许为空");
        }
        // 手机号存在返回
        BmInfoConditionBo bmInfoConditionBo = new BmInfoConditionBo();
        bmInfoConditionBo.setContactMobile(bmInfoBo.getContactMobile());
        bmInfoConditionBo.setActivId(bmInfoBo.getActivId());
        BmInfoVo bmInfoVoExist = bmInfoService.getBmInfoByCondition(bmInfoConditionBo);
        if (bmInfoVoExist != null) {
            // 手机号码已存在
            ActivInfoConditionBo activInfoConditionBo = new ActivInfoConditionBo();
            activInfoConditionBo.setActivId(bmInfoBo.getActivId());
            ActivInfoVo activInfoByCondition = activInfoService.getActivInfoByCondition(activInfoConditionBo);
            if (activInfoByCondition.getActivBizType() != null
                && activInfoByCondition.getActivBizType().equals(ActivBizType.NORMAL.getValue())) {
                BmInfoCheckVo checkVo = new BmInfoCheckVo();
                checkVo.setType(ActivBmCheckType.ALREADY_SUBMIT.getCode());
                return AjaxResult.success(checkVo);
            }
        }

        return bmInfoService.addBmInfoWithJoiner(bmInfoBo);
    }

    @Override
    public AjaxResult addBmInfoWithJoiner(@RequestBody BmInfoBo bmInfoBo) {
        // 参数校验
        if (StringUtils.isBlank(bmInfoBo.getContactMobile())) {
            // 参数错误，手机号码不允许为空
            return AjaxResult.fail("手机号码不允许为空");
        }
        if (CollectionUtils.isEmpty(bmInfoBo.getBmInfoJoinerBoList()) && (bmInfoBo.getActivBizType() == null
            || !bmInfoBo.getActivBizType().equals(ActivBizType.SUB.getValue()))) {
            // 参数错误，参与人不允许为空
            return AjaxResult.success("参与人不允许为空");
        }
        return bmInfoService.addBmInfoWithJoiner(bmInfoBo);
    }

    @Override
    public AjaxResult<List<BmInfoExportVo>> getBmInfoListByConditionExport(@RequestBody BmInfoConditionBo condition) {
        List<BmInfoExportVo> bmInfoListByConditionExport = bmInfoService.getBmInfoListByConditionExport(condition);
        return AjaxResult.success(bmInfoListByConditionExport);
    }

    @Override
    public AjaxResult<List<BmInfoExportZdVo>> getBmInfoListByConditionExportZd(BmInfoConditionBo condition) {
        List<BmInfoExportZdVo> bmInfoListByConditionExport = bmInfoService.getBmInfoListByConditionExportZd(condition);
        if (CollectionUtils.isNotEmpty(bmInfoListByConditionExport)) {
            bmInfoListByConditionExport.stream().forEach(bmInfoExportZdVo -> {
                bmInfoExportZdVo.setCmTypeName(CmType.OFFICIAL.getName());
                if (StringUtils.isNotBlank(bmInfoExportZdVo.getCm())) {
                    bmInfoExportZdVo.setCmTypeName(CmType.AGENT.getName());
                }
            });
        }
        return AjaxResult.success(bmInfoListByConditionExport);
    }

    @Override
    public AjaxResult<String> generateNotRepeatSignInCode(BmInfoBo bmInfoBo) {
        String code = bmInfoService.generateNotRepeatSignInCode(bmInfoBo, false);
        return AjaxResult.success(code);
    }

    @Override
    public AjaxResult checkActivContact(@RequestBody BmInfoBo bmInfoBo) {
        return bmInfoService.checkActivContact(bmInfoBo);
    }

    @Override
    public AjaxResult bmInfoDetailByMobile(@RequestBody BmInfoConditionBo condition) {
        return AjaxResult.success(bmInfoService.bmInfoDetailByMobile(condition));
    }

    @Override
    public AjaxResult generateAccount(BmInfoBo bmInfoBo) {
        if (bmInfoBo.getActivId() == null) {
            return AjaxResult.fail("参数错误");
        }
        try {
            boolean result = bmInfoService.generateAccount(bmInfoBo);
            return result ? AjaxResult.success() : AjaxResult.fail();
        } catch (Exception e) {
            log.error("generateAccount error:", e);
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult bmInfoSignIn(@RequestBody BmInfoBo bmInfoBo) {
        return bmInfoService.bmInfoSignIn(bmInfoBo);
    }

    @Override
    public AjaxResult<Boolean> checkPhoneForLogin(@RequestParam("phone") String phone) {
        return bmInfoService.checkPhoneForLogin(phone);
    }

    @Override
    public AjaxResult myOrder(Long activId, String phone) {
        BmInfoConditionBo bmInfoConditionBo = new BmInfoConditionBo();
        bmInfoConditionBo.setActivId(activId);
        bmInfoConditionBo.setContactMobile(phone);
        bmInfoConditionBo.setPageNo(SystemConstants.NO_PAGE);
        bmInfoConditionBo.setOrderBy("update_time desc");
        List<BmInfoVo> bmInfoListByCondition = bmInfoService.getBmInfoListWithOrder(bmInfoConditionBo);
        List<Long> timeoutOrderIds = Lists.newArrayList();
        List<Long> timeoutInfoIds = Lists.newArrayList();
        for (BmInfoVo vo : bmInfoListByCondition) {
            // 本次调整的为征订活动订单，状态为待支付，且超过72小时未处理，修改状态为超时未支付
            boolean isTimeout =  ActivBizType.SUB.getValue().equals(vo.getActivBizType())
                    && OrderEnum.ORDER_STATE_PENDING_PAY.getCode().equals(vo.getOrderState())
                    && DateUtil.between(vo.getOrderCreateTime(), new Date(), DateUnit.HOUR) >= OrderEnum.TIMEOUT_HOURS.getCode();
            if (isTimeout) {
                vo.setOrderState(OrderEnum.ORDER_STATE_TIMEOUT.getCode());
                timeoutOrderIds.add(vo.getOrderId());
                timeoutInfoIds.add(vo.getInfoId());
            }
        }
        // 超时未支付订单修改状态
        if (CollectionUtils.isNotEmpty(timeoutOrderIds)) {
            activOrderService.changeActivOrderTimeout(timeoutOrderIds);
        }
        if (CollectionUtils.isNotEmpty(timeoutInfoIds)) {
            bmInfoService.updatePayTypeBatch(timeoutInfoIds, BmInfoPayType.CANCEL.getCode());
        }
        return AjaxResult.success(bmInfoListByCondition);
    }

    @Override
    public AjaxResult updateBmInfoByInfoId(@RequestBody BmInfoBo bmInfoBo) {
        return AjaxResult.success(bmInfoService.updateBmInfoByInfoId(bmInfoBo));
    }


//    @Override
//    public byte[] asposeWord(){
//        String content = "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\"><br />应急救护校本课程的评价方式，应该从课程目标出发，关注学生在学习过程中安全意识、应急能力的变化，从而全面而客观地对学生进行评价。同时评价还应遵循形成性评价和结果性评价相结合的原则，既要对课程实施结果进行评价，又有对课程实施过程进行评价，既有对学生学业成绩的评价，又有对学生日常课堂表现和学习收获的评价。学生的考核成绩是课程实施效果的直接反映，通过对学生的学业成果进行评价，可以客观了解课程建设与实施效果的一致性程度。学生的课堂表现则很好的记录了学生的课堂投入度与学习态度，反映出学生对课堂学习的重视程度。</span></span></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">结合以上原则及分析，确定本课程的评价体系，本课采取结果性和过程性相结合的方式进行评价，结果性评价包含学生的理论考试成绩与实操考核成绩，过程性评价则是学生的课堂综合表现，其评价主体不仅仅只包含老师，也包括学生自身与学习小组的组长，如图</span><span style=\"font-family: 宋体;\">2所示。学生的总成绩=理论知识成绩*20%+课堂表现成绩*30%+实操考核（成长档案）*50</span></span><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">。</span></span></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\">&nbsp;</span></p>\n" +
//            "<p style=\"text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><strong><span style=\"font-family: 宋体; font-weight: bold; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">（</span><span style=\"font-family: 宋体;\">1）理论知识考核</span></span></strong></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">针对学生设计了急救知识的前测卷和后测卷，为保证试卷对比的有效性，试卷涉及的知识面基本相同。理论测试成绩包含前测成绩和后测成绩，其中后测卷成绩占比</span><span style=\"font-family: 宋体;\">60%，后测卷较前测卷成绩的增值性评价占40%。</span></span></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">试卷维度包含对突发意外伤害事件进行判断并选择正确的急救措施、根据所给材料提出设计方案并对患者进行救护、理解急救措施在社会生活中的作用以及学习急救措施的意义等方面。通过理论考试的形式，检验学生是否达成课程目标，利用生命观念解释急救知识，设计探究方案，利用科学思维对突发意外情况进行分析等。</span></span></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><br /><img src=\"http://yunping.fhsljy.com/gateway/manage/common/attachment/preview/ef5859cf69c349b690be0d03514d2b4b\" /></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><img src=\"http://yunping.fhsljy.com/gateway/manage/common/attachment/preview/75929028d8c74a4fbf63fafa9eb785ab\" /></p>\n" +
//            "<p style=\"text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><strong><span style=\"font-family: 宋体; font-weight: bold; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">（</span><span style=\"font-family: 宋体;\">2）学生课堂表现的统计结果分析</span></span></strong></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">本课程对于学生课堂表现的评价主要从</span><span style=\"font-family: 宋体;\">&ldquo;学生自评、组长评价和教师评价&rdquo;三方面进行。学生自评成绩占学生课堂表现成绩的20%；组长评价成绩占学生课堂表现成绩的30%，这两部分在每节课结束后进行，由教师向学生发放课堂表现评价表，填写后收回，每月对结果统计一次；教师评价环节由授课教师完成，占学生课堂表现成绩的 50%，本部分为阶段性评价，每月进行一次。<br /></span></span></p>\n" +
//            "<p style=\"text-indent: 24pt; text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\"><img src=\"http://yunping.fhsljy.com/gateway/manage/common/attachment/preview/f9adc98fd2f84dccaf4266ccd8ee2563\" /></span></span></p>\n" +
//            "<p style=\"text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><strong><span style=\"font-family: 宋体; font-weight: bold; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">（</span><span style=\"font-family: 宋体;\">3）成长档案袋</span></span></strong></p>\n" +
//            "<p style=\"text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\">&nbsp;&nbsp;&nbsp;&nbsp;<span style=\"font-family: 宋体;\">在课程的开发和实施过程中，我们按照各环节操作流程，整理出实操考核标准，其中，准备过程占</span><span style=\"font-family: 宋体;\">10%，操作步骤占70%，口述和整体性评价各占10%。这既是参考依据，又是学生学习的成长记录册，里面介绍了应急救护各项操作的基本理论和实操流程，每节课后都设置了针对性探究问题，并附加了拓展资料和实际案例，还有学习和评价中用到的各类&ldquo;学习清单&rdquo;和&ldquo;评价量表&rdquo;，有助于学生更好地掌握每一项急救技能。这本小册子放在学生身边，构成了学生学习的成长档案，既可以用来自学理论、规范实践，还可用作课程记录和评价，帮助学生成长，并完善课程。</span></span></p>\n" +
//            "<p style=\"text-align: left; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 10.5pt;\"><span style=\"font-family: 宋体; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\"><img src=\"http://yunping.fhsljy.com/gateway/manage/common/attachment/preview/8aff0526cf114d3291acdb039daac97f\" /></span></span></p>\n" +
//            "<p style=\"text-indent: 36.15pt; margin: 0pt 0pt 0.0001pt; text-align: justify; font-family: Calibri; font-size: 10.5pt;\"><strong><span style=\"font-family: 宋体; font-weight: bold; font-size: 12.0000pt;\">&nbsp;</span></strong></p>";
//        String target = "<p style=\"margin: 0pt; text-indent: 21pt; text-align: left; font-family: Calibri; font-size: 12pt;\"><span style=\"font-family: 宋体; color: #000000; font-weight: normal; font-style: normal; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">高中应急救护校本课程是指：学校在充分分析与评估校内外环境和学生需求的基础上，从学校的办学特色和理念出发，结合社会上的急救资源，为本校学生开发的一门应对常见意外伤害事件的急救课程。学生通过本课程的学习，能够树立急救意识，明白急救措施在减轻意外伤害程度方面和挽救生命方面具有的重要作用，初步掌握应对具体伤害事件的急救处理办法。并树立急救意识和社会责任感。其课程目标定位为：</span></span></p>\n" +
//            "<p style=\"margin: 0pt; text-align: left; font-family: Calibri; font-size: 12pt;\"><span style=\"font-family: 宋体; color: #000000; font-weight: normal; font-style: normal; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">（一）通过应急救护课程学习，学生树立生命观念，能够运用结构与功能相适应的观念、稳态与平衡的观念等认识人体的结构和功能，认识到生命现象，解释生命现象，正确理解急救知识。认同急救措施的重要性，树立急救的意识，同时形成正确的科学观念，用于指导解决生活中的问题，养成开展生物学实践活动的意识和社会责任感，关注生物学知识在社会生活中的应用。</span></span></p>\n" +
//            "<p style=\"margin: 0pt; text-align: left; font-family: Calibri; font-size: 12pt;\"><span style=\"font-family: 宋体; color: #000000; font-weight: normal; font-style: normal; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">（二）通过应急救护课程学习，能够运用已有的生物学知识和逻辑思维对突发意外伤害事件进行判断、分析和论证，初步掌握应对常见的突发意外灾害事故的处理方法。学生学会处理现场外伤急救、日常危重急症、突发意外灾害的一般方法，在具体的突发事件场景中，完成急救措施的操作，能够有效开展现场外伤自救、互救，从而为患者赢得宝贵的抢救时机，</span></span></p>\n" +
//            "<p style=\"margin: 0pt; text-align: justify; font-family: Calibri; font-size: 12pt;\"><span style=\"font-family: 宋体; color: #000000; font-weight: normal; font-style: normal; font-size: 12.0000pt;\"><span style=\"font-family: 宋体;\">（三）通过应急救护课程学习，学生形成科学思维习惯，掌握科学探究的一般方法，经历提出问题、做出假设、实验验证和得出结论的一般过程，善于从实践层面探究日常危重急症中的现实问题。</span></span></p>";
//        Map<String,String> map = new HashMap<>();
//        String templatePath = getClass().getClassLoader().getResource("testTemplate.docx").getPath();
//        map.put("{content}",content);
//        map.put("{target}",target);
//        return asposeBizService.asposeWord(map,templatePath);
//    }



}
