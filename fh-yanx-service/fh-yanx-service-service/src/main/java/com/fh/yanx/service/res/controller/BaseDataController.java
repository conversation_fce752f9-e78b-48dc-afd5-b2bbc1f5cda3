package com.fh.yanx.service.res.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.enums.ExpertRoleEnum;
import com.fh.yanx.service.enums.ReceptionRoleEnum;
import com.fh.yanx.service.enums.RoleIdentifierType;
import com.fh.yanx.service.org.entity.bo.OrganizationBoExt;
import com.fh.yanx.service.org.entity.bo.OrganizationExtConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationVoExt;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.google.common.collect.Lists;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.core.entity.AjaxResult;
import com.light.user.account.entity.bo.AccountBo;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.admin.entity.bo.AdminBo;
import com.light.user.admin.entity.bo.AdminConditionBo;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.organization.entity.bo.*;
import com.light.user.organization.entity.vo.OrganizationSettingVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.role.entity.vo.RoleVo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.bo.UserTransferBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/12/12 19:28
 */
@RestController
@Validated
public class BaseDataController implements BaseDataApi {
    @Autowired
    private BaseDataService baseDataService;
    @Value("${ineoschool.organization.id:1}")
    private Long ineoschoolOrganizationId;

    // 学校教师角色id
    private static final Long SCHOOL_TEACHER = 8L;

    @Override
    public AjaxResult<TeacherVo> getTeacherVoByUserOid(String userOid) {
        return AjaxResult.success(baseDataService.getTeacherVoByUserOid(userOid));
    }

    @Override
    public AjaxResult<StudentVo> getStudentVoByUserOid(String userOid) {
        return AjaxResult.success(baseDataService.getStudentVoByUserOid(userOid));
    }

    @Override
    public AjaxResult getSubjectList() {
        return AjaxResult.success(baseDataService.getSubjectList());
    }

    @Override
    public AjaxResult listValueByTypes(List<String> dictTypes) {
        List<DictionaryDataVo> dictionaryDataVos = baseDataService.listValueByTypes(dictTypes);
        Map<String, List<DictionaryDataVo>> resMap =
            dictionaryDataVos.stream().collect(Collectors.groupingBy(DictionaryDataVo::getDictType));
        return AjaxResult.success(resMap);
    }

    @Override
    public AjaxResult<Long> addOrganization(OrganizationBo organizationBo) {
        Long orgId = baseDataService.addOrganization(organizationBo);
        if (orgId != null) {
            return AjaxResult.success(orgId);
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult updateOrganization(OrganizationBoExt organizationBo) {
        return AjaxResult.success(baseDataService.updateOrganization(organizationBo));
    }

    @Override
    public AjaxResult<OrganizationVoExt> getOrganizationDetail(Long organizationId) {
        return AjaxResult.success(baseDataService.getOrganizationDetail(organizationId));
    }

    @Override
    public AjaxResult getOrganizationListByCondition(OrganizationExtConditionBo conditionBo) {
        return AjaxResult.success(baseDataService.getOrganizationListByCondition(conditionBo));
    }

    @Override
    public AjaxResult<OrganizationVo> getOrganizationByCondition(OrganizationConditionBo conditionBo) {
        return baseDataService.getOrganizationByCondition(conditionBo);
    }

    @Override
    public AjaxResult addOrUpdateOrganizationSetting(OrganizationSettingBo organizationSettingBo) {
        return AjaxResult.success(baseDataService.addOrUpdateOrganizationSetting(organizationSettingBo));
    }

    @Override
    public AjaxResult<OrganizationSettingVo> getOrganizationSetting(OrganizationSettingConditionBo conditionBo) {
        return AjaxResult.success(baseDataService.getOrganizationSetting(conditionBo));
    }

    @Override
    public AjaxResult changePassword(String oldPassword, String password) {
        return baseDataService.changePassword(oldPassword, password);
    }

    @Override
    public AjaxResult updateAccount(AccountBo accountBo) {
        return baseDataService.updateAccount(accountBo);
    }

    @Override
    public AjaxResult updateUser(UserBo userBo) {
        if (baseDataService.updateUser(userBo)) {
            return AjaxResult.success("更新成功");
        }
        return AjaxResult.fail("更新失败");
    }

    @Override
    public AjaxResult resetPassword(Long accountId) {
        if (baseDataService.resetPassword(accountId)) {
            return AjaxResult.success("重置密码成功");
        }
        return AjaxResult.fail("重置密码失败");
    }

    @Override
    public AjaxResult addTeacher(TeacherBo teacherBo) {
        // 教师角色
        if (CollectionUtil.isEmpty(teacherBo.getUser().getRoleIds())) {
            teacherBo.getUser().setRoleIds(Lists.newArrayList(SCHOOL_TEACHER));
        } else {
            teacherBo.getUser().getRoleIds().add(SCHOOL_TEACHER);
        }
        return baseDataService.addTeacher(teacherBo);
    }

    @Override
    public AjaxResult updateTeacher(TeacherBo teacherBo) {
        return baseDataService.updateTeacher(teacherBo);
    }

    @Override
    public AjaxResult getTeacherDetail(String userOid) {
        return AjaxResult.success(baseDataService.getTeacherDetail(userOid));
    }

    @Override
    public AjaxResult delTeacher(Long teacherId) {
        if (baseDataService.delTeacher(teacherId)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult getTeacherList(TeacherConditionBo conditionBo) {
        return AjaxResult.success(baseDataService.getTeacherList(conditionBo));
    }

    @Override
    public AjaxResult resetPasswordByUserOid(String userOid) {
        if (baseDataService.resetPasswordByUserOid(userOid)) {
            return AjaxResult.success("重置密码成功");
        }
        return AjaxResult.fail("重置密码失败");
    }

    @Override
    public AjaxResult<LoginAccountVo> getCurrentUser() {
        return AjaxResult.success(baseDataService.getCurrentUser());
    }

    @Override
    public AjaxResult removeOutTeacher(UserTransferBo userTransferBo) {
        return baseDataService.addUserTransfer(userTransferBo);
    }

    @Override
    public AjaxResult<List<AdminVo>> getAdminListByCondition(AdminConditionBo conditionBo) {
        return AjaxResult.success(baseDataService.getAdminListByCondition(conditionBo));
    }

    @Override
    public AjaxResult getAdminPageListByCondition(AdminConditionBo conditionBo) {
        return baseDataService.getAdminPageListByCondition(conditionBo);
    }

    @Override
    public AjaxResult addAdmin(AdminBo adminBo) {
        return baseDataService.addAdmin(adminBo);
    }

    @Override
    public AjaxResult updateAdmin(AdminBo adminBo) {
        return baseDataService.updateAdmin(adminBo);
    }

    @Override
    public AjaxResult delAdmin(Long adminId) {
        return baseDataService.delAdmin(adminId);
    }

    @Override
    public AjaxResult<AdminVo> getAdminDetail(Long adminId) {
        return baseDataService.getAdminDetail(adminId);
    }

    @Override
    public AjaxResult enableAdmin(Long adminId, Integer status) {
        return baseDataService.enableAdmin(adminId, status);
    }

    @Override
    public AjaxResult resetAdminPassword(Long adminId, String initPwd) {
        return baseDataService.resetAdminPassword(adminId, initPwd);
    }

    @Override
    public AjaxResult getAllYanxRoles() {
        List<RoleVo> roleVos = Arrays.stream(RoleIdentifierType.values()).filter(
            roleIdentifierType -> roleIdentifierType.getRoleId() != RoleIdentifierType.BUREAU_SUPER_ADMIN.getRoleId()
                && roleIdentifierType.getRoleId() != RoleIdentifierType.SCHOOL_SUPER_ADMIN.getRoleId()
                && roleIdentifierType.getRoleId() != RoleIdentifierType.OPERATION_ADMIN.getRoleId()
                && roleIdentifierType.getRoleId() != RoleIdentifierType.OPERATION_CRM.getRoleId())
            .map(roleIdentifierType -> {
                RoleVo roleVo = new RoleVo();
                roleVo.setId(roleIdentifierType.getRoleId());
                roleVo.setCode(roleIdentifierType.getCode());
                roleVo.setName(roleIdentifierType.getName());
                return roleVo;
            }).collect(Collectors.toList());

        return AjaxResult.success(roleVos);
    }

    @Override
    public AjaxResult receptionRoles() {
        List<RoleVo> roleVos = Arrays.stream(ReceptionRoleEnum.values())
                .filter(r -> !r.getRoleId().equals(ReceptionRoleEnum.SCHOOL_TEACHER.getRoleId()))
                .map(r -> {
                    RoleVo roleVo = new RoleVo();
                    roleVo.setId(r.getRoleId());
                    roleVo.setCode(r.getCode());
                    roleVo.setName(r.getName());
                    return roleVo;
                }).collect(Collectors.toList());
        return AjaxResult.success(roleVos);
    }

    @Override
    public AjaxResult expertRoles() {
        List<RoleVo> roleVos = Arrays.stream(ExpertRoleEnum.values()).filter(role ->
                        role.getRoleId().equals(ExpertRoleEnum.AUDIT_EXPERT.getRoleId())
                                || role.getRoleId().equals(ExpertRoleEnum.GUIDE_EXPERT.getRoleId()))
                .map(r -> {
                    RoleVo roleVo = new RoleVo();
                    roleVo.setId(r.getRoleId());
                    roleVo.setCode(r.getCode());
                    roleVo.setName(r.getName());
                    return roleVo;
                }).collect(Collectors.toList());

        return AjaxResult.success(roleVos);
    }

    @Override
    public AjaxResult expertAssistantRoles() {
        List<RoleVo> roleVos = Arrays.stream(ExpertRoleEnum.values()).filter(role ->
                        role.getRoleId().equals(ExpertRoleEnum.EXPERT_ASSISTANT.getRoleId()))
                .map(r -> {
                    RoleVo roleVo = new RoleVo();
                    roleVo.setId(r.getRoleId());
                    roleVo.setCode(r.getCode());
                    roleVo.setName(r.getName());
                    return roleVo;
                }).collect(Collectors.toList());

        return AjaxResult.success(roleVos);
    }

    @Override
    public AjaxResult addUserAndGeneratorAccount(UserBo userBo) {
        // 专家助理放在新时代组织下
        if (CollectionUtil.isNotEmpty(userBo.getRoleIds())
                && userBo.getRoleIds().contains(ExpertRoleEnum.EXPERT_ASSISTANT.getRoleId())) {
            userBo.setOrganizationId(ineoschoolOrganizationId);
        }
        return baseDataService.addUserAndGeneratorAccount(userBo);
    }

    @Override
    public AjaxResult updateExpertUser(UserBo userBo) {
        return baseDataService.updateExpertUser(userBo);
    }

    @Override
    public AjaxResult userList(UserConditionBoExt conditionBo) {
        conditionBo.setOrderBy("create_time desc");
        return baseDataService.getUserList(conditionBo);
    }

    @Override
    public AjaxResult getUserDetail(String userOid) {
        return baseDataService.getUserDetail(userOid);
    }

    @Override
    public AjaxResult expertAssistantList(UserConditionBoExt conditionBo) {
        conditionBo.setOrganizationId(ineoschoolOrganizationId);
        conditionBo.setRoleIds(Stream.of(ExpertRoleEnum.EXPERT_ASSISTANT.getRoleId()).collect(Collectors.toList()));
        conditionBo.setOrderBy("create_time asc");
        return baseDataService.getUserList(conditionBo);
    }

    @Override
    public AjaxResult delUser(String userOid) {
        return baseDataService.delUser(userOid);
    }

    @Override
    public AjaxResult delOrganization(Long organizationId) {
        return baseDataService.delOrganization(organizationId);
    }

    @Override
    public AjaxResult changePhone(UserBo userBo) {
        userBo.setOid(baseDataService.getCurrentUserOid());
        return baseDataService.updatePhoneByUserOid(userBo);
    }

    @Override
    public AjaxResult addBatchTeacher(List<TeacherBo> teacherBos) {
        return baseDataService.addBatchTeacher(teacherBos);
    }

    @Override
    public AjaxResult addBatchUser(List<UserBo> userBos) {
        return baseDataService.addUserAndGeneratorAccountBatch(userBos);
    }

    @Override
    public AjaxResult expertAndCaseOwnerRoles() {
        List<RoleVo> expertRoles = Arrays.stream(ExpertRoleEnum.values())
                .filter(r -> !r.getRoleId().equals(ExpertRoleEnum.EXPERT_ASSISTANT.getRoleId()))
                .map(r -> {
                    RoleVo roleVo = new RoleVo();
                    roleVo.setId(r.getRoleId());
                    roleVo.setCode(r.getCode());
                    roleVo.setName(r.getName());
                    return roleVo;
                }).collect(Collectors.toList());
//        List<RoleVo> caseOwnerRoles = Arrays.stream(ReceptionRoleEnum.values())
//                .filter(r -> !r.getRoleId().equals(ReceptionRoleEnum.SCHOOL_TEACHER.getRoleId()))
//                .map(r -> {
//                    RoleVo roleVo = new RoleVo();
//                    roleVo.setId(r.getRoleId());
//                    roleVo.setCode(r.getCode());
//                    roleVo.setName(r.getName());
//                    return roleVo;
//                }).collect(Collectors.toList());
        List<RoleVo> expertAndCaseOwner = Lists.newArrayList();
        expertAndCaseOwner.addAll(expertRoles);
//        expertAndCaseOwner.addAll(caseOwnerRoles);
        return AjaxResult.success(expertAndCaseOwner);
    }
}
