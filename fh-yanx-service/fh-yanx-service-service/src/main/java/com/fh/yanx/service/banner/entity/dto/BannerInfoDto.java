package com.fh.yanx.service.banner.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代文化校园banner信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("banner_info")
public class BannerInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "banner_info_id", type = IdType.AUTO)
	private Long bannerInfoId;

	/**
	 * banner位置 1-移动端活动管理
	 */
	@TableField("banner_show_type")
	private Integer bannerShowType;

	/**
	 * banner图片文件oid
	 */
	@TableField("banner_image_id")
	private String bannerImageId;

	/**
	 * banner图片文件url
	 */
	@TableField("banner_image_url")
	private String bannerImageUrl;

	/**
	 * banner跳转url
	 */
	@TableField("jump_url")
	private String jumpUrl;

	/**
	 * 开始时间
	 */
	@TableField("start_time")
	private Date startTime;

	/**
	 * 截止时间类型 1-永久 2-选定日期
	 */
	@TableField("end_type")
	private Integer endType;

	/**
	 * 截止时间
	 */
	@TableField("end_time")
	private Date endTime;

	/**
	 * 状态 1-待发布 2-已发布 3-已下架
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
