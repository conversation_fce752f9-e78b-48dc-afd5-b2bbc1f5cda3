package com.fh.yanx.service.course.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo;
import com.fh.yanx.service.course.service.CourseModuleAttachmentApiService;
import com.fh.yanx.service.course.service.ICourseModuleAttachmentService;
import com.light.core.constants.SystemConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseModuleDto;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;
import com.fh.yanx.service.course.mapper.CourseModuleMapper;
import com.fh.yanx.service.course.service.ICourseModuleService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 课程模块接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Service
public class CourseModuleServiceImpl extends ServiceImpl<CourseModuleMapper, CourseModuleDto>
    implements ICourseModuleService {

    @Resource
    private CourseModuleMapper courseModuleMapper;
    @Resource
    private ICourseModuleAttachmentService courseModuleAttachmentService;

    @Override
    public List<CourseModuleVo> getCourseModuleListByCondition(CourseModuleConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return courseModuleMapper.getCourseModuleListByCondition(condition);
    }

    @Override
    public AjaxResult addCourseModule(CourseModuleBo courseModuleBo) {
        CourseModuleDto courseModule = new CourseModuleDto();
        BeanUtils.copyProperties(courseModuleBo, courseModule);
        courseModule.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(courseModule)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCourseModule(CourseModuleBo courseModuleBo) {
        CourseModuleDto courseModule = new CourseModuleDto();
        BeanUtils.copyProperties(courseModuleBo, courseModule);
        if (updateById(courseModule)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public CourseModuleVo getCourseModuleByCondition(CourseModuleConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        CourseModuleVo vo = courseModuleMapper.getCourseModuleByCondition(condition);
        return vo;
    }

    @Override
    public List<CourseModuleVo> getCourseModuleListByConditionWithAttachment(CourseModuleConditionBo condition,
        boolean isQueryAttachment) {
        // 查询课程模块信息
        condition.setPageNo(SystemConstants.NO_PAGE);
        condition.setOrderBy("course_module_index");
        List<CourseModuleVo> courseModuleVos = getCourseModuleListByCondition(condition);
        if (!isQueryAttachment || CollectionUtils.isEmpty(courseModuleVos)) {
            return courseModuleVos;
        }
        List<Long> courseModuleIds =
            courseModuleVos.stream().map(CourseModuleVo::getCourseModuleId).distinct().collect(Collectors.toList());

        // 查询模块附件并设置到返回的list中
        CourseModuleAttachmentConditionBo courseModuleAttachmentConditionBo = new CourseModuleAttachmentConditionBo();
        courseModuleAttachmentConditionBo.setCourseModuleIds(courseModuleIds);
        List<CourseModuleAttachmentVo> courseModuleAttachmentListByCondition =
            courseModuleAttachmentService.getCourseModuleAttachmentListByCondition(courseModuleAttachmentConditionBo);
        if (CollectionUtils.isEmpty(courseModuleAttachmentListByCondition)) {
            return courseModuleVos;
        }
        Map<Long, List<CourseModuleAttachmentVo>> courseModuleAttachmentMap = courseModuleAttachmentListByCondition
            .stream().collect(Collectors.groupingBy(CourseModuleAttachmentVo::getCourseModuleId));
        courseModuleVos.forEach(courseModuleVo -> courseModuleVo
            .setCourseModuleAttachmentVos(courseModuleAttachmentMap.get(courseModuleVo.getCourseModuleId())));
        return courseModuleVos;
    }

    @Override
    public void saveCourseModule(Long casesId, Integer courseModuleType, List<CourseModuleBo> courseModuleBoList) {
        // 删除
        LambdaUpdateWrapper<CourseModuleDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CourseModuleDto::getCasesId, casesId);
        if(courseModuleType != null){
            updateWrapper.eq(CourseModuleDto::getCourseModuleType, courseModuleType);
        }
        updateWrapper.set(CourseModuleDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        update(updateWrapper);
        if (CollectionUtils.isEmpty(courseModuleBoList)) {
            return;
        }

        // 新增或更新
        List<CourseModuleDto> courseModuleDtos = courseModuleBoList.stream().map(courseModuleBo -> {
            CourseModuleDto courseModuleDto = new CourseModuleDto();
            BeanUtils.copyProperties(courseModuleBo, courseModuleDto);
            courseModuleDto.setCasesId(casesId);
            return courseModuleDto;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(courseModuleDtos)) {
            saveOrUpdateBatch(courseModuleDtos);
        }

        // 设置最新的id
        for (int i = 0; CollectionUtils.isNotEmpty(courseModuleBoList) && i < courseModuleBoList.size(); i++) {
            CourseModuleBo courseModuleBo = courseModuleBoList.get(i);
            CourseModuleDto courseModuleDto = courseModuleDtos.get(i);
            if (courseModuleDto == null) {
                continue;
            }
            courseModuleBo.setCourseModuleId(courseModuleDto.getCourseModuleId());
        }
    }
}