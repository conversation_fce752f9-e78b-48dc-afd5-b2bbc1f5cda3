package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResStationContentDetailDto;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailConditionBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentDetailVo;

/**
 * 工作站具体内容详情Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationContentDetailMapper extends BaseMapper<ResStationContentDetailDto> {

	List<ResStationContentDetailVo> getResStationContentDetailListByCondition(ResStationContentDetailConditionBo condition);

}
