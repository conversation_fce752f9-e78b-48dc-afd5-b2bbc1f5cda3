package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicProcessRecordDto;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicProcessRecordVo;

/**
 * 课题流程记录Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicProcessRecordMapper extends BaseMapper<ResTopicProcessRecordDto> {

	List<ResTopicProcessRecordVo> getResTopicProcessRecordListByCondition(ResTopicProcessRecordConditionBo condition);

}
