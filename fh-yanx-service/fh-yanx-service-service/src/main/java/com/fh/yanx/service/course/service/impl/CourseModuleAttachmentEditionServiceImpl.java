package com.fh.yanx.service.course.service.impl;

import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentEditionVo;
import com.fh.yanx.service.course.service.ICourseModuleAttachmentEditionService;
import com.fh.yanx.service.course.mapper.CourseModuleAttachmentEditionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 课程资源或成果样例附件表-模块附件版本表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:25
 */
@Service
public class CourseModuleAttachmentEditionServiceImpl extends ServiceImpl<CourseModuleAttachmentEditionMapper, CourseModuleAttachmentEditionDto> implements ICourseModuleAttachmentEditionService {

	@Resource
	private CourseModuleAttachmentEditionMapper courseModuleAttachmentEditionMapper;
	
    @Override
	public List<CourseModuleAttachmentEditionVo> getCourseModuleAttachmentEditionListByCondition(CourseModuleAttachmentEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseModuleAttachmentEditionMapper.getCourseModuleAttachmentEditionListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseModuleAttachmentEdition(CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo) {
		CourseModuleAttachmentEditionDto courseModuleAttachmentEdition = new CourseModuleAttachmentEditionDto();
		BeanUtils.copyProperties(courseModuleAttachmentEditionBo, courseModuleAttachmentEdition);
		courseModuleAttachmentEdition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseModuleAttachmentEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseModuleAttachmentEdition(CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo) {
		CourseModuleAttachmentEditionDto courseModuleAttachmentEdition = new CourseModuleAttachmentEditionDto();
		BeanUtils.copyProperties(courseModuleAttachmentEditionBo, courseModuleAttachmentEdition);
		if(updateById(courseModuleAttachmentEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CourseModuleAttachmentEditionVo getCourseModuleAttachmentEditionByCondition(CourseModuleAttachmentEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return courseModuleAttachmentEditionMapper.getCourseModuleAttachmentEditionByCondition(condition);
	}

}