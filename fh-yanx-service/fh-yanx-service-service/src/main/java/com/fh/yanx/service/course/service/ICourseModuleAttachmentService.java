package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程资源或成果样例附件表-模块附件接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface ICourseModuleAttachmentService extends IService<CourseModuleAttachmentDto> {

    List<CourseModuleAttachmentVo>
        getCourseModuleAttachmentListByCondition(CourseModuleAttachmentConditionBo condition);

    AjaxResult addCourseModuleAttachment(CourseModuleAttachmentBo courseModuleAttachmentBo);

    AjaxResult updateCourseModuleAttachment(CourseModuleAttachmentBo courseModuleAttachmentBo);

    CourseModuleAttachmentVo getCourseModuleAttachmentByCondition(CourseModuleAttachmentConditionBo condition);

    /**
     * 保存模块附件信息
     *
     * @param courseModuleAttachmentBoList the course module attachment bo list
     * <AUTHOR>
     * @date 2023 -08-18 16:58:29
     */
    void saveCourseModuleAttachment(List<Long> courseModuleIds,
        List<CourseModuleAttachmentBo> courseModuleAttachmentBoList);
}
