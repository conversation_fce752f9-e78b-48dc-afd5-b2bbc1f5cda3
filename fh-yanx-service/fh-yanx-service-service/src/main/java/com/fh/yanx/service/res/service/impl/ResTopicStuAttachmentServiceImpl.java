package com.fh.yanx.service.res.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.enums.ResTopicProcessEnum;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CreateTodoEvent;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.entity.dto.ResTopicRepAttachmentDto;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;
import com.fh.yanx.service.res.service.IResTopicProcessRecordService;
import com.fh.yanx.service.res.service.IResTopicService;
import com.fh.yanx.service.res.service.IResTopicTeacherService;
import com.google.common.collect.Lists;
import com.light.base.attachment.entity.bo.MultipartFormData;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicStuAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStuAttachmentVo;
import com.fh.yanx.service.res.service.IResTopicStuAttachmentService;
import com.fh.yanx.service.res.mapper.ResTopicStuAttachmentMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

/**
 * 课题研究附件表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Service
public class ResTopicStuAttachmentServiceImpl extends ServiceImpl<ResTopicStuAttachmentMapper, ResTopicStuAttachmentDto>
    implements IResTopicStuAttachmentService {

    @Resource
    private ResTopicStuAttachmentMapper resTopicStuAttachmentMapper;
    @Resource
    private IResTopicService resTopicService;
    @Resource
    private IResTopicTeacherService resTopicTeacherService;

    @Override
    public List<ResTopicStuAttachmentVo>
        getResTopicStuAttachmentListByCondition(ResTopicStuAttachmentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicStuAttachmentMapper.getResTopicStuAttachmentListByCondition(condition);
    }

    @Override
    public AjaxResult addResTopicStuAttachment(@RequestBody ResTopicStuAttachmentBo resTopicStuAttachmentBo) {
        // 保存研究材料
        ResTopicStuAttachmentDto resTopicStuAttachment = new ResTopicStuAttachmentDto();
        BeanUtils.copyProperties(resTopicStuAttachmentBo, resTopicStuAttachment);
        resTopicStuAttachment.setIsDelete(StatusEnum.NOTDELETE.getCode());
        save(resTopicStuAttachment);

        ResTopicDto byId = resTopicService.getById(resTopicStuAttachmentBo.getTopicId());
        if (null == byId) {
            return AjaxResult.fail("课题不存在");
        }
        // 修改课题状态
        ResTopicBo resTopicBo = new ResTopicBo();
        resTopicBo.setTopicId(byId.getTopicId());
        resTopicBo.setTopicProcess(ResTopicProcessEnum.STU_ATTACHMENT.getCode());
        resTopicService.updateResTopicStatus(resTopicBo);
        // 成代办事件及流转记录
        List<String> oids = resTopicTeacherService.getResTopicTeacherOidListByTopicId(byId.getTopicId());
        resTopicService.publishEvent(byId.getTopicId(), ResTopicProcessEnum.STU_ATTACHMENT.getCode(),
            byId.getTopicName(), oids, resTopicStuAttachmentBo.getCurrentUserOid(),
            resTopicStuAttachmentBo.getCurrentUserOid());
        return AjaxResult.success("保存成功");

    }

    @Override
    public AjaxResult updateResTopicStuAttachment(ResTopicStuAttachmentBo resTopicStuAttachmentBo) {
        LambdaUpdateWrapper<ResTopicStuAttachmentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResTopicStuAttachmentDto::getStuFileOid, resTopicStuAttachmentBo.getStuFileOid());

        ResTopicStuAttachmentDto resTopicStuAttachment = new ResTopicStuAttachmentDto();
        BeanUtils.copyProperties(resTopicStuAttachmentBo, resTopicStuAttachment);
        if (update(resTopicStuAttachment,updateWrapper)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ResTopicStuAttachmentVo getDetail(Long id) {
        ResTopicStuAttachmentConditionBo condition = new ResTopicStuAttachmentConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicStuAttachmentVo> list =
            resTopicStuAttachmentMapper.getResTopicStuAttachmentListByCondition(condition);
        ResTopicStuAttachmentVo vo = new ResTopicStuAttachmentVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

}