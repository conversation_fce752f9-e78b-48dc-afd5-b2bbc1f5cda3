package com.fh.yanx.service.org.controller;

import com.fh.yanx.service.org.api.OrganizationApplyApi;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyDto;
import com.fh.yanx.service.org.service.IOrganizationApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.org.entity.bo.OrganizationApplyConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyBo;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;

/**
 * 组织申请表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
@RestController
@Validated
public class OrganizationApplyController implements OrganizationApplyApi {

    @Autowired
    private IOrganizationApplyService organizationApplyService;

    /**
     * 查询组织申请表分页列表
     *
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<PageInfo<OrganizationApplyVo>> getOrganizationApplyPageListByCondition(@RequestBody OrganizationApplyConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<OrganizationApplyVo> pageInfo = new PageInfo<>(organizationApplyService.getOrganizationApplyListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询组织申请表列表
     *
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<List<OrganizationApplyVo>> getOrganizationApplyListByCondition(@RequestBody OrganizationApplyConditionBo condition) {
        List<OrganizationApplyVo> list = organizationApplyService.getOrganizationApplyListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增组织申请表
     *
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult addOrganizationApply(@Validated @RequestBody OrganizationApplyBo organizationApplyBo) {
        return organizationApplyService.addOrganizationApply(organizationApplyBo);
    }

    /**
     * 修改组织申请表
     *
     * @param organizationApplyBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult updateOrganizationApply(@Validated @RequestBody OrganizationApplyBo organizationApplyBo) {
        if (null == organizationApplyBo.getOrganizationApplyId()) {
            return AjaxResult.fail("组织申请表id不能为空");
        }
        return organizationApplyService.updateOrganizationApply(organizationApplyBo);
    }

    /**
     * 查询组织申请表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<OrganizationApplyVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("组织申请表id不能为空");
        }
        OrganizationApplyConditionBo condition = new OrganizationApplyConditionBo();
        condition.setOrganizationApplyId(id);
        OrganizationApplyVo vo = organizationApplyService.getOrganizationApplyByCondition(condition);
        return AjaxResult.success(vo);
    }


    /**
     * 删除组织申请表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        OrganizationApplyDto organizationApplyDto = new OrganizationApplyDto();
        organizationApplyDto.setOrganizationApplyId(id);
        organizationApplyDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (organizationApplyService.updateById(organizationApplyDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
