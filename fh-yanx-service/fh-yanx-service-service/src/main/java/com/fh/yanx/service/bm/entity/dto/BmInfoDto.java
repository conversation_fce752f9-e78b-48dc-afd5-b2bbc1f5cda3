package com.fh.yanx.service.bm.entity.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新时代文化校园报名活动申请表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bm_info")
public class BmInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "info_id", type = IdType.AUTO)
    private Long infoId;

    /**
     * 所属省份ID
     */
    @TableField("province_id")
    private Long provinceId;

    /**
     * 所属省份名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 所属市ID
     */
    @TableField("city_id")
    private Long cityId;

    /**
     * 所属市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 所属县区ID
     */
    @TableField("area_id")
    private Long areaId;

    /**
     * 所属县区名称
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 单位名称
     */
    @TableField("depart_name")
    private String departName;

    /**
     * 学段信息
     */
    @TableField("period")
    private String period;

    /**
     * 参与类型：1个人，2团体
     */
    @TableField("join_type")
    private Integer joinType;

    /**
     * 联系人
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人手机号码
     */
    @TableField("contact_mobile")
    private String contactMobile;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 确认状态：1待确认，2沟通中，3已确认，4已拒绝
     */
    @TableField("submit_type")
    private Integer submitType;

    /**
     * 缴费状态：1未缴费，1已缴费
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 发票状态：1未开发票，2已开发票
     */
    @TableField("invoice_type")
    private Integer invoiceType;

    /**
     * 发票文件oid
     */
    @TableField("invoice_id")
    private String invoiceId;

    /**
     * 发票文件地址
     */
    @TableField("invoice_url")
    private String invoiceUrl;

    /**
     * 发票名称不带后缀
     */
    @TableField("invoice_name")
    private String invoiceName;

    /**
     * 发票名称带后缀
     */
    @TableField("invoice_name_ori")
    private String invoiceNameOri;

    /**
     * 支付凭证文件oid
     */
    @TableField("pay_id")
    private String payId;

    /**
     * 支付凭证文件地址
     */
    @TableField("pay_url")
    private String payUrl;

    /**
     * 支付凭证名称不带后缀
     */
    @TableField("pay_name")
    private String payName;

    /**
     * 支付凭证名称带后缀
     */
    @TableField("pay_name_ori")
    private String payNameOri;

    /**
     * 活动id
     */
    @TableField("activ_id")
    private Long activId;


    /**
     * 签到状态 1-未签到 2-已签到
     */
    @TableField("sign_in_state")
    private Integer signInState;

    /**
     * 签到码
     */
    @TableField("sign_in_code")
    private String signInCode;

    /**
     * 开票类型 1-个人 2-单位
     */
    @TableField("invoicing_type")
    private Integer invoicingType;

    /**
     * 发票抬头
     */
    @TableField("invoice_header")
    private String invoiceHeader;

    /**
     * 发票备注
     *
     */
    @TableField("invoice_remark")
    private String invoiceRemark;

    /**
     * 税号
     */
    @TableField("duty_code")
    private String dutyCode;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 付款凭证上传时间
     */
    @TableField("pay_time")
    private Date payTime;

    /**
     * 付款凭证上传人oid
     */
    @TableField("pay_user_oid")
    private String payUserOid;

    /**
     * 付款凭证上传人
     */
    @TableField("pay_user_name")
    private String payUserName;

    /**
     * 支付方式类型
     */
    @TableField("pay_way_type")
    private Integer payWayType;

    /**
     * 转账记录
     */
    @TableField("pay_record")
    private String payRecord;

    /**
     * 征订数量，用于记录显示
     */
    @TableField("sub_number")
    private Long subNumber;
    /**
     * 订单金额，用于记录显示，具体支付金额见订单表
     * 征订总价，改订单金额需同步修改
     */
    @TableField("sub_book_total_price")
    private BigDecimal subBookTotalPrice;
    /**
     * 商品单价，下单时候的商品价格
     */
    @TableField("sub_book_price")
    private BigDecimal subBookPrice;
    /**
     * 收件人-联系人
     */
    @TableField("rec_name")
    private String recName;
    /**
     * 收件人-联系人手机号码
     */
    @TableField("rec_mobile")
    private String recMobile;
    /**
     * 收件人-所属省份ID
     */
    @TableField("rec_province_id")
    private Long recProvinceId;
    /**
     * 收件人-所属省份名称
     */
    @TableField("rec_province_name")
    private String recProvinceName;
    /**
     * 收件人-所属市ID
     */
    @TableField("rec_city_id")
    private Long recCityId;
    /**
     * 收件人-所属市名称
     */
    @TableField("rec_city_name")
    private String recCityName;
    /**
     * 收件人-所属县区ID
     */
    @TableField("rec_area_id")
    private Long recAreaId;
    /**
     * 收件人-所属县区名称
     */
    @TableField("rec_area_name")
    private String recAreaName;
    /**
     * 收件人-地址
     */
    @TableField("rec_address")
    private String recAddress;

    /**
     * 是否选择材料默认1不选择，1不选择材料，2选择材料
     */
    @TableField("select_material_type")
    private Integer selectMaterialType;

    /**
     * 征订书籍名称
     */
    @TableField("sub_book_name")
    private String subBookName;

    /**
     * 活动的门票价格，即活动价格
     */
    @TableField("activ_ticket_price")
    private BigDecimal activTicketPrice;

    /**
     * 活动的总价格（活动+材料）
     */
    @TableField("activ_total_price")
    private BigDecimal activTotalPrice;

    /**
     * 代理厂商
     */
    @TableField("cm")
    private String cm;

    /**
     * 发货状态 1-未发货 2-已发货
     */
    @TableField("deliver_state")
    private Integer deliverState;

    /**
     * 物流单号
     */
    @TableField("logistics_code")
    private String logisticsCode;

    /**
     * 物流公司
     */
    @TableField("logistics_org")
    private String logisticsOrg;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款方式类型
     */
    @TableField("refund_way_type")
    private Integer refundWayType;

    /**
     * 退款时间
     */
    @TableField("refund_time")
    private Date refundTime;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 退款凭证文件oid
     */
    @TableField("refund_id")
    private String refundId;

    /**
     * 退款凭证文件地址
     */
    @TableField("refund_url")
    private String refundUrl;

    /**
     * 退款凭证名称不带后缀
     */
    @TableField("refund_name")
    private String refundName;

    /**
     * 退款凭证名称带后缀
     */
    @TableField("refund_name_ori")
    private String refundNameOri;
}
