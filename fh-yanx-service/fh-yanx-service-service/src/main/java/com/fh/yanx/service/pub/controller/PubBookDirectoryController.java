package com.fh.yanx.service.pub.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.pub.api.PubBookDirectoryApi;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryConditionBo;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryDto;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryVo;
import com.fh.yanx.service.pub.service.IPubBookDirectoryService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 融合出版书目录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@RestController
@Validated
public class PubBookDirectoryController implements PubBookDirectoryApi{
	
    @Autowired
    private IPubBookDirectoryService pubBookDirectoryService;

    /**
     * 查询融合出版书目录分页列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @Override
    public AjaxResult<PageInfo<PubBookDirectoryVo>> getPubBookDirectoryPageListByCondition(@RequestBody PubBookDirectoryConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<PubBookDirectoryVo> pageInfo = new PageInfo<>(pubBookDirectoryService.getPubBookDirectoryListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询融合出版书目录列表
	 * <AUTHOR>
	 * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult<List<PubBookDirectoryVo>> getPubBookDirectoryListByCondition(@RequestBody PubBookDirectoryConditionBo condition){
		List<PubBookDirectoryVo> list = pubBookDirectoryService.getPubBookDirectoryListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增融合出版书目录
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
	@Override
    public AjaxResult addPubBookDirectory(@Validated @RequestBody PubBookDirectoryBo pubBookDirectoryBo){
		return pubBookDirectoryService.addPubBookDirectory(pubBookDirectoryBo);
    }

    /**
	 * 修改融合出版书目录
	 * @param pubBookDirectoryBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult updatePubBookDirectory(@Validated @RequestBody PubBookDirectoryBo pubBookDirectoryBo) {
		if(null == pubBookDirectoryBo.getBookDirectoryId()) {
			return AjaxResult.fail("融合出版书目录id不能为空");
		}
		return pubBookDirectoryService.updatePubBookDirectory(pubBookDirectoryBo);
	}

	/**
	 * 查询融合出版书目录详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult<PubBookDirectoryVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("融合出版书目录id不能为空");
		}
		PubBookDirectoryConditionBo condition = new PubBookDirectoryConditionBo();
		condition.setBookDirectoryId(id);
		PubBookDirectoryVo vo = pubBookDirectoryService.getPubBookDirectoryByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除融合出版书目录
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		PubBookDirectoryDto pubBookDirectoryDto = new PubBookDirectoryDto();
		pubBookDirectoryDto.setBookDirectoryId(id);
		pubBookDirectoryDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(pubBookDirectoryService.updateById(pubBookDirectoryDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
