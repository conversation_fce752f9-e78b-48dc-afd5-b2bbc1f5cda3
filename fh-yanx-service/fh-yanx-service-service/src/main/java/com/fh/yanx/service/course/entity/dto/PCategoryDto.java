package com.fh.yanx.service.course.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资源类别
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_category")
public class PCategoryDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 父级ID
	 */
	@TableField("parent_id")
	private Long parentId;

	/**
	 * 
	 */
	@TableField("name")
	private String name;

	/**
	 * 
	 */
	@TableField("description")
	private String description;

	/**
	 * 所属模块ID
	 */
	@TableField("module_id")
	private Long moduleId;

	/**
	 * 层级
	 */
	@TableField("level")
	private Integer level;

	/**
	 * 类型
	 */
	@TableField("type_id")
	private Long typeId;

	/**
	 * 
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 状态（1、正常，2、禁用 0 删除）
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 
	 */
	@TableField("create_user_oid")
	private String createUserOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 顺序
	 */
	@TableField("sequence")
	private Integer sequence;

	/**
	 * 
	 */
	@TableField("edusoa_id")
	private Long edusoaId;

}
