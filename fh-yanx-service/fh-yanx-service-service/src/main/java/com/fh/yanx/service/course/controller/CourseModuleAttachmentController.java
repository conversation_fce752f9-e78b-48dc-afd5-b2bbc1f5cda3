package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.CourseModuleAttachmentApi;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentDto;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo;
import com.fh.yanx.service.course.service.ICourseModuleAttachmentService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 课程资源或成果样例附件表-模块附件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@RestController
@Validated
public class CourseModuleAttachmentController implements CourseModuleAttachmentApi{
	
    @Autowired
    private ICourseModuleAttachmentService courseModuleAttachmentService;

    /**
     * 查询课程资源或成果样例附件表-模块附件分页列表
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @Override
    public AjaxResult<PageInfo<CourseModuleAttachmentVo>> getCourseModuleAttachmentPageListByCondition(@RequestBody CourseModuleAttachmentConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseModuleAttachmentVo> pageInfo = new PageInfo<>(courseModuleAttachmentService.getCourseModuleAttachmentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程资源或成果样例附件表-模块附件列表
	 * <AUTHOR>
	 * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult<List<CourseModuleAttachmentVo>> getCourseModuleAttachmentListByCondition(@RequestBody CourseModuleAttachmentConditionBo condition){
		List<CourseModuleAttachmentVo> list = courseModuleAttachmentService.getCourseModuleAttachmentListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程资源或成果样例附件表-模块附件
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
	@Override
    public AjaxResult addCourseModuleAttachment(@Validated @RequestBody CourseModuleAttachmentBo courseModuleAttachmentBo){
		return courseModuleAttachmentService.addCourseModuleAttachment(courseModuleAttachmentBo);
    }

    /**
	 * 修改课程资源或成果样例附件表-模块附件
	 * @param courseModuleAttachmentBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult updateCourseModuleAttachment(@Validated @RequestBody CourseModuleAttachmentBo courseModuleAttachmentBo) {
		if(null == courseModuleAttachmentBo.getCourseModuleAttachmentId()) {
			return AjaxResult.fail("课程资源或成果样例附件表-模块附件id不能为空");
		}
		return courseModuleAttachmentService.updateCourseModuleAttachment(courseModuleAttachmentBo);
	}

	/**
	 * 查询课程资源或成果样例附件表-模块附件详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult<CourseModuleAttachmentVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程资源或成果样例附件表-模块附件id不能为空");
		}
		CourseModuleAttachmentConditionBo condition = new CourseModuleAttachmentConditionBo();
		condition.setCourseModuleAttachmentId(id);
		CourseModuleAttachmentVo vo = courseModuleAttachmentService.getCourseModuleAttachmentByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程资源或成果样例附件表-模块附件
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseModuleAttachmentDto courseModuleAttachmentDto = new CourseModuleAttachmentDto();
		courseModuleAttachmentDto.setCourseModuleAttachmentId(id);
		courseModuleAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseModuleAttachmentService.updateById(courseModuleAttachmentDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
