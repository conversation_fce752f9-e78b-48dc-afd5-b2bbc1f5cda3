package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.dto.PCourseCasesTypeDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesTypeVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本课程案例类型接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface IPCourseCasesTypeService extends IService<PCourseCasesTypeDto> {

    List<PCourseCasesTypeVo> getPCourseCasesTypeListByCondition(PCourseCasesTypeConditionBo condition);

	AjaxResult addPCourseCasesType(PCourseCasesTypeBo pCourseCasesTypeBo);

	AjaxResult updatePCourseCasesType(PCourseCasesTypeBo pCourseCasesTypeBo);

	PCourseCasesTypeVo getPCourseCasesTypeByCondition(PCourseCasesTypeConditionBo condition);

	/**
	 * 获取案例相关联类型名称
	 * @param casesId
	 * @return
	 */
	List<String> getCasesTypeNameList(Long casesId);

	/**
	 * 保存类型信息，保存后设置id到Bo里面
	 *
	 * @param casesId the cases id
	 * @param courseCasesTypeBoList the course cases type bo list
	 * <AUTHOR>
	 * @date 2023 -08-18 16:52:05
	 */
	void saveCourseType(Long casesId, List<PCourseCasesTypeBo> courseCasesTypeBoList);
}

