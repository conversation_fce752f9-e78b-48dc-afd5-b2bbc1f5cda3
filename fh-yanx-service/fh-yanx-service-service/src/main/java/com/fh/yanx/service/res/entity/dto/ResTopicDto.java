package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课题表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_topic")
public class ResTopicDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "topic_id", type = IdType.AUTO)
    private Long topicId;

    /**
     * 课题名称
     */
    @TableField("topic_name")
    private String topicName;

    /**
     * 学校id
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 班级id
     */
    @TableField("classes_id")
    private Long classesId;

    /**
     * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定
     */
    @TableField("topic_process")
    private Integer topicProcess;

    /**
     * 当前提交人
     */
    @TableField("submit_user")
    private String submitUser;

    /**
     * 当前审批人
     */
    @TableField("verify_user")
    private String verifyUser;

    /**
     * 课题类别：待定
     */
    @TableField("topic_type")
    private Integer topicType;

    /**
     * 关联学科：待定（可能是type或者字典值），多个使用逗号分割
     */
    @TableField("rel_subject")
    private String relSubject;

    /**
     * 课题介绍/界定
     */
    @TableField("topic_desc")
    private String topicDesc;

    /**
     * 课题背景
     */
    @TableField("topic_back")
    private String topicBack;

    /**
     * 课题目的
     */
    @TableField("topic_goal")
    private String topicGoal;

    /**
     * 研究内容
     */
    @TableField("topic_content")
    private String topicContent;

    /**
     * 研究方法
     */
    @TableField("topic_method")
    private String topicMethod;

    /**
     * 研究条件
     */
    @TableField("topic_condition")
    private String topicCondition;

    /**
     * 研究计划
     */
    @TableField("topic_plan")
    private String topicPlan;

    /**
     * 研究预期成果
     */
    @TableField("topic_expected")
    private String topicExpected;

    /**
     * 最终评定结果：1通过，2不通过
     */
    @TableField("evaluate_result")
    private Integer evaluateResult;

    /**
     * 优秀课题类型：1不是优秀课题，2是优秀课题
     */
    @TableField("excellent_type")
    private Integer excellentType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 课题来源：1校内自建，2校外导入
     */
    @TableField("topic_source")
    private Integer topicSource;

    /**
     * 校外课题指导老师信息
     */
    @TableField("topic_tutor")
    private String topicTutor;

    /**
     * 校外课题id,第三方id
     */
    @TableField("topic_third_id")
    private String topicThirdId;

    /**
     * 首页展示：1展示
     */
    @TableField("home_show")
    private Integer homeShow;


    /**
     * 课题第三方来源类型：1有方，2汇景
     */
    @TableField("topic_third_type")
    private String topicThirdType;


    /**
     * 关联学科字典值,逗号分隔
     */
    @TableField("subject_code")
    private String subjectCode;

}
