package com.fh.yanx.service.aspose;

import com.aspose.words.*;

import java.util.ArrayList;

/**
 * @Classname ReplaceWithRitchEvaluator
 * @Description 替换
 * @Date 2025/3/10 13:55
 * @Created by admin
 */
public class ReplaceWithRichEvaluator implements IReplacingCallback {
    private String html;

    public ReplaceWithRichEvaluator(String html) {
        this.html = html;
    }

    @Override
    public int replacing(ReplacingArgs e) throws Exception {
        // 当前匹配的节点（Run）包含匹配字符串的开始或完整匹配
        Node currentNode = e.getMatchNode();

        // 如果匹配不是从 Run 开头开始，则拆分这个 Run 节点
        if (e.getMatchOffset() > 0) {
            currentNode = splitRun((Run) currentNode, e.getMatchOffset());
        }

        // 用于存储所有包含匹配文本的 Run 节点
        ArrayList<Run> runs = new ArrayList<>();

        int remainingLength = e.getMatch().group().length();
        // 遍历所有包含匹配文本的 Run 节点
        while (remainingLength > 0 && currentNode != null && currentNode.getText().length() <= remainingLength) {
            runs.add((Run) currentNode);
            remainingLength -= currentNode.getText().length();

            // 查找下一个 Run 节点（跳过 BookmarkStart 等其它类型节点）
            do {
                currentNode = currentNode.getNextSibling();
            } while (currentNode != null && currentNode.getNodeType() != NodeType.RUN);
        }

        // 如果最后一个 Run 中还包含部分匹配文本，则拆分该节点
        if (currentNode != null && remainingLength > 0) {
            splitRun((Run) currentNode, remainingLength);
            runs.add((Run) currentNode);
        }

        // 使用 DocumentBuilder 定位到第一个匹配的 Run 节点位置
        DocumentBuilder builder = new DocumentBuilder((Document) e.getMatchNode().getDocument());
        builder.moveTo(runs.get(0));

        // 插入 HTML 富文本内容
        builder.insertHtml(html);

        // 移除原有匹配文本所在的所有 Run 节点
        for (Run run : runs) {
            run.remove();
        }

        return ReplaceAction.SKIP;
    }

    // 拆分 Run 节点，将文本拆分为两部分：前段保留，后段返回。
    private static Run splitRun(Run run, int position) throws Exception {
        Run afterRun = (Run) run.deepClone(true);
        afterRun.setText(run.getText().substring(position));
        run.setText(run.getText().substring(0, position));
        run.getParentNode().insertAfter(afterRun, run);
        return afterRun;
    }
}
