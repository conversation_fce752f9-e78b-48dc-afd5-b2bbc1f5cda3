package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResApplyDto;
import com.fh.yanx.service.res.entity.bo.ResApplyConditionBo;
import com.fh.yanx.service.res.entity.vo.ResApplyVo;

/**
 * 合作意向申请表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-27 16:19:35
 */
public interface ResApplyMapper extends BaseMapper<ResApplyDto> {

	List<ResApplyVo> getResApplyListByCondition(ResApplyConditionBo condition);

}
