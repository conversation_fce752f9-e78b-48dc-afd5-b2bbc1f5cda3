package com.fh.yanx.service.bm.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.aspose.AsposeBizService;
import com.fh.yanx.service.bm.entity.bo.BmInfoCheckBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoDto;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.fh.yanx.service.bm.mapper.BmInfoMapper;
import com.fh.yanx.service.enums.BmInfoStudyProveType;
import com.fh.yanx.service.enums.BmInfoSubmitType;
import com.google.common.collect.Lists;
import com.light.base.attachment.entity.vo.AttachmentVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoJoinerDto;
import com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo;
import com.fh.yanx.service.bm.mapper.BmInfoJoinerMapper;
import com.fh.yanx.service.bm.service.IBmInfoJoinerService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 新时代文化校园报名活动申请表-参与人信息表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Service
public class BmInfoJoinerServiceImpl extends ServiceImpl<BmInfoJoinerMapper, BmInfoJoinerDto>
    implements IBmInfoJoinerService {

    @Resource
    private BmInfoJoinerMapper bmInfoJoinerMapper;
    @Resource
    private AsposeBizService asposeBizService;
    @Resource
    private BmInfoMapper bmInfoMapper;

    @Override
    public List<BmInfoJoinerVo> getBmInfoJoinerListByCondition(BmInfoJoinerConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return bmInfoJoinerMapper.getBmInfoJoinerListByCondition(condition);
    }

    @Override
    public AjaxResult addBmInfoJoiner(BmInfoJoinerBo bmInfoJoinerBo) {
        BmInfoJoinerDto bmInfoJoiner = new BmInfoJoinerDto();
        BeanUtils.copyProperties(bmInfoJoinerBo, bmInfoJoiner);
        bmInfoJoiner.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(bmInfoJoiner)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBmInfoJoiner(BmInfoJoinerBo bmInfoJoinerBo) {
        BmInfoJoinerDto bmInfoJoiner = new BmInfoJoinerDto();
        BeanUtils.copyProperties(bmInfoJoinerBo, bmInfoJoiner);
        if (updateById(bmInfoJoiner)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public BmInfoJoinerVo getBmInfoJoinerByCondition(BmInfoJoinerConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        BmInfoJoinerVo vo = bmInfoJoinerMapper.getBmInfoJoinerByCondition(condition);
        if (null != vo) {
            return vo;
        }
        return null;
    }

    @Override
    public List<BmInfoVo> getJoinerCountByInfoId(List<Long> infoIds) {
        return bmInfoJoinerMapper.getJoinerCountByInfoId(infoIds);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean deleteAndBatchAddBmInfoJoiner(Long infoId, List<BmInfoJoinerBo> bmInfoJoinerBos) {
        if (infoId == null) {
            return false;
        }
        // 删除
        LambdaUpdateWrapper<BmInfoJoinerDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BmInfoJoinerDto::getInfoId, infoId);
        updateWrapper.set(BmInfoJoinerDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        boolean update = update(updateWrapper);
        if (!update) {
            return false;
        }
        if (CollectionUtils.isEmpty(bmInfoJoinerBos)) {
            return true;
        }

        // 新增
        List<BmInfoJoinerDto> bmInfoJoinerDtos = bmInfoJoinerBos.stream().map(bmInfoJoinerBo -> {
            BmInfoJoinerDto bmInfoJoinerDto = new BmInfoJoinerDto();
            BeanUtils.copyProperties(bmInfoJoinerBo, bmInfoJoinerDto);
            bmInfoJoinerDto.setInfoId(infoId);
            return bmInfoJoinerDto;
        }).collect(Collectors.toList());
        return saveBatch(bmInfoJoinerDtos);
    }

    @Override
    public List<BmInfoJoinerVo> checkBmInfoJoiner(BmInfoCheckBo checkBo) {
        if (CollectionUtil.isEmpty(checkBo.getMobiles())) {
            return null;
        }
        return baseMapper.getBmInfoJoinerByMobile(checkBo.getActivId(), checkBo.getMobiles());
    }

    @Override
    public List<BmInfoJoinerVo> listJoinerByActivId(Long activId, Integer submitType) {
        if(activId == null){
            return Lists.newArrayList();
        }
        return bmInfoJoinerMapper.listJoinerByActivId(activId, submitType);
    }

    @Override
    public AjaxResult studyProveGenerate() {
        // 获取生成中状态的记录
        List<BmInfoJoinerDto> bmInfoJoinerDtos = list(new LambdaQueryWrapper<BmInfoJoinerDto>()
                .eq(BmInfoJoinerDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(BmInfoJoinerDto::getStudyProveType, BmInfoStudyProveType.IN_GENERATE.getValue()));
        if (CollectionUtil.isEmpty(bmInfoJoinerDtos)) {
            return AjaxResult.success();
        }
        Date nowTime = new Date();
        for (BmInfoJoinerDto dto : bmInfoJoinerDtos) {
            Map<String, String> params = new HashMap<>();
            params.put("name", dto.getJoinerName());
            String period = dto.getStudyHours().setScale(1).toString();
            if (period.endsWith(".0")) {
                period = period.substring(0, period.indexOf("."));
            }
            params.put("period", period);
            params.put("year", DateUtil.year(nowTime)+"");
            params.put("month", DateUtil.month(nowTime)+1+"");
            params.put("day", DateUtil.dayOfMonth(nowTime)+"");
            AttachmentVo attachmentVo = asposeBizService.asposeWort2Png(params);
            dto.setStudyProveId(attachmentVo.getFileOid());
            dto.setStudyProveName(attachmentVo.getOriginalName());
            dto.setStudyProveNameOri(attachmentVo.getNewName());
            dto.setStudyProveUrl(attachmentVo.getPreviewAllPath());
            dto.setStudyProveType(BmInfoStudyProveType.ALREADY_GENERATE.getValue());
        }
        // 更新学时证明信息
        updateBatchById(bmInfoJoinerDtos);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateByMobile(BmInfoJoinerBo bmInfoJoinerBo) {
        BmInfoJoinerDto dto = getOne(new LambdaQueryWrapper<BmInfoJoinerDto>()
                .eq(BmInfoJoinerDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(BmInfoJoinerDto::getJoinerMobile, bmInfoJoinerBo.getJoinerMobile())
                .eq(BmInfoJoinerDto::getInfoId, bmInfoJoinerBo.getInfoId())
                .orderByDesc(BmInfoJoinerDto::getId).last("limit 1"));
        if (dto == null) {
            return AjaxResult.fail("信息不存在");
        }
        BmInfoDto bmInfoDto = bmInfoMapper.selectById(dto.getInfoId());
        if (!BmInfoSubmitType.ATTEND.getCode().equals(bmInfoDto.getSubmitType())) {
            return AjaxResult.fail("未确认参会，无法获取学时证明");
        }
        BmInfoJoinerDto entity = new BmInfoJoinerDto();
        BeanUtils.copyProperties(bmInfoJoinerBo, entity);
        entity.setId(dto.getId());
        if (updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult getByMobile(BmInfoJoinerBo bmInfoJoinerBo) {
        BmInfoJoinerDto dto = getOne(new LambdaQueryWrapper<BmInfoJoinerDto>()
                .eq(BmInfoJoinerDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(BmInfoJoinerDto::getJoinerMobile, bmInfoJoinerBo.getJoinerMobile())
                .eq(BmInfoJoinerDto::getInfoId, bmInfoJoinerBo.getInfoId())
                .orderByDesc(BmInfoJoinerDto::getId).last("limit 1"));
        if (dto == null) {
            return AjaxResult.fail("信息不存在");
        }
        BmInfoJoinerVo vo = new BmInfoJoinerVo();
        BeanUtils.copyProperties(dto, vo);
        return AjaxResult.success(vo);
    }
}