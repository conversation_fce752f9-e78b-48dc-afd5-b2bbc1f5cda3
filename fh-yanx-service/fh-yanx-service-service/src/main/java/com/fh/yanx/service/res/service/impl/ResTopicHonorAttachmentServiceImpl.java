package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicHonorAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicHonorAttachmentVo;
import com.fh.yanx.service.res.service.IResTopicHonorAttachmentService;
import com.fh.yanx.service.res.mapper.ResTopicHonorAttachmentMapper;
import com.light.core.entity.AjaxResult;

/**
 * 课题荣誉表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResTopicHonorAttachmentServiceImpl extends
    ServiceImpl<ResTopicHonorAttachmentMapper, ResTopicHonorAttachmentDto> implements IResTopicHonorAttachmentService {

    @Resource
    private ResTopicHonorAttachmentMapper resTopicHonorAttachmentMapper;

    @Autowired
    private IResTopicHonorAttachmentService resTopicHonorAttachmentService;

    @Override
    public List<ResTopicHonorAttachmentVo>
        getResTopicHonorAttachmentListByCondition(ResTopicHonorAttachmentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicHonorAttachmentMapper.getResTopicHonorAttachmentListByCondition(condition);
    }

    @Override
    public AjaxResult addResTopicHonorAttachment(ResTopicHonorAttachmentBo resTopicHonorAttachmentBo) {
        ResTopicHonorAttachmentDto resTopicHonorAttachment = new ResTopicHonorAttachmentDto();
        BeanUtils.copyProperties(resTopicHonorAttachmentBo, resTopicHonorAttachment);
        resTopicHonorAttachment.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(resTopicHonorAttachment)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateResTopicHonorAttachment(ResTopicHonorAttachmentBo resTopicHonorAttachmentBo) {
        ResTopicHonorAttachmentDto resTopicHonorAttachment = new ResTopicHonorAttachmentDto();
        BeanUtils.copyProperties(resTopicHonorAttachmentBo, resTopicHonorAttachment);
        if (updateById(resTopicHonorAttachment)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ResTopicHonorAttachmentVo getDetail(Long id) {
        ResTopicHonorAttachmentConditionBo condition = new ResTopicHonorAttachmentConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicHonorAttachmentVo> list =
            resTopicHonorAttachmentMapper.getResTopicHonorAttachmentListByCondition(condition);
        ResTopicHonorAttachmentVo vo = new ResTopicHonorAttachmentVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public boolean deleteAndSaveTopicHonorAttachmentBatch(Long topicId,
        List<ResTopicHonorAttachmentBo> resTopicHonorAttachmentBos) {
        // 删除
        LambdaUpdateWrapper<ResTopicHonorAttachmentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResTopicHonorAttachmentDto::getTopicId, topicId);
        updateWrapper.eq(ResTopicHonorAttachmentDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ResTopicHonorAttachmentDto resTopicHonorAttachmentDto = new ResTopicHonorAttachmentDto();
        resTopicHonorAttachmentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        resTopicHonorAttachmentMapper.update(resTopicHonorAttachmentDto, updateWrapper);

        // 批量新增
        List<ResTopicHonorAttachmentDto> resTopicHonorAttachmentDtos =
            resTopicHonorAttachmentBos.stream().map(resTopicHonorAttachmentBo -> {
                ResTopicHonorAttachmentDto resTopicHonorAttachment = new ResTopicHonorAttachmentDto();
                BeanUtils.copyProperties(resTopicHonorAttachmentBo, resTopicHonorAttachment);
                return resTopicHonorAttachment;
            }).collect(Collectors.toList());
        return saveBatch(resTopicHonorAttachmentDtos);
    }
}