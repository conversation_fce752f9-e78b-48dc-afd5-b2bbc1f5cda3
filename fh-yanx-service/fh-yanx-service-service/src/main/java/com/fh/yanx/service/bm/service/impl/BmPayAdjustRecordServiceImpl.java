package com.fh.yanx.service.bm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.bm.entity.bo.BmPayAdjustRecordBo;
import com.fh.yanx.service.bm.entity.bo.BmPayAdjustRecordConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmPayAdjustRecordDto;
import com.fh.yanx.service.bm.entity.vo.BmPayAdjustRecordVo;
import com.fh.yanx.service.bm.mapper.BmPayAdjustRecordMapper;
import com.fh.yanx.service.bm.service.IBmPayAdjustRecordService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.BeanUtils;
import com.light.core.utils.FuzzyQueryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-05-27  17:14
 */
@Service
@Slf4j
public class BmPayAdjustRecordServiceImpl extends ServiceImpl<BmPayAdjustRecordMapper, BmPayAdjustRecordDto>
    implements IBmPayAdjustRecordService {
    @Resource
    private BmPayAdjustRecordMapper bmPayAdjustRecordMapper;

    @Override
    public List<BmPayAdjustRecordVo> getBmPayAdjustRecordListByCondition(BmPayAdjustRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return bmPayAdjustRecordMapper.getBmPayAdjustRecordListByCondition(condition);
    }

    @Override
    public AjaxResult addBmPayAdjustRecord(BmPayAdjustRecordBo bmPayAdjustRecordBo) {
        BmPayAdjustRecordDto bmPayAdjustRecord = new BmPayAdjustRecordDto();
        BeanUtils.copyProperties(bmPayAdjustRecordBo, bmPayAdjustRecord);
        if (save(bmPayAdjustRecord)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBmPayAdjustRecord(BmPayAdjustRecordBo bmPayAdjustRecordBo) {
        BmPayAdjustRecordDto bmPayAdjustRecord = new BmPayAdjustRecordDto();
        BeanUtils.copyProperties(bmPayAdjustRecordBo, bmPayAdjustRecord);
        if (updateById(bmPayAdjustRecord)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public BmPayAdjustRecordVo getBmPayAdjustRecordDetail(BmPayAdjustRecordConditionBo condition) {
        BmPayAdjustRecordVo vo = bmPayAdjustRecordMapper.getBmPayAdjustRecordByCondition(condition);
        return vo;
    }
}
