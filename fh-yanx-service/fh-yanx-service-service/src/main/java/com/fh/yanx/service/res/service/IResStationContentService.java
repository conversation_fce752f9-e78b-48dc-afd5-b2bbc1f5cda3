package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResStationContentDto;
import com.fh.yanx.service.res.entity.bo.ResStationContentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 工作站具体内容接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResStationContentService extends IService<ResStationContentDto> {

    List<ResStationContentVo> getResStationContentListByCondition(ResStationContentConditionBo condition);

	AjaxResult addResStationContent(ResStationContentBo resStationContentBo);

	AjaxResult updateResStationContent(ResStationContentBo resStationContentBo);

	ResStationContentVo getDetail(Long id);

}

