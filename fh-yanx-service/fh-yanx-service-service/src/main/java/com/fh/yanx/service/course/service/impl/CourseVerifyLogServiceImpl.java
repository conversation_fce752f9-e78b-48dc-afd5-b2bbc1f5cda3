package com.fh.yanx.service.course.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.enums.CourseVerifySource;
import com.fh.yanx.service.res.entity.dto.ResTopicStudentDto;
import com.light.core.utils.FuzzyQueryUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseVerifyLogDto;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.fh.yanx.service.course.service.ICourseVerifyLogService;
import com.fh.yanx.service.course.mapper.CourseVerifyLogMapper;
import com.light.core.entity.AjaxResult;

/**
 * 课程审核流水表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@Service
public class CourseVerifyLogServiceImpl extends ServiceImpl<CourseVerifyLogMapper, CourseVerifyLogDto>
    implements ICourseVerifyLogService {

    @Resource
    private CourseVerifyLogMapper courseVerifyLogMapper;

    @Override
    public List<CourseVerifyLogVo> getCourseVerifyLogListByCondition(CourseVerifyLogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return courseVerifyLogMapper.getCourseVerifyLogListByCondition(condition);
    }

    @Override
    public AjaxResult addCourseVerifyLog(CourseVerifyLogBo courseVerifyLogBo) {
        CourseVerifyLogDto courseVerifyLog = new CourseVerifyLogDto();
        BeanUtils.copyProperties(courseVerifyLogBo, courseVerifyLog);
        courseVerifyLog.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(courseVerifyLog)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCourseVerifyLog(CourseVerifyLogBo courseVerifyLogBo) {
        CourseVerifyLogDto courseVerifyLog = new CourseVerifyLogDto();
        BeanUtils.copyProperties(courseVerifyLogBo, courseVerifyLog);
        if (updateById(courseVerifyLog)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCourseVerifyLog(List<CourseVerifyLogBo> courseVerifyLogBo) {
        List<CourseVerifyLogDto> courseVerifyLogDtos = BeanUtil.copyToList(courseVerifyLogBo, CourseVerifyLogDto.class);
        updateBatchById(courseVerifyLogDtos);
        return AjaxResult.success("保存成功");
    }

    @Override
    public CourseVerifyLogVo getCourseVerifyLogByCondition(CourseVerifyLogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        CourseVerifyLogVo vo = courseVerifyLogMapper.getCourseVerifyLogByCondition(condition);
        return vo;
    }

    @Override
    public boolean deleteAndSave(CourseVerifyLogBo courseVerifyLogBo) {
        // 根据条件先删除（根据：cases_id，verify_process_type，user_oid,course_verify_source删除再增加）
        LambdaUpdateWrapper<CourseVerifyLogDto> courseVerifyLogUpdateWrapper = new LambdaUpdateWrapper<>();
        courseVerifyLogUpdateWrapper.set(CourseVerifyLogDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        courseVerifyLogUpdateWrapper.eq(CourseVerifyLogDto::getCasesId, courseVerifyLogBo.getCasesId());
        courseVerifyLogUpdateWrapper.eq(CourseVerifyLogDto::getVerifyProcessType,
            courseVerifyLogBo.getVerifyProcessType());
        courseVerifyLogUpdateWrapper.eq(CourseVerifyLogDto::getUserOid, courseVerifyLogBo.getUserOid());
        if (courseVerifyLogBo.getCourseVerifySource() != null) {
            courseVerifyLogUpdateWrapper.eq(CourseVerifyLogDto::getCourseVerifySource,
                courseVerifyLogBo.getCourseVerifySource());
        }
        update(courseVerifyLogUpdateWrapper);

        // 再新增
        CourseVerifyLogDto courseVerifyLog = new CourseVerifyLogDto();
        BeanUtils.copyProperties(courseVerifyLogBo, courseVerifyLog);
        courseVerifyLog.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(courseVerifyLog);
    }

    @Override
    public List<CourseVerifyLogDto> getCurrentUserCourseVerifyLogList(PCourseCasesConditionBo condition) {
        // 根据查询数据 normalVerifyTypes有值为校本课程审核 bestVerifyTypes有值为精品课程审核 查询时做区分
        LambdaQueryWrapper<CourseVerifyLogDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CourseVerifyLogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(CourseVerifyLogDto::getUserOid, condition.getCurrentUserOid());
        if (CollectionUtils.isNotEmpty(condition.getNormalVerifyTypes())) {
            lqw.eq(CourseVerifyLogDto::getCourseVerifySource, CourseVerifySource.NORMAL.getValue());
        } else if (CollectionUtils.isNotEmpty(condition.getBestVerifyTypes())) {
            lqw.eq(CourseVerifyLogDto::getCourseVerifySource, CourseVerifySource.BEST.getValue());
        }
        return list(lqw);
    }
}