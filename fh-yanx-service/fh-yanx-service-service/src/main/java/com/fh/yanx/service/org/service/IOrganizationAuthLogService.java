package com.fh.yanx.service.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.org.entity.dto.OrganizationAuthLogDto;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogBo;
import com.fh.yanx.service.org.entity.vo.OrganizationAuthLogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 组织认证日志记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
public interface IOrganizationAuthLogService extends IService<OrganizationAuthLogDto> {

    List<OrganizationAuthLogVo> getOrganizationAuthLogListByCondition(OrganizationAuthLogConditionBo condition);

	AjaxResult addOrganizationAuthLog(OrganizationAuthLogBo organizationAuthLogBo);

	AjaxResult updateOrganizationAuthLog(OrganizationAuthLogBo organizationAuthLogBo);

	OrganizationAuthLogVo getOrganizationAuthLogByCondition(OrganizationAuthLogConditionBo condition);

}

