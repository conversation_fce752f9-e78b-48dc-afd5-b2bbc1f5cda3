package com.fh.yanx.service.pub.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.pub.entity.dto.PubBookDirectoryAttachmentDto;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentConditionBo;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryAttachmentVo;

/**
 * 融合出版书目录附件Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface PubBookDirectoryAttachmentMapper extends BaseMapper<PubBookDirectoryAttachmentDto> {

	List<PubBookDirectoryAttachmentVo> getPubBookDirectoryAttachmentListByCondition(PubBookDirectoryAttachmentConditionBo condition);

	PubBookDirectoryAttachmentVo getPubBookDirectoryAttachmentByCondition(PubBookDirectoryAttachmentConditionBo condition);

}
