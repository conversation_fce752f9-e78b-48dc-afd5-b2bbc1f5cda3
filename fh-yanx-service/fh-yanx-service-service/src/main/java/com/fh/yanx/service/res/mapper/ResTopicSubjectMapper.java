package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicSubjectDto;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicSubjectVo;

/**
 * 关联科目表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicSubjectMapper extends BaseMapper<ResTopicSubjectDto> {

	List<ResTopicSubjectVo> getResTopicSubjectListByCondition(ResTopicSubjectConditionBo condition);

}
