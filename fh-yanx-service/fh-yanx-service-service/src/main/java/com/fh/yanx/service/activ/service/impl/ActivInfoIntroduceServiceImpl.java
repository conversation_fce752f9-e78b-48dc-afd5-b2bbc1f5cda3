package com.fh.yanx.service.activ.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.activ.entity.dto.ActivInfoIntroduceDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo;
import com.fh.yanx.service.activ.service.IActivInfoIntroduceService;
import com.fh.yanx.service.activ.mapper.ActivInfoIntroduceMapper;
import com.light.core.entity.AjaxResult;
/**
 * 新时代文化校园活动介绍表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
@Service
public class ActivInfoIntroduceServiceImpl extends ServiceImpl<ActivInfoIntroduceMapper, ActivInfoIntroduceDto> implements IActivInfoIntroduceService {

	@Resource
	private ActivInfoIntroduceMapper activInfoIntroduceMapper;
	
    @Override
	public List<ActivInfoIntroduceVo> getActivInfoIntroduceListByCondition(ActivInfoIntroduceConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return activInfoIntroduceMapper.getActivInfoIntroduceListByCondition(condition);
	}

	@Override
	public AjaxResult addActivInfoIntroduce(ActivInfoIntroduceBo activInfoIntroduceBo) {
		ActivInfoIntroduceDto activInfoIntroduce = new ActivInfoIntroduceDto();
		BeanUtils.copyProperties(activInfoIntroduceBo, activInfoIntroduce);
		activInfoIntroduce.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(activInfoIntroduce)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateActivInfoIntroduce(ActivInfoIntroduceBo activInfoIntroduceBo) {
		ActivInfoIntroduceDto activInfoIntroduce = new ActivInfoIntroduceDto();
		BeanUtils.copyProperties(activInfoIntroduceBo, activInfoIntroduce);
		if(updateById(activInfoIntroduce)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ActivInfoIntroduceVo getActivInfoIntroduceByCondition(ActivInfoIntroduceConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return activInfoIntroduceMapper.getActivInfoIntroduceByCondition(condition);
	}

	@Override
	public boolean deleteAndAddActivIntroduces(ActivInfoBo activInfoBo) {
    	if (null == activInfoBo.getActivId()) {
    		return false;
		}
		// 删除
		LambdaUpdateWrapper<ActivInfoIntroduceDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(ActivInfoIntroduceDto::getActivId, activInfoBo.getActivId());
		updateWrapper.set(ActivInfoIntroduceDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		update(updateWrapper);

		if (CollectionUtil.isEmpty(activInfoBo.getActivInfoIntroduces())) {
			return true;
		}

		// 新增
		List<ActivInfoIntroduceDto> activInfoIntroduceDtos = activInfoBo.getActivInfoIntroduces().stream()
				.map(bo -> {
					ActivInfoIntroduceDto infoIntroduceDto = new ActivInfoIntroduceDto();
					BeanUtils.copyProperties(bo, infoIntroduceDto);
					infoIntroduceDto.setActivId(activInfoBo.getActivId());
					return infoIntroduceDto;
				}).collect(Collectors.toList());
		return saveBatch(activInfoIntroduceDtos);
	}


}