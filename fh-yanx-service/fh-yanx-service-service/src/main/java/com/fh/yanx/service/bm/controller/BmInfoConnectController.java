package com.fh.yanx.service.bm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.bm.api.BmInfoConnectApi;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectConditionBo;
import com.fh.yanx.service.bm.entity.dto.BmInfoConnectDto;
import com.fh.yanx.service.bm.entity.vo.BmInfoConnectVo;
import com.fh.yanx.service.bm.service.IBmInfoConnectService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 报名活动沟通确认表（本表只有新增记录）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
@RestController
@Validated
public class BmInfoConnectController implements BmInfoConnectApi {

    @Autowired
    private IBmInfoConnectService bmInfoConnectService;

    /**
     * 查询报名活动沟通确认表（本表只有新增记录）分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @Override
    public AjaxResult<PageInfo<BmInfoConnectVo>>
        getBmInfoConnectPageListByCondition(@RequestBody BmInfoConnectConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<BmInfoConnectVo> pageInfo =
            new PageInfo<>(bmInfoConnectService.getBmInfoConnectListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询报名活动沟通确认表（本表只有新增记录）列表
     * 
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @Override
    public AjaxResult<List<BmInfoConnectVo>>
        getBmInfoConnectListByCondition(@RequestBody BmInfoConnectConditionBo condition) {
        List<BmInfoConnectVo> list = bmInfoConnectService.getBmInfoConnectListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增报名活动沟通确认表（本表只有新增记录）
     * 
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @Override
    public AjaxResult addBmInfoConnect(@Validated @RequestBody BmInfoConnectBo bmInfoConnectBo) {
        return bmInfoConnectService.addBmInfoConnect(bmInfoConnectBo);
    }

    /**
     * 新增报名活动沟通确认表（本表只有新增记录）-同时更新BmInfo表的submitType
     * 
     * @param bmInfoConnectBo
     * @return
     */
    @Override
    public AjaxResult addBmInfoConnectWithUpdateBmInfo(BmInfoConnectBo bmInfoConnectBo) {
        return bmInfoConnectService.addBmInfoConnectWithUpdateBmInfo(bmInfoConnectBo);
    }

    /**
     * 修改报名活动沟通确认表（本表只有新增记录）
     * 
     * @param bmInfoConnectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @Override
    public AjaxResult updateBmInfoConnect(@Validated @RequestBody BmInfoConnectBo bmInfoConnectBo) {
        if (null == bmInfoConnectBo.getId()) {
            return AjaxResult.fail("报名活动沟通确认表（本表只有新增记录）id不能为空");
        }
        return bmInfoConnectService.updateBmInfoConnect(bmInfoConnectBo);
    }

    /**
     * 查询报名活动沟通确认表（本表只有新增记录）详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @Override
    public AjaxResult<BmInfoConnectVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("报名活动沟通确认表（本表只有新增记录）id不能为空");
        }
        BmInfoConnectConditionBo condition = new BmInfoConnectConditionBo();
        condition.setId(id);
        BmInfoConnectVo vo = bmInfoConnectService.getBmInfoConnectByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除报名活动沟通确认表（本表只有新增记录）
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        BmInfoConnectDto bmInfoConnectDto = new BmInfoConnectDto();
        bmInfoConnectDto.setId(id);
        bmInfoConnectDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (bmInfoConnectService.updateById(bmInfoConnectDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
