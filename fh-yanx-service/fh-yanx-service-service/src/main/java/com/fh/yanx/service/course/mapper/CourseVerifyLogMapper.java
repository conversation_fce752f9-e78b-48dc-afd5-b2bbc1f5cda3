package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseVerifyLogDto;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;

/**
 * 课程审核流水表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
public interface CourseVerifyLogMapper extends BaseMapper<CourseVerifyLogDto> {

	List<CourseVerifyLogVo> getCourseVerifyLogListByCondition(CourseVerifyLogConditionBo condition);

	CourseVerifyLogVo getCourseVerifyLogByCondition(CourseVerifyLogConditionBo condition);

}
