package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResInformationApi;
import com.fh.yanx.service.res.entity.bo.ResInformationBo;
import com.fh.yanx.service.res.entity.bo.ResInformationConditionBo;
import com.fh.yanx.service.res.entity.dto.ResInformationDto;
import com.fh.yanx.service.res.entity.vo.ResInformationVo;
import com.fh.yanx.service.res.service.IResInformationService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资讯
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
@RestController
@Validated
public class ResInformationController implements ResInformationApi {

    @Autowired
    private IResInformationService resInformationService;

    /**
     * 查询资讯分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @Override
    public AjaxResult<PageInfo<ResInformationVo>> getResInformationPageListByCondition(@RequestBody ResInformationConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResInformationVo> pageInfo = new PageInfo<>(resInformationService.getResInformationListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询资讯列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @Override
    public AjaxResult<List<ResInformationVo>> getResInformationListByCondition(@RequestBody ResInformationConditionBo condition) {
        List<ResInformationVo> list = resInformationService.getResInformationListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增资讯
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @Override
    public AjaxResult addResInformation(@Validated @RequestBody ResInformationBo resInformationBo) {
        return resInformationService.addResInformation(resInformationBo);
    }

    /**
     * 修改资讯
     *
     * @param resInformationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @Override
    public AjaxResult updateResInformation(@Validated @RequestBody ResInformationBo resInformationBo) {
        if (null == resInformationBo.getInformationId()) {
            return AjaxResult.fail("资讯id不能为空");
        }
        return resInformationService.updateResInformation(resInformationBo);
    }

    /**
     * 查询资讯详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @Override
    public AjaxResult<ResInformationVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("资讯id不能为空");
        }
        ResInformationVo vo = resInformationService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除资讯
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResInformationDto resInformationDto = new ResInformationDto();
        resInformationDto.setInformationId(id);
        resInformationDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resInformationService.updateById(resInformationDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
