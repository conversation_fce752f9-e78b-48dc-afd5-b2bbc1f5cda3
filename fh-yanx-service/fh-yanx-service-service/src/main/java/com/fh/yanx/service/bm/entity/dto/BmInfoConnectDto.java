package com.fh.yanx.service.bm.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 报名活动沟通确认表（本表只有新增记录）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bm_info_connect")
public class BmInfoConnectDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 活动报名记录id
	 */
	@TableField("info_id")
	private Long infoId;

	/**
	 * 确认状态：1待确认(本表不会出现该状态的数据)，2沟通中，3已确认，4已拒绝
	 */
	@TableField("submit_type")
	private Integer submitType;

	/**
	 * 沟通内容
	 */
	@TableField("connect_content")
	private String connectContent;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
