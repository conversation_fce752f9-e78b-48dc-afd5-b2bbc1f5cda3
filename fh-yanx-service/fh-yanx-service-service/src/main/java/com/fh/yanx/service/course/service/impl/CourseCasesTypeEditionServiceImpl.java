package com.fh.yanx.service.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fh.yanx.service.course.entity.dto.CourseCasesInfoEditionDto;
import com.fh.yanx.service.course.entity.dto.PCourseCasesInfoDto;
import com.fh.yanx.service.course.entity.dto.PCourseCasesTypeDto;
import com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseCasesTypeEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesTypeEditionVo;
import com.fh.yanx.service.course.service.ICourseCasesTypeEditionService;
import com.fh.yanx.service.course.mapper.CourseCasesTypeEditionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 校本课程案例类型版本记录接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
@Service
public class CourseCasesTypeEditionServiceImpl extends ServiceImpl<CourseCasesTypeEditionMapper, CourseCasesTypeEditionDto> implements ICourseCasesTypeEditionService {

	@Resource
	private CourseCasesTypeEditionMapper courseCasesTypeEditionMapper;
	
    @Override
	public List<CourseCasesTypeEditionVo> getCourseCasesTypeEditionListByCondition(CourseCasesTypeEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseCasesTypeEditionMapper.getCourseCasesTypeEditionListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseCasesTypeEdition(CourseCasesTypeEditionBo courseCasesTypeEditionBo) {
		CourseCasesTypeEditionDto courseCasesTypeEdition = new CourseCasesTypeEditionDto();
		BeanUtils.copyProperties(courseCasesTypeEditionBo, courseCasesTypeEdition);
		courseCasesTypeEdition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseCasesTypeEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseCasesTypeEdition(CourseCasesTypeEditionBo courseCasesTypeEditionBo) {
		CourseCasesTypeEditionDto courseCasesTypeEdition = new CourseCasesTypeEditionDto();
		BeanUtils.copyProperties(courseCasesTypeEditionBo, courseCasesTypeEdition);
		if(updateById(courseCasesTypeEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CourseCasesTypeEditionVo getCourseCasesTypeEditionByCondition(CourseCasesTypeEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return courseCasesTypeEditionMapper.getCourseCasesTypeEditionByCondition(condition);
	}

	@Override
	public void saveCourseTypeEdition(Long casesEditionId, List<CourseCasesTypeEditionBo> courseCasesTypeEditionBos) {
		// 历史版本，仅保存
		if (CollectionUtils.isEmpty(courseCasesTypeEditionBos)) {
			return;
		}
		// 新增
		List<CourseCasesTypeEditionDto> courseCasesTypeEditionDtos = courseCasesTypeEditionBos.stream().map(courseCasesTypeBo -> {
			CourseCasesTypeEditionDto courseCasesTypeEditionDto = new CourseCasesTypeEditionDto();
			BeanUtils.copyProperties(courseCasesTypeBo, courseCasesTypeEditionDto);
			courseCasesTypeEditionDto.setId(null);
			courseCasesTypeEditionDto.setCasesEditionId(casesEditionId);
			return courseCasesTypeEditionDto;
		}).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(courseCasesTypeEditionDtos)) {
			saveOrUpdateBatch(courseCasesTypeEditionDtos);
		}
	}

	@Override
	public List<String> getCasesTypeNameList(Long casesEditionId) {
		List<String> casesTypeNameList = new ArrayList<>();
		QueryWrapper<CourseCasesTypeEditionDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("cases_edition_id", casesEditionId).eq("is_delete", 0);
		List<CourseCasesTypeEditionDto> list = courseCasesTypeEditionMapper.selectList(queryWrapper);
		for (CourseCasesTypeEditionDto item : list) {
			casesTypeNameList.add(item.getName());
		}
		return casesTypeNameList;
	}
}