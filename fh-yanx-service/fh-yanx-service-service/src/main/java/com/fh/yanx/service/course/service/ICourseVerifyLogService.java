package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseVerifyLogDto;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程审核流水表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
public interface ICourseVerifyLogService extends IService<CourseVerifyLogDto> {

    List<CourseVerifyLogVo> getCourseVerifyLogListByCondition(CourseVerifyLogConditionBo condition);

	AjaxResult addCourseVerifyLog(CourseVerifyLogBo courseVerifyLogBo);

	AjaxResult updateCourseVerifyLog(CourseVerifyLogBo courseVerifyLogBo);

	AjaxResult updateCourseVerifyLog(List<CourseVerifyLogBo> courseVerifyLogBo);

	CourseVerifyLogVo getCourseVerifyLogByCondition(CourseVerifyLogConditionBo condition);

	/**
	 * 删除课程审核流水表并保存（根据：cases_id，verify_process_type，user_oid删除再增加）
	 *
	 * @param courseVerifyLogBo the course verify log bo
	 * @return boolean boolean
	 * <AUTHOR>
	 * @date 2024 -03-11 14:29:23
	 */
	boolean deleteAndSave(CourseVerifyLogBo courseVerifyLogBo);

	/**
	 * 查询登录用户课程审核记录
	 *
	 * @param condition
	 * @return
	 */
	List<CourseVerifyLogDto> getCurrentUserCourseVerifyLogList(PCourseCasesConditionBo condition);
}

