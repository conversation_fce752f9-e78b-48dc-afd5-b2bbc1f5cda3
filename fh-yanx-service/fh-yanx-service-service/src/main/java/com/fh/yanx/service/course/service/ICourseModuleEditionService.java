package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseModuleEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleEditionVo;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程模块版本记录表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:17
 */
public interface ICourseModuleEditionService extends IService<CourseModuleEditionDto> {

    List<CourseModuleEditionVo> getCourseModuleEditionListByCondition(CourseModuleEditionConditionBo condition);

	AjaxResult addCourseModuleEdition(CourseModuleEditionBo courseModuleEditionBo);

	AjaxResult updateCourseModuleEdition(CourseModuleEditionBo courseModuleEditionBo);

	CourseModuleEditionVo getCourseModuleEditionByCondition(CourseModuleEditionConditionBo condition);

	/**
	 * 保存课程模块，保存后设置id到Bo里面
	 *
	 * @param casesEditionId the cases id
	 * @param courseModuleType the course module type
	 * @param courseModuleBoList the course module bo list
	 * <AUTHOR>
	 * @date 2023 -08-18 16:52:05
	 */
	void saveCourseModuleEdition(Long casesEditionId, Integer courseModuleType, List<CourseModuleBo> courseModuleBoList);

	/**
	 * 根据条件查询课程模块内容，可选是否查询附件
	 *
	 * @param condition the condition
	 * @param isQueryAttachment 是否查询附件
	 * @return course module list by condition with attachment
	 */
	List<CourseModuleEditionVo> getCourseModuleListByConditionWithAttachment(CourseModuleEditionConditionBo condition,
																	  boolean isQueryAttachment);
}

