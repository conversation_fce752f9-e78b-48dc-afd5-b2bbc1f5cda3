package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.dto.CourseCasesGradeEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesGradeEditionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本课程案例年级版本记录接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:00
 */
public interface ICourseCasesGradeEditionService extends IService<CourseCasesGradeEditionDto> {

    List<CourseCasesGradeEditionVo> getCourseCasesGradeEditionListByCondition(CourseCasesGradeEditionConditionBo condition);

	AjaxResult addCourseCasesGradeEdition(CourseCasesGradeEditionBo courseCasesGradeEditionBo);

	AjaxResult updateCourseCasesGradeEdition(CourseCasesGradeEditionBo courseCasesGradeEditionBo);

	CourseCasesGradeEditionVo getCourseCasesGradeEditionByCondition(CourseCasesGradeEditionConditionBo condition);

	/**
	 * 保存年级信息，保存后设置id到Bo里面
	 *
	 * @param casesEditionId the cases id
	 * @param courseCasesGradeEditionBos the course cases grade bo list
	 * <AUTHOR>
	 * @date 2023 -08-18 16:52:05
	 */
	void saveCourseGradeEdition(Long casesEditionId, List<CourseCasesGradeEditionBo> courseCasesGradeEditionBos);

}

