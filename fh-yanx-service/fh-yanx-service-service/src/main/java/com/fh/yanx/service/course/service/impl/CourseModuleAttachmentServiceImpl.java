package com.fh.yanx.service.course.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.yanx.service.course.entity.dto.CourseModuleDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentDto;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo;
import com.fh.yanx.service.course.mapper.CourseModuleAttachmentMapper;
import com.fh.yanx.service.course.service.ICourseModuleAttachmentService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 课程资源或成果样例附件表-模块附件接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Service
public class CourseModuleAttachmentServiceImpl extends
    ServiceImpl<CourseModuleAttachmentMapper, CourseModuleAttachmentDto> implements ICourseModuleAttachmentService {

    @Resource
    private CourseModuleAttachmentMapper courseModuleAttachmentMapper;

    @Override
    public List<CourseModuleAttachmentVo>
        getCourseModuleAttachmentListByCondition(CourseModuleAttachmentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return courseModuleAttachmentMapper.getCourseModuleAttachmentListByCondition(condition);
    }

    @Override
    public AjaxResult addCourseModuleAttachment(CourseModuleAttachmentBo courseModuleAttachmentBo) {
        CourseModuleAttachmentDto courseModuleAttachment = new CourseModuleAttachmentDto();
        BeanUtils.copyProperties(courseModuleAttachmentBo, courseModuleAttachment);
        courseModuleAttachment.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(courseModuleAttachment)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCourseModuleAttachment(CourseModuleAttachmentBo courseModuleAttachmentBo) {
        CourseModuleAttachmentDto courseModuleAttachment = new CourseModuleAttachmentDto();
        BeanUtils.copyProperties(courseModuleAttachmentBo, courseModuleAttachment);
        if (updateById(courseModuleAttachment)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public CourseModuleAttachmentVo getCourseModuleAttachmentByCondition(CourseModuleAttachmentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        CourseModuleAttachmentVo vo = courseModuleAttachmentMapper.getCourseModuleAttachmentByCondition(condition);
        return vo;
    }

    @Override
    public void saveCourseModuleAttachment(List<Long> courseModuleIds,
        List<CourseModuleAttachmentBo> courseModuleAttachmentBoList) {
        if (CollectionUtils.isEmpty(courseModuleIds)) {
            return;
        }
        // 删除
        LambdaUpdateWrapper<CourseModuleAttachmentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CourseModuleAttachmentDto::getCourseModuleId, courseModuleIds);
        updateWrapper.set(CourseModuleAttachmentDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        update(updateWrapper);

        // 新增或更新
        List<CourseModuleAttachmentDto> courseModuleAttachmentDtos =
            courseModuleAttachmentBoList.stream().map(courseModuleAttachmentBo -> {
                CourseModuleAttachmentDto courseModuleAttachmentDto = new CourseModuleAttachmentDto();
                BeanUtils.copyProperties(courseModuleAttachmentBo, courseModuleAttachmentDto);
                return courseModuleAttachmentDto;
            }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(courseModuleAttachmentDtos)) {
            saveOrUpdateBatch(courseModuleAttachmentDtos);
        }
    }
}