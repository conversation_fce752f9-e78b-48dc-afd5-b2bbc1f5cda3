package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.service.enums.ResTopicProcessEnum;
import com.fh.yanx.service.enums.TopicTodoType;
import com.fh.yanx.service.event.CompleteTodoEvent;
import com.fh.yanx.service.event.CreateTodoEvent;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;
import com.fh.yanx.service.res.service.IResTopicService;
import com.fh.yanx.service.res.service.IResTopicTeacherService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicStudentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;
import com.fh.yanx.service.res.service.IResTopicStudentService;
import com.fh.yanx.service.res.mapper.ResTopicStudentMapper;
import com.light.core.entity.AjaxResult;

/**
 * 课题组成员表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Service
public class ResTopicStudentServiceImpl extends ServiceImpl<ResTopicStudentMapper, ResTopicStudentDto>
        implements IResTopicStudentService {

    @Resource
    private ResTopicStudentMapper resTopicStudentMapper;
    @Resource
    private IResTopicService resTopicService;
    @Resource
    private IResTopicTeacherService resTopicTeacherService;

    @Override
    public List<ResTopicStudentVo> getResTopicStudentListByCondition(ResTopicStudentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicStudentMapper.getResTopicStudentListByCondition(condition);
    }

    @Override
    public List<String> getResTopicStudentOidListByTopicId(Long topicId) {
        List<String> oids = new ArrayList<>();
        ResTopicStudentConditionBo condition = new ResTopicStudentConditionBo();
        condition.setTopicId(topicId);
        List<ResTopicStudentVo> resTopicStudentVos = getResTopicStudentListByCondition(condition);
        if (CollectionUtils.isNotEmpty(resTopicStudentVos)) {
            oids = resTopicStudentVos.stream().map(ResTopicStudentVo::getUserOid).collect(Collectors.toList());
        }
        return oids;
    }

    @Override
    public AjaxResult addResTopicStudent(ResTopicStudentBo resTopicStudentBo) {
        ResTopicStudentDto resTopicStudent = new ResTopicStudentDto();
        BeanUtils.copyProperties(resTopicStudentBo, resTopicStudent);
        resTopicStudent.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(resTopicStudent)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateResTopicStudent(ResTopicStudentBo resTopicStudentBo) {
        ResTopicStudentDto resTopicStudent = new ResTopicStudentDto();
        BeanUtils.copyProperties(resTopicStudentBo, resTopicStudent);
        updateById(resTopicStudent);
        if (StatusEnum.YES.getCode().equals(resTopicStudentBo.getIsAddEvaluate())) {
            ResTopicStudentDto teacherDto = this.getById(resTopicStudentBo.getId());
            ResTopicDto byId = resTopicService.getById(teacherDto.getTopicId());
            if (null == byId) {
                return AjaxResult.fail("课题不存在");
            }
            // 修改课题状态
            ResTopicBo resTopicBo = new ResTopicBo();
            resTopicBo.setTopicId(byId.getTopicId());
            resTopicBo.setTopicProcess(ResTopicProcessEnum.STUDENT_EVALUATE.getCode());
            resTopicService.updateResTopicStatus(resTopicBo);
            // 成代办事件及流转记录
            List<String> oidListByTopicId = resTopicTeacherService.getResTopicTeacherOidListByTopicId(byId.getTopicId());
            resTopicService.publishEvent(byId.getTopicId(), ResTopicProcessEnum.STUDENT_EVALUATE.getCode(),
                    byId.getTopicName(), oidListByTopicId, resTopicStudentBo.getCurrentUserOid(),
                    resTopicStudentBo.getCurrentUserOid());
        }
        return AjaxResult.success("保存成功");

    }

    @Override
    public ResTopicStudentVo getDetail(Long id) {
        ResTopicStudentConditionBo condition = new ResTopicStudentConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ResTopicStudentVo> list = resTopicStudentMapper.getResTopicStudentListByCondition(condition);
        ResTopicStudentVo vo = new ResTopicStudentVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

}