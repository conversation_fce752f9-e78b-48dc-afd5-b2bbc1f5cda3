package com.fh.yanx.service;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableFeignClients(basePackages = {"com.fh.yanx", "com.light", "com.fh.app.role"})
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.fh.yanx.**.**.*", "com.light.**.**.*", "com.fh.app.role.**.**.*"})
@MapperScan({"com.fh.yanx.**.mapper"})
@EnableSwagger2
@EnableKnife4j
public class YanxServiceServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(YanxServiceServiceApplication.class, args);
    }

}
