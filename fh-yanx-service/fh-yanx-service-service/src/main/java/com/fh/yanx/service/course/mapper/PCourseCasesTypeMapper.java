package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.PCourseCasesTypeDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeConditionBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesTypeVo;

/**
 * 校本课程案例类型Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesTypeMapper extends BaseMapper<PCourseCasesTypeDto> {

	List<PCourseCasesTypeVo> getPCourseCasesTypeListByCondition(PCourseCasesTypeConditionBo condition);

	PCourseCasesTypeVo getPCourseCasesTypeByCondition(PCourseCasesTypeConditionBo condition);

}
