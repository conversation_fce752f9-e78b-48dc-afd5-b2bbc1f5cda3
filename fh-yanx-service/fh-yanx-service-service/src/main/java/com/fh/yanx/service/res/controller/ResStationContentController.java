package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResStationContentApi;
import com.fh.yanx.service.res.entity.bo.ResStationContentBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentConditionBo;
import com.fh.yanx.service.res.entity.dto.ResStationContentDto;
import com.fh.yanx.service.res.entity.vo.ResStationContentVo;
import com.fh.yanx.service.res.service.IResStationContentService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工作站具体内容
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
public class ResStationContentController implements ResStationContentApi {

    @Autowired
    private IResStationContentService resStationContentService;

    /**
     * 查询工作站具体内容分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<PageInfo<ResStationContentVo>> getResStationContentPageListByCondition(@RequestBody ResStationContentConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResStationContentVo> pageInfo = new PageInfo<>(resStationContentService.getResStationContentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询工作站具体内容列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<List<ResStationContentVo>> getResStationContentListByCondition(@RequestBody ResStationContentConditionBo condition) {
        List<ResStationContentVo> list = resStationContentService.getResStationContentListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增工作站具体内容
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult addResStationContent(@Validated @RequestBody ResStationContentBo resStationContentBo) {
        return resStationContentService.addResStationContent(resStationContentBo);
    }

    /**
     * 修改工作站具体内容
     *
     * @param resStationContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult updateResStationContent(@Validated @RequestBody ResStationContentBo resStationContentBo) {
        if (null == resStationContentBo.getStationContentId()) {
            return AjaxResult.fail("工作站具体内容id不能为空");
        }
        return resStationContentService.updateResStationContent(resStationContentBo);
    }

    /**
     * 查询工作站具体内容详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult<ResStationContentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("工作站具体内容id不能为空");
        }
        ResStationContentVo vo = resStationContentService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除工作站具体内容
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResStationContentDto resStationContentDto = new ResStationContentDto();
        resStationContentDto.setStationContentId(id);
        resStationContentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resStationContentService.updateById(resStationContentDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
