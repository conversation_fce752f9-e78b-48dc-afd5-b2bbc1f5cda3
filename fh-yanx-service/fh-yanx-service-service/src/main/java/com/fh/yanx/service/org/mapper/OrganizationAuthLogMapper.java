package com.fh.yanx.service.org.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.org.entity.dto.OrganizationAuthLogDto;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogConditionBo;
import com.fh.yanx.service.org.entity.vo.OrganizationAuthLogVo;

/**
 * 组织认证日志记录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
public interface OrganizationAuthLogMapper extends BaseMapper<OrganizationAuthLogDto> {

	List<OrganizationAuthLogVo> getOrganizationAuthLogListByCondition(OrganizationAuthLogConditionBo condition);

	OrganizationAuthLogVo getOrganizationAuthLogByCondition(OrganizationAuthLogConditionBo condition);

}
