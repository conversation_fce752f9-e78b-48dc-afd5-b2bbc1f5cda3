package com.fh.yanx.service.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ResTopicProcessEnum {

    /**
     * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定,9修改后暂存
     */
    TEMP(1,"暂存"),
    SUBMIT_EXAMINE(2,"提交、教师审批中"),
    TEACHER_REJECT(3,"教师审核驳回"),
    TEACHER_PASS(4,"教师审核通过"),
    SUBMIT_ATTACHMENT(5,"提交材料"),
    STUDENT_EVALUATE(6,"学生评定"),
    TEACHER_EVALUATE(7,"教师评定"),
    SCHOOL_EVALUATE(8,"学校评定"),
    UPDATE_TEMP(9,"修改后暂存"),
    STU_ATTACHMENT(10,"提交研究材料"),
    REP_ATTACHMENT(11,"提交论文、答辩材料")
    ;
    private Integer code;
    private String value;
}
