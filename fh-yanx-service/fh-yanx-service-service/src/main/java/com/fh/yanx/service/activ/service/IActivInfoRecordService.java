package com.fh.yanx.service.activ.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordDto;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 新时代文化校园活动内容表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
public interface IActivInfoRecordService extends IService<ActivInfoRecordDto> {

    List<ActivInfoRecordVo> getActivInfoRecordListByCondition(ActivInfoRecordConditionBo condition);

	AjaxResult addActivInfoRecord(ActivInfoRecordBo activInfoRecordBo);

	AjaxResult updateActivInfoRecord(ActivInfoRecordBo activInfoRecordBo);

	ActivInfoRecordVo getActivInfoRecordByCondition(ActivInfoRecordConditionBo condition);

	boolean deleteAndAddActivInfoRecords(ActivInfoBo activInfoBo);

}

