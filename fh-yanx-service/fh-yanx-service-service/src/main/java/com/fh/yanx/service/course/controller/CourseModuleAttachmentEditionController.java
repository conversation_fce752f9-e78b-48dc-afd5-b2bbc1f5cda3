package com.fh.yanx.service.course.controller;

import com.fh.yanx.service.course.api.CourseModuleAttachmentEditionApi;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentEditionDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentEditionVo;
import com.fh.yanx.service.course.service.ICourseModuleAttachmentEditionService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 课程资源或成果样例附件表-模块附件版本表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:25
 */
@RestController
@Validated
public class CourseModuleAttachmentEditionController implements CourseModuleAttachmentEditionApi{
	
    @Autowired
    private ICourseModuleAttachmentEditionService courseModuleAttachmentEditionService;

    /**
     * 查询课程资源或成果样例附件表-模块附件版本表分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
    @Override
    public AjaxResult<PageInfo<CourseModuleAttachmentEditionVo>> getCourseModuleAttachmentEditionPageListByCondition(@RequestBody CourseModuleAttachmentEditionConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseModuleAttachmentEditionVo> pageInfo = new PageInfo<>(courseModuleAttachmentEditionService.getCourseModuleAttachmentEditionListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程资源或成果样例附件表-模块附件版本表列表
	 * <AUTHOR>
	 * @date 2024-12-05 10:40:25
	 */
	@Override
	public AjaxResult<List<CourseModuleAttachmentEditionVo>> getCourseModuleAttachmentEditionListByCondition(@RequestBody CourseModuleAttachmentEditionConditionBo condition){
		List<CourseModuleAttachmentEditionVo> list = courseModuleAttachmentEditionService.getCourseModuleAttachmentEditionListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程资源或成果样例附件表-模块附件版本表
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
	@Override
    public AjaxResult addCourseModuleAttachmentEdition(@Validated @RequestBody CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo){
		return courseModuleAttachmentEditionService.addCourseModuleAttachmentEdition(courseModuleAttachmentEditionBo);
    }

    /**
	 * 修改课程资源或成果样例附件表-模块附件版本表
	 * @param courseModuleAttachmentEditionBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
	 */
	@Override
	public AjaxResult updateCourseModuleAttachmentEdition(@Validated @RequestBody CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo) {
		if(null == courseModuleAttachmentEditionBo.getCourseModuleAttachmentEditionId()) {
			return AjaxResult.fail("课程资源或成果样例附件表-模块附件版本表id不能为空");
		}
		return courseModuleAttachmentEditionService.updateCourseModuleAttachmentEdition(courseModuleAttachmentEditionBo);
	}

	/**
	 * 查询课程资源或成果样例附件表-模块附件版本表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
	 */
	@Override
	public AjaxResult<CourseModuleAttachmentEditionVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程资源或成果样例附件表-模块附件版本表id不能为空");
		}
		CourseModuleAttachmentEditionConditionBo condition = new CourseModuleAttachmentEditionConditionBo();
		condition.setCourseModuleAttachmentEditionId(id);
		CourseModuleAttachmentEditionVo vo = courseModuleAttachmentEditionService.getCourseModuleAttachmentEditionByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程资源或成果样例附件表-模块附件版本表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseModuleAttachmentEditionDto courseModuleAttachmentEditionDto = new CourseModuleAttachmentEditionDto();
		courseModuleAttachmentEditionDto.setCourseModuleAttachmentEditionId(id);
		courseModuleAttachmentEditionDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseModuleAttachmentEditionService.updateById(courseModuleAttachmentEditionDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
