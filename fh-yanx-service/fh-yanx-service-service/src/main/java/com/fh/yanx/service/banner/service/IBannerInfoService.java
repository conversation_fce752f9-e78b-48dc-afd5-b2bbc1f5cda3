package com.fh.yanx.service.banner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.banner.entity.bo.BannerInfoBo;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.fh.yanx.service.banner.entity.dto.BannerInfoDto;
import com.fh.yanx.service.banner.entity.vo.BannerInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 新时代文化校园banner信息表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
public interface IBannerInfoService extends IService<BannerInfoDto> {

    List<BannerInfoVo> getBannerListByCondition(BannerInfoConditionBo condition);

	AjaxResult addBanner(BannerInfoBo bannerInfoBo);

	AjaxResult updateBanner(BannerInfoBo bannerInfoBo);

	BannerInfoVo getBannerByCondition(BannerInfoConditionBo condition);

}

