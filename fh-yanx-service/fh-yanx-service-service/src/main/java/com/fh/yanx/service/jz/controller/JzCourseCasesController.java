package com.fh.yanx.service.jz.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.consts.ConstantsInteger;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.dto.PCategoryDto;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.fh.yanx.service.course.service.IPCategoryService;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesInfoVo;
import com.fh.yanx.service.jz.service.IJzCourseCasesInfoService;
import com.fh.yanx.service.jz.service.IJzCourseCasesTypeService;
import com.light.core.constants.SystemConstants;
import com.light.user.account.entity.vo.LoginAccountVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.jz.api.JzCourseCasesApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesConditionBo;
import com.fh.yanx.service.jz.entity.dto.JzCourseCasesDto;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo;
import com.fh.yanx.service.jz.service.IJzCourseCasesService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;

/**
 * 金中-校本课程案例
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@RestController
@Validated
public class JzCourseCasesController implements JzCourseCasesApi{
	
    @Resource
    private IJzCourseCasesService jzCourseCasesService;
	@Resource
	private IJzCourseCasesTypeService jzCourseCasesTypeService;
	@Resource
	private IPCategoryService pCategoryService;
	@Autowired
	private BaseDataService baseDataService;
	@Resource
	private IJzCourseCasesInfoService jzCourseCasesInfoService;

    /**
     * 查询金中-校本课程案例分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @Override
    public AjaxResult<PageInfo<JzCourseCasesVo>> getJzCourseCasesPageListByCondition(@RequestBody JzCourseCasesConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<JzCourseCasesVo> pageInfo = new PageInfo<>(jzCourseCasesService.getJzCourseCasesListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询金中-校本课程案例列表
	 * <AUTHOR>
	 * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<List<JzCourseCasesVo>> getJzCourseCasesListByCondition(@RequestBody JzCourseCasesConditionBo condition){
		List<JzCourseCasesVo> list = jzCourseCasesService.getJzCourseCasesListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增金中-校本课程案例
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
	@Override
    public AjaxResult addJzCourseCases(@Validated @RequestBody JzCourseCasesBo jzCourseCasesBo){
		return jzCourseCasesService.addJzCourseCases(jzCourseCasesBo);
    }

    /**
	 * 修改金中-校本课程案例
	 * @param jzCourseCasesBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult updateJzCourseCases(@Validated @RequestBody JzCourseCasesBo jzCourseCasesBo) {
		if(null == jzCourseCasesBo.getId()) {
			return AjaxResult.fail("金中-校本课程案例id不能为空");
		}
		return jzCourseCasesService.updateJzCourseCases(jzCourseCasesBo);
	}

	/**
	 * 查询金中-校本课程案例详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult<JzCourseCasesVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("金中-校本课程案例id不能为空");
		}
		JzCourseCasesConditionBo condition = new JzCourseCasesConditionBo();
		condition.setId(id);
		JzCourseCasesVo vo = jzCourseCasesService.getJzCourseCasesByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除金中-校本课程案例
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		JzCourseCasesDto jzCourseCasesDto = new JzCourseCasesDto();
		jzCourseCasesDto.setId(id);
		jzCourseCasesDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(jzCourseCasesService.updateById(jzCourseCasesDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	@Override
	public AjaxResult getHomeList(JzCourseCasesConditionBo conditionBo) {
		// 分页和不分页都支持
		if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())
				|| Integer.valueOf(ConstantsInteger.NUM_0).equals(conditionBo.getPageNo())) {
			List<JzCourseCasesVo> pCourseCasesVos = jzCourseCasesService.getHomeList(conditionBo);
			// 处理返回结果
			convertHomeResultList(pCourseCasesVos);
			Map<String, Object> map = new HashMap<>();
			map.put("list", pCourseCasesVos);
			map.put("total", pCourseCasesVos.size());
			return AjaxResult.success(map);
		} else {
			PageHelper.startPage(conditionBo.getPageNo(), conditionBo.getPageSize(), conditionBo.getOrderBy());
			List<JzCourseCasesVo> jzCourseCasesVos = jzCourseCasesService.getHomeList(conditionBo);
			// 处理返回结果
			convertHomeResultList(jzCourseCasesVos);
			PageInfo<JzCourseCasesVo> pageInfo = new PageInfo<>(jzCourseCasesVos);
			return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), conditionBo.getPageNo(),
					conditionBo.getPageSize());
		}
	}

	@Override
	public AjaxResult homeDetail(Long casesId) {
		if (casesId == null) {
			return AjaxResult.fail("参数错误！");
		}
		JzCourseCasesVo detail = jzCourseCasesService.getHomeDetail(casesId);
		convertHomeResultOne(detail);
		return AjaxResult.success(detail);
	}

	/**
	 * 处理返回结果的数据
	 *
	 * @param pCourseCasesVos the p course cases vos
	 * <AUTHOR>
	 * @date 2023 -08-14 13:52:20
	 */
	private void convertHomeResultList(List<JzCourseCasesVo> jzCourseCasesVos) {
		if (CollectionUtils.isEmpty(jzCourseCasesVos)) {
			return;
		}
		for (JzCourseCasesVo jzCourseCasesVo : jzCourseCasesVos) {
			// 案例类型名称集合
			List<String> casesTypeNameList = jzCourseCasesTypeService.getCasesTypeNameList(jzCourseCasesVo.getId());
			jzCourseCasesVo.setTypeNameList(casesTypeNameList);
			// 学段翻译
			if (null != jzCourseCasesVo.getPhase()) {
				PCategoryDto pCategoryDto = pCategoryService.getById(jzCourseCasesVo.getPhase());
				jzCourseCasesVo.setPhaseName(pCategoryDto.getName());
			}
			// 年份翻译
			if (null != jzCourseCasesVo.getYear()) {
				PCategoryDto pCategoryDto = pCategoryService.getById(jzCourseCasesVo.getYear());
				jzCourseCasesVo.setYearName(pCategoryDto.getName());
			}
			// 浏览量
			if (null != jzCourseCasesVo.getViews()) {
				jzCourseCasesVo.setViewsStr(jzCourseCasesVo.getViews().toString());
				if (10000 <= jzCourseCasesVo.getViews()) {
					Double result = jzCourseCasesVo.getViews().doubleValue() / 10000;
					jzCourseCasesVo.setViewsStr(String.format("%.1f", result) + "w");
				}
			}
			// 若简介长度大于45，则后面部分展示...
			if (StringUtils.isNotEmpty(jzCourseCasesVo.getIntroduction())
					&& 45 < jzCourseCasesVo.getIntroduction().length()) {
				String str = jzCourseCasesVo.getIntroduction().substring(0, 45);
				jzCourseCasesVo.setIntroduction(str + "...");
			}
		}
	}

	/**
	 * 处理返回结果的数据
	 *
	 * @param pCourseCasesVos the p course cases vos
	 * <AUTHOR>
	 * @date 2023 -08-14 13:52:20
	 */
	private void convertHomeResultOne(JzCourseCasesVo jzCourseCasesVo) {
		if (jzCourseCasesVo == null) {
			return;
		}
		LoginAccountVo currentUser = baseDataService.getCurrentUser();
		// 案例类型名称集合
		List<String> casesTypeNameList = jzCourseCasesTypeService.getCasesTypeNameList(jzCourseCasesVo.getId());
		jzCourseCasesVo.setTypeNameList(casesTypeNameList);
		// 学段翻译
		if (null != jzCourseCasesVo.getPhase()) {
			PCategoryDto pCategoryDto = pCategoryService.getById(jzCourseCasesVo.getPhase());
			jzCourseCasesVo.setPhaseName(pCategoryDto.getName());
		}
		// 年份翻译
		if (null != jzCourseCasesVo.getYear()) {
			PCategoryDto pCategoryDto = pCategoryService.getById(jzCourseCasesVo.getYear());
			jzCourseCasesVo.setYearName(pCategoryDto.getName());
		}
		// 浏览量
		if (null != jzCourseCasesVo.getViews()) {
			jzCourseCasesVo.setViewsStr(jzCourseCasesVo.getViews().toString());
			if (10000 <= jzCourseCasesVo.getViews()) {
				Double result = jzCourseCasesVo.getViews().doubleValue() / 10000;
				jzCourseCasesVo.setViewsStr(String.format("%.1f", result) + "w");
			}
		}
		// 若已登录状态，则展示案例详细信息
		if (null != currentUser) {
			JzCourseCasesInfoVo jzCourseCasesInfoVo = jzCourseCasesInfoService.infoDetail(jzCourseCasesVo.getId());
			jzCourseCasesVo.setJzCourseCasesInfoVo(jzCourseCasesInfoVo);
			CourseModuleConditionBo condition = new CourseModuleConditionBo();
			condition.setCasesId(jzCourseCasesVo.getId());
			condition.setPageNo(SystemConstants.NO_PAGE);
		}
	}
}
