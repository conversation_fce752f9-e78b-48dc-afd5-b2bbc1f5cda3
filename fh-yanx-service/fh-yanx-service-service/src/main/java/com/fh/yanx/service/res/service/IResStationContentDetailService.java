package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResStationContentDetailDto;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentDetailVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 工作站具体内容详情接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResStationContentDetailService extends IService<ResStationContentDetailDto> {

    List<ResStationContentDetailVo> getResStationContentDetailListByCondition(ResStationContentDetailConditionBo condition);

	AjaxResult addResStationContentDetail(ResStationContentDetailBo resStationContentDetailBo);

	AjaxResult updateResStationContentDetail(ResStationContentDetailBo resStationContentDetailBo);

	ResStationContentDetailVo getDetail(Long id);

}

