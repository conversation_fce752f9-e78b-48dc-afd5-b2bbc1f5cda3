package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResStationContentDto;
import com.fh.yanx.service.res.entity.bo.ResStationContentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentVo;
import com.fh.yanx.service.res.service.IResStationContentService;
import com.fh.yanx.service.res.mapper.ResStationContentMapper;
import com.light.core.entity.AjaxResult;
/**
 * 工作站具体内容接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Service
public class ResStationContentServiceImpl extends ServiceImpl<ResStationContentMapper, ResStationContentDto> implements IResStationContentService {

	@Resource
	private ResStationContentMapper resStationContentMapper;
	
    @Override
	public List<ResStationContentVo> getResStationContentListByCondition(ResStationContentConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resStationContentMapper.getResStationContentListByCondition(condition);
	}

	@Override
	public AjaxResult addResStationContent(ResStationContentBo resStationContentBo) {
		ResStationContentDto resStationContent = new ResStationContentDto();
		BeanUtils.copyProperties(resStationContentBo, resStationContent);
		resStationContent.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resStationContent)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResStationContent(ResStationContentBo resStationContentBo) {
		ResStationContentDto resStationContent = new ResStationContentDto();
		BeanUtils.copyProperties(resStationContentBo, resStationContent);
		if(updateById(resStationContent)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResStationContentVo getDetail(Long id) {
		ResStationContentConditionBo condition = new ResStationContentConditionBo();
		condition.setStationContentId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResStationContentVo> list = resStationContentMapper.getResStationContentListByCondition(condition);
		ResStationContentVo vo = new ResStationContentVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}