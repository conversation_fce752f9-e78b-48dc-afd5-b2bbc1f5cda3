package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResTopicSubjectDto;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicSubjectVo;
import com.fh.yanx.service.res.service.IResTopicSubjectService;
import com.fh.yanx.service.res.mapper.ResTopicSubjectMapper;
import com.light.core.entity.AjaxResult;
/**
 * 关联科目表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Service
public class ResTopicSubjectServiceImpl extends ServiceImpl<ResTopicSubjectMapper, ResTopicSubjectDto> implements IResTopicSubjectService {

	@Resource
	private ResTopicSubjectMapper resTopicSubjectMapper;
	
    @Override
	public List<ResTopicSubjectVo> getResTopicSubjectListByCondition(ResTopicSubjectConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resTopicSubjectMapper.getResTopicSubjectListByCondition(condition);
	}

	@Override
	public AjaxResult addResTopicSubject(ResTopicSubjectBo resTopicSubjectBo) {
		ResTopicSubjectDto resTopicSubject = new ResTopicSubjectDto();
		BeanUtils.copyProperties(resTopicSubjectBo, resTopicSubject);
		resTopicSubject.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resTopicSubject)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResTopicSubject(ResTopicSubjectBo resTopicSubjectBo) {
		ResTopicSubjectDto resTopicSubject = new ResTopicSubjectDto();
		BeanUtils.copyProperties(resTopicSubjectBo, resTopicSubject);
		if(updateById(resTopicSubject)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResTopicSubjectVo getDetail(Long id) {
		ResTopicSubjectConditionBo condition = new ResTopicSubjectConditionBo();
		condition.setId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResTopicSubjectVo> list = resTopicSubjectMapper.getResTopicSubjectListByCondition(condition);
		ResTopicSubjectVo vo = new ResTopicSubjectVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}