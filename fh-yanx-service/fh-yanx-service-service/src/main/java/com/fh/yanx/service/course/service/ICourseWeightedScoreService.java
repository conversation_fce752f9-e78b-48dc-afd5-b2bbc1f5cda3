package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseWeightedScoreDto;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreBo;
import com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程加权分表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
public interface ICourseWeightedScoreService extends IService<CourseWeightedScoreDto> {

    List<CourseWeightedScoreVo> getCourseWeightedScoreListByCondition(CourseWeightedScoreConditionBo condition);

	AjaxResult addCourseWeightedScore(CourseWeightedScoreBo courseWeightedScoreBo);

	AjaxResult updateCourseWeightedScore(CourseWeightedScoreBo courseWeightedScoreBo);

	CourseWeightedScoreVo getCourseWeightedScoreByCondition(CourseWeightedScoreConditionBo condition);

}

