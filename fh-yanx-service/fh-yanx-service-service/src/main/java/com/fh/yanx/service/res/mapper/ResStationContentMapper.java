package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResStationContentDto;
import com.fh.yanx.service.res.entity.bo.ResStationContentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentVo;

/**
 * 工作站具体内容Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationContentMapper extends BaseMapper<ResStationContentDto> {

	List<ResStationContentVo> getResStationContentListByCondition(ResStationContentConditionBo condition);

}
