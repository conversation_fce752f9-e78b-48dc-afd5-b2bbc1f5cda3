package com.fh.yanx.service.order.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 订单表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activ_order")
public class ActivOrderDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@TableId(value = "order_id", type = IdType.AUTO)
	private Long orderId;

	/**
	 * 用户oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 订单编号
	 */
	@TableField("order_number")
	private String orderNumber;

	/**
	 * 订单名称
	 */
	@TableField("order_name")
	private String orderName;

	/**
	 * 订单类型：1消费、2充值、3管理员修改
	 */
	@TableField("order_type")
	private Integer orderType;

	/**
	 * 交易流水号
	 */
	@TableField("transaction_id")
	private String transactionId;

	/**
	 * 订单状态（ 1：待支付、2：已付款、3：待退款、4：已退款、5：退款驳回、 6：已取消、7：已发货、8：超时未支付)
	 */
	@TableField("order_state")
	private Integer orderState;

	/**
	 * 支付方式：1支付宝、2微信、3苹果、4管理员修改
	 */
	@TableField("pay_mode")
	private Integer payMode;

	/**
	 * 商品类型（1-活动报名bm_info）
	 */
	@TableField("goods_type")
	private Integer goodsType;

	/**
	 * 商品id，根据订单类型记录商品id
	 */
	@TableField("goods_id")
	private Long goodsId;

	/**
	 * 商品数量
	 */
	@TableField("goods_quantity")
	private Long goodsQuantity;

	/**
	 * 地址
	 */
	@TableField("address_info")
	private String addressInfo;

	/**
	 * 终端类型（1：iOS、2：安卓、3：web、4：PAD、5：H5）
	 */
	@TableField("terminal_type")
	private Integer terminalType;

	/**
	 * 实际支付金额
	 */
	@TableField("pay_amount")
	private BigDecimal payAmount;

	/**
	 * 订单金额
	 */
	@TableField("order_amount")
	private BigDecimal orderAmount;

	/**
	 * 支付场景（1：正式、2：沙箱）
	 */
	@TableField("pay_scene")
	private Integer payScene;

	/**
	 * 结束时间
	 */
	@TableField("end_time")
	private Date endTime;

	/**
	 * 付款时间
	 */
	@TableField("pay_time")
	private Date payTime;

	/**
	 * 申请退款金额
	 */
	@TableField("apply_refund_money")
	private BigDecimal applyRefundMoney;

	/**
	 * 申请退款原因
	 */
	@TableField("apply_refund_reason")
	private String applyRefundReason;

	/**
	 * 申请退款时间
	 */
	@TableField("apply_refund_time")
	private Date applyRefundTime;

	/**
	 * 实际退款时间
	 */
	@TableField("refund_time")
	private Date refundTime;

	/**
	 * 实际退款金额
	 */
	@TableField("refund_money")
	private BigDecimal refundMoney;

	/**
	 * 驳回时间
	 */
	@TableField("reject_time")
	private Date rejectTime;

	/**
	 * 取消时间
	 */
	@TableField("cancel_time")
	private Date cancelTime;

	/**
	 * 首次支付标识:  1: 是首次支付  2:非首次支付
	 */
	@TableField("first_pay")
	private Integer firstPay;

	/**
	 * 备注
	 */
	@TableField("note")
	private String note;

	/**
	 * 是否删除：0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
