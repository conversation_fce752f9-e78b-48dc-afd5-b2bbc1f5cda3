package com.fh.yanx.service.course.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.course.entity.dto.CourseCasesEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesEditionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesEditionVo;

/**
 * 课程版本表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
public interface CourseCasesEditionMapper extends BaseMapper<CourseCasesEditionDto> {

	List<CourseCasesEditionVo> getCourseCasesEditionListByCondition(CourseCasesEditionConditionBo condition);

	CourseCasesEditionVo getCourseCasesEditionByCondition(CourseCasesEditionConditionBo condition);

}
