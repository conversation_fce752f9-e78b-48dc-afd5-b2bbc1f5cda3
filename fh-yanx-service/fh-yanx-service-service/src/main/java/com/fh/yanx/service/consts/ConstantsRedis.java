package com.fh.yanx.service.consts;

/**
 * <AUTHOR>
 * @date 2023/8/4 14:53
 */
public interface ConstantsRedis {
    /**
     * 签到码生成防止重复，redis类型字符串：BM_SIGN_IN_CODE_{activId}_{code}
     */
    String BM_SIGN_IN_CODE_PREFIX = "BM_SIGN_IN_CODE";

    /**
     * 收藏，redis类型字符串：COURSE_STORE_PREFIX_{cases_id}
     */
    String COURSE_STORE_PREFIX = "COURSE_STORE_PREFIX";

    /**
     * 签到码生成防止重复缓存1分钟
     */
    long BM_SIGN_IN_CODE_EXPIRE_IN = 1L * 60;
}
