package com.fh.yanx.service.aspose;

import com.alibaba.fastjson.JSON;
import com.aspose.words.FindReplaceOptions;
import com.aspose.words.License;
import com.aspose.words.Document;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * aspose相关服务工具service
 * 
 * <AUTHOR>
 * @date 2024/3/14 11:57
 */
@Slf4j
@Service
public class AsposeService {
    private static InputStream license;

    public static final String PARAM_MATCH = "\\{[a-zA-Z]+\\}";

    /**
     * 获取license
     *
     * @return
     */
    public boolean getLicense() throws Exception {
        boolean result = false;
        license = AsposeService.class.getClassLoader().getResourceAsStream("license.xml");
        if (license != null) {
            License aposeLic = new License();
            aposeLic.setLicense(license);
            result = true;
        }
        return result;
    }

    /**
     * 替换文档中的参数
     *
     * @param paramMap the param map
     * @param doc the doc
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2024 -03-14 13:51:32
     */
    public void replaceText(Map<String, String> paramMap, Document doc) throws Exception {
        FindReplaceOptions opt = new FindReplaceOptions();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            String key = String.format("{%s}", entry.getKey());
            String value = Objects.isNull(entry.getValue()) ? "" : entry.getValue();
            doc.getRange().replace(key, value, opt);
        }
        int replace = doc.getRange().replace(Pattern.compile(PARAM_MATCH), "", opt);
        if (replace > 0) {
            log.error("替换失败，参数:{}", JSON.toJSONString(paramMap));
        }
    }

}
