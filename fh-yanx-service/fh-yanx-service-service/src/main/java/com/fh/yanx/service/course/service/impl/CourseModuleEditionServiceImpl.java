package com.fh.yanx.service.course.service.impl;

import com.fh.yanx.service.course.entity.bo.*;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentDto;
import com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentEditionDto;
import com.fh.yanx.service.course.entity.dto.CourseModuleDto;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentEditionVo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;
import com.fh.yanx.service.course.service.ICourseModuleAttachmentEditionService;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.dto.CourseModuleEditionDto;
import com.fh.yanx.service.course.entity.vo.CourseModuleEditionVo;
import com.fh.yanx.service.course.service.ICourseModuleEditionService;
import com.fh.yanx.service.course.mapper.CourseModuleEditionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 课程模块版本记录表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:17
 */
@Service
public class CourseModuleEditionServiceImpl extends ServiceImpl<CourseModuleEditionMapper, CourseModuleEditionDto> implements ICourseModuleEditionService {

	@Resource
	private CourseModuleEditionMapper courseModuleEditionMapper;
	@Resource
	private ICourseModuleAttachmentEditionService courseModuleAttachmentEditionService;
	
    @Override
	public List<CourseModuleEditionVo> getCourseModuleEditionListByCondition(CourseModuleEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseModuleEditionMapper.getCourseModuleEditionListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseModuleEdition(CourseModuleEditionBo courseModuleEditionBo) {
		CourseModuleEditionDto courseModuleEdition = new CourseModuleEditionDto();
		BeanUtils.copyProperties(courseModuleEditionBo, courseModuleEdition);
		courseModuleEdition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseModuleEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseModuleEdition(CourseModuleEditionBo courseModuleEditionBo) {
		CourseModuleEditionDto courseModuleEdition = new CourseModuleEditionDto();
		BeanUtils.copyProperties(courseModuleEditionBo, courseModuleEdition);
		if(updateById(courseModuleEdition)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CourseModuleEditionVo getCourseModuleEditionByCondition(CourseModuleEditionConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return courseModuleEditionMapper.getCourseModuleEditionByCondition(condition);
	}

	@Override
	public void saveCourseModuleEdition(Long casesEditionId, Integer courseModuleType, List<CourseModuleBo> courseModuleBoList) {
		// 仅保存
		if (CollectionUtils.isEmpty(courseModuleBoList)) {
			return;
		}

		List<CourseModuleAttachmentEditionDto> allCourseModuleAttachmentEditionDtos = Lists.newArrayList();
		// 新增或更新
		for (CourseModuleBo courseModuleBo : courseModuleBoList) {
			CourseModuleEditionDto courseModuleEditionDto = new CourseModuleEditionDto();
			BeanUtils.copyProperties(courseModuleBo, courseModuleEditionDto);
			courseModuleEditionDto.setCourseModuleEditionId(null);
			courseModuleEditionDto.setCasesEditionId(casesEditionId);
			save(courseModuleEditionDto);
			if (CollectionUtils.isNotEmpty(courseModuleBo.getCourseModuleAttachmentBoList())) {
				List<CourseModuleAttachmentEditionDto> courseModuleAttachmentEditionDtos
						= courseModuleBo.getCourseModuleAttachmentBoList()
						.stream().map(x -> {
							CourseModuleAttachmentEditionDto courseModuleAttachmentEditionDto = new CourseModuleAttachmentEditionDto();
							BeanUtils.copyProperties(x, courseModuleAttachmentEditionDto);
							courseModuleAttachmentEditionDto.setCourseModuleAttachmentEditionId(null);
							courseModuleAttachmentEditionDto.setCourseModuleEditionId(courseModuleEditionDto.getCourseModuleEditionId());
							return courseModuleAttachmentEditionDto;
						}).collect(Collectors.toList());
				allCourseModuleAttachmentEditionDtos.addAll(courseModuleAttachmentEditionDtos);
			}
		}

		if (CollectionUtils.isNotEmpty(allCourseModuleAttachmentEditionDtos)) {
			courseModuleAttachmentEditionService.saveBatch(allCourseModuleAttachmentEditionDtos);
		}

	}


	@Override
	public List<CourseModuleEditionVo> getCourseModuleListByConditionWithAttachment(CourseModuleEditionConditionBo condition,
																			 boolean isQueryAttachment) {
		// 查询课程模块信息
		condition.setPageNo(SystemConstants.NO_PAGE);
		condition.setOrderBy("course_module_index");
		List<CourseModuleEditionVo> courseModuleEditionVos = getCourseModuleEditionListByCondition(condition);
		if (!isQueryAttachment || CollectionUtils.isEmpty(courseModuleEditionVos)) {
			return courseModuleEditionVos;
		}
		List<Long> courseModuleEditionIds =
				courseModuleEditionVos.stream().map(CourseModuleEditionVo::getCourseModuleEditionId).distinct().collect(Collectors.toList());

		// 查询模块附件并设置到返回的list中
		CourseModuleAttachmentEditionConditionBo attachmentEditionConditionBo = new CourseModuleAttachmentEditionConditionBo();
		attachmentEditionConditionBo.setCourseModuleEditionIds(courseModuleEditionIds);
		List<CourseModuleAttachmentEditionVo> attachmentEditionVos =
				courseModuleAttachmentEditionService.getCourseModuleAttachmentEditionListByCondition(attachmentEditionConditionBo);
		if (CollectionUtils.isEmpty(attachmentEditionVos)) {
			return courseModuleEditionVos;
		}
		Map<Long, List<CourseModuleAttachmentEditionVo>> attachmentEditionMap = attachmentEditionVos
				.stream().collect(Collectors.groupingBy(CourseModuleAttachmentEditionVo::getCourseModuleEditionId));
		courseModuleEditionVos.forEach(x -> x
				.setCourseModuleAttachmentVos(attachmentEditionMap.get(x.getCourseModuleEditionId())));
		return courseModuleEditionVos;
	}
}