package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.PCourseCasesInfoDto;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本课程案例详细信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface IPCourseCasesInfoService extends IService<PCourseCasesInfoDto> {

    List<PCourseCasesInfoVo> getPCourseCasesInfoListByCondition(PCourseCasesInfoConditionBo condition);

    AjaxResult addPCourseCasesInfo(PCourseCasesInfoBo pCourseCasesInfoBo);

    AjaxResult updatePCourseCasesInfo(PCourseCasesInfoBo pCourseCasesInfoBo);

    PCourseCasesInfoVo getPCourseCasesInfoByCondition(PCourseCasesInfoConditionBo condition);

    /**
     * 获取详情
     *
     * @param casesId the cases id
     * @return p course cases info vo
     * <AUTHOR>
     * @date 2023 -08-14 14:17:01
     */
    PCourseCasesInfoVo infoDetail(Long casesId);
}
