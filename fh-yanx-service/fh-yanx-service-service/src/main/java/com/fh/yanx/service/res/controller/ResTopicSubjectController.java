package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.res.api.ResTopicSubjectApi;
import com.fh.yanx.service.res.entity.dto.ResTopicSubjectDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicSubjectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicSubjectVo;
import com.fh.yanx.service.res.service.IResTopicSubjectService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 关联科目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
public class ResTopicSubjectController implements ResTopicSubjectApi {

    @Autowired
    private IResTopicSubjectService resTopicSubjectService;

    /**
     * 查询关联科目表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<PageInfo<ResTopicSubjectVo>> getResTopicSubjectPageListByCondition(@RequestBody ResTopicSubjectConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicSubjectVo> pageInfo = new PageInfo<>(resTopicSubjectService.getResTopicSubjectListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询关联科目表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<List<ResTopicSubjectVo>> getResTopicSubjectListByCondition(@RequestBody ResTopicSubjectConditionBo condition) {
        List<ResTopicSubjectVo> list = resTopicSubjectService.getResTopicSubjectListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增关联科目表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult addResTopicSubject(@Validated @RequestBody ResTopicSubjectBo resTopicSubjectBo) {
        return resTopicSubjectService.addResTopicSubject(resTopicSubjectBo);
    }

    /**
     * 修改关联科目表
     *
     * @param resTopicSubjectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult updateResTopicSubject(@Validated @RequestBody ResTopicSubjectBo resTopicSubjectBo) {
        if (null == resTopicSubjectBo.getId()) {
            return AjaxResult.fail("关联科目表id不能为空");
        }
        return resTopicSubjectService.updateResTopicSubject(resTopicSubjectBo);
    }

    /**
     * 查询关联科目表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<ResTopicSubjectVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("关联科目表id不能为空");
        }
        ResTopicSubjectVo vo = resTopicSubjectService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除关联科目表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicSubjectDto resTopicSubjectDto = new ResTopicSubjectDto();
        resTopicSubjectDto.setId(id);
        resTopicSubjectDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicSubjectService.updateById(resTopicSubjectDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
