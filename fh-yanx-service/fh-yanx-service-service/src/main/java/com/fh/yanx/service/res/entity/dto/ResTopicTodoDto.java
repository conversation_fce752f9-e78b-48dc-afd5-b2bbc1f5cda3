package com.fh.yanx.service.res.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户待办事项
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("res_topic_todo")
public class ResTopicTodoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "todo_id", type = IdType.AUTO)
	private Long todoId;

	/**
	 * 待办事项类型：
	 */
	@TableField("todo_type")
	private Integer todoType;
	/**
	 * 待办事项名称或操作：
	 */
	@TableField("todo_name")
	private String todoName;
	/**
	 * 待办事项key：
	 */
	@TableField("todo_key")
	private String todoKey;
	/**
	 * 待办事项消息
	 */
	@TableField("todo_msg")
	private String todoMsg;

	/**
	 * 待办事项业务类型：1课题
	 */
	@TableField("todo_business_type")
	private Integer todoBusinessType;

	/**
	 * 待办事项业务主键id：例如课题id，和上面的todo_business_type对应
	 */
	@TableField("todo_business_id")
	private Long todoBusinessId;

	/**
	 * 待办事项内容，json字符串
	 */
	@TableField("todo_json")
	private String todoJson;

	/**
	 * 待办事项用户
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 待办状态：1待办，2已办理
	 */
	@TableField("todo_status")
	private Integer todoStatus;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 待办数量，用于合并展示时候使用
	 */
	@TableField("todo_num")
	private Integer todoNum;

}
