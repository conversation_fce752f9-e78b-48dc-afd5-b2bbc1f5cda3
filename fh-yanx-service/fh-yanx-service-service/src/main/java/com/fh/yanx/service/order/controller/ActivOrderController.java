package com.fh.yanx.service.order.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.yanx.service.activ.entity.dto.ActivInfoDto;
import com.fh.yanx.service.order.api.ActivOrderApi;
import com.fh.yanx.service.order.entity.dto.ActivOrderDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import com.fh.yanx.service.order.service.IActivOrderService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;

/**
 * 订单表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
@RestController
@Validated
public class ActivOrderController implements ActivOrderApi {

    @Autowired
    private IActivOrderService activOrderService;

    /**
     * 查询订单表分页列表
     * 
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<PageInfo<ActivOrderVo>>
        getActivOrderPageListByCondition(@RequestBody ActivOrderConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ActivOrderVo> pageInfo = new PageInfo<>(activOrderService.getActivOrderListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询订单表列表
     * 
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<List<ActivOrderVo>> getActivOrderListByCondition(@RequestBody ActivOrderConditionBo condition) {
        List<ActivOrderVo> list = activOrderService.getActivOrderListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增订单表
     * 
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult addActivOrder(@Validated @RequestBody ActivOrderBo activOrderBo) {
        return activOrderService.addActivOrder(activOrderBo);
    }

    /**
     * 修改订单表
     * 
     * @param activOrderBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult updateActivOrder(@Validated @RequestBody ActivOrderBo activOrderBo) {
        if (null == activOrderBo.getOrderId()) {
            return AjaxResult.fail("订单表id不能为空");
        }
        return activOrderService.updateActivOrder(activOrderBo);
    }

    /**
     * 查询订单表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<ActivOrderVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("订单表id不能为空");
        }
        ActivOrderConditionBo condition = new ActivOrderConditionBo();
        condition.setOrderId(id);
        ActivOrderVo vo = activOrderService.getActivOrderByCondition(condition);
        return AjaxResult.success(vo);
    }

    @Override
    public AjaxResult<ActivOrderVo> getDetailByOrderNumber(String orderNumber) {
        if (StringUtils.isBlank(orderNumber)) {
            return AjaxResult.fail("订单号不能为空");
        }
        ActivOrderConditionBo condition = new ActivOrderConditionBo();
        condition.setOrderNumber(orderNumber);
        ActivOrderVo vo = activOrderService.getActivOrderByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除订单表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ActivOrderDto activOrderDto = new ActivOrderDto();
        activOrderDto.setOrderId(id);
        activOrderDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (activOrderService.updateById(activOrderDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult changeOrderAmount(@RequestBody ActivOrderBo orderBo) {
        return activOrderService.changeOrderAmount(orderBo);
    }

    @Override
    public AjaxResult completeOrder(@RequestBody ActivOrderBo orderBo) {
        return activOrderService.completeOrder(orderBo);
    }

    @Override
    public AjaxResult getUserOrderList(@RequestBody ActivOrderBo orderBo) {
        return null;
    }
}
