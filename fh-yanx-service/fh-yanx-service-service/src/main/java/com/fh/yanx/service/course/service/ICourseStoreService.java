package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.dto.CourseStoreDto;
import com.fh.yanx.service.course.entity.bo.CourseStoreConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseStoreBo;
import com.fh.yanx.service.course.entity.vo.CourseStoreVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课程收藏表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-04 18:33:34
 */
public interface ICourseStoreService extends IService<CourseStoreDto> {

    List<CourseStoreVo> getCourseStoreListByCondition(CourseStoreConditionBo condition);

	AjaxResult addCourseStore(CourseStoreBo courseStoreBo);

	AjaxResult updateCourseStore(CourseStoreBo courseStoreBo);

	AjaxResult deleteCourseStore(CourseStoreBo courseStoreBo);

	CourseStoreVo getCourseStoreByCondition(CourseStoreConditionBo condition);

}

