package com.fh.yanx.service.org.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织申请表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("organization_apply")
public class OrganizationApplyDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 组织申请id
	 */
	@TableId(value = "organization_apply_id", type = IdType.AUTO)
	private Long organizationApplyId;

	/**
	 * 组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 组织机构名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 所属省份
	 */
	@TableField("province_id")
	private Long provinceId;

	/**
	 * 所属市
	 */
	@TableField("city_id")
	private Long cityId;

	/**
	 * 所属县区
	 */
	@TableField("area_id")
	private Long areaId;

	/**
	 * 联系方式
	 */
	@TableField("contact")
	private String contact;

	/**
	 * 联系人姓名
	 */
	@TableField("concat_name")
	private String concatName;

	/**
	 * 处理状态：1：处理中，2：已处理
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 是否删除：0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
