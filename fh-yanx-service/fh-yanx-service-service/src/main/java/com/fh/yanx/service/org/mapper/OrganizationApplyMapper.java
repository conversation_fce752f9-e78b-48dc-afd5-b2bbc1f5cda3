package com.fh.yanx.service.org.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyConditionBo;
import com.fh.yanx.service.org.entity.dto.OrganizationApplyDto;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyVo;

import java.util.List;

/**
 * 组织申请表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
public interface OrganizationApplyMapper extends BaseMapper<OrganizationApplyDto> {

	List<OrganizationApplyVo> getOrganizationApplyListByCondition(OrganizationApplyConditionBo condition);

	OrganizationApplyVo getOrganizationApplyByCondition(OrganizationApplyConditionBo condition);

}
