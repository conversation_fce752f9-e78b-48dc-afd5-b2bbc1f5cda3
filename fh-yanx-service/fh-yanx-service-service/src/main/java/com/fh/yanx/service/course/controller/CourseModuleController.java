package com.fh.yanx.service.course.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.CourseModuleApi;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseModuleDto;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;
import com.fh.yanx.service.course.service.ICourseModuleService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 课程模块
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@RestController
@Validated
public class CourseModuleController implements CourseModuleApi{
	
    @Autowired
    private ICourseModuleService courseModuleService;

    /**
     * 查询课程模块分页列表
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @Override
    public AjaxResult<PageInfo<CourseModuleVo>> getCourseModulePageListByCondition(@RequestBody CourseModuleConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseModuleVo> pageInfo = new PageInfo<>(courseModuleService.getCourseModuleListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程模块列表
	 * <AUTHOR>
	 * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult<List<CourseModuleVo>> getCourseModuleListByCondition(@RequestBody CourseModuleConditionBo condition){
		List<CourseModuleVo> list = courseModuleService.getCourseModuleListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程模块
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
	@Override
    public AjaxResult addCourseModule(@Validated @RequestBody CourseModuleBo courseModuleBo){
		return courseModuleService.addCourseModule(courseModuleBo);
    }

    /**
	 * 修改课程模块
	 * @param courseModuleBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult updateCourseModule(@Validated @RequestBody CourseModuleBo courseModuleBo) {
		if(null == courseModuleBo.getCourseModuleId()) {
			return AjaxResult.fail("课程模块id不能为空");
		}
		return courseModuleService.updateCourseModule(courseModuleBo);
	}

	/**
	 * 查询课程模块详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult<CourseModuleVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程模块id不能为空");
		}
		CourseModuleConditionBo condition = new CourseModuleConditionBo();
		condition.setCourseModuleId(id);
		CourseModuleVo vo = courseModuleService.getCourseModuleByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程模块
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseModuleDto courseModuleDto = new CourseModuleDto();
		courseModuleDto.setCourseModuleId(id);
		courseModuleDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseModuleService.updateById(courseModuleDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
