package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResInformationDto;
import com.fh.yanx.service.res.entity.bo.ResInformationConditionBo;
import com.fh.yanx.service.res.entity.bo.ResInformationBo;
import com.fh.yanx.service.res.entity.vo.ResInformationVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 资讯接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
public interface IResInformationService extends IService<ResInformationDto> {

    List<ResInformationVo> getResInformationListByCondition(ResInformationConditionBo condition);

	AjaxResult addResInformation(ResInformationBo resInformationBo);

	AjaxResult updateResInformation(ResInformationBo resInformationBo);

	ResInformationVo getDetail(Long id);

}

