package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicProcessRecordDto;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.vo.ResTopicProcessRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课题流程记录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResTopicProcessRecordService extends IService<ResTopicProcessRecordDto> {

    List<ResTopicProcessRecordVo> getResTopicProcessRecordListByCondition(ResTopicProcessRecordConditionBo condition);

    AjaxResult addResTopicProcessRecord(ResTopicProcessRecordBo resTopicProcessRecordBo);

    AjaxResult updateResTopicProcessRecord(ResTopicProcessRecordBo resTopicProcessRecordBo);

    ResTopicProcessRecordVo getDetail(Long id);

    /**
     * 添加过程记录并进行是否重复的比较，唯一条件：topicId、topicProcess、processSubmitUser、processVerifyUser[可选]
     *
     * @param resTopicProcessRecordBo the res topic process record bo
     * @return ajax result
     */
    AjaxResult addResTopicProcessRecordWithCompare(ResTopicProcessRecordBo resTopicProcessRecordBo);

}
