package com.fh.yanx.service.aspose;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.IoUtils;
import com.aspose.words.*;
import com.light.base.attachment.api.AttachmentApi;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.entity.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * aspose使用方面的业务service
 * 
 * <AUTHOR>
 * @date 2024/3/14 14:21
 */
@Slf4j
@Service
public class AsposeBizService {

    @Resource
    private AsposeService asposeService;
    @Resource
    private AttachmentApi attachmentApi;

    @Value("${studyProveTemplate:}")
    private String studyProveTemplate;

    private static final String name = "劳动教育视域下“非遗”校本课程的建设与实践";

    private static final String school = "山东省临沂市河东区八湖镇刘店子中心小学";

    private static final String teacher = "赵守卿";



    /**
     * 新时代活动证书生成
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -03-14 14:24:12
     */
    public AttachmentVo test() {
        // 本地用测试用例的时候要改成不带中文的地址，否则会找不到文件
        String templatePath = getClass().getClassLoader().getResource("test.docx").getPath();
        File file = new File(templatePath);
        if (!file.exists()) {
            return null;
        }

        ByteArrayOutputStream outStream = null;
        InputStream contentInputStream = null;
        try {
            Document document;
            document = new Document(new FileInputStream(templatePath));
            Map paramsMap = new HashMap();
            paramsMap.put("realname", "刘德华");
            paramsMap.put("addr", "香港");
            String fileName = "test.pdf";
            try {
                asposeService.replaceText(paramsMap, document);
            } catch (Exception e) {

            }
            outStream = new ByteArrayOutputStream();
            document.save(outStream, SaveFormat.PDF);
            byte[] bytes = outStream.toByteArray();
            contentInputStream = new ByteArrayInputStream(bytes);
            if (contentInputStream == null) {
                log.error("contentStream is null, function stack return!");
                return null;
            }
            MultipartFile cMultiFile = new MockMultipartFile("file", fileName, null, contentInputStream);
            AjaxResult result = attachmentApi.upload(cMultiFile);
            if (null == result || result.isFail() || null == result.getData()) {
                log.error("attachmentApi.upload fail , function stack return!");
                return null;
            }
            final Map<String, Object> map = (Map<String, Object>)result.getData();
            AttachmentVo attachmentVo = BeanUtil.mapToBean(map, AttachmentVo.class, true);
            return attachmentVo;
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            IoUtils.closeQuietly(outStream);
            IoUtils.closeQuietly(contentInputStream);
        }
        return null;
    }

    /**
     * 根据指定正则，替换模板word，输出文件流。
     * @param regexContentMap 占位字符和替换字符字典。key：待替换的字符 value：替换后的字符。例如:  {name}=张三
     * @param path
     */
    public byte[] asposeWord(Map<String,String> regexContentMap,String path){
        File file = new File(path);
        if (!file.exists()) {
            return null;
        }
        try {
            Document document = new Document(new FileInputStream(path));
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();

            for (Map.Entry<String,String> entry: regexContentMap.entrySet()){

                // 创建查找替换选项，并设置查找方向为 BACKWARD（向后查找）
                FindReplaceOptions contentOptions = new FindReplaceOptions();
                contentOptions.setDirection(FindReplaceDirection.BACKWARD);
                // 设置自定义替换回调，将匹配到的占位符替换为 HTML 富文本内容
                contentOptions.setReplacingCallback(new ReplaceWithRichEvaluator(entry.getValue()));

                document.getRange().replace(entry.getKey(), "", contentOptions);
            }
            // 保存
            document.save(outStream, SaveFormat.DOCX);
            return outStream.toByteArray();
        }catch (Exception e){
            throw new RuntimeException();
        }
    }
        public AttachmentVo asposeWort2Png(Map<String, String> params) {
        // 本地用测试用例的时候要改成不带中文的地址，否则会找不到文件
//        String templatePath = getClass().getClassLoader().getResource("test.docx").getPath();
        String templatePath = studyProveTemplate;
        File file = new File(templatePath);
        if (!file.exists()) {
            return null;
        }

        ByteArrayOutputStream outStream = null;
        InputStream contentInputStream = null;
        try {
            Document document;
            document = new Document(new FileInputStream(file));
            String fileName = params.get("name").concat("学时证明.png");
            try {
                asposeService.replaceText(params, document);
            } catch (Exception e) {

            }
            outStream = new ByteArrayOutputStream();
            document.save(outStream, SaveFormat.PNG);
            byte[] bytes = outStream.toByteArray();
            contentInputStream = new ByteArrayInputStream(bytes);
            if (contentInputStream == null) {
                log.error("contentStream is null, function stack return!");
                return null;
            }
            MultipartFile cMultiFile = new MockMultipartFile("file", fileName, null, contentInputStream);
            AjaxResult result = attachmentApi.upload(cMultiFile);
            if (null == result || result.isFail() || null == result.getData()) {
                log.error("attachmentApi.upload fail , function stack return!");
                return null;
            }
            final Map<String, Object> map = (Map<String, Object>)result.getData();
            AttachmentVo attachmentVo = BeanUtil.mapToBean(map, AttachmentVo.class, true);
            return attachmentVo;
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            IoUtils.closeQuietly(outStream);
            IoUtils.closeQuietly(contentInputStream);
        }
        return null;
    }
}
