package com.fh.yanx.service.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.order.entity.bo.ActivOrderSmsRemindRecordConditionBo;
import com.fh.yanx.service.order.entity.dto.ActivOrderSmsRemindRecordDto;
import com.fh.yanx.service.order.entity.vo.ActivOrderSmsRemindRecordVo;

import java.util.List;

/**
 * 订单表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
public interface ActivOrderSmsRemindRecordMapper extends BaseMapper<ActivOrderSmsRemindRecordDto> {

    List<ActivOrderSmsRemindRecordVo> getActivOrderSmsRemindRecordListByCondition(ActivOrderSmsRemindRecordConditionBo condition);

    ActivOrderSmsRemindRecordVo getActivOrderSmsRemindRecordByCondition(ActivOrderSmsRemindRecordConditionBo condition);

}
