package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicApprovalDto;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalBo;
import com.fh.yanx.service.res.entity.vo.ResTopicApprovalVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课题审批意见表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResTopicApprovalService extends IService<ResTopicApprovalDto> {

    List<ResTopicApprovalVo> getResTopicApprovalListByCondition(ResTopicApprovalConditionBo condition);

	AjaxResult addResTopicApproval(ResTopicApprovalBo resTopicApprovalBo);

	AjaxResult updateResTopicApproval(ResTopicApprovalBo resTopicApprovalBo);

	ResTopicApprovalVo getDetail(Long id);

}

