package com.fh.yanx.service.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeBo;
import com.fh.yanx.service.course.entity.dto.CourseCasesTypeEditionDto;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo;
import com.fh.yanx.service.course.entity.vo.CourseCasesTypeEditionVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本课程案例类型版本记录接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
public interface ICourseCasesTypeEditionService extends IService<CourseCasesTypeEditionDto> {

    List<CourseCasesTypeEditionVo> getCourseCasesTypeEditionListByCondition(CourseCasesTypeEditionConditionBo condition);

	AjaxResult addCourseCasesTypeEdition(CourseCasesTypeEditionBo courseCasesTypeEditionBo);

	AjaxResult updateCourseCasesTypeEdition(CourseCasesTypeEditionBo courseCasesTypeEditionBo);

	CourseCasesTypeEditionVo getCourseCasesTypeEditionByCondition(CourseCasesTypeEditionConditionBo condition);

	/**
	 * 保存类型信息，保存后设置id到Bo里面
	 *
	 * @param casesEditionId the cases id
	 * @param courseCasesTypeEditionBos the course cases type bo list
	 * <AUTHOR>
	 * @date 2023 -08-18 16:52:05
	 */
	void saveCourseTypeEdition(Long casesEditionId, List<CourseCasesTypeEditionBo> courseCasesTypeEditionBos);

	/**
	 * 获取案例相关联类型名称
	 * @param casesEditionId
	 * @return
	 */
	List<String> getCasesTypeNameList(Long casesEditionId);

}

