package com.fh.yanx.service.res.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.yanx.service.res.entity.dto.ResInformationDto;
import com.fh.yanx.service.res.entity.bo.ResInformationConditionBo;
import com.fh.yanx.service.res.entity.bo.ResInformationBo;
import com.fh.yanx.service.res.entity.vo.ResInformationVo;
import com.fh.yanx.service.res.service.IResInformationService;
import com.fh.yanx.service.res.mapper.ResInformationMapper;
import com.light.core.entity.AjaxResult;
/**
 * 资讯接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
@Service
public class ResInformationServiceImpl extends ServiceImpl<ResInformationMapper, ResInformationDto> implements IResInformationService {

	@Resource
	private ResInformationMapper resInformationMapper;
	
    @Override
	public List<ResInformationVo> getResInformationListByCondition(ResInformationConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return resInformationMapper.getResInformationListByCondition(condition);
	}

	@Override
	public AjaxResult addResInformation(ResInformationBo resInformationBo) {
		ResInformationDto resInformation = new ResInformationDto();
		BeanUtils.copyProperties(resInformationBo, resInformation);
		resInformation.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resInformation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResInformation(ResInformationBo resInformationBo) {
		ResInformationDto resInformation = new ResInformationDto();
		BeanUtils.copyProperties(resInformationBo, resInformation);
		if(updateById(resInformation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResInformationVo getDetail(Long id) {
		ResInformationConditionBo condition = new ResInformationConditionBo();
		condition.setInformationId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ResInformationVo> list = resInformationMapper.getResInformationListByCondition(condition);
		ResInformationVo vo = new ResInformationVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}