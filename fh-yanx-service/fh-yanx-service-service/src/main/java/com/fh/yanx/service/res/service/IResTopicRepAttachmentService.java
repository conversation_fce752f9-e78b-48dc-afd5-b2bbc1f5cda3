package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicRepAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicRepAttachmentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 结题答辩附件表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface IResTopicRepAttachmentService extends IService<ResTopicRepAttachmentDto> {

    List<ResTopicRepAttachmentVo> getResTopicRepAttachmentListByCondition(ResTopicRepAttachmentConditionBo condition);

    AjaxResult addResTopicRepAttachment(ResTopicRepAttachmentBo resTopicRepAttachmentBo);

    AjaxResult updateResTopicRepAttachment(ResTopicRepAttachmentBo resTopicRepAttachmentBo);

    /**
     * 批量修改附件表
     */
    AjaxResult updateBatchResTopicRepAttachment(List<ResTopicRepAttachmentBo> resTopicRepAttachmentBos);

    ResTopicRepAttachmentVo getDetail(Long id);

    /**
     * 查询论文的年份集合
     *
     * @param condition
     * @return
     */
    List<String> getResTopicRepAttachmentYearsByCondition(ResTopicRepAttachmentConditionBo condition);
}

