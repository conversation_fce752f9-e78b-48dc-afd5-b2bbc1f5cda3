package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicCollectDto;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicCollectVo;

/**
 * 用户收藏的课题Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicCollectMapper extends BaseMapper<ResTopicCollectDto> {

	List<ResTopicCollectVo> getResTopicCollectListByCondition(ResTopicCollectConditionBo condition);

}
