package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResOpenCourseDto;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseConditionBo;
import com.fh.yanx.service.res.entity.vo.ResOpenCourseVo;

/**
 * 公开课表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
public interface ResOpenCourseMapper extends BaseMapper<ResOpenCourseDto> {

	List<ResOpenCourseVo> getResOpenCourseListByCondition(ResOpenCourseConditionBo condition);

}
