package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.res.api.ResTopicTeacherApi;
import com.fh.yanx.service.res.entity.dto.ResTopicTeacherDto;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;
import com.fh.yanx.service.res.service.IResTopicTeacherService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;
import java.util.List;

/**
 * 课题指导老师表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
public class ResTopicTeacherController implements ResTopicTeacherApi {

    @Autowired
    private IResTopicTeacherService resTopicTeacherService;

    @Resource
    private BaseDataService baseDataService;

    /**
     * 查询课题指导老师表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<PageInfo<ResTopicTeacherVo>> getResTopicTeacherPageListByCondition(@RequestBody ResTopicTeacherConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicTeacherVo> pageInfo = new PageInfo<>(resTopicTeacherService.getResTopicTeacherListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题指导老师表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<List<ResTopicTeacherVo>> getResTopicTeacherListByCondition(@RequestBody ResTopicTeacherConditionBo condition) {
        List<ResTopicTeacherVo> list = resTopicTeacherService.getResTopicTeacherListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增课题指导老师表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult addResTopicTeacher(@Validated @RequestBody ResTopicTeacherBo resTopicTeacherBo) {
        return resTopicTeacherService.addResTopicTeacher(resTopicTeacherBo);
    }

    /**
     * 修改课题指导老师表
     *
     * @param resTopicTeacherBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult updateResTopicTeacher(@Validated @RequestBody ResTopicTeacherBo resTopicTeacherBo) {
        if (null == resTopicTeacherBo.getId()) {
            return AjaxResult.fail("课题指导老师表id不能为空");
        }
        return resTopicTeacherService.updateResTopicTeacher(resTopicTeacherBo);
    }

    /**
     * 查询课题指导老师表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<ResTopicTeacherVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题指导老师表id不能为空");
        }
        ResTopicTeacherVo vo = resTopicTeacherService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课题指导老师表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicTeacherDto resTopicTeacherDto = new ResTopicTeacherDto();
        resTopicTeacherDto.setId(id);
        resTopicTeacherDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicTeacherService.updateById(resTopicTeacherDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
    * 根据条件获取教师列表
    *
    * @param condition
    * @return com.light.core.entity.AjaxResult
    * <AUTHOR>
    * @date 2023/2/2 15:20
    */
    @Override
    public AjaxResult getTeacherListByCondition(TeacherConditionBo condition) {
        return AjaxResult.success(baseDataService.getTeacherVoList(condition));
    }
}
