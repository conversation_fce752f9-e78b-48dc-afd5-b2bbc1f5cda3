package com.fh.yanx.service.enums;

/**
 * 待办事项类型
 *
 * <AUTHOR>
 */
public enum TopicTodoType {
    // ---------------------------- 学生工作台-创建 -------------------------------
    /**
     * 学生课题暂存
     */
    STUDENT_TOPIC_STASH(1, "暂存课题", "student_topic_stash"),

    /**
     * 学生课题审批通过
     */
    STUDENT_TOPIC_PASS(2, "课题审批通过", "student_topic_pass"),

    /**
     * 课题被驳回
     */
    STUDENT_TOPIC_REJECT(3, "课题被驳回", "student_topic_reject"),

    /**
     * 老师评定课题
     */
    STUDENT_TOPIC_TEACHER(4, "老师评定课题", "student_topic_teacher"),
    /**
     * 学校通过（未设置优秀）
     */
    STUDENT_TOPIC_SCHOOL_PASS(5, "学校通过（未设置优秀）", "student_topic_school_pass"),
    /**
     * 学校通过（设置优秀）
     */
    STUDENT_TOPIC_SCHOOL_PASS_YOU(6, "学校通过（设置优秀）", "student_topic_school_pass_you"),

    // -------------------------- 学生工作台-完成 --------------------------
    /**
     * 学生课题编辑提交
     */
    STUDENT_TOPIC_EDIT(7, "编辑", "student_topic_edit"),

    /**
     * 学生课题查看
     */
    STUDENT_TOPIC_VIEW(8, "查看", "student_topic_view"),

    // --------------------------- 教师工作台-创建 ---------------------------

    /**
     * 学生课题提交
     */
    TEACHER_TOPIC_SUBMIT(9, "学生课题提交", "teacher_topic_submit"),
    /**
     * 学生课题重新提交
     */
    TEACHER_TOPIC_RESUBMIT(10, "学生课题重新提交", "teacher_topic_resubmit"),
    /**
     * 学生提交课题材料
     */
    TEACHER_TOPIC_SUBMIT_ATTACHMENT(11, "学生提交课题材料", "teacher_topic_submit_attachment"),
    /**
     * 学生提交结题答辩论文
     */
    TEACHER_TOPIC_SUBMIT_ATTACHMENT_PAPER(12, "学生提交结题答辩论文", "teacher_topic_submit_attachment_paper"),
    /**
     * 指导教师进行课题评定
     */
    TEACHER_TOPIC_EVALUATE(13, "指导教师进行课题评定", "teacher_topic_evaluate"),
    /**
     * 学校通过（未设置优秀）
     */
    TEACHER_TOPIC_SCHOOL_PASS(14, "学校通过（未设置优秀）", "teacher_topic_school_pass"),
    /**
     * 学校通过（设置优秀）
     */
    TEACHER_TOPIC_SCHOOL_PASS_EXCELLENT(15, "学校通过（设置优秀）", "teacher_topic_school_pass_excellent"),
    /**
     * 学校驳回
     */
    TEACHER_TOPIC_SCHOOL_REJECT(16, "学校驳回", "teacher_topic_school_reject"),
    // -------------------------- 教师工作台-完成 --------------------------
    /**
     * 教师审批
     */
    TEACHER_TOPIC_JUDGE(17, "审批", "teacher_topic_judge"),
    /**
     * 教师评定完成
     */
    TEACHER_TOPIC_EVALUATE_COMPLETE(18, "评定", "teacher_topic_evaluate_complete"),

    /**
     * 教师课题查看
     */
    TEACHER_TOPIC_VIEW(19, "查看", "teacher_topic_view"),
    ;

    private Integer type;
    private String name;
    private String key;

    TopicTodoType(Integer type, String name, String key) {
        this.type = type;
        this.name = name;
        this.key = key;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getKey() {
        return key;
    }

    /**
     * 通过type获取枚举值
     * 
     * @param type
     * @return
     */
    public static TopicTodoType findEnumByType(Integer type) {
        for (TopicTodoType topicTodoType : TopicTodoType.values()) {
            if (topicTodoType.getType().equals(type)) {
                return topicTodoType;
            }

        }
        throw new IllegalArgumentException("code is not support");
    }

}