package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResStationImgDto;
import com.fh.yanx.service.res.entity.bo.ResStationImgConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationImgBo;
import com.fh.yanx.service.res.entity.vo.ResStationImgVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 工作站介绍图片表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResStationImgService extends IService<ResStationImgDto> {

    List<ResStationImgVo> getResStationImgListByCondition(ResStationImgConditionBo condition);

	AjaxResult addResStationImg(ResStationImgBo resStationImgBo);

	AjaxResult updateResStationImg(ResStationImgBo resStationImgBo);

	ResStationImgVo getDetail(Long id);

}

