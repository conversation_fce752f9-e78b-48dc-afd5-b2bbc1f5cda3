package com.fh.yanx.service.res.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.res.entity.dto.ResTopicDto;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;

/**
 * 课题表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicMapper extends BaseMapper<ResTopicDto> {

	List<ResTopicVo> getResTopicListByCondition(ResTopicConditionBo condition);

}
