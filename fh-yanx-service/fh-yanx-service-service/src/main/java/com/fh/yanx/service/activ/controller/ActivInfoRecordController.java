package com.fh.yanx.service.activ.controller;

import com.fh.yanx.service.activ.api.ActivInfoRecordApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.dto.ActivInfoRecordDto;
import com.fh.yanx.service.activ.service.IActivInfoViewPermissionService;
import com.fh.yanx.service.enums.ActivInfoViewType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import com.fh.yanx.service.activ.service.IActivInfoRecordService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 新时代文化校园活动内容表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
@RestController
@Validated
public class ActivInfoRecordController implements ActivInfoRecordApi{
	
    @Autowired
    private IActivInfoRecordService activInfoRecordService;
    @Autowired
	private IActivInfoViewPermissionService activInfoViewPermissionService;

    /**
     * 查询新时代文化校园活动内容表分页列表
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ActivInfoRecordVo>> getActivInfoRecordPageListByCondition(@RequestBody ActivInfoRecordConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ActivInfoRecordVo> pageInfo = new PageInfo<>(activInfoRecordService.getActivInfoRecordListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询新时代文化校园活动内容表列表
	 * <AUTHOR>
	 * @date 2023-07-04 10:33:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ActivInfoRecordVo>> getActivInfoRecordListByCondition(@RequestBody ActivInfoRecordConditionBo condition){
		List<ActivInfoRecordVo> list = activInfoRecordService.getActivInfoRecordListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增新时代文化校园活动内容表
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addActivInfoRecord(@Validated @RequestBody ActivInfoRecordBo activInfoRecordBo){
		return activInfoRecordService.addActivInfoRecord(activInfoRecordBo);
    }

    /**
	 * 修改新时代文化校园活动内容表
	 * @param activInfoRecordBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateActivInfoRecord(@Validated @RequestBody ActivInfoRecordBo activInfoRecordBo) {
		if(null == activInfoRecordBo.getRecordId()) {
			return AjaxResult.fail("新时代文化校园活动内容表id不能为空");
		}
		return activInfoRecordService.updateActivInfoRecord(activInfoRecordBo);
	}

	/**
	 * 查询新时代文化校园活动内容表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ActivInfoRecordVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("新时代文化校园活动内容表id不能为空");
		}
		ActivInfoRecordConditionBo condition = new ActivInfoRecordConditionBo();
		condition.setRecordId(id);
		ActivInfoRecordVo vo = activInfoRecordService.getActivInfoRecordByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除新时代文化校园活动内容表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ActivInfoRecordDto activInfoRecordDto = new ActivInfoRecordDto();
		activInfoRecordDto.setRecordId(id);
		activInfoRecordDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(activInfoRecordService.updateById(activInfoRecordDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	/**
	 * 保存活动内容
	 *
	 * @param activInfoBo
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/7/4 17:11
	 **/
	@Override
	public AjaxResult saveActivInfoRecords(ActivInfoBo activInfoBo) {
		if (null == activInfoBo.getActivId()) {
			return AjaxResult.fail("活动id不能为空");
		}
		if (activInfoRecordService.deleteAndAddActivInfoRecords(activInfoBo)) {
			activInfoBo.setViewType(ActivInfoViewType.ACTIV_RECORD.getCode());
			activInfoViewPermissionService.deleteAndAddActivInfoViewPermission(activInfoBo);
			return AjaxResult.success("保存活动内容成功");
		}
		return AjaxResult.fail("保存活动内容失败");
	}


}
