package com.fh.yanx.service.course.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreBo;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreConditionBo;
import com.fh.yanx.service.course.entity.dto.CourseWeightedScoreDto;
import com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo;
import com.fh.yanx.service.course.mapper.CourseWeightedScoreMapper;
import com.fh.yanx.service.course.service.ICourseWeightedScoreService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * 课程加权分表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
@Service
public class CourseWeightedScoreServiceImpl extends ServiceImpl<CourseWeightedScoreMapper, CourseWeightedScoreDto> implements ICourseWeightedScoreService {

	@Resource
	private CourseWeightedScoreMapper courseWeightedScoreMapper;
	
    @Override
	public List<CourseWeightedScoreVo> getCourseWeightedScoreListByCondition(CourseWeightedScoreConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return courseWeightedScoreMapper.getCourseWeightedScoreListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseWeightedScore(CourseWeightedScoreBo courseWeightedScoreBo) {
		CourseWeightedScoreDto courseWeightedScore = new CourseWeightedScoreDto();
		BeanUtils.copyProperties(courseWeightedScoreBo, courseWeightedScore);
		courseWeightedScore.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(courseWeightedScore)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseWeightedScore(CourseWeightedScoreBo courseWeightedScoreBo) {
		CourseWeightedScoreDto courseWeightedScore = new CourseWeightedScoreDto();
		BeanUtils.copyProperties(courseWeightedScoreBo, courseWeightedScore);
		if(updateById(courseWeightedScore)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CourseWeightedScoreVo getCourseWeightedScoreByCondition(CourseWeightedScoreConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		CourseWeightedScoreVo vo = courseWeightedScoreMapper.getCourseWeightedScoreByCondition(condition);
		return vo;
	}

}