package com.fh.yanx.service.order.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.yanx.service.order.entity.dto.ActivOrderDto;
import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import org.apache.ibatis.annotations.Param;

/**
 * 订单表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
public interface ActivOrderMapper extends BaseMapper<ActivOrderDto> {

	List<ActivOrderVo> getActivOrderListByCondition(ActivOrderConditionBo condition);

	ActivOrderVo getActivOrderByCondition(ActivOrderConditionBo condition);

	/**
	 * 修改订单状态为超时未支付
	 *
	 * @param orderIds
	 * @return java.lang.Integer
	 * <AUTHOR>
	 * @date 2024/9/11 10:18
	 **/
	Integer changeActivOrderTimeout(@Param("orderIds") List<Long> orderIds);
}
