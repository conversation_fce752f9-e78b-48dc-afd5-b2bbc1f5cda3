package com.fh.yanx.service.res.controller;

import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.res.api.ResTopicStudentApi;
import com.fh.yanx.service.res.entity.dto.ResTopicStudentDto;
import com.light.user.student.entity.bo.StudentConditionBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;
import com.fh.yanx.service.res.service.IResTopicStudentService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;
import java.util.List;

/**
 * 课题组成员表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
public class ResTopicStudentController implements ResTopicStudentApi {

    @Autowired
    private IResTopicStudentService resTopicStudentService;
    @Resource
    private BaseDataService baseDataService;

    /**
     * 查询课题组成员表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<PageInfo<ResTopicStudentVo>> getResTopicStudentPageListByCondition(@RequestBody ResTopicStudentConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResTopicStudentVo> pageInfo = new PageInfo<>(resTopicStudentService.getResTopicStudentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课题组成员表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<List<ResTopicStudentVo>> getResTopicStudentListByCondition(@RequestBody ResTopicStudentConditionBo condition) {
        List<ResTopicStudentVo> list = resTopicStudentService.getResTopicStudentListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增课题组成员表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult addResTopicStudent(@Validated @RequestBody ResTopicStudentBo resTopicStudentBo) {
        return resTopicStudentService.addResTopicStudent(resTopicStudentBo);
    }

    /**
     * 修改课题组成员表
     *
     * @param resTopicStudentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult updateResTopicStudent(@Validated @RequestBody ResTopicStudentBo resTopicStudentBo) {
        if (null == resTopicStudentBo.getId()) {
            return AjaxResult.fail("课题组成员表id不能为空");
        }
        return resTopicStudentService.updateResTopicStudent(resTopicStudentBo);
    }

    /**
     * 查询课题组成员表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult<ResTopicStudentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题组成员表id不能为空");
        }
        ResTopicStudentVo vo = resTopicStudentService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课题组成员表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ResTopicStudentDto resTopicStudentDto = new ResTopicStudentDto();
        resTopicStudentDto.setId(id);
        resTopicStudentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (resTopicStudentService.updateById(resTopicStudentDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult getStudentListByCondition(StudentConditionBo condition) {
        return AjaxResult.success(baseDataService.getStudentVoList(condition));
    }
}
