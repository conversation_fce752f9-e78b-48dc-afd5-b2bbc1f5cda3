package com.fh.yanx.service.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.res.entity.dto.ResTopicHonorAttachmentDto;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicHonorAttachmentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课题荣誉表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface IResTopicHonorAttachmentService extends IService<ResTopicHonorAttachmentDto> {

    List<ResTopicHonorAttachmentVo>
        getResTopicHonorAttachmentListByCondition(ResTopicHonorAttachmentConditionBo condition);

    AjaxResult addResTopicHonorAttachment(ResTopicHonorAttachmentBo resTopicHonorAttachmentBo);

    AjaxResult updateResTopicHonorAttachment(ResTopicHonorAttachmentBo resTopicHonorAttachmentBo);

    ResTopicHonorAttachmentVo getDetail(Long id);

    /**
     * 批量删除和新增
     * 
     * @param topicId
     * @param resTopicHonorAttachmentBos
     * @return
     */
    boolean deleteAndSaveTopicHonorAttachmentBatch(Long topicId,
        List<ResTopicHonorAttachmentBo> resTopicHonorAttachmentBos);

}
