package com.fh.yanx.service.courseReviewExpertConfig.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.fh.yanx.service.course.service.ICourseVerifyLogService;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.service.org.entity.vo.UserVoExt;
import com.light.core.constants.SystemConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.courseReviewExpertConfig.api.CourseReviewExpertConfigApi;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.dto.CourseReviewExpertConfigDto;
import com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo;
import com.fh.yanx.service.courseReviewExpertConfig.service.ICourseReviewExpertConfigService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 课程审核专家配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@RestController
@Validated
public class CourseReviewExpertConfigController implements CourseReviewExpertConfigApi{
	
    @Autowired
    private ICourseReviewExpertConfigService courseReviewExpertConfigService;

	@Value("${ineoschool.organization.id:1}")
	private Long ineoschoolOrganizationId;

	@Autowired
	private BaseDataService baseDataService;

	@Autowired
	private ICourseVerifyLogService courseVerifyLogService;

    /**
     * 查询课程审核专家配置表分页列表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @Override
    public AjaxResult<PageInfo<CourseReviewExpertConfigVo>> getCourseReviewExpertConfigPageListByCondition(@RequestBody CourseReviewExpertConfigConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseReviewExpertConfigVo> pageInfo = new PageInfo<>(courseReviewExpertConfigService.getCourseReviewExpertConfigListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程审核专家配置表列表
	 * <AUTHOR>
	 * @date 2024-03-05 14:13:59
	 */
	@Override
	public AjaxResult<List<CourseReviewExpertConfigVo>> getCourseReviewExpertConfigListByCondition(@RequestBody CourseReviewExpertConfigConditionBo condition){
		List<CourseReviewExpertConfigVo> list = courseReviewExpertConfigService.getCourseReviewExpertConfigListByCondition(condition);
		if (CollUtil.isNotEmpty(list)){
			//获取专家Id
			List<String> userOidList = list.stream()
					.map(CourseReviewExpertConfigVo::getUserOid)
					.distinct()
					.collect(Collectors.toList());

			UserConditionBoExt userCondition = new UserConditionBoExt();
			userCondition.setOids(new ArrayList<>(userOidList));
			userCondition.setPageNo(SystemConstants.NO_PAGE);
			List<UserVoExt> userVoExtList = baseDataService.getUserList(userCondition).getList(UserVoExt.class, "list");

			Map<String, UserVoExt> userVoExtMap = userVoExtList
					.stream()
					.collect(Collectors.toMap(UserVoExt::getOid, Function.identity(), (v1, v2) -> v1));

			CourseVerifyLogConditionBo courseVerifyLogConditionBo = new CourseVerifyLogConditionBo();
			courseVerifyLogConditionBo.setCasesId(condition.getCasesId());

			// 课程审核流水
			List<CourseVerifyLogVo> courseVerifyLogVos =
					courseVerifyLogService.getCourseVerifyLogListByCondition(courseVerifyLogConditionBo);

			Map<String, List<CourseVerifyLogVo>> courseVerifyLogVosMap  = courseVerifyLogVos
					.stream()
					.collect(Collectors.groupingBy(item -> item.getCasesId() + "_" + item.getUserOid()));

			list.forEach(vo -> {
				vo.setUserVoExt(userVoExtMap.get(vo.getUserOid()));
				List<CourseVerifyLogVo> courseVerifyLogVosTemp = courseVerifyLogVosMap.get(vo.getCasesId() + "_" + vo.getUserOid());
				vo.setIsScore(CollUtil.isNotEmpty(courseVerifyLogVosTemp));
			});

		}


		return AjaxResult.success(list);
	}


    /**
     * 新增课程审核专家配置表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
	@Override
	@Transactional(rollbackFor = Exception.class)
    public AjaxResult addCourseReviewExpertConfig(@Validated @RequestBody List<CourseReviewExpertConfigBo> courseReviewExpertConfigBo){
		return courseReviewExpertConfigService.addCourseReviewExpertConfig(courseReviewExpertConfigBo);
    }

    /**
	 * 修改课程审核专家配置表
	 * @param courseReviewExpertConfigBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
	 */
	@Override
	public AjaxResult updateCourseReviewExpertConfig(@Validated @RequestBody CourseReviewExpertConfigBo courseReviewExpertConfigBo) {
		if(null == courseReviewExpertConfigBo.getId()) {
			return AjaxResult.fail("课程审核专家配置表id不能为空");
		}
		return courseReviewExpertConfigService.updateCourseReviewExpertConfig(courseReviewExpertConfigBo);
	}

	/**
	 * 查询课程审核专家配置表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
	 */
	@Override
	public AjaxResult<CourseReviewExpertConfigVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程审核专家配置表id不能为空");
		}
		CourseReviewExpertConfigConditionBo condition = new CourseReviewExpertConfigConditionBo();
		condition.setId(id);
		CourseReviewExpertConfigVo vo = courseReviewExpertConfigService.getCourseReviewExpertConfigByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除课程审核专家配置表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseReviewExpertConfigDto courseReviewExpertConfigDto = new CourseReviewExpertConfigDto();
		courseReviewExpertConfigDto.setId(id);
		courseReviewExpertConfigDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseReviewExpertConfigService.updateById(courseReviewExpertConfigDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
