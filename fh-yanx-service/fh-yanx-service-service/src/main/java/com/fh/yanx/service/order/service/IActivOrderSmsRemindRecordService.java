package com.fh.yanx.service.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.yanx.service.order.entity.bo.ActivOrderSmsRemindRecordBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderSmsRemindRecordConditionBo;
import com.fh.yanx.service.order.entity.dto.ActivOrderSmsRemindRecordDto;
import com.fh.yanx.service.order.entity.vo.ActivOrderSmsRemindRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 订单表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
public interface IActivOrderSmsRemindRecordService extends IService<ActivOrderSmsRemindRecordDto> {

    List<ActivOrderSmsRemindRecordVo> getActivOrderSmsRemindRecordListByCondition(ActivOrderSmsRemindRecordConditionBo condition);

    AjaxResult addActivOrderSmsRemindRecord(ActivOrderSmsRemindRecordBo activOrderBo);

    AjaxResult updateActivOrderSmsRemindRecord(ActivOrderSmsRemindRecordBo activOrderBo);

    ActivOrderSmsRemindRecordVo getActivOrderSmsRemindRecordByCondition(ActivOrderSmsRemindRecordConditionBo condition);

    boolean addActivOrderSmsRemindRecordBatchByOrderIds(List<Long> orderIds);
}
