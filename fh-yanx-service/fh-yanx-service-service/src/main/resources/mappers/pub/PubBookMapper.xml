<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.pub.mapper.PubBookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.pub.entity.dto.PubBookDto" id="BaseResultMap">
        <result property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="labelNames" column="label_names"/>
        <result property="pressName" column="press_name"/>
        <result property="bookNo" column="book_no"/>
        <result property="readTimes" column="read_times"/>
        <result property="bookIndex" column="book_index"/>
        <result property="coverFileOid" column="cover_file_oid"/>
        <result property="coverFileUrl" column="cover_file_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="bookId != null ">and book_id = #{bookId}</if>
			<if test="bookName != null and bookName != '' ">and book_name like concat('%', #{bookName}, '%')</if>
			<if test="labelNames != null and labelNames != '' ">and label_names like concat('%', #{labelNames}, '%')</if>
			<if test="pressName != null and pressName != '' ">and press_name like concat('%', #{pressName}, '%')</if>
			<if test="bookNo != null and bookNo != '' ">and book_no like concat('%', #{bookNo}, '%')</if>
			<if test="readTimes != null ">and read_times = #{readTimes}</if>
			<if test="bookIndex != null ">and book_index = #{bookIndex}</if>
			<if test="coverFileOid != null and coverFileOid != '' ">and cover_file_oid like concat('%', #{coverFileOid}, '%')</if>
			<if test="coverFileUrl != null and coverFileUrl != '' ">and cover_file_url like concat('%', #{coverFileUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.book_id
	 		,t.book_name
	 		,t.label_names
	 		,t.press_name
	 		,t.book_no
	 		,t.read_times
		    ,t.book_index
	 		,t.cover_file_oid
	 		,t.cover_file_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from pub_book a
		 ) t

	</sql>

    <select id="getPubBookListByCondition" resultType="com.fh.yanx.service.pub.entity.vo.PubBookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPubBookByCondition" resultType="com.fh.yanx.service.pub.entity.vo.PubBookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<update id="incrPubBookReadTimes" parameterType="map">
		update pub_book set read_times = read_times + 1 where book_id = #{bookId}
	</update>
</mapper>