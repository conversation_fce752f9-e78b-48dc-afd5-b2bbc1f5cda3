<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.pub.mapper.PubBookDirectoryAttachmentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.pub.entity.dto.PubBookDirectoryAttachmentDto" id="BaseResultMap">
        <result property="bookDirectoryAttachmentId" column="book_directory_attachment_id"/>
        <result property="bookId" column="book_id"/>
        <result property="bookDirectoryId" column="book_directory_id"/>
        <result property="bookDirectoryFileName" column="book_directory_file_name"/>
        <result property="bookDirectoryFileUrl" column="book_directory_file_url"/>
        <result property="bookDirectoryFileOid" column="book_directory_file_oid"/>
        <result property="bookDirectoryFileIndex" column="book_directory_file_index"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="bookDirectoryAttachmentId != null ">and book_directory_attachment_id = #{bookDirectoryAttachmentId}</if>
			<if test="bookId != null ">and book_id = #{bookId}</if>
			<if test="bookDirectoryId != null ">and book_directory_id = #{bookDirectoryId}</if>
			<if test="bookDirectoryFileName != null and bookDirectoryFileName != '' ">and book_directory_file_name like concat('%', #{bookDirectoryFileName}, '%')</if>
			<if test="bookDirectoryFileUrl != null and bookDirectoryFileUrl != '' ">and book_directory_file_url like concat('%', #{bookDirectoryFileUrl}, '%')</if>
			<if test="bookDirectoryFileOid != null and bookDirectoryFileOid != '' ">and book_directory_file_oid like concat('%', #{bookDirectoryFileOid}, '%')</if>
			<if test="bookDirectoryFileIndex != null ">and book_directory_file_index = #{bookDirectoryFileIndex}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.book_directory_attachment_id
	 		,t.book_id
	 		,t.book_directory_id
	 		,t.book_directory_file_name
	 		,t.book_directory_file_url
	 		,t.book_directory_file_oid
			,t.book_directory_file_index
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from pub_book_directory_attachment a
		 ) t

	</sql>

	<select id="getPubBookDirectoryAttachmentListByCondition" resultType="com.fh.yanx.service.pub.entity.vo.PubBookDirectoryAttachmentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPubBookDirectoryAttachmentByCondition" resultType="com.fh.yanx.service.pub.entity.vo.PubBookDirectoryAttachmentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>