<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.pub.mapper.PubBookDirectoryMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.pub.entity.dto.PubBookDirectoryDto" id="BaseResultMap">
        <result property="bookDirectoryId" column="book_directory_id"/>
        <result property="bookId" column="book_id"/>
        <result property="bookDirectoryName" column="book_directory_name"/>
        <result property="authorName" column="author_name"/>
        <result property="authorSchool" column="author_school"/>
        <result property="bookDirectoryIndex" column="book_directory_index"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="bookDirectoryId != null ">and book_directory_id = #{bookDirectoryId}</if>
			<if test="bookId != null ">and book_id = #{bookId}</if>
			<if test="bookDirectoryName != null and bookDirectoryName != '' ">and book_directory_name like concat('%', #{bookDirectoryName}, '%')</if>
			<if test="authorName != null and authorName != '' ">and author_name like concat('%', #{authorName}, '%')</if>
			<if test="authorSchool != null and authorSchool != '' ">and author_school like concat('%', #{authorSchool}, '%')</if>
			<if test="bookDirectoryIndex != null">and book_directory_index = #{bookDirectoryIndex}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.book_directory_id
	 		,t.book_id
	 		,t.book_directory_name
	 		,t.author_name
	 		,t.author_school
			,t.book_directory_index
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from pub_book_directory a
		 ) t

	</sql>

	<select id="getPubBookDirectoryListByCondition" resultType="com.fh.yanx.service.pub.entity.vo.PubBookDirectoryVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPubBookDirectoryByCondition" resultType="com.fh.yanx.service.pub.entity.vo.PubBookDirectoryVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>