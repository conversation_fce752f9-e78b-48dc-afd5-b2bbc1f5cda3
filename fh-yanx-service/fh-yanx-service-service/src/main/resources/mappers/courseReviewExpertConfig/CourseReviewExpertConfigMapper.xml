<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.courseReviewExpertConfig.mapper.CourseReviewExpertConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.courseReviewExpertConfig.entity.dto.CourseReviewExpertConfigDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesId" column="cases_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="verifyProcessType" column="verify_process_type"/>
        <result property="needScore" column="need_score"/>
        <result property="expertType" column="expert_type"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="verifyProcessType != null ">and verify_process_type = #{verifyProcessType}</if>
			<if test="needScore != null ">and need_score = #{needScore}</if>
			<if test="expertType != null ">and expert_type = #{expertType}</if>
			<if test="casesIds != null and casesIds.size() != 0">
				and cases_id in
				<foreach collection="casesIds" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.cases_id
	 		,t.user_oid
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.verify_process_type
			,t.need_score
			,t.expert_type
		from (
			 select a.* from course_review_expert_config a
		 ) t

	</sql>

	<select id="getCourseReviewExpertConfigListByCondition" resultType="com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCourseReviewExpertConfigByCondition" resultType="com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>