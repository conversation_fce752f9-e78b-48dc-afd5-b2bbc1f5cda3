<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseHomeConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseHomeConfigDto" id="BaseResultMap">
        <result property="homeConfigId" column="home_config_id"/>
        <result property="casesId" column="cases_id"/>
        <result property="homeType" column="home_type"/>
        <result property="isExcellent" column="is_excellent"/>
        <result property="homeIndex" column="home_index"/>
        <result property="recommendText" column="recommend_text"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="courseName" column="course_name"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="homeConfigId != null ">and home_config_id = #{homeConfigId}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="homeType != null ">and home_type = #{homeType}</if>
			<if test="isExcellent != null ">and is_excellent = #{isExcellent}</if>
			<if test="homeIndex != null ">and home_index = #{homeIndex}</if>
			<if test="recommendText != null and recommendText != '' ">and recommend_text like concat('%', #{recommendText}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="courseName != null and courseName != ''">and course_name like concat('%', #{courseName}, '%')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.home_config_id
	 		,t.cases_id
	 		,t.home_type
	 		,t.is_excellent
	 		,t.home_index
	 		,t.recommend_text
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.course_name
		from (
			 select a.* from course_home_config a
		 ) t

	</sql>

	<select id="getCourseHomeConfigListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseHomeConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCourseHomeConfigByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseHomeConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>