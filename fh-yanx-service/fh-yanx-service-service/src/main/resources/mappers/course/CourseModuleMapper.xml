<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseModuleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseModuleDto" id="BaseResultMap">
        <result property="courseModuleId" column="course_module_id"/>
        <result property="casesId" column="cases_id"/>
        <result property="courseModuleType" column="course_module_type"/>
        <result property="courseModuleTitle" column="course_module_title"/>
        <result property="courseModuleSubTitle" column="course_module_sub_title"/>
        <result property="courseModuleResStyle" column="course_module_res_style"/>
        <result property="courseModuleIndex" column="course_module_index"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="uuid" column="uuid"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="courseModuleId != null ">and course_module_id = #{courseModuleId}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="courseModuleType != null ">and course_module_type = #{courseModuleType}</if>
			<if test="courseModuleTitle != null and courseModuleTitle != '' ">and course_module_title like concat('%', #{courseModuleTitle}, '%')</if>
			<if test="courseModuleSubTitle != null and courseModuleSubTitle != '' ">and course_module_sub_title like concat('%', #{courseModuleSubTitle}, '%')</if>
			<if test="courseModuleResStyle != null ">and course_module_res_style = #{courseModuleResStyle}</if>
			<if test="courseModuleIndex != null ">and course_module_index = #{courseModuleIndex}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="uuid != null ">and uuid = #{uuid}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.course_module_id
	 		,t.cases_id
	 		,t.course_module_type
	 		,t.course_module_title
	 		,t.course_module_sub_title
	 		,t.course_module_res_style
	 		,t.course_module_index
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.uuid
		from (
			 select a.* from course_module a
		 ) t

	</sql>

	<select id="getCourseModuleListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseModuleVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		<if test= "pageNo == -1 and orderBy != null and orderBy != ''">
			order by ${orderBy}
		</if>
	</select>

	<select id="getCourseModuleByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseModuleVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>