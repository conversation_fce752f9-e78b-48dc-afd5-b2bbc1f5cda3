<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.PLessonCasesMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.PLessonCasesDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="cover" column="cover"/>
        <result property="lcZone" column="lc_zone"/>
        <result property="lcSubject" column="lc_subject"/>
        <result property="mediaId" column="media_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUserOid" column="create_user_oid"/>
        <result property="remark" column="remark"/>
        <result property="organizationId" column="organization_id"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="description != null and description != '' ">and description like concat('%', #{description}, '%')</if>
			<if test="cover != null and cover != '' ">and cover like concat('%', #{cover}, '%')</if>
			<if test="lcZone != null ">and lc_zone = #{lcZone}</if>
			<if test="lcSubject != null ">and lc_subject = #{lcSubject}</if>
			<if test="mediaId != null and mediaId != '' ">and media_id like concat('%', #{mediaId}, '%')</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="createUserOid != null and createUserOid != '' ">and create_user_oid like concat('%', #{createUserOid}, '%')</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.title
	 		,t.description
	 		,t.cover
	 		,t.lc_zone
	 		,t.lc_subject
	 		,t.media_id
	 		,t.source_type
	 		,t.create_time
	 		,t.update_time
	 		,t.create_user_oid
	 		,t.remark
	 		,t.organization_id
	 		,t.is_delete
		from (
			 select a.* from p_lesson_cases a
		 ) t

	</sql>

	<select id="getPLessonCasesListByCondition" resultType="com.fh.yanx.service.course.entity.vo.PLessonCasesVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPLessonCasesByCondition" resultType="com.fh.yanx.service.course.entity.vo.PLessonCasesVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>