<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.PCategoryMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.PCategoryDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="moduleId" column="module_id"/>
        <result property="level" column="level"/>
        <result property="typeId" column="type_id"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUserOid" column="create_user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="sequence" column="sequence"/>
        <result property="edusoaId" column="edusoa_id"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="parentId != null ">and parent_id = #{parentId}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="description != null and description != '' ">and description like concat('%', #{description}, '%')</if>
			<if test="moduleId != null ">and module_id = #{moduleId}</if>
			<if test="level != null ">and level = #{level}</if>
			<if test="typeId != null ">and type_id = #{typeId}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="state != null ">and state = #{state}</if>
			<if test="createUserOid != null and createUserOid != '' ">and create_user_oid like concat('%', #{createUserOid}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="sequence != null ">and sequence = #{sequence}</if>
			<if test="edusoaId != null ">and edusoa_id = #{edusoaId}</if>
			<if test="ids != null and ids.size() != 0">
				and id in
				<foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.parent_id
	 		,t.name
	 		,t.description
	 		,t.module_id
	 		,t.level
	 		,t.type_id
	 		,t.remark
	 		,t.state
	 		,t.create_time
	 		,t.update_time
	 		,t.create_user_oid
	 		,t.organization_id
	 		,t.sequence
	 		,t.edusoa_id
		from (
			 select a.* from p_category a
		 ) t

	</sql>

	<select id="getPCategoryListByCondition" resultType="com.fh.yanx.service.course.entity.vo.PCategoryVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPCategoryByCondition" resultType="com.fh.yanx.service.course.entity.vo.PCategoryVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>