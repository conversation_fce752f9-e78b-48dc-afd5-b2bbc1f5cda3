<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseVerifyLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseVerifyLogDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesId" column="cases_id"/>
        <result property="verifyProcessType" column="verify_process_type"/>
        <result property="bestVerifyType" column="best_verify_type"/>
        <result property="normalVerifyType" column="normal_verify_type"/>
        <result property="verifyRemark" column="verify_remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="verifyScoreDetailJson" column="verify_score_detail_json"/>
        <result property="verifyScoreTotal" column="verify_score_total"/>
        <result property="userOid" column="user_oid"/>
        <result property="roleId" column="role_id"/>
        <result property="realName" column="real_name"/>
        <result property="roleName" column="role_name"/>
        <result property="courseVerifySource" column="course_verify_source"/>
		<result property="adoptType" column="adopt_type"/>
		<result property="showType" column="show_type"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="verifyProcessType != null ">and verify_process_type = #{verifyProcessType}</if>
			<if test="bestVerifyType != null ">and best_verify_type = #{bestVerifyType}</if>
			<if test="normalVerifyType != null ">and normal_verify_type = #{normalVerifyType}</if>
			<if test="verifyRemark != null and verifyRemark != '' ">and verify_remark like concat('%', #{verifyRemark}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="verifyScoreDetailJson != null ">and verify_score_detail_json = #{verifyScoreDetailJson}</if>
			<if test="verifyScoreTotal != null ">and verify_score_total = #{verifyScoreTotal}</if>
			<if test="userOid != null ">and user_oid = #{userOid}</if>
			<if test="roleId != null ">and role_id = #{roleId}</if>
			<if test="realName != null and realName != ''">and real_name = #{realName}</if>
			<if test="roleName != null and roleName !=''">and role_name = #{roleName}</if>
			<if test="courseVerifySource != null">and course_verify_source = #{courseVerifySource}</if>
			<if test="showType != null">and show_type = #{showType}</if>
			<if test="casesIds != null and casesIds.size() >0">
				and cases_id in
				<foreach collection="casesIds" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="verifyProcessTypes != null and verifyProcessTypes.size() != 0">
				and verify_process_type in
				<foreach collection="verifyProcessTypes" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="adoptType != null">and adopt_type = #{adoptType}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.cases_id
	 		,t.verify_process_type
	 		,t.best_verify_type
			,t.normal_verify_type
	 		,t.verify_remark
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.verify_score_detail_json
			,t.verify_score_total
			,t.user_oid
			,t.role_id
			,t.real_name
			,t.role_name
			,t.course_verify_source
			,t.adopt_type
			,t.show_type
			,ifnull(t.need_score, 1) need_score
		from (
			 select a.*
				,b.need_score
			 from course_verify_log a
			 left join course_review_expert_config b on a.user_oid = b.user_oid and a.cases_id = b.cases_id and b.is_delete = 0
		 ) t

	</sql>

	<select id="getCourseVerifyLogListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by create_time desc
	</select>

	<select id="getCourseVerifyLogByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>