<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseCasesInfoEditionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseCasesInfoEditionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesEditionId" column="cases_edition_id"/>
        <result property="background" column="background"/>
        <result property="goal" column="goal"/>
        <result property="content" column="content"/>
        <result property="eval" column="eval"/>
        <result property="operationInfo" column="operation_info"/>
        <result property="exp" column="exp"/>
        <result property="effect" column="effect"/>
        <result property="problem" column="problem"/>
        <result property="structure" column="structure"/>
        <result property="teacherCaseInfo" column="teacher_case_info"/>
        <result property="teacherCaseName" column="teacher_case_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesEditionId != null ">and cases_edition_id = #{casesEditionId}</if>
			<if test="background != null and background != '' ">and background like concat('%', #{background}, '%')</if>
			<if test="goal != null and goal != '' ">and goal like concat('%', #{goal}, '%')</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
			<if test="eval != null and eval != '' ">and eval like concat('%', #{eval}, '%')</if>
			<if test="operationInfo != null and operationInfo != '' ">and operation_info like concat('%', #{operationInfo}, '%')</if>
			<if test="exp != null and exp != '' ">and exp like concat('%', #{exp}, '%')</if>
			<if test="effect != null and effect != '' ">and effect like concat('%', #{effect}, '%')</if>
			<if test="problem != null and problem != '' ">and problem like concat('%', #{problem}, '%')</if>
			<if test="structure != null and structure != '' ">and structure like concat('%', #{structure}, '%')</if>
			<if test="teacherCaseInfo != null and teacherCaseInfo != '' ">and teacher_case_info like concat('%', #{teacherCaseInfo}, '%')</if>
			<if test="teacherCaseName != null and teacherCaseName != '' ">and teacher_case_name like concat('%', #{teacherCaseName}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesEditionId != null ">and cases_edition_id = #{casesEditionId}</if>
			<if test="background != null and background != '' ">and background like concat('%', #{background}, '%')</if>
			<if test="goal != null and goal != '' ">and goal like concat('%', #{goal}, '%')</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
			<if test="eval != null and eval != '' ">and eval like concat('%', #{eval}, '%')</if>
			<if test="operationInfo != null and operationInfo != '' ">and operation_info like concat('%', #{operationInfo}, '%')</if>
			<if test="exp != null and exp != '' ">and exp like concat('%', #{exp}, '%')</if>
			<if test="effect != null and effect != '' ">and effect like concat('%', #{effect}, '%')</if>
			<if test="problem != null and problem != '' ">and problem like concat('%', #{problem}, '%')</if>
			<if test="structure != null and structure != '' ">and structure like concat('%', #{structure}, '%')</if>
			<if test="teacherCaseInfo != null and teacherCaseInfo != '' ">and teacher_case_info like concat('%', #{teacherCaseInfo}, '%')</if>
			<if test="teacherCaseName != null and teacherCaseName != '' ">and teacher_case_name like concat('%', #{teacherCaseName}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.cases_edition_id
	 		,t.background
	 		,t.goal
	 		,t.content
	 		,t.eval
	 		,t.operation_info
	 		,t.exp
	 		,t.effect
	 		,t.problem
	 		,t.structure
	 		,t.teacher_case_info
	 		,t.teacher_case_name
	 		,t.is_delete
	 		,t.create_date
	 		,t.update_date
		from (
			select a.* from p_course_cases_info_edition a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getCourseCasesInfoEditionListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCourseCasesInfoEditionByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>