<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseCasesEditionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseCasesEditionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesId" column="cases_id"/>
        <result property="userId" column="user_id"/>
        <result property="schoolName" column="school_name"/>
        <result property="courseName" column="course_name"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="countyId" column="county_id"/>
        <result property="phase" column="phase"/>
        <result property="year" column="year"/>
        <result property="picture" column="picture"/>
        <result property="status" column="status"/>
        <result property="operation" column="operation"/>
        <result property="phone" column="phone"/>
        <result property="introduction" column="introduction"/>
        <result property="views" column="views"/>
        <result property="isAuth" column="is_auth"/>
        <result property="homeType" column="home_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="filePath" column="file_path"/>
        <result property="wordPath" column="word_path"/>
        <result property="isUpload" column="is_upload"/>
        <result property="customerId" column="customer_id"/>
        <result property="isExcellent" column="is_excellent"/>
        <result property="userOid" column="user_oid"/>
        <result property="sourceType" column="source_type"/>
        <result property="bestVerifyType" column="best_verify_type"/>
        <result property="templateType" column="template_type"/>
        <result property="templateLayoutOne" column="template_layout_one"/>
        <result property="templateLayoutTwo" column="template_layout_two"/>
        <result property="templateLayoutThree" column="template_layout_three"/>
        <result property="holdType" column="hold_type"/>
        <result property="normalVerifyType" column="normal_verify_type"/>
        <result property="verifyProcessType" column="verify_process_type"/>
        <result property="isExcellentLabel" column="is_excellent_label"/>
        <result property="viewPermission" column="view_permission"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="userId != null ">and user_id = #{userId}</if>
			<if test="schoolName != null and schoolName != '' ">and school_name like concat('%', #{schoolName}, '%')</if>
			<if test="courseName != null and courseName != '' ">and course_name like concat('%', #{courseName}, '%')</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="countyId != null ">and county_id = #{countyId}</if>
			<if test="phase != null ">and phase = #{phase}</if>
			<if test="year != null ">and year = #{year}</if>
			<if test="picture != null and picture != '' ">and picture like concat('%', #{picture}, '%')</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="operation != null and operation != '' ">and operation like concat('%', #{operation}, '%')</if>
			<if test="phone != null and phone != '' ">and phone like concat('%', #{phone}, '%')</if>
			<if test="introduction != null and introduction != '' ">and introduction like concat('%', #{introduction}, '%')</if>
			<if test="views != null ">and views = #{views}</if>
			<if test="isAuth != null ">and is_auth = #{isAuth}</if>
			<if test="homeType != null ">and home_type = #{homeType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
			<if test="filePath != null and filePath != '' ">and file_path like concat('%', #{filePath}, '%')</if>
			<if test="wordPath != null and wordPath != '' ">and word_path like concat('%', #{wordPath}, '%')</if>
			<if test="isUpload != null ">and is_upload = #{isUpload}</if>
			<if test="customerId != null ">and customer_id = #{customerId}</if>
			<if test="isExcellent != null ">and is_excellent = #{isExcellent}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="bestVerifyType != null ">and best_verify_type = #{bestVerifyType}</if>
			<if test="templateType != null ">and template_type = #{templateType}</if>
			<if test="templateLayoutOne != null and templateLayoutOne != '' ">and template_layout_one like concat('%', #{templateLayoutOne}, '%')</if>
			<if test="templateLayoutTwo != null and templateLayoutTwo != '' ">and template_layout_two like concat('%', #{templateLayoutTwo}, '%')</if>
			<if test="templateLayoutThree != null and templateLayoutThree != '' ">and template_layout_three like concat('%', #{templateLayoutThree}, '%')</if>
			<if test="holdType != null ">and hold_type = #{holdType}</if>
			<if test="normalVerifyType != null ">and normal_verify_type = #{normalVerifyType}</if>
			<if test="verifyProcessType != null ">and verify_process_type = #{verifyProcessType}</if>
			<if test="isExcellentLabel != null and isExcellentLabel != '' ">and is_excellent_label like concat('%', #{isExcellentLabel}, '%')</if>
			<if test="viewPermission != null and viewPermission != '' ">and view_permission like concat('%', #{viewPermission}, '%')</if>
			<if test="casesIds != null and casesIds.size() != 0">
				and cases_id in
				<foreach collection="casesIds" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="userId != null ">and user_id = #{userId}</if>
			<if test="schoolName != null and schoolName != '' ">and school_name like concat('%', #{schoolName}, '%')</if>
			<if test="courseName != null and courseName != '' ">and course_name like concat('%', #{courseName}, '%')</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="countyId != null ">and county_id = #{countyId}</if>
			<if test="phase != null ">and phase = #{phase}</if>
			<if test="year != null ">and year = #{year}</if>
			<if test="picture != null and picture != '' ">and picture like concat('%', #{picture}, '%')</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="operation != null and operation != '' ">and operation like concat('%', #{operation}, '%')</if>
			<if test="phone != null and phone != '' ">and phone like concat('%', #{phone}, '%')</if>
			<if test="introduction != null and introduction != '' ">and introduction like concat('%', #{introduction}, '%')</if>
			<if test="views != null ">and views = #{views}</if>
			<if test="isAuth != null ">and is_auth = #{isAuth}</if>
			<if test="homeType != null ">and home_type = #{homeType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
			<if test="filePath != null and filePath != '' ">and file_path like concat('%', #{filePath}, '%')</if>
			<if test="wordPath != null and wordPath != '' ">and word_path like concat('%', #{wordPath}, '%')</if>
			<if test="isUpload != null ">and is_upload = #{isUpload}</if>
			<if test="customerId != null ">and customer_id = #{customerId}</if>
			<if test="isExcellent != null ">and is_excellent = #{isExcellent}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="bestVerifyType != null ">and best_verify_type = #{bestVerifyType}</if>
			<if test="templateType != null ">and template_type = #{templateType}</if>
			<if test="templateLayoutOne != null and templateLayoutOne != '' ">and template_layout_one like concat('%', #{templateLayoutOne}, '%')</if>
			<if test="templateLayoutTwo != null and templateLayoutTwo != '' ">and template_layout_two like concat('%', #{templateLayoutTwo}, '%')</if>
			<if test="templateLayoutThree != null and templateLayoutThree != '' ">and template_layout_three like concat('%', #{templateLayoutThree}, '%')</if>
			<if test="holdType != null ">and hold_type = #{holdType}</if>
			<if test="normalVerifyType != null ">and normal_verify_type = #{normalVerifyType}</if>
			<if test="verifyProcessType != null ">and verify_process_type = #{verifyProcessType}</if>
			<if test="isExcellentLabel != null and isExcellentLabel != '' ">and is_excellent_label like concat('%', #{isExcellentLabel}, '%')</if>
			<if test="viewPermission != null and viewPermission != '' ">and view_permission like concat('%', #{viewPermission}, '%')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.cases_id
	 		,t.user_id
	 		,t.school_name
	 		,t.course_name
	 		,t.province_id
	 		,t.city_id
	 		,t.county_id
	 		,t.phase
	 		,t.year
	 		,t.picture
	 		,t.status
	 		,t.operation
	 		,t.phone
	 		,t.introduction
	 		,t.views
	 		,t.is_auth
	 		,t.home_type
	 		,t.is_delete
	 		,t.create_date
	 		,t.update_date
	 		,t.file_path
	 		,t.word_path
	 		,t.is_upload
	 		,t.customer_id
	 		,t.is_excellent
	 		,t.user_oid
	 		,t.source_type
	 		,t.best_verify_type
	 		,t.template_type
	 		,t.template_layout_one
	 		,t.template_layout_two
	 		,t.template_layout_three
	 		,t.hold_type
	 		,t.normal_verify_type
	 		,t.verify_process_type
	 		,t.is_excellent_label
	 		,t.view_permission
		from (
			select a.* from p_course_cases_edition a
		 ) t

	</sql>

	<select id="getCourseCasesEditionListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseCasesEditionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCourseCasesEditionByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseCasesEditionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>