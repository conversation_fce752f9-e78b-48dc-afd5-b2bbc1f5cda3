<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseDiscussSectionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseDiscussSectionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesId" column="cases_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="superiorIds" column="superior_ids"/>
        <result property="userOid" column="user_oid"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>



	<select id="getCourseDiscussSectionListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo">
		select
		*
		from course_discuss_section t
		<where>
		    and is_delete = 0
			<if test="id != null ">and id = #{id}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="parentId != null ">and parent_id = #{parentId}</if>
			<if test="superiorIds != null and superiorIds != '' ">and superior_ids like concat('%', #{superiorIds}, '%')</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
		</where>
		order by create_time desc
	</select>

	<update id="deleteByParentId" >
		update course_discuss_section set is_delete = 1 where is_delete = 0 and find_in_set(#{parentId},superior_ids )
	</update>

    <update id="addReplyNumById">
		update course_discuss_section set reply_num = if(reply_num + #{num} &lt; 0,0,reply_num + #{num}) where is_delete = 0 and id = #{id}
	</update>
</mapper>