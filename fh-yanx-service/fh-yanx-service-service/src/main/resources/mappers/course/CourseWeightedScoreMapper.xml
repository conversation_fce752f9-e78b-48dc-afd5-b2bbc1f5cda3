<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseWeightedScoreMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseWeightedScoreDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesId" column="cases_id"/>
        <result property="normalBestType" column="normal_best_type"/>
        <result property="weightedScoreOne" column="weighted_score_one"/>
        <result property="weightedScoreTwo" column="weighted_score_two"/>
        <result property="weightedScoreThree" column="weighted_score_three"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="normalBestType != null ">and normal_best_type = #{normalBestType}</if>
			<if test="weightedScoreOne != null ">and weighted_score_one = #{weightedScoreOne}</if>
			<if test="weightedScoreTwo != null ">and weighted_score_two = #{weightedScoreTwo}</if>
			<if test="weightedScoreThree != null ">and weighted_score_three = #{weightedScoreThree}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="casesIds != null">
				and cases_id in
				<foreach collection="casesIds" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.cases_id
	 		,t.normal_best_type
	 		,t.weighted_score_one
	 		,t.weighted_score_two
	 		,t.weighted_score_three
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from course_weighted_score a
		 ) t

	</sql>

	<select id="getCourseWeightedScoreListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCourseWeightedScoreByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>