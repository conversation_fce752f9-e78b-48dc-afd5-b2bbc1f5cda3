<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseCasesGradeEditionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseCasesGradeEditionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesEditionId" column="cases_edition_id"/>
        <result property="name" column="name"/>
        <result property="grade" column="grade"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesEditionId != null ">and cases_edition_id = #{casesEditionId}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="grade != null ">and grade = #{grade}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesEditionId != null ">and cases_edition_id = #{casesEditionId}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="grade != null ">and grade = #{grade}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.cases_edition_id
	 		,t.name
	 		,t.grade
	 		,t.is_delete
	 		,t.create_date
	 		,t.update_date
		from (
			select a.* from p_course_cases_grade_edition a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getCourseCasesGradeEditionListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseCasesGradeEditionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCourseCasesGradeEditionByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseCasesGradeEditionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>