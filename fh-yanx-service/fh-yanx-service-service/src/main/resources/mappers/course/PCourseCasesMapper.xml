<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.PCourseCasesMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.PCourseCasesDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="schoolName" column="school_name"/>
        <result property="courseName" column="course_name"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="countyId" column="county_id"/>
        <result property="phase" column="phase"/>
        <result property="year" column="year"/>
        <result property="picture" column="picture"/>
        <result property="status" column="status"/>
        <result property="operation" column="operation"/>
        <result property="phone" column="phone"/>
        <result property="introduction" column="introduction"/>
        <result property="views" column="views"/>
        <result property="isAuth" column="is_auth"/>
        <result property="homeType" column="home_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="filePath" column="file_path"/>
        <result property="wordPath" column="word_path"/>
        <result property="isUpload" column="is_upload"/>
        <result property="customerId" column="customer_id"/>
        <result property="isExcellent" column="is_excellent"/>
        <result property="userOid" column="user_oid"/>
        <result property="sourceType" column="source_type"/>
        <result property="bestVerifyType" column="best_verify_type"/>
        <result property="templateType" column="template_type"/>
        <result property="templateLayoutOne" column="template_layout_one"/>
        <result property="templateLayoutTwo" column="template_layout_two"/>
        <result property="templateLayoutThree" column="template_layout_three"/>
        <result property="normalVerifyType" column="normal_verify_type"/>
        <result property="verifyProcessType" column="verify_process_type"/>
        <result property="isExcellentLabel" column="is_excellent_label"/>
		<result property="viewPermission" column="view_permission"/>
		<result property="isSupplement" column="is_supplement"/>
		<result property="eduPhilosophy" column="edu_philosophy"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userId != null ">and user_id = #{userId}</if>
			<if test="schoolName != null and schoolName != '' ">and school_name like concat('%', #{schoolName}, '%')</if>
			<if test="courseName != null and courseName != '' ">and course_name like concat('%', #{courseName}, '%')</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="countyId != null ">and county_id = #{countyId}</if>
			<if test="phase != null ">and phase = #{phase}</if>
			<if test="year != null ">and year = #{year}</if>
			<if test="picture != null and picture != '' ">and picture like concat('%', #{picture}, '%')</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="operation != null and operation != '' ">and operation like concat('%', #{operation}, '%')</if>
			<if test="phone != null and phone != '' ">and phone like concat('%', #{phone}, '%')</if>
			<if test="introduction != null and introduction != '' ">and introduction like concat('%', #{introduction}, '%')</if>
			<if test="views != null ">and views = #{views}</if>
			<if test="isAuth != null ">and is_auth = #{isAuth}</if>
			<if test="homeType != null ">and home_type = #{homeType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
			<if test="filePath != null and filePath != '' ">and file_path like concat('%', #{filePath}, '%')</if>
			<if test="wordPath != null and wordPath != '' ">and word_path like concat('%', #{wordPath}, '%')</if>
			<if test="isUpload != null ">and is_upload = #{isUpload}</if>
			<if test="customerId != null ">and customer_id = #{customerId}</if>
			<if test="isExcellent != null ">and is_excellent = #{isExcellent}</if>
			<if test="userOid != null ">and user_oid = #{userOid}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="bestVerifyType != null ">and best_verify_type = #{bestVerifyType}</if>
			<if test="templateType != null ">and template_type = #{templateType}</if>
			<if test="templateLayoutOne != null ">and template_layout_one = #{templateLayoutOne}</if>
			<if test="templateLayoutTwo != null ">and template_layout_two = #{templateLayoutTwo}</if>
			<if test="templateLayoutThree != null ">and template_layout_three = #{templateLayoutThree}</if>
			<if test="holdType != null">and hold_type=#{holdType}</if>
			<if test="normalVerifyType != null">and normal_verify_type=#{normalVerifyType}</if>
			<if test="verifyProcessType != null">and verify_process_type=#{verifyProcessType}</if>
			<if test="isExcellentLabel != null">and is_excellent_label=#{isExcellentLabel}</if>
			<if test="isSupplement != null">and is_supplement=#{isSupplement}</if>
			<if test="eduPhilosophy != null and eduPhilosophy != ''">and edu_philosophy like concat('%', #{eduPhilosophy}, '%')</if>
			<if test="viewPermission != null and viewPermission != ''">and find_in_set(#{viewPermission}, view_permission)</if>
			<if test="courseCasesIds != null and courseCasesIds.size() != 0">
				and id in
				<foreach item="item" collection="courseCasesIds" separator="," open="(" close=")" index="">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.user_id
	 		,t.school_name
	 		,t.course_name
	 		,t.province_id
	 		,t.city_id
	 		,t.county_id
	 		,t.phase
	 		,t.year
	 		,t.picture
	 		,t.status
	 		,t.operation
	 		,t.phone
	 		,t.introduction
	 		,t.views
	 		,t.is_auth
	 		,t.home_type
	 		,t.is_delete
	 		,t.create_date
	 		,t.update_date
	 		,t.file_path
	 		,t.word_path
	 		,t.is_upload
	 		,t.customer_id
	 		,t.is_excellent
			,t.hold_type
			,t.user_oid
			,t.source_type
			,t.best_verify_type
			,t.template_type
			,t.template_layout_one
			,t.template_layout_two
			,t.template_layout_three
			,t.normal_verify_type
			,t.verify_process_type
			,t.is_excellent_label
			,t.view_permission
			,t.is_supplement
			,t.edu_philosophy
		    <if test="courseSortType != 0">
				,t.weighted_score_one
				,t.weighted_score_two
				,t.weighted_score_three
			</if>
			<if test="courseSortType == 7 or courseSortType == 8">
				,t.avg_score
			</if>
			<if test="queryAdoptType">
				,t.adopt_type
			</if>
			<if test="verifyUserOid != null and verifyUserOid != ''">
				,t.need_score
			</if>
			<if test="queryStoreType">
				,t.course_store_time
			</if>
		from (
			 select a.*
				<if test="verifyUserOid != null and verifyUserOid != ''">
					,crec.need_score
				</if>
				<if test="queryStoreType">
					,cs.course_store_time
				</if>
			<if test="courseSortType != 0">
				,ifnull(cws.weighted_score_one,0) as weighted_score_one
				,ifnull(cws.weighted_score_two,0) as weighted_score_two
				,ifnull(cws.weighted_score_three,0) as weighted_score_three
			</if>
			<if test="courseSortType == 7 or courseSortType == 8">
			 	,ifnull(m.avg_score,0) as avg_score
			</if>
			<if test="queryAdoptType">
				,cvl.adopt_type
			</if>
			 from p_course_cases a
			<!-- 只查询专家的课程 -->
			<if test="verifyUserOid != null and verifyUserOid != ''">
				join course_review_expert_config crec on a.id =crec.cases_id and crec.user_oid=#{verifyUserOid}
				<if test="reviewConfigVerifyProcessType != null">and crec.verify_process_type=#{reviewConfigVerifyProcessType}</if>
			    and crec.is_delete=0
			</if>
			<!-- 如果type不为空则增加type作为查询条件-->
			<if test="type != null">
				join p_course_cases_type pcct on a.id = pcct.cases_id and pcct.type = #{type} and pcct.is_delete = 0
			</if>
			<if test="types != null and types.size() != 0">
				join p_course_cases_type pcct2 on a.id = pcct2.cases_id and pcct2.is_delete = 0 and pcct2.type in
				<foreach item="item" collection="types" separator="," open="(" close=")" index="">
					#{item}
				</foreach>
			</if>
			<if test="courseSortType !=0">
				left join course_weighted_score cws on a.id = cws.cases_id and cws.is_delete = 0
			</if>
		    <if test="courseSortType == 7 or courseSortType == 8">
				left join (select cvl.cases_id,AVG(cvl.verify_score_total) as avg_score from course_verify_log cvl where cvl.is_delete=0 group by cvl.cases_id)m on a.id = m.cases_id
			</if>
			<if test="queryAdoptType">
				left join (select * from course_verify_log
				where verify_process_type = 8
				and user_oid = #{currentUserOid}
				and is_delete = 0
				<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
					and course_verify_source = 1
				</if>
				<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
					and course_verify_source = 2
				</if>
				) cvl on cvl.cases_id = a.id
			</if>
		<if test="queryStoreType">
			left join (select create_time as course_store_time, cases_id from course_store
			where is_delete = 0 and user_oid= #{currentUserOid}
			) cs on cs.cases_id = a.id
		</if>
		 ) t
	</sql>

	<select id="getPCourseCasesListByCondition" resultType="com.fh.yanx.service.course.entity.vo.PCourseCasesVo">
		<if test="queryHis == true">
			<include refid="common_select"></include>
			<include refid="common_where"></include>
			and source_type=1
			<if test="queryRecommendType != null">
				<if test="queryRecommendType">
					and exists (
					select cases_id
					from course_recommend
					where
					is_delete = 0
					and recommend_type = #{recommendType}
					and cases_id = t.id
					)
				</if>
				<if test="!queryRecommendType">
					and not exists (
					select cases_id
					from course_recommend
					where
					is_delete = 0
					and recommend_type = #{recommendType}
					and cases_id = t.id
					)
				</if>
			</if>
			union
		</if>

		<!-- 默认的查询 -->
		<include refid="common_select"></include>
        <include refid="common_where"></include>

		<if test="queryOrTypes == false">
			<if test="bestVerifyTypes != null and bestVerifyTypes.size() >0">
				AND best_verify_type in
				<foreach collection="bestVerifyTypes" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="normalVerifyTypes != null and normalVerifyTypes.size() >0">
				AND normal_verify_type in
				<foreach collection="normalVerifyTypes" item="normalVerifyType" open="(" separator="," close=")">
					#{normalVerifyType}
				</foreach>
			</if>
		</if>
		<if test="queryOrTypes == true and bestVerifyTypes != null and bestVerifyTypes.size() >0 and normalVerifyTypes != null and normalVerifyTypes.size() >0">
			AND (
			best_verify_type in
			<foreach collection="bestVerifyTypes" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			normal_verify_type in
			<foreach collection="normalVerifyTypes" item="normalVerifyType" open="(" separator="," close=")">
				#{normalVerifyType}
			</foreach>
			)
		</if>
		<if test="verifyOperateType != null and currentUserOid != null and currentUserOid != ''">
			<if test="verifyOperateType == 1">
				and not exists (
					select cases_id
					from course_verify_log
					where
						is_delete = 0
						and user_oid = #{currentUserOid}
						and cases_id = t.id
						<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
							and course_verify_source = 1
						</if>
						<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
							and course_verify_source = 2
						</if>
						and verify_process_type in (4, 7, 9, 10)
				)
			</if>
			<if test="verifyOperateType == 2">
				and exists (
					select cases_id
					from course_verify_log
					where
						is_delete = 0
						and user_oid = #{currentUserOid}
						and cases_id = t.id
						<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
							and course_verify_source = 1
						</if>
						<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
							and course_verify_source = 2
						</if>
						and verify_process_type in (4, 7, 9, 10)
				)
			</if>
		</if>
		<if test="comprehensiveOperateType ==1">
			and not exists (
			select cases_id
			from course_verify_log
			where
			is_delete = 0
			and cases_id = t.id
			and verify_process_type=4
			<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
				and course_verify_source = 1
			</if>
			<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
				and course_verify_source = 2
			</if>
			)
		</if>
		<if test="comprehensiveOperateType ==2">
			and exists (
			select cases_id
			from course_verify_log
			where
			is_delete = 0
			and cases_id = t.id
			and verify_process_type=4
			<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
				and course_verify_source = 1
			</if>
			<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
				and course_verify_source = 2
			</if>
			)
		</if>
		<if test="dispatchOperateType ==1">
			and not exists (
			select cases_id
			from course_verify_log
			where
			is_delete = 0
			and cases_id = t.id
			and verify_process_type=5
			<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
				and course_verify_source = 1
			</if>
			<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
				and course_verify_source = 2
			</if>
			)
		</if>
		<if test="dispatchOperateType ==2">
			and exists (
			select cases_id
			from course_verify_log
			where
			is_delete = 0
			and cases_id = t.id
			and verify_process_type=5
			<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
				and course_verify_source = 1
			</if>
			<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
				and course_verify_source = 2
			</if>
			)
		</if>
		<if test="adoptType == 1">
			and exists (
			select cases_id
			from course_verify_log
			where
			is_delete = 0
			and cases_id = t.id
			and verify_process_type=8
			and adopt_type = 1
			<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
				and course_verify_source = 1
			</if>
			<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
				and course_verify_source = 2
			</if>
			and user_oid = #{currentUserOid}
			)
		</if>
		<if test="adoptType == 2">
			and exists (
			select cases_id
			from course_verify_log
			where
			is_delete = 0
			and cases_id = t.id
			and verify_process_type=8
			and adopt_type = 2
			<if test="normalVerifyTypes != null and normalVerifyTypes.size() != 0">
				and course_verify_source = 1
			</if>
			<if test="bestVerifyTypes != null and bestVerifyTypes.size() != 0">
				and course_verify_source = 2
			</if>
			and user_oid = #{currentUserOid}
			)
		</if>
		<if test="queryStoreType == true">
			and exists (
			select cases_id
			from course_store
			where
			is_delete = 0
			and user_oid = #{storeUserOid}
			and cases_id = t.id
			)
		</if>
		<if test="queryRecommendType != null">
			<if test="queryRecommendType">
				and exists (
				select cases_id
				from course_recommend
				where
				is_delete = 0
				and recommend_type = #{recommendType}
				and cases_id = t.id
				)
			</if>
			<if test="!queryRecommendType">
				and not exists (
				select cases_id
				from course_recommend
				where
				is_delete = 0
				and recommend_type = #{recommendType}
				and cases_id = t.id
				)
			</if>
		</if>
		<if test="dispatchType!=null">
			and
			<if test="dispatchType==1">
				not
			</if> exists (
			select cases_id
			from course_review_expert_config
			where
			is_delete = 0
			and expert_type = 3
			and cases_id = t.id
			)
		</if>
		<if test="auditDispatchOperateType!=null">
			and
			<if test="auditDispatchOperateType==1">
				not
			</if> exists (
			select cases_id
			from course_review_expert_config
			where
			is_delete = 0
			and expert_type = 1
			and cases_id = t.id
			)
		</if>
		<if test="guideDispatchOperateType!=null">
			and
			<if test="guideDispatchOperateType==1">
				not
			</if> exists (
			select cases_id
			from course_review_expert_config
			where
			is_delete = 0
			and expert_type = 2
			and cases_id = t.id
			)
		</if>
        <if test="queryHis == true or (types != null and types.size() != 0)">
			group by id
		</if>
		<include refid="orderByCourseType"></include>
    </select>

	<!-- 专家课程列表根据固定类型排序 -->
	<sql id="orderByCourseType">
		<if test="courseSortType == 0">
			order by update_date desc
		</if>
		<if test="courseSortType == 1">
			order by weighted_score_one ASC
		</if>
		<if test="courseSortType == 2">
			order by weighted_score_one DESC
		</if>
		<if test="courseSortType == 3">
			order by weighted_score_two ASC
		</if>
		<if test="courseSortType == 4">
			order by weighted_score_two DESC
		</if>
		<if test="courseSortType == 5">
			order by weighted_score_three ASC
		</if>
		<if test="courseSortType == 6">
			order by weighted_score_three DESC
		</if>
		<if test="courseSortType == 7">
			order by avg_score ASC
		</if>
		<if test="courseSortType == 8">
			order by avg_score DESC
		</if>
		<if test="courseSortType == 9">
			order by course_store_time DESC
		</if>
		<if test="courseSortType == 10">
			order by if(school_name = #{courseCaseSchoolName}, 1, 2) asc, update_date desc
		</if>
	</sql>

	<select id="getPCourseCasesByCondition" resultType="com.fh.yanx.service.course.entity.vo.PCourseCasesVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getHomeList" resultType="com.fh.yanx.service.course.entity.vo.PCourseCasesVo">
		SELECT
		t1.id,
		t1.course_name as courseName,
		t1.school_name as schoolName,
		t1.operation,
		t1.phase,
		t1.year,
		t1.introduction,
		t1.views,
		t1.picture,
		t1.home_type,
		t1.is_excellent,
		t1.hold_type,
		t1.user_oid,
		t1.source_type,
		t1.best_verify_type,
		t1.template_type,
		t1.template_layout_one,
		t1.template_layout_two,
		t1.template_layout_three,
		t1.normal_verify_type,
		t1.verify_process_type,
		t1.is_excellent_label,
		t1.is_supplement,
		t1.edu_philosophy
		FROM
		p_course_cases t1
		<if test="grade != null">
			JOIN p_course_cases_grade t2 ON t1.id = t2.cases_id
		</if>
		<if test="type != null">
			JOIN p_course_cases_type t3 ON t1.id = t3.cases_id
		</if>
		WHERE
		t1.is_delete = 0 and t1.hold_type=1
		<!--AND t1.is_auth = 1-->
		<if test="phase != null">AND t1.phase = #{phase}</if>
		<if test="grade != null">AND t2.is_delete = 0 AND t2.grade = #{grade}</if>
		<if test="provinceId != null">AND t1.province_id = #{provinceId}</if>
		<if test="type != null">AND t3.is_delete = 0 AND t3.type = #{type}</if>
		<if test="year != null">AND t1.year = #{year}</if>
		<if test="homeType != null"> AND t1.home_type=#{homeType}</if>
		<if test="isExcellent != null"> AND t1.is_excellent=#{isExcellent}</if>
		<if test="courseName != null and courseName !=''"> AND t1.course_name like concat('%',#{courseName},'%')</if>
		<if test="userOid != null and userOid !=''"> AND t1.user_oid = #{userOid}</if>
		<if test="sourceType != null"> AND t1.source_type = #{sourceType}</if>
		<if test="bestVerifyType != null"> AND t1.best_verify_type = #{bestVerifyType}</if>
		<if test="normalVerifyType != null"> AND t1.normal_verify_type = #{normalVerifyType}</if>
		<if test="verifyProcessType != null">and t1.verify_process_type=#{verifyProcessType}</if>
		<if test="isExcellentLabel != null and isExcellentLabel != ''"> and FIND_IN_SET(#{isExcellentLabel},t1.is_excellent_label)</if>
		<if test="templateType != null"> AND t1.template_type = #{templateType}</if>
		<if test="templateLayoutOne != null"> AND t1.template_layout_one like concat('%', #{templateLayoutOne},'%')</if>
		<if test="templateLayoutTwo != null"> AND t1.template_layout_two like concat('%', #{templateLayoutTwo},'%')</if>
		<if test="templateLayoutThree != null"> AND t1.template_layout_three like concat('%', #{templateLayoutThree},'%')</if>
		<!-- 首页查询以上架为准，不根据审核类型以及状态来过滤
		and (source_type=1 or (source_type=2 and best_verify_type=2))
		-->
		<if test="searchKey != null and searchKey != ''">
			and (t1.course_name like concat('%',#{searchKey},'%') or t1.school_name like concat('%',#{searchKey},'%') or t1.operation like concat('%',#{searchKey},'%'))
		</if>
	</select>

	<select id="topList" resultType="com.fh.yanx.service.course.entity.vo.PCourseCasesVo">
		SELECT
		t1.id,
		t1.course_name as courseName,
		t1.school_name as schoolName,
		t1.operation,
		t1.phase,
		t1.year,
		t1.introduction,
		t1.views,
		t1.picture,
		t1.is_excellent,
		t1.hold_type,
		t1.user_oid,
		t1.source_type,
		t1.best_verify_type,
		t1.template_type,
		t1.template_layout_one,
		t1.template_layout_two,
		t1.template_layout_three,
		t1.normal_verify_type,
		t1.verify_process_type,
		t1.is_excellent_label,
		t4.home_type,
		t4.recommend_text,
		t1.is_supplement,
		t1.edu_philosophy
		FROM
		p_course_cases t1
		join course_home_config t4 on t1.id = t4.cases_id and t4.is_delete=0
		WHERE
		t1.is_delete = 0 and t1.hold_type=1
		<!--AND t1.is_auth = 1-->
		<if test="isExcellent != null"> and t4.is_excellent=#{isExcellent}</if>
		<if test="homeType != null">and t4.home_type=#{homeType}</if>
		order by t4.home_index
	</select>

</mapper>