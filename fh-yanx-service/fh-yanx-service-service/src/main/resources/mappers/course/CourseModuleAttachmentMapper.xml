<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.course.mapper.CourseModuleAttachmentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.course.entity.dto.CourseModuleAttachmentDto" id="BaseResultMap">
        <result property="courseModuleAttachmentId" column="course_module_attachment_id"/>
        <result property="courseModuleId" column="course_module_id"/>
        <result property="casesId" column="cases_id"/>
        <result property="resourceType" column="resource_type"/>
        <result property="attachmentMediaId" column="attachment_media_id"/>
        <result property="attachmentMediaIdCompress" column="attachment_media_id_compress"/>
        <result property="attachmentMediaType" column="attachment_media_type"/>
        <result property="attachmentMediaSuffix" column="attachment_media_suffix"/>
        <result property="attachmentMediaUrl" column="attachment_media_url"/>
        <result property="attachmentMediaUrlCompress" column="attachment_media_url_compress"/>
        <result property="attachmentMediaName" column="attachment_media_name"/>
        <result property="attachmentMediaNameOri" column="attachment_media_name_ori"/>
        <result property="attachmentMediaTransferType" column="attachment_media_transfer_type"/>
        <result property="attachmentMediaOnlineUrl" column="attachment_media_online_url"/>
        <result property="attachmentMediaSort" column="attachment_media_sort"/>
        <result property="downloadType" column="download_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="uuid" column="uuid"/>
        <result property="content" column="content"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="courseModuleAttachmentId != null ">and course_module_attachment_id = #{courseModuleAttachmentId}</if>
			<if test="courseModuleId != null ">and course_module_id = #{courseModuleId}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="resourceType != null ">and resource_type = #{resourceType}</if>
			<if test="attachmentMediaId != null and attachmentMediaId != '' ">and attachment_media_id like concat('%', #{attachmentMediaId}, '%')</if>
			<if test="attachmentMediaIdCompress != null and attachmentMediaIdCompress != '' ">and attachment_media_id_compress like concat('%', #{attachmentMediaIdCompress}, '%')</if>
			<if test="attachmentMediaType != null ">and attachment_media_type = #{attachmentMediaType}</if>
			<if test="attachmentMediaSuffix != null and attachmentMediaSuffix != '' ">and attachment_media_suffix like concat('%', #{attachmentMediaSuffix}, '%')</if>
			<if test="attachmentMediaUrl != null and attachmentMediaUrl != '' ">and attachment_media_url like concat('%', #{attachmentMediaUrl}, '%')</if>
			<if test="attachmentMediaUrlCompress != null and attachmentMediaUrlCompress != '' ">and attachment_media_url_compress like concat('%', #{attachmentMediaUrlCompress}, '%')</if>
			<if test="attachmentMediaName != null and attachmentMediaName != '' ">and attachment_media_name like concat('%', #{attachmentMediaName}, '%')</if>
			<if test="attachmentMediaNameOri != null and attachmentMediaNameOri != '' ">and attachment_media_name_ori like concat('%', #{attachmentMediaNameOri}, '%')</if>
			<if test="attachmentMediaTransferType != null ">and attachment_media_transfer_type = #{attachmentMediaTransferType}</if>
			<if test="attachmentMediaOnlineUrl != null and attachmentMediaOnlineUrl != '' ">and attachment_media_online_url like concat('%', #{attachmentMediaOnlineUrl}, '%')</if>
			<if test="attachmentMediaSort != null ">and attachment_media_sort = #{attachmentMediaSort}</if>
			<if test="downloadType != null ">and download_type = #{downloadType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="courseModuleIds != null and courseModuleIds.size()>0">
				and course_module_id in
				<foreach collection="courseModuleIds" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="uuid != null ">and uuid = #{uuid}</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.course_module_attachment_id
	 		,t.course_module_id
	 		,t.cases_id
	 		,t.resource_type
	 		,t.attachment_media_id
	 		,t.attachment_media_id_compress
	 		,t.attachment_media_type
	 		,t.attachment_media_suffix
	 		,t.attachment_media_url
	 		,t.attachment_media_url_compress
	 		,t.attachment_media_name
	 		,t.attachment_media_name_ori
	 		,t.attachment_media_transfer_type
	 		,t.attachment_media_online_url
	 		,t.attachment_media_sort
	 		,t.download_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.uuid
			,t.content
		from (
			 select a.* from course_module_attachment a
		 ) t

	</sql>

	<select id="getCourseModuleAttachmentListByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCourseModuleAttachmentByCondition" resultType="com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>