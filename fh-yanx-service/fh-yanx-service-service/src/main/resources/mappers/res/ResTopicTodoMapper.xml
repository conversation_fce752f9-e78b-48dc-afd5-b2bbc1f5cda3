<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicTodoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicTodoDto" id="BaseResultMap">
        <result property="todoId" column="todo_id"/>
        <result property="todoType" column="todo_type"/>
        <result property="todoName" column="todo_name"/>
        <result property="todoKey" column="todo_key"/>
        <result property="todoMsg" column="todo_msg"/>
        <result property="todoBusinessType" column="todo_business_type"/>
        <result property="todoBusinessId" column="todo_business_id"/>
        <result property="todoJson" column="todo_json"/>
        <result property="userOid" column="user_oid"/>
        <result property="todoStatus" column="todo_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="todoNum" column="todo_num"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="todoId != null ">and todo_id = #{todoId}</if>
			<if test="todoType != null ">and todo_type = #{todoType}</if>
			<if test="todoName != null and todoName!= ''">and todo_name = #{todoName}</if>
			<if test="todoKey != null and todoKey!= ''">and todo_key = #{todoKey}</if>
			<if test="todoMsg != null and todoMsg!= ''">and todo_msg = #{todoMsg}</if>
			<if test="todoBusinessType != null ">and todo_business_type = #{todoBusinessType}</if>
			<if test="todoBusinessId != null ">and todo_business_id = #{todoBusinessId}</if>
			<if test="todoJson != null and todoJson != '' ">and todo_json like concat('%', #{todoJson}, '%')</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="todoStatus != null ">and todo_status = #{todoStatus}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="todoNum != null ">and todo_num = #{todoNum}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.todo_id
	 		,t.todo_type
	 		,t.todo_name
	 		,t.todo_key
	 		,t.todo_msg
	 		,t.todo_business_type
	 		,t.todo_business_id
	 		,t.todo_json
	 		,t.user_oid
	 		,t.todo_status
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.todo_num
		from (
			 select a.* from res_topic_todo a
			 <if test="todoBusinessType == 1">
				 join res_topic b on a.todo_business_id = b.topic_id and b.is_delete=0
			 </if>
		 ) t

	</sql>

	<select id="getResTopicTodoListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicTodoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>