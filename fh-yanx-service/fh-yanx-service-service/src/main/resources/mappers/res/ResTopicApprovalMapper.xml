<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicApprovalMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicApprovalDto" id="BaseResultMap">
        <result property="topicApprovalId" column="topic_approval_id"/>
        <result property="topicId" column="topic_id"/>
        <result property="verifyTeacher" column="verify_teacher"/>
        <result property="topicNameSug" column="topic_name_sug"/>
        <result property="topicTypeSug" column="topic_type_sug"/>
        <result property="relSubjectSug" column="rel_subject_sug"/>
        <result property="topicDescSug" column="topic_desc_sug"/>
        <result property="topicBackSug" column="topic_back_sug"/>
        <result property="topicGoalSug" column="topic_goal_sug"/>
        <result property="topicContentSug" column="topic_content_sug"/>
        <result property="topicMethodSug" column="topic_method_sug"/>
        <result property="topicConditionSug" column="topic_condition_sug"/>
        <result property="topicPlanSug" column="topic_plan_sug"/>
        <result property="topicExpectedSug" column="topic_expected_sug"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="topicApprovalId != null ">and topic_approval_id = #{topicApprovalId}</if>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="verifyTeacher != null and verifyTeacher != '' ">and verify_teacher like concat('%', #{verifyTeacher}, '%')</if>
			<if test="topicNameSug != null and topicNameSug != '' ">and topic_name_sug like concat('%', #{topicNameSug}, '%')</if>
			<if test="topicTypeSug != null and topicTypeSug != '' ">and topic_type_sug like concat('%', #{topicTypeSug}, '%')</if>
			<if test="relSubjectSug != null and relSubjectSug != '' ">and rel_subject_sug like concat('%', #{relSubjectSug}, '%')</if>
			<if test="topicDescSug != null and topicDescSug != '' ">and topic_desc_sug like concat('%', #{topicDescSug}, '%')</if>
			<if test="topicBackSug != null and topicBackSug != '' ">and topic_back_sug like concat('%', #{topicBackSug}, '%')</if>
			<if test="topicGoalSug != null and topicGoalSug != '' ">and topic_goal_sug like concat('%', #{topicGoalSug}, '%')</if>
			<if test="topicContentSug != null and topicContentSug != '' ">and topic_content_sug like concat('%', #{topicContentSug}, '%')</if>
			<if test="topicMethodSug != null and topicMethodSug != '' ">and topic_method_sug like concat('%', #{topicMethodSug}, '%')</if>
			<if test="topicConditionSug != null and topicConditionSug != '' ">and topic_condition_sug like concat('%', #{topicConditionSug}, '%')</if>
			<if test="topicPlanSug != null and topicPlanSug != '' ">and topic_plan_sug like concat('%', #{topicPlanSug}, '%')</if>
			<if test="topicExpectedSug != null and topicExpectedSug != '' ">and topic_expected_sug like concat('%', #{topicExpectedSug}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.topic_approval_id
	 		,t.topic_id
	 		,t.verify_teacher
	 		,t.topic_name_sug
	 		,t.topic_type_sug
	 		,t.rel_subject_sug
	 		,t.topic_desc_sug
	 		,t.topic_back_sug
	 		,t.topic_goal_sug
	 		,t.topic_content_sug
	 		,t.topic_method_sug
	 		,t.topic_condition_sug
	 		,t.topic_plan_sug
	 		,t.topic_expected_sug
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_topic_approval a
		 ) t

	</sql>

	<select id="getResTopicApprovalListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicApprovalVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>