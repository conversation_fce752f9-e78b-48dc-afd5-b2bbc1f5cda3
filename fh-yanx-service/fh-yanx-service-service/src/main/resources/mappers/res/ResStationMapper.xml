<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResStationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResStationDto" id="BaseResultMap">
        <result property="stationId" column="station_id"/>
        <result property="stationName" column="station_name"/>
        <result property="stationDesc" column="station_desc"/>
        <result property="logoFileNameOri" column="logo_file_name_ori"/>
        <result property="logoFileName" column="logo_file_name"/>
        <result property="logoFileUrl" column="logo_file_url"/>
        <result property="stationType" column="station_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="stationId != null ">and station_id = #{stationId}</if>
			<if test="stationName != null and stationName != '' ">and station_name like concat('%', #{stationName}, '%')</if>
			<if test="stationDesc != null and stationDesc != '' ">and station_desc like concat('%', #{stationDesc}, '%')</if>
			<if test="logoFileNameOri != null and logoFileNameOri != '' ">and logo_file_name_ori like concat('%', #{logoFileNameOri}, '%')</if>
			<if test="logoFileName != null and logoFileName != '' ">and logo_file_name like concat('%', #{logoFileName}, '%')</if>
			<if test="logoFileUrl != null and logoFileUrl != '' ">and logo_file_url like concat('%', #{logoFileUrl}, '%')</if>
			<if test="stationType != null ">and station_type = #{stationType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.station_id
	 		,t.station_name
	 		,t.station_desc
	 		,t.logo_file_name_ori
	 		,t.logo_file_name
	 		,t.logo_file_url
	 		,t.station_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_station a
		 ) t

	</sql>

	<select id="getResStationListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResStationVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>