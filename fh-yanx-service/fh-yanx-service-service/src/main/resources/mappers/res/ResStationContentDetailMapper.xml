<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResStationContentDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResStationContentDetailDto" id="BaseResultMap">
        <result property="stationContentDetailId" column="station_content_detail_id"/>
        <result property="stationContentId" column="station_content_id"/>
        <result property="stationId" column="station_id"/>
        <result property="stationContentDetailName" column="station_content_detail_name"/>
        <result property="stationContentDetailGroup" column="station_content_detail_group"/>
        <result property="stationContentDetailIndex" column="station_content_detail_index"/>
        <result property="contentDetailFileNameOri" column="content_detail_file_name_ori"/>
        <result property="contentDetailFileName" column="content_detail_file_name"/>
        <result property="contentDetailFileUrl" column="content_detail_file_url"/>
        <result property="contentDetailFileUrlLink" column="content_detail_file_url_link"/>
        <result property="contentDetailSubname" column="content_detail_subname"/>
        <result property="contentDetailLabel" column="content_detail_label"/>
        <result property="contentDetailMember" column="content_detail_member"/>
        <result property="contentDetailCollect" column="content_detail_collect"/>
        <result property="contentDetailPv" column="content_detail_pv"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="stationContentDetailId != null ">and station_content_detail_id = #{stationContentDetailId}</if>
			<if test="stationContentId != null ">and station_content_id = #{stationContentId}</if>
			<if test="stationId != null ">and station_id = #{stationId}</if>
			<if test="stationContentDetailName != null and stationContentDetailName != '' ">and station_content_detail_name like concat('%', #{stationContentDetailName}, '%')</if>
			<if test="stationContentDetailGroup != null ">and station_content_detail_group = #{stationContentDetailGroup}</if>
			<if test="stationContentDetailIndex != null ">and station_content_detail_index = #{stationContentDetailIndex}</if>
			<if test="contentDetailFileNameOri != null and contentDetailFileNameOri != '' ">and content_detail_file_name_ori like concat('%', #{contentDetailFileNameOri}, '%')</if>
			<if test="contentDetailFileName != null and contentDetailFileName != '' ">and content_detail_file_name like concat('%', #{contentDetailFileName}, '%')</if>
			<if test="contentDetailFileUrl != null and contentDetailFileUrl != '' ">and content_detail_file_url like concat('%', #{contentDetailFileUrl}, '%')</if>
			<if test="contentDetailFileUrlLink != null and contentDetailFileUrlLink != '' ">and content_detail_file_url_link like concat('%', #{contentDetailFileUrlLink}, '%')</if>
			<if test="contentDetailSubname != null and contentDetailSubname != '' ">and content_detail_subname like concat('%', #{contentDetailSubname}, '%')</if>
			<if test="contentDetailLabel != null and contentDetailLabel != '' ">and content_detail_label like concat('%', #{contentDetailLabel}, '%')</if>
			<if test="contentDetailMember != null and contentDetailMember != '' ">and content_detail_member like concat('%', #{contentDetailMember}, '%')</if>
			<if test="contentDetailCollect != null ">and content_detail_collect = #{contentDetailCollect}</if>
			<if test="contentDetailPv != null ">and content_detail_pv = #{contentDetailPv}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.station_content_detail_id
	 		,t.station_content_id
	 		,t.station_id
	 		,t.station_content_detail_name
	 		,t.station_content_detail_group
	 		,t.station_content_detail_index
	 		,t.content_detail_file_name_ori
	 		,t.content_detail_file_name
	 		,t.content_detail_file_url
	 		,t.content_detail_file_url_link
	 		,t.content_detail_subname
	 		,t.content_detail_label
	 		,t.content_detail_member
	 		,t.content_detail_collect
	 		,t.content_detail_pv
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_station_content_detail a
		 ) t

	</sql>

	<select id="getResStationContentDetailListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResStationContentDetailVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>