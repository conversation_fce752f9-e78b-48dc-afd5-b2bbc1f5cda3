<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResStationImgMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResStationImgDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="stationId" column="station_id"/>
        <result property="imgIndex" column="img_index"/>
        <result property="imgFileNameOri" column="img_file_name_ori"/>
        <result property="imgFileName" column="img_file_name"/>
        <result property="imgFileUrl" column="img_file_url"/>
        <result property="imgFileUrlLink" column="img_file_url_link"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="stationId != null ">and station_id = #{stationId}</if>
			<if test="imgIndex != null ">and img_index = #{imgIndex}</if>
			<if test="imgFileNameOri != null and imgFileNameOri != '' ">and img_file_name_ori like concat('%', #{imgFileNameOri}, '%')</if>
			<if test="imgFileName != null and imgFileName != '' ">and img_file_name like concat('%', #{imgFileName}, '%')</if>
			<if test="imgFileUrl != null and imgFileUrl != '' ">and img_file_url like concat('%', #{imgFileUrl}, '%')</if>
			<if test="imgFileUrlLink != null and imgFileUrlLink != '' ">and img_file_url_link like concat('%', #{imgFileUrlLink}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.station_id
	 		,t.img_index
	 		,t.img_file_name_ori
	 		,t.img_file_name
	 		,t.img_file_url
	 		,t.img_file_url_link
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_station_img a
		 ) t

	</sql>

	<select id="getResStationImgListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResStationImgVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>