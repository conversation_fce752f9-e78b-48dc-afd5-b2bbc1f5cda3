<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicRepAttachmentMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicRepAttachmentDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="topicId" column="topic_id"/>
        <result property="repName" column="rep_name"/>
        <result property="repDesc" column="rep_desc"/>
        <result property="repFileNameOri" column="rep_file_name_ori"/>
        <result property="repFileName" column="rep_file_name"/>
        <result property="repFileUrl" column="rep_file_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="repType" column="rep_type"/>
        <result property="repAuthorName" column="rep_author_name"/>
        <result property="repOrganizationName" column="rep_organization_name"/>
        <result property="repPubDate" column="rep_pub_date"/>
        <result property="rsSource" column="rs_source"/>
        <result property="rsThirdType" column="rs_third_type"/>
        <result property="homeShow" column="home_show"/>
        <result property="repFileOid" column="rep_file_oid"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="topicId != null ">and topic_id = #{topicId}</if>
            <if test="repName != null and repName != '' ">and rep_name like concat('%', #{repName}, '%')</if>
            <if test="repDesc != null and repDesc != '' ">and rep_desc like concat('%', #{repDesc}, '%')</if>
            <if test="repFileNameOri != null and repFileNameOri != '' ">and rep_file_name_ori like concat('%',
                #{repFileNameOri}, '%')
            </if>
            <if test="repFileName != null and repFileName != '' ">and rep_file_name like concat('%', #{repFileName},
                '%')
            </if>
            <if test="repFileUrl != null and repFileUrl != '' ">and rep_file_url like concat('%', #{repFileUrl}, '%')
            </if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="repType != null ">and rep_type = #{repType}</if>

            <if test="repAuthorName != null ">and rep_author_name = #{repAuthorName}</if>
            <if test="repOrganizationName != null ">and rep_organization_name = #{repOrganizationName}</if>
            <if test="repPubDate != null ">and DATE_FORMAT(rep_pub_date,'%Y') =
                DATE_FORMAT(#{repPubDate},'%Y')
            </if>
            <if test="rsSource != null ">and rs_source = #{rsSource}</if>
            <if test="rsThirdType != null">and rs_third_type = #{rsThirdType}</if>
            <if test="homeShow!=null">and home_show=#{homeShow}</if>
            <if test="searchKey != null and searchKey != '' ">
                and (rep_name like concat('%', #{searchKey}, '%') or rep_desc like concat('%', #{searchKey}, '%'))
            </if>
            <if test="repFileOid != null and repFileOid != '' ">and rep_file_oid = #{repFileOid}</if>
        </where>
    </sql>

    <sql id="common_select">
		select
			t.id
	 		,t.topic_id
	 		,t.rep_name
	 		,t.rep_desc
	 		,t.rep_file_name_ori
	 		,t.rep_file_name
	 		,t.rep_file_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.rep_type
	 		,t.rep_author_name
	 		,t.rep_organization_name
	 		,t.rep_pub_date
	 		,t.rs_source
	 		,t.rs_third_type
	 		,t.organization_id
	 		,t.home_show
		    ,t.rep_file_oid
		from (
			 select a.*,b.organization_id from res_topic_rep_attachment a
			 left join res_topic b on b.topic_id=a.topic_id
        <if test="orderBy != null">
            order by ${orderBy}
        </if>
		 ) t

	</sql>

    <select id="getResTopicRepAttachmentListByCondition"
            resultType="com.fh.yanx.service.res.entity.vo.ResTopicRepAttachmentVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>

    <select id="getResTopicRepAttachmentYearsByCondition" resultType="java.lang.String" parameterType="map">
        select distinct date_format(rep_pub_date,'%Y') as year from res_topic_rep_attachment
        where rep_pub_date is not null
        order by year
    </select>
</mapper>