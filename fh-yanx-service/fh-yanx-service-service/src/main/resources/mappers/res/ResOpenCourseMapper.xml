<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResOpenCourseMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResOpenCourseDto" id="BaseResultMap">
        <result property="openCourseId" column="open_course_id"/>
        <result property="openCourseName" column="open_course_name"/>
        <result property="openCourseDesc" column="open_course_desc"/>
        <result property="openCourseView" column="open_course_view"/>
        <result property="openCourseIndex" column="open_course_index"/>
        <result property="organizationId" column="organization_id"/>
        <result property="organizationName" column="organization_name"/>
        <result property="userOid" column="user_oid"/>
        <result property="realName" column="real_name"/>
        <result property="mediaFileNameOri" column="media_file_name_ori"/>
        <result property="mediaFileName" column="media_file_name"/>
        <result property="mediaFileUrl" column="media_file_url"/>
        <result property="imgFileNameOri" column="img_file_name_ori"/>
        <result property="imgFileName" column="img_file_name"/>
        <result property="imgFileUrl" column="img_file_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="openCourseId != null ">and open_course_id = #{openCourseId}</if>
			<if test="openCourseName != null and openCourseName != '' ">and open_course_name like concat('%', #{openCourseName}, '%')</if>
			<if test="openCourseDesc != null and openCourseDesc != '' ">and open_course_desc like concat('%', #{openCourseDesc}, '%')</if>
			<if test="openCourseView != null ">and open_course_view = #{openCourseView}</if>
			<if test="openCourseIndex != null ">and open_course_index = #{openCourseIndex}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="organizationName != null and organizationName != '' ">and organization_name like concat('%', #{organizationName}, '%')</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="realName != null and realName != '' ">and real_name like concat('%', #{realName}, '%')</if>
			<if test="mediaFileNameOri != null and mediaFileNameOri != '' ">and media_file_name_ori like concat('%', #{mediaFileNameOri}, '%')</if>
			<if test="mediaFileName != null and mediaFileName != '' ">and media_file_name like concat('%', #{mediaFileName}, '%')</if>
			<if test="mediaFileUrl != null and mediaFileUrl != '' ">and media_file_url like concat('%', #{mediaFileUrl}, '%')</if>
			<if test="imgFileNameOri != null and imgFileNameOri != '' ">and img_file_name_ori like concat('%', #{imgFileNameOri}, '%')</if>
			<if test="imgFileName != null and imgFileName != '' ">and img_file_name like concat('%', #{imgFileName}, '%')</if>
			<if test="imgFileUrl != null and imgFileUrl != '' ">and img_file_url like concat('%', #{imgFileUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.open_course_id
	 		,t.open_course_name
	 		,t.open_course_desc
	 		,t.open_course_view
	 		,t.open_course_index
	 		,t.organization_id
	 		,t.organization_name
	 		,t.user_oid
	 		,t.real_name
	 		,t.media_file_name_ori
	 		,t.media_file_name
	 		,t.media_file_url
	 		,t.img_file_name_ori
	 		,t.img_file_name
	 		,t.img_file_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_open_course a
		 ) t

	</sql>

	<select id="getResOpenCourseListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResOpenCourseVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>