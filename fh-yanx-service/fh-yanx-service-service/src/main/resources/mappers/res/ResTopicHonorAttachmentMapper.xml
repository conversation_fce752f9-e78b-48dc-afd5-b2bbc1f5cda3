<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicHonorAttachmentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicHonorAttachmentDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="topicId" column="topic_id"/>
        <result property="honorName" column="honor_name"/>
        <result property="honorFileNameOri" column="honor_file_name_ori"/>
        <result property="honorFileName" column="honor_file_name"/>
        <result property="honorFileUrl" column="honor_file_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="honorFileOid" column="honor_file_oid"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="honorName != null and honorName != '' ">and honor_name like concat('%', #{honorName}, '%')</if>
			<if test="honorFileNameOri != null and honorFileNameOri != '' ">and honor_file_name_ori like concat('%', #{honorFileNameOri}, '%')</if>
			<if test="honorFileName != null and honorFileName != '' ">and honor_file_name like concat('%', #{honorFileName}, '%')</if>
			<if test="honorFileUrl != null and honorFileUrl != '' ">and honor_file_url like concat('%', #{honorFileUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="honorFileOid != null and honorFileOid != ''">and honor_file_oid = #{honorFileOid}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.topic_id
	 		,t.honor_name
	 		,t.honor_file_name_ori
	 		,t.honor_file_name
	 		,t.honor_file_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.honor_file_oid
		from (
			 select a.* from res_topic_honor_attachment a
		 ) t

	</sql>

	<select id="getResTopicHonorAttachmentListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicHonorAttachmentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>