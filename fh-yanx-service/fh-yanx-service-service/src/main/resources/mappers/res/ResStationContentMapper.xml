<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResStationContentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResStationContentDto" id="BaseResultMap">
        <result property="stationContentId" column="station_content_id"/>
        <result property="stationId" column="station_id"/>
        <result property="stationContentName" column="station_content_name"/>
        <result property="stationContentType" column="station_content_type"/>
        <result property="stationContentIndex" column="station_content_index"/>
        <result property="contentFileNameOri" column="content_file_name_ori"/>
        <result property="contentFileName" column="content_file_name"/>
        <result property="contentFileUrl" column="content_file_url"/>
        <result property="contentFileUrlLink" column="content_file_url_link"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="stationContentId != null ">and station_content_id = #{stationContentId}</if>
			<if test="stationId != null ">and station_id = #{stationId}</if>
			<if test="stationContentName != null and stationContentName != '' ">and station_content_name like concat('%', #{stationContentName}, '%')</if>
			<if test="stationContentType != null ">and station_content_type = #{stationContentType}</if>
			<if test="stationContentIndex != null ">and station_content_index = #{stationContentIndex}</if>
			<if test="contentFileNameOri != null and contentFileNameOri != '' ">and content_file_name_ori like concat('%', #{contentFileNameOri}, '%')</if>
			<if test="contentFileName != null and contentFileName != '' ">and content_file_name like concat('%', #{contentFileName}, '%')</if>
			<if test="contentFileUrl != null and contentFileUrl != '' ">and content_file_url like concat('%', #{contentFileUrl}, '%')</if>
			<if test="contentFileUrlLink != null and contentFileUrlLink != '' ">and content_file_url_link like concat('%', #{contentFileUrlLink}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.station_content_id
	 		,t.station_id
	 		,t.station_content_name
	 		,t.station_content_type
	 		,t.station_content_index
	 		,t.content_file_name_ori
	 		,t.content_file_name
	 		,t.content_file_url
	 		,t.content_file_url_link
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_station_content a
		 ) t

	</sql>

	<select id="getResStationContentListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResStationContentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>