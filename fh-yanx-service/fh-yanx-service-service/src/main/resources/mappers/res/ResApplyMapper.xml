<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResApplyMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResApplyDto" id="BaseResultMap">
        <result property="applyId" column="apply_id"/>
        <result property="realName" column="real_name"/>
        <result property="phone" column="phone"/>
        <result property="unit" column="unit"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="applyId != null ">and apply_id = #{applyId}</if>
			<if test="realName != null and realName != '' ">and real_name like concat('%', #{realName}, '%')</if>
			<if test="phone != null and informationDesc != '' ">and phone like concat('%', #{phone}, '%')</if>
			<if test="unit != null ">and unit = #{unit}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.apply_id
	 		,t.real_name
	 		,t.phone
	 		,t.unit
	 		,t.remark
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_apply a
		 ) t

	</sql>

	<select id="getResApplyListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResApplyVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>