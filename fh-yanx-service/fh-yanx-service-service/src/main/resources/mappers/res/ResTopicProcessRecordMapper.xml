<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicProcessRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicProcessRecordDto" id="BaseResultMap">
        <result property="processRecordId" column="process_record_id"/>
        <result property="topicId" column="topic_id"/>
        <result property="processName" column="process_name"/>
        <result property="topicProcess" column="topic_process"/>
        <result property="processSubmitUser" column="process_submit_user"/>
        <result property="processVerifyUser" column="process_verify_user"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="processRecordId != null ">and process_record_id = #{processRecordId}</if>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="processName != null and processName != '' ">and process_name like concat('%', #{processName}, '%')</if>
			<if test="topicProcess != null ">and topic_process = #{topicProcess}</if>
			<if test="processSubmitUser != null and processSubmitUser != '' ">and process_submit_user like concat('%', #{processSubmitUser}, '%')</if>
			<if test="processVerifyUser != null and processVerifyUser != '' ">and process_verify_user like concat('%', #{processVerifyUser}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.process_record_id
	 		,t.topic_id
	 		,t.process_name
	 		,t.topic_process
	 		,t.process_submit_user
	 		,t.process_verify_user
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_topic_process_record a
			 join res_topic b on a.topic_id= b.topic_id and b.is_delete=0
		 ) t

	</sql>

	<select id="getResTopicProcessRecordListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicProcessRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>