<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicDto" id="BaseResultMap">
        <result property="topicId" column="topic_id"/>
        <result property="topicName" column="topic_name"/>
        <result property="organizationId" column="organization_id"/>
        <result property="classesId" column="classes_id"/>
        <result property="topicProcess" column="topic_process"/>
        <result property="submitUser" column="submit_user"/>
        <result property="verifyUser" column="verify_user"/>
        <result property="topicType" column="topic_type"/>
        <result property="relSubject" column="rel_subject"/>
        <result property="topicDesc" column="topic_desc"/>
        <result property="topicBack" column="topic_back"/>
        <result property="topicGoal" column="topic_goal"/>
        <result property="topicContent" column="topic_content"/>
        <result property="topicMethod" column="topic_method"/>
        <result property="topicCondition" column="topic_condition"/>
        <result property="topicPlan" column="topic_plan"/>
        <result property="topicExpected" column="topic_expected"/>
        <result property="evaluateResult" column="evaluate_result"/>
        <result property="excellentType" column="excellent_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="topicSource" column="topic_source"/>
        <result property="topicTutor" column="topic_tutor"/>
        <result property="topicThirdId" column="topic_third_id"/>
		<result property="homeShow" column="home_show"/>
		<result property="topicThirdType" column="topic_third_type"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="topicName != null and topicName != '' ">and topic_name like concat('%', #{topicName}, '%')</if>
			<choose>
				<when test="organizationId != null ">
					AND (organization_id = #{organizationId} or topic_source = 2)
				</when>
				<when test="isDetailQuery == null">
					AND topic_source = 2
				</when>
			</choose>
			<if test="classesId != null ">and classes_id = #{classesId}</if>
			<if test="topicProcess != null ">and topic_process = #{topicProcess}</if>
			<if test="submitUser != null and submitUser != '' ">and submit_user like concat('%', #{submitUser}, '%')</if>
			<if test="verifyUser != null and verifyUser != '' ">and verify_user like concat('%', #{verifyUser}, '%')</if>
			<if test="topicType != null ">and topic_type = #{topicType}</if>
			<if test="relSubject != null and relSubject != '' ">and rel_subject like concat('%', #{relSubject}, '%')</if>
			<if test="topicDesc != null and topicDesc != '' ">and topic_desc like concat('%', #{topicDesc}, '%')</if>
			<if test="topicBack != null and topicBack != '' ">and topic_back like concat('%', #{topicBack}, '%')</if>
			<if test="topicGoal != null and topicGoal != '' ">and topic_goal like concat('%', #{topicGoal}, '%')</if>
			<if test="topicContent != null and topicContent != '' ">and topic_content like concat('%', #{topicContent}, '%')</if>
			<if test="topicMethod != null and topicMethod != '' ">and topic_method like concat('%', #{topicMethod}, '%')</if>
			<if test="topicCondition != null and topicCondition != '' ">and topic_condition like concat('%', #{topicCondition}, '%')</if>
			<if test="topicPlan != null and topicPlan != '' ">and topic_plan like concat('%', #{topicPlan}, '%')</if>
			<if test="topicExpected != null and topicExpected != '' ">and topic_expected like concat('%', #{topicExpected}, '%')</if>
			<if test="evaluateResult != null ">and evaluate_result = #{evaluateResult}</if>
			<if test="excellentType != null ">and excellent_type = #{excellentType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="topicSource != null ">and topic_source = #{topicSource}</if>
			<if test="topicTutor != null ">and topic_tutor = #{topicTutor}</if>
			<if test="topicThirdId != null and topicThirdId !=''  ">and topic_third_id = #{topicThirdId}</if>
			<if test="homeShow != null "> and home_show=  #{homeShow}</if>
			<if test="topicThirdType != null">and topic_third_type=#{topicThirdType}</if>
			<if test="searchKey != null and searchKey != '' ">
				and (topic_name like concat('%', #{searchKey}, '%') or topic_back like concat('%', #{searchKey}, '%'))
			</if>

			<if test="topicIds != null and topicIds.size() >0">
				and topic_id in
				<foreach collection="topicIds" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
			<if test="topicProcessStatus != null and topicProcessStatus.size() >0">
				and topic_process in
				<foreach collection="topicProcessStatus" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.topic_id
	 		,t.topic_name
	 		,t.organization_id
	 		,t.classes_id
	 		,t.topic_process
	 		,t.submit_user
	 		,t.verify_user
	 		,t.topic_type
	 		,t.rel_subject
	 		,t.topic_desc
	 		,t.topic_back
	 		,t.topic_goal
	 		,t.topic_content
	 		,t.topic_method
	 		,t.topic_condition
	 		,t.topic_plan
	 		,t.topic_expected
	 		,t.evaluate_result
	 		,t.excellent_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,topic_source
			,topic_tutor
			,topic_third_id
			,t.home_show
			,t.topic_third_type
			,subject_code
		from (
			 select a.* from res_topic a
		<if test="teacherOid!= null and teacherOid != '' and isAdmin != true">
			inner join res_topic_teacher b on a.topic_id=b.topic_id and b.user_oid=#{teacherOid} and b.is_delete=0
			 </if>
		 ) t

	</sql>

	<select id="getResTopicListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>