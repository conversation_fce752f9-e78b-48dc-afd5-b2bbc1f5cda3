<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicCollectMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicCollectDto" id="BaseResultMap">
        <result property="collectId" column="collect_id"/>
        <result property="topicId" column="topic_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="collectId != null ">and collect_id = #{collectId}</if>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.collect_id
	 		,t.topic_id
	 		,t.user_oid
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_topic_collect a
		 ) t

	</sql>

	<select id="getResTopicCollectListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicCollectVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>