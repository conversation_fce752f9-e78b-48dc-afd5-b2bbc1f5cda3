<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResInformationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResInformationDto" id="BaseResultMap">
        <result property="informationId" column="information_id"/>
        <result property="informationName" column="information_name"/>
        <result property="informationDesc" column="information_desc"/>
        <result property="informationIndex" column="information_index"/>
        <result property="imgFileNameOri" column="img_file_name_ori"/>
        <result property="imgFileName" column="img_file_name"/>
        <result property="imgFileUrl" column="img_file_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="informationId != null ">and information_id = #{informationId}</if>
			<if test="informationName != null and informationName != '' ">and information_name like concat('%', #{informationName}, '%')</if>
			<if test="informationDesc != null and informationDesc != '' ">and information_desc like concat('%', #{informationDesc}, '%')</if>
			<if test="informationIndex != null ">and information_index = #{informationIndex}</if>
			<if test="imgFileNameOri != null and imgFileNameOri != '' ">and img_file_name_ori like concat('%', #{imgFileNameOri}, '%')</if>
			<if test="imgFileName != null and imgFileName != '' ">and img_file_name like concat('%', #{imgFileName}, '%')</if>
			<if test="imgFileUrl != null and imgFileUrl != '' ">and img_file_url like concat('%', #{imgFileUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.information_id
	 		,t.information_name
	 		,t.information_desc
	 		,t.information_index
	 		,t.img_file_name_ori
	 		,t.img_file_name
	 		,t.img_file_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_information a
		 ) t

	</sql>

	<select id="getResInformationListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResInformationVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>