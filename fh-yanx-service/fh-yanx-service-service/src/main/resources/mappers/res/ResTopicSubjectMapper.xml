<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicSubjectMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicSubjectDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="topicId" column="topic_id"/>
        <result property="relSubject" column="rel_subject"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="relSubject != null ">and rel_subject = #{relSubject}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.topic_id
	 		,t.rel_subject
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_topic_subject a
		 ) t

	</sql>

	<select id="getResTopicSubjectListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicSubjectVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>