<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicStuAttachmentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicStuAttachmentDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="topicId" column="topic_id"/>
        <result property="stuName" column="stu_name"/>
        <result property="stuFileNameOri" column="stu_file_name_ori"/>
        <result property="stuFileName" column="stu_file_name"/>
        <result property="stuFileUrl" column="stu_file_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="stuFileOid" column="stu_file_oid"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="stuName != null and stuName != '' ">and stu_name like concat('%', #{stuName}, '%')</if>
			<if test="stuFileNameOri != null and stuFileNameOri != '' ">and stu_file_name_ori like concat('%', #{stuFileNameOri}, '%')</if>
			<if test="stuFileName != null and stuFileName != '' ">and stu_file_name like concat('%', #{stuFileName}, '%')</if>
			<if test="stuFileUrl != null and stuFileUrl != '' ">and stu_file_url like concat('%', #{stuFileUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="stuFileOid != null and stuFileOid != '' ">and stu_file_oid = #{stuFileOid}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.topic_id
	 		,t.stu_name
	 		,t.stu_file_name_ori
	 		,t.stu_file_name
	 		,t.stu_file_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.stu_file_oid
		from (
			 select a.* from res_topic_stu_attachment a
		<if test="orderBy != null">
			order by ${orderBy}
		</if>
		 ) t

	</sql>

	<select id="getResTopicStuAttachmentListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicStuAttachmentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>