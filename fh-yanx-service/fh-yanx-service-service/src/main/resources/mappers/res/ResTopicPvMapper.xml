<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.res.mapper.ResTopicPvMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.res.entity.dto.ResTopicPvDto" id="BaseResultMap">
        <result property="pvId" column="pv_id"/>
        <result property="topicId" column="topic_id"/>
        <result property="topicView" column="topic_view"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="pvId != null ">and pv_id = #{pvId}</if>
			<if test="topicId != null ">and topic_id = #{topicId}</if>
			<if test="topicView != null ">and topic_view = #{topicView}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.pv_id
	 		,t.topic_id
	 		,t.topic_view
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from res_topic_pv a
		 ) t

	</sql>

	<select id="getResTopicPvListByCondition" resultType="com.fh.yanx.service.res.entity.vo.ResTopicPvVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>