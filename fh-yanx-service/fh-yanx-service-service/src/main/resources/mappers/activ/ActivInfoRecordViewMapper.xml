<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.activ.mapper.ActivInfoRecordViewMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.activ.entity.dto.ActivInfoRecordViewDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="activId" column="activ_id"/>
        <result property="viewName" column="view_name"/>
        <result property="viewDate" column="view_date"/>
        <result property="viewActionType" column="view_action_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="recordId != null ">and record_id = #{recordId}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="viewName != null and viewName != '' ">and view_name like concat('%', #{viewName}, '%')</if>
			<if test="viewDate != null ">and view_date = #{viewDate}</if>
			<if test="viewActionType != null ">and view_action_type = #{viewActionType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.record_id
	 		,t.activ_id
	 		,t.view_name
	 		,t.view_date
	 		,t.view_action_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from activ_info_record_view a
		 ) t

	</sql>

	<select id="getActivInfoRecordViewListByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getActivInfoRecordViewByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getActivInfoRecordViewStatistics"
			resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo">
		select airv.*,ai.activ_name,count(1) as viewCountDay
		from activ_info_record_view airv
		join activ_info ai on airv.activ_id = ai.activ_id and ai.is_delete=0
		<if test="statisticsDateTopCount != null and statisticsDateTopCount != 0">
			join (select distinct view_date from activ_info_record_view order by view_date desc limit #{statisticsDateTopCount}) t on airv.view_date = t.view_date
		</if>
		where airv.is_delete=0
		<if test="activId != null">
			and airv.activ_id=#{activId}
		</if>
		group by airv.view_date,airv.record_id
		order by view_date desc
	</select>
</mapper>