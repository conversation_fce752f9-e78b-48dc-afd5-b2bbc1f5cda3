<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.activ.mapper.ActivInfoRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.activ.entity.dto.ActivInfoRecordDto" id="BaseResultMap">
        <result property="recordId" column="record_id"/>
        <result property="activId" column="activ_id"/>
        <result property="recordType" column="record_type"/>
        <result property="recordSort" column="record_sort"/>
        <result property="recordTitle" column="record_title"/>
        <result property="recordContent" column="record_content"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="recordId != null ">and record_id = #{recordId}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="recordType != null ">and record_type = #{recordType}</if>
			<if test="recordSort != null ">and record_sort = #{recordSort}</if>
			<if test="recordTitle != null and recordTitle != '' ">and record_title like concat('%', #{recordTitle}, '%')</if>
			<if test="recordContent != null and recordContent != '' ">and record_content like concat('%',
				#{recordContent}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
		<if test="pageNo == -1 and orderBy != null and orderBy != ''">
			order by ${orderBy}
		</if>
	</sql>

	<sql id="common_select">
		select
	 		t.record_id
	 		,t.activ_id
	 		,t.record_type
	 		,t.record_sort
	 		,t.record_title
	 		,t.record_content
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from activ_info_record a
		 ) t

	</sql>

	<select id="getActivInfoRecordListByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getActivInfoRecordByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>