<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.activ.mapper.ActivInfoIntroduceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.activ.entity.dto.ActivInfoIntroduceDto" id="BaseResultMap">
        <result property="introduceId" column="introduce_id"/>
        <result property="activId" column="activ_id"/>
        <result property="introduceType" column="introduce_type"/>
        <result property="introduceSort" column="introduce_sort"/>
        <result property="introduceTitle" column="introduce_title"/>
        <result property="introduceImageId" column="introduce_image_id"/>
        <result property="introduceImageUrl" column="introduce_image_url"/>
        <result property="introduceContent" column="introduce_content"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="introduceId != null ">and introduce_id = #{introduceId}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="introduceType != null ">and introduce_type = #{introduceType}</if>
			<if test="introduceSort != null ">and introduce_sort = #{introduceSort}</if>
			<if test="introduceTitle != null and introduceTitle != '' ">and introduce_title like concat('%', #{introduceTitle}, '%')</if>
			<if test="introduceImageId != null and introduceImageId != '' ">and introduce_image_id like concat('%', #{introduceImageId}, '%')</if>
			<if test="introduceImageUrl != null and introduceImageUrl != '' ">and introduce_image_url like concat('%', #{introduceImageUrl}, '%')</if>
			<if test="introduceContent != null and introduceContent != '' ">and introduce_content like concat('%', #{introduceContent}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.introduce_id
	 		,t.activ_id
	 		,t.introduce_type
	 		,t.introduce_sort
	 		,t.introduce_title
	 		,t.introduce_image_id
	 		,t.introduce_image_url
	 		,t.introduce_content
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from activ_info_introduce a
		 ) t

	</sql>

	<select id="getActivInfoIntroduceListByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getActivInfoIntroduceByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>