<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.activ.mapper.ActivInfoViewPermissionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.activ.entity.dto.ActivInfoViewPermissionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="activId" column="activ_id"/>
        <result property="viewType" column="view_type"/>
        <result property="viewPermission" column="view_permission"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="viewType != null ">and view_type = #{viewType}</if>
			<if test="viewPermission != null ">and view_permission = #{viewPermission}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="viewType != null ">and view_type = #{viewType}</if>
			<if test="viewPermission != null ">and view_permission = #{viewPermission}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.activ_id
	 		,t.view_type
	 		,t.view_permission
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from activ_info_view_permission a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getActivInfoViewPermissionListByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getActivInfoViewPermissionByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>