<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.activ.mapper.ActivInfoScheduleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.activ.entity.dto.ActivInfoScheduleDto" id="BaseResultMap">
        <result property="scheduleId" column="schedule_id"/>
        <result property="activId" column="activ_id"/>
        <result property="scheduleName" column="schedule_name"/>
        <result property="scheduleUrl" column="schedule_url"/>
        <result property="scheduleIndex" column="schedule_index"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="scheduleId != null ">and schedule_id = #{scheduleId}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="scheduleName != null and scheduleName != '' ">and schedule_name like concat('%', #{scheduleName}, '%')</if>
			<if test="scheduleUrl != null and scheduleUrl != '' ">and schedule_url like concat('%', #{scheduleUrl}, '%')</if>
			<if test="scheduleIndex != null ">and schedule_index = #{scheduleIndex}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="scheduleId != null ">and schedule_id = #{scheduleId}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="scheduleName != null and scheduleName != '' ">and schedule_name like concat('%', #{scheduleName}, '%')</if>
			<if test="scheduleUrl != null and scheduleUrl != '' ">and schedule_url like concat('%', #{scheduleUrl}, '%')</if>
			<if test="scheduleIndex != null ">and schedule_index = #{scheduleIndex}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.schedule_id
	 		,t.activ_id
	 		,t.schedule_name
	 		,t.schedule_url
	 		,t.schedule_index
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from activ_info_schedule a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getActivInfoScheduleListByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getActivInfoScheduleByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>