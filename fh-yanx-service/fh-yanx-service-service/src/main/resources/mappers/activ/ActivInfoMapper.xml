<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.activ.mapper.ActivInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.activ.entity.dto.ActivInfoDto" id="BaseResultMap">
        <result property="activId" column="activ_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="activName" column="activ_name"/>
        <result property="activDescription" column="activ_description"/>
        <result property="activCoverId" column="activ_cover_id"/>
        <result property="activCoverUrl" column="activ_cover_url"/>
        <result property="activStartTime" column="activ_start_time"/>
        <result property="activEndTime" column="activ_end_time"/>
        <result property="activFormType" column="activ_form_type"/>
        <result property="activType" column="activ_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="showType" column="show_type"/>
		<result property="showIndex" column="show_index"/>
		<result property="activBizType" column="activ_biz_type"/>
		<result property="subBookName" column="sub_book_name"/>
		<result property="subMaxNumber" column="sub_max_number"/>
		<result property="bmName" column="bm_name"/>
		<result property="bmContent" column="bm_content"/>
		<result property="chargeType" column="charge_type"/>
		<result property="chargeAmount" column="charge_amount"/>
		<result property="webBmStartTime" column="web_bm_start_time"/>
		<result property="webBmEndTime" column="web_bm_end_time"/>
		<result property="bmStartTime" column="bm_start_time"/>
		<result property="bmEndTime" column="bm_end_time"/>
		<result property="activAddress" column="activ_address"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="activName != null and activName != '' ">and activ_name like concat('%', #{activName}, '%')</if>
			<if test="activDescription != null and activDescription != '' ">and activ_description like concat('%', #{activDescription}, '%')</if>
			<if test="activCoverId != null and activCoverId != '' ">and activ_cover_id like concat('%', #{activCoverId}, '%')</if>
			<if test="activCoverUrl != null and activCoverUrl != '' ">and activ_cover_url like concat('%', #{activCoverUrl}, '%')</if>
			<if test="activFormType != null ">and activ_form_type = #{activFormType}</if>
			<if test="activType != null ">and activ_type = #{activType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="activStartType != null">
				<choose>
					<when test="activStartType == 1">
						and DATE_FORMAT(activ_start_time, '%Y-%m-%d') &gt; DATE_FORMAT(#{nowTime}, '%Y-%m-%d')
					</when>
					<when test="activStartType == 2">
						and DATE_FORMAT(activ_start_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{nowTime}, '%Y-%m-%d')
						and DATE_FORMAT(activ_end_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{nowTime}, '%Y-%m-%d')
					</when>
					<otherwise>
						and DATE_FORMAT(activ_end_time, '%Y-%m-%d') &lt; DATE_FORMAT(#{nowTime}, '%Y-%m-%d')
					</otherwise>
				</choose>
			</if>
			<if test="showType != null">and show_type = #{showType}</if>
			<if test="showIndex != null">and show_index = #{showIndex}</if>
			<if test="activBizType != null">and activ_biz_type = #{activBizType}</if>
			<if test="subBookName != null and subBookName!=''">and sub_book_name like concat('%', #{subBookName}, '%')</if>
			<if test="subMaxNumber != null">and sub_max_number = #{subMaxNumber}</if>
			<if test="activStartTime != null and activEndTime != null">
				and (
					DATE_FORMAT(activ_start_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{activStartTime}, '%Y-%m-%d')
					and
					DATE_FORMAT(activ_end_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{activEndTime}, '%Y-%m-%d')
				)
			</if>
			<if test="bmName != null and bmName != '' ">and bm_name like concat('%', #{bmName}, '%')</if>
			<if test="bmContent != null and bmContent != '' ">and bm_content like concat('%', #{bmContent}, '%')</if>
			<if test="chargeType != null ">and charge_type = #{chargeType}</if>
			<if test="chargeAmount != null ">and charge_amount = #{chargeAmount}</if>
			<if test="webBmStartTime != null ">and web_bm_start_time = #{webBmStartTime}</if>
			<if test="webBmEndTime != null ">and web_bm_end_time = #{webBmEndTime}</if>
			<if test="bmStartTime != null ">and bm_start_time = #{bmStartTime}</if>
			<if test="bmEndTime != null ">and bm_end_time = #{bmEndTime}</if>
			<if test="activAddress != null and activAddress != ''">and activ_address like concat('%', #{activAddress}, '%')</if>
			<if test="activIds != null and activIds.size() != 0">
				and activ_id in
				<foreach collection="activIds" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="adminOid != null and adminOid != ''">
				and admin_oid = #{adminOid}
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.activ_id
	 		,t.organization_id
	 		,t.activ_name
	 		,t.activ_description
	 		,t.activ_cover_id
	 		,t.activ_cover_url
	 		,t.activ_start_time
	 		,t.activ_end_time
	 		,t.activ_form_type
	 		,t.activ_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.show_type
			,t.show_index
		    ,t.activ_biz_type
		    ,t.sub_book_name
		    ,t.sub_max_number
			,t.bm_name
			,t.bm_content
			,t.charge_type
			,t.charge_amount
			,t.web_bm_start_time
			,t.web_bm_end_time
			,t.bm_start_time
			,t.bm_end_time
			,t.activ_address
		    ,t.admin_oid
			<if test="nowTime != null">
				,case when DATE_FORMAT(#{nowTime}, '%Y-%m-%d') &lt; DATE_FORMAT(t.activ_start_time, '%Y-%m-%d') then 1
				when DATE_FORMAT(#{nowTime}, '%Y-%m-%d') &gt; DATE_FORMAT(t.activ_end_time, '%Y-%m-%d') then 3
				else 2 end as activStartType
			</if>
		from (
			 select a.*,c.admin_oid from activ_info a
		     left join cm_info c on a.activ_id = c.activ_id and c.is_delete = 0
		 ) t

	</sql>

	<select id="getActivInfoListByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		group by t.activ_id
		<if test= "pageNo == -1 and orderBy != null and orderBy != ''">
			order by ${orderBy}
		</if>
	</select>

	<select id="getActivInfoByCondition" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
    <select id="getUserActivList" resultType="com.fh.yanx.service.activ.entity.vo.ActivInfoVo">
		select distinct ai.*
		from activ_info ai
		right join bm_info bi on ai.activ_id = bi.activ_id
		right join bm_info_joiner bij on bi.info_id = bij.info_id
		<where>
			<if test="isDelete != null">
				and ai.is_delete = #{isDelete}
				and bi.is_delete = #{isDelete}
				and bij.is_delete = #{isDelete}
			</if>
			<if test="phone != null and phone != ''">
				and bij.joiner_mobile = #{phone}
			</if>
			<if test="activType != null">
				and ai.activ_type = #{activType}
			</if>
			<if test="activId != null">
				and ai.activ_id = #{activId}
			</if>
		</where>
	</select>
</mapper>