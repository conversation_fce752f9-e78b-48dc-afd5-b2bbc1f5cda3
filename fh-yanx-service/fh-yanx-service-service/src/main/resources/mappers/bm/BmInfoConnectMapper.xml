<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.bm.mapper.BmInfoConnectMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.bm.entity.dto.BmInfoConnectDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="infoId" column="info_id"/>
        <result property="submitType" column="submit_type"/>
        <result property="connectContent" column="connect_content"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="infoId != null ">and info_id = #{infoId}</if>
			<if test="submitType != null">and submit_type = #{submitType}</if>
			<if test="connectContent != null and connectContent != '' ">and connect_content like concat('%', #{connectContent}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.info_id
	 		,t.submit_type
	 		,t.connect_content
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from bm_info_connect a
		 ) t
	</sql>

	<sql id="orderBy">
		order by update_time desc
	</sql>

	<select id="getBmInfoConnectListByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoConnectVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		<include refid="orderBy"></include>
	</select>

	<select id="getBmInfoConnectByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoConnectVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>