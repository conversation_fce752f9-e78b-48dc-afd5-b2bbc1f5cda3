<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.bm.mapper.BmInfoJoinerMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.bm.entity.dto.BmInfoJoinerDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="infoId" column="info_id"/>
        <result property="joinerName" column="joiner_name"/>
        <result property="joinerGender" column="joiner_gender"/>
        <result property="joinerNation" column="joiner_nation"/>
        <result property="joinerDuties" column="joiner_duties"/>
        <result property="joinerTeach" column="joiner_teach"/>
        <result property="joinerMail" column="joiner_mail"/>
        <result property="joinerMobile" column="joiner_mobile"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="infoId != null ">and info_id = #{infoId}</if>
            <if test="joinerName != null and joinerName != '' ">and joiner_name like concat('%', #{joinerName}, '%')
            </if>
            <if test="joinerGender != null ">and joiner_gender = #{joinerGender}</if>
            <if test="joinerNation != null and joinerNation != '' ">and joiner_nation like concat('%', #{joinerNation},
                '%')
            </if>
            <if test="joinerDuties != null and joinerDuties != '' ">and joiner_duties like concat('%', #{joinerDuties},
                '%')
            </if>
            <if test="joinerTeach != null and joinerTeach != '' ">and joiner_teach like concat('%', #{joinerTeach},
                '%')
            </if>
            <if test="joinerMail != null and joinerMail != '' ">and joiner_mail like concat('%', #{joinerMail}, '%')
            </if>
            <if test="joinerMobile != null and joinerMobile != '' ">and joiner_mobile like concat('%', #{joinerMobile},
                '%')
            </if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
        </where>
    </sql>

    <sql id="common_select">
        select
        <!-- 如果主键不是id，这里需要修改 -->
        t.id
        ,t.info_id
        ,t.joiner_name
        ,t.joiner_gender
        ,t.joiner_nation
        ,t.joiner_duties
        ,t.joiner_teach
        ,t.joiner_mail
        ,t.joiner_mobile
        ,t.create_time
        ,t.create_by
        ,t.update_time
        ,t.update_by
        ,t.is_delete
        ,t.study_hours
        ,t.join_prove_id
        ,t.join_prove_url
        ,t.join_prove_name
        ,t.join_prove_name_ori
        ,t.study_prove_type
        ,t.study_prove_id
        ,t.study_prove_url
        ,t.study_prove_name
        ,t.study_prove_name_ori
        from (
        select a.* from bm_info_joiner a
        ) t

    </sql>

    <select id="getBmInfoJoinerListByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>

    <select id="getBmInfoJoinerByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
        limit 1
    </select>

    <select id="getJoinerCountByInfoId" parameterType="map" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoVo">
        select info_id as infoId, count(1) as joinerCount
        from bm_info_joiner
        where is_delete = 0
        and info_id in
        <foreach collection="infoIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        group by info_id
    </select>

    <select id="getBmInfoJoinerByMobile" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo">
        select
            j.*
            ,b.contact_name         contactName
            ,b.contact_mobile       contactMobile
        from bm_info b
        left join bm_info_joiner j on b.info_id = j.info_id
        <where>
            and b.is_delete = 0
            and b.activ_id = #{activId}
            and j.joiner_mobile in
            <foreach collection="mobiles" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="listJoinerByActivId" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo">
        select bij.* from bm_info bi
        join bm_info_joiner bij on bi.info_id = bij.info_id and bij.is_delete=0
        where bi.activ_id=#{activId} and bi.is_delete=0
        <if test="submitType != null">
            and bi.submit_type=#{submitType}
        </if>
    </select>

</mapper>