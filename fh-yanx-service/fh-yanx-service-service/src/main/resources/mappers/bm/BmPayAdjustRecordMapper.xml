<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.bm.mapper.BmPayAdjustRecordMapper">

	<resultMap id="BaseResultMap" type="com.fh.yanx.service.bm.entity.dto.BmPayAdjustRecordDto">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="info_id" property="infoId" jdbcType="BIGINT"/>
		<result column="pay_id" property="payId" jdbcType="VARCHAR"/>
		<result column="pay_url" property="payUrl" jdbcType="VARCHAR"/>
		<result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
		<result column="pay_user_oid" property="payUserOid" jdbcType="VARCHAR"/>
		<result column="pay_user_name" property="payUserName" jdbcType="VARCHAR"/>
		<result column="pay_way_type" property="payWayType" jdbcType="INTEGER"/>
		<result column="pay_record" property="payRecord" jdbcType="VARCHAR"/>
        <result column="transaction_id" property="transactionId" jdbcType="VARCHAR"/>
        <result column="pay_adjust_reason" property="payAdjustReason" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="create_by" property="createBy" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
		<result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
	</resultMap>

    <sql id="commonSelect">
        select t.id,
               t.info_id,
               t.pay_id,
               t.pay_url,
               t.pay_time,
               t.pay_user_oid,
               t.pay_user_name,
               t.pay_way_type,
               t.pay_record,
               t.transaction_id,
               t.pay_adjust_reason,
               t.create_time,
               t.create_by,
               t.update_time,
               t.update_by,
               t.is_delete
        from bm_pay_adjust_record t
    </sql>

    <sql id="commonWhere">
        <where>
            <if test="infoId != null">
                and t.info_id = #{infoId}
            </if>
            <if test="payId != null">
                and t.pay_id = #{payId}
            </if>
            <if test="payUrl != null">
                and t.pay_url = #{payUrl}
            </if>
            <if test="payTime != null">
                and t.pay_time = #{payTime}
            </if>
            <if test="payUserOid != null">
                and t.pay_user_oid = #{payUserOid}
            </if>
            <if test="payUserName != null">
                and t.pay_user_name = #{payUserName}
            </if>
            <if test="payWayType != null">
                and t.pay_way_type = #{payWayType}
            </if>
            <if test="payRecord != null">
                and t.pay_record = #{payRecord}
            </if>
            <if test="transactionId != null">
                and t.transaction_id = #{transactionId}
            </if>
            <if test="payAdjustReason != null">
                and t.pay_adjust_reason = #{payAdjustReason}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime}
            </if>
            <if test="createBy != null">
                and t.create_by = #{createBy}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime}
            </if>
            <if test="updateBy != null">
                and t.update_by = #{updateBy}
            </if>
            <if test="isDelete != null">
                and t.is_delete = #{isDelete}
            </if>
        </where>
    </sql>

	<select id="getBmPayAdjustRecordListByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmPayAdjustRecordVo">
        <include refid="commonSelect"/>
        <include refid="commonWhere"/>
        order by pay_time desc
    </select>

	<select id="getBmPayAdjustRecordByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmPayAdjustRecordVo">
        <include refid="commonSelect"/>
        <include refid="commonWhere"/>
        limit 1
    </select>
</mapper>