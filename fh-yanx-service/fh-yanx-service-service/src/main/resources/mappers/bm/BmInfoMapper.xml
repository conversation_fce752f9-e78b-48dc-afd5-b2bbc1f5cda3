<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.bm.mapper.BmInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.bm.entity.dto.BmInfoDto" id="BaseResultMap">
        <result property="infoId" column="info_id"/>
        <result property="provinceId" column="province_id"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="areaId" column="area_id"/>
        <result property="areaName" column="area_name"/>
        <result property="address" column="address"/>
        <result property="departName" column="depart_name"/>
        <result property="period" column="period"/>
        <result property="joinType" column="join_type"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactMobile" column="contact_mobile"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="submitType" column="submit_type"/>
        <result property="payType" column="pay_type"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="invoiceId" column="invoice_id"/>
        <result property="invoiceUrl" column="invoice_url"/>
        <result property="invoiceName" column="invoice_name"/>
        <result property="invoiceNameOri" column="invoice_name_ori"/>
        <result property="payId" column="pay_id"/>
        <result property="payUrl" column="pay_url"/>
        <result property="payName" column="pay_name"/>
        <result property="payNameOri" column="pay_name_ori"/>
		<result property="activId" column="activ_id"/>
		<result property="joinType" column="join_type"/>
		<result property="signInState" column="sign_in_state"/>
		<result property="signInCode" column="sign_in_code"/>
		<result property="invoicingType" column="invoicing_type"/>
		<result property="invoiceHeader" column="invoice_header"/>
		<result property="dutyCode" column="duty_code"/>
		<result property="email" column="email"/>
		<result property="payTime" column="pay_time"/>
		<result property="payUserOid" column="pay_user_oid"/>
		<result property="payUserName" column="pay_user_name"/>
		<result property="subNumber" column="sub_number"/>
		<result property="subBookTotalPrice" column="sub_book_total_price"/>
		<result property="subBookPrice" column="sub_book_price"/>
		<result property="recName" column="rec_name"/>
		<result property="recMobile" column="rec_mobile"/>
		<result property="recProvinceId" column="rec_province_id"/>
		<result property="recProvinceName" column="rec_province_name"/>
		<result property="recCityId" column="rec_city_id"/>
		<result property="recCityName" column="rec_city_name"/>
		<result property="recAreaId" column="rec_area_id"/>
		<result property="recAreaName" column="rec_area_name"/>
		<result property="recAddress" column="rec_address"/>
		<result property="payWayType" column="pay_way_type"/>
		<result property="payRecord" column="pay_record"/>
		<result property="invoiceRemark" column="invoice_remark"/>
		<result property="selectMaterialType" column="select_material_type"/>
		<result property="subBookName" column="sub_book_name"/>
		<result property="activTicketPrice" column="activ_ticket_price"/>
		<result property="activTotalPrice" column="activ_total_price"/>
		<result property="cm" column="cm"/>
		<result property="deliverState" column="deliver_state"/>
		<result property="logisticsCode" column="logistics_code"/>
		<result property="logisticsOrg" column="logistics_org"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="infoId != null ">and info_id = #{infoId}</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and province_name like concat('%', #{provinceName}, '%')</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="cityName != null and cityName != '' ">and city_name like concat('%', #{cityName}, '%')</if>
			<if test="areaId != null ">and area_id = #{areaId}</if>
			<if test="areaName != null and areaName != '' ">and area_name like concat('%', #{areaName}, '%')</if>
			<if test="address != null and address != '' ">and address like concat('%', #{address}, '%')</if>
			<if test="departName != null and departName != '' ">and depart_name like concat('%', #{departName}, '%')</if>
			<if test="period != null and period != '' ">and period like concat('%', #{period}, '%')</if>
			<if test="joinType != null ">and join_type = #{joinType}</if>
			<if test="contactName != null and contactName != '' ">and contact_name like concat('%', #{contactName}, '%')</if>
			<if test="contactMobile != null and contactMobile != '' ">and contact_mobile = #{contactMobile}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="submitType != null ">and submit_type = #{submitType}</if>
			<if test="payType != null ">and pay_type = #{payType}</if>
			<if test="invoiceType != null ">and invoice_type = #{invoiceType}</if>
			<if test="invoiceId != null and invoiceId !=''">and invoice_id = #{invoiceId}</if>
			<if test="invoiceUrl != null and invoiceUrl !=''">and invoice_url = #{invoiceUrl}</if>
			<if test="invoiceName != null and invoiceName !=''">and invoice_name = #{invoiceName}</if>
			<if test="invoiceNameOri != null and invoiceNameOri !=''">and invoice_name_ori = #{invoiceNameOri}</if>
			<if test="payId != null and payId !=''">and pay_id = #{payId}</if>
			<if test="payUrl != null and payUrl !=''">and pay_url = #{payUrl}</if>
			<if test="payName != null and payName !=''">and pay_name = #{payName}</if>
			<if test="payNameOri != null and payNameOri !=''">and pay_name_ori = #{payNameOri}</if>

			<if test="startTime != null">
				and create_time <![CDATA[  >= ]]> #{startTime}
			</if>
			<if test="endTime != null">
				and create_time <![CDATA[  <= ]]> #{endTime}
			</if>
			<if test="notEqInfoId != null">
				and info_id != #{notEqInfoId}
			</if>
			<if test="activId != null">and activ_id = #{activId}</if>
			<if test="signInState != null">and sign_in_state = #{signInState}</if>
			<if test="submitTypeList != null and submitTypeList.size() != 0">
				and submit_type in
				<foreach collection="submitTypeList" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="signInCode != null and signInCode != ''">
				and sign_in_code = #{signInCode}
			</if>
			<if test="joinType != null">and join_type = #{joinType}</if>
			<if test="invoicingType != null">and invoicing_type = #{invoicingType}</if>
			<if test="invoiceHeader != null and invoiceHeader != ''">and invoice_header like concat('%', #{invoiceHeader}, '%')</if>
			<if test="dutyCode != null and dutyCode != ''">and duty_code like concat('%', #{dutyCode}, '%')</if>
			<if test="email != null and email != ''">and email like concat('%', #{email}, '%')</if>
			<if test="payTime != null">and pay_time = #{payTime}</if>
			<if test="payUserOid != null and payUserOid != ''">and pay_user_oid = #{payUserOid}</if>
			<if test="payUserName != null and payUserName != ''">and pay_user_name like concat('%', #{payUserName}, '%')</if>
			<if test="subNumber != null">and sub_number = #{subNumber}</if>
			<if test="subBookTotalPrice != null">and sub_book_total_price = #{subBookTotalPrice}</if>
			<if test="subBookPrice != null">and sub_book_price = #{subBookPrice}</if>
			<if test="recName != null and recName != ''">and rec_name like concat('%', #{recName}, '%')</if>
			<if test="recMobile != null and recMobile != ''">and rec_mobile like concat('%', #{recMobile}, '%')</if>
			<if test="recProvinceId != null ">and rec_province_id = #{recProvinceId}</if>
			<if test="recProvinceName != null and recProvinceName != ''">and rec_province_name like concat('%', #{recProvinceName}, '%')</if>
			<if test="recCityId != null">and rec_city_id = #{recCityId}</if>
			<if test="recCityName != null and recCityName != ''">and rec_city_name like concat('%', #{recCityName}, '%')</if>
			<if test="recAreaId != null">and rec_area_id = #{recAreaId}</if>
			<if test="recAreaName != null and recAreaName != ''">and rec_area_name like concat('%', #{recAreaName}, '%')</if>
			<if test="recAddress != null and recAddress != ''">and rec_address like concat('%', #{recAddress}, '%')</if>
			<if test="payWayType != null">and pay_way_type=#{payWayType}</if>
			<if test="payRecord != null">and pay_record=#{payRecord}</if>
			<if test="selectMaterialType != null">and select_material_type=#{selectMaterialType}</if>
			<if test="subBookName != null and subBookName != ''">and sub_book_name=#{subBookName}</if>
			<if test="activTicketPrice != null">and activ_ticket_price=#{activTicketPrice}</if>
			<if test="activTotalPrice != null">and activ_total_price=#{activTotalPrice}</if>
			<if test="cm != null and cm != ''">and cm=#{cm}</if>
			<if test="deliverState != null">and deliver_state = #{deliverState}</if>
			<if test="logisticsCode != null and logisticsCode != ''">and logistics_code like concat('%', #{logisticsCode}, '%')</if>
			<if test="logisticsOrg != null and logisticsOrg != ''">and logistics_org like concat('%', #{logisticsOrg}, '%')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.info_id
	 		,t.province_id
	 		,t.province_name
	 		,t.city_id
	 		,t.city_name
	 		,t.area_id
	 		,t.area_name
	 		,t.address
	 		,t.depart_name
	 		,t.period
	 		,t.join_type
	 		,t.contact_name
	 		,t.contact_mobile
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.submit_type
			,t.pay_type
			,t.invoice_type
			,t.invoice_id
			,t.invoice_url
			,t.invoice_name
			,t.invoice_name_ori
			,t.pay_id
			,t.pay_url
			,t.pay_name
			,t.pay_name_ori
			,t.pay_time
			,t.pay_user_oid
			,t.pay_user_name
			,t.activ_id
			,t.sign_in_state
			,t.sign_in_code
			,t.invoicing_type
			,t.invoice_header
			,t.duty_code
			,t.email
			,t.sub_number
			,t.sub_book_total_price
		    ,t.sub_book_price
			,t.rec_name
			,t.rec_mobile
			,t.rec_province_id
			,t.rec_province_name
			,t.rec_city_id
			,t.rec_city_name
			,t.rec_area_id
			,t.rec_area_name
			,t.rec_address
			,t.pay_way_type
			,t.pay_record
			,t.invoice_remark
			,t.select_material_type
			,t.sub_book_name
			,t.activ_ticket_price
			,t.activ_total_price
			,t.cm
			,t.deliver_state
			,t.logistics_code
			,t.logistics_org
			,t.refund_amount
			,t.refund_way_type
			,t.refund_time
			,t.refund_reason
			,t.refund_id
			,t.refund_url
			,t.refund_name
			,t.refund_name_ori
		from (
			 select a.* from bm_info a
		 ) t

	</sql>

	<select id="getBmInfoListByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getBmInfoByCondition" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

    <select id="getBmInfoListByConditionExport" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoExportVo">
		select bi.info_id as infoId,DATE_FORMAT(bi.create_time,'%Y-%m-%d %k:%i') as time,IFNULL(bi.address,'') as address,bi.depart_name as depart
		,if(bi.join_type=1,'个人参会',if(bi.join_type=2,'单位/学校参会','')) as joinType,bi.contact_name as contactName,bi.contact_mobile as contactMobile
		,if(bi.submit_type=1,'待确认',if(bi.submit_type=2,'沟通中',if(bi.submit_type=3,'已确认','已拒绝'))) as submit
		,if(bi.pay_type=1,'未缴费', if(bi.pay_type=2,'已缴费', if(bi.pay_type=3,'已退款', '已取消'))) as pay
		,if(bi.invoice_type=1,'未开票','已开票') as invoice
		,bij.joiner_name as 'bmInfoJoinerExportVo.joinerName',if(bij.joiner_gender=1,'男','女') as 'bmInfoJoinerExportVo.joinerGender',bij.joiner_mobile as 'bmInfoJoinerExportVo.joinerMobile'
		,bij.joiner_nation as 'bmInfoJoinerExportVo.joinerNation',bij.joiner_duties as 'bmInfoJoinerExportVo.joinerDuties',bij.joiner_teach as 'bmInfoJoinerExportVo.joinerTeach'
		,bij.joiner_mail as 'bmInfoJoinerExportVo.joinerMail',if(bi.select_material_type=1,'否',if(bi.select_material_type=2,'是','')) as selectMaterialType
		from bm_info bi
		join bm_info_joiner bij on bi.info_id = bij.info_id and bij.is_delete=0
		where bi.is_delete=0
		<if test="startTime != null">
		<![CDATA[ and bi.create_time>=#{startTime} ]]>
		</if>
		<if test="endTime != null">
			<![CDATA[ and bi.create_time <=#{endTime} ]]>
		</if>
		<if test="contactName != null and contactName !=''">
			and bi.contact_name like concat('%',#{contactName},'%')
		</if>
		<if test="contactMobile != null and contactMobile != ''">
			and bi.contact_mobile=#{contactMobile}
		</if>
		<if test="submitType != null">
			and bi.submit_type=#{submitType}
		</if>
		<if test="payType != null">
			and bi.pay_type=#{payType}
		</if>
		<if test="invoiceType != null">
			and bi.invoice_type=#{invoiceType}
		</if>
		<if test="activId != null">
			and bi.activ_id = #{activId}
		</if>
		<if test="provinceId != null ">and bi.province_id = #{provinceId}</if>
		<if test="cityId != null ">and bi.city_id = #{cityId}</if>
		<if test="areaId != null ">and bi.area_id = #{areaId}</if>
	</select>

	<select id="getBmInfoListByConditionExportZd" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoExportZdVo">
		select bi.info_id as infoId,DATE_FORMAT(bi.create_time,'%Y-%m-%d %k:%i') as time,IFNULL(bi.address,'') as address
		,bi.contact_name as contactName,bi.contact_mobile as contactMobile
		,if(bi.submit_type=1,'待确认',if(bi.submit_type=2,'沟通中',if(bi.submit_type=3,'已确认','已拒绝'))) as submit
		,bi.sub_number as subNumber,bi.sub_book_total_price as subBookTotalPrice
		,ai.sub_book_name as subBookName,ao.order_number as orderNumber
		,if(bi.pay_type=1,'未缴费', if(bi.pay_type=2,'已缴费', if(bi.pay_type=3,'已退款', '已取消'))) as payType
		,if(bi.invoice_type=1,'未开发票','已开发票') as invoiceType
		,bi.depart_name as departName
		,bi.rec_name as recName
		,bi.rec_mobile as recMobile
		,concat(IFNULL(bi.rec_province_name,''),IFNULL(bi.rec_city_name,''),IFNULL(bi.rec_area_name,''),IFNULL(bi.rec_address,'')) as recAddress
		,ao.transaction_id as transactionId
		,ifnull(bi.pay_time, ao.pay_time) payTime
		,c.real_name as cmRealName
		,bi.cm
		,if(bi.deliver_state = 2, '已发货', '未发货') as deliverState
		,bi.logistics_code
		,bi.logistics_org
		,if(pay_type != 3, null, bi.refund_amount) as refundAmount
		,if(pay_type != 3, null, if(bi.refund_way_type is null, null, if (bi.refund_way_type = 2, '对公账户转账支付', '个人账户转账支付'))) as refundWayType
		,if(pay_type != 3, null, if(bi.refund_time is null, null, DATE_FORMAT(bi.refund_time,'%Y-%m-%d %k:%i'))) as refundTime
		,if(pay_type != 3, null, bi.refund_reason) as refundReason
		from bm_info bi
		join activ_info ai on bi.activ_id = ai.activ_id and ai.is_delete=0
		left join activ_order ao on bi.info_id = ao.goods_id and ao.goods_type = 1 and ao.is_delete = 0
		left join cm_info c on bi.cm = c.cm and bi.activ_id = c.activ_id and c.is_delete = 0
		where bi.is_delete=0
		<if test="startTime != null">
			<![CDATA[ and bi.create_time>=#{startTime} ]]>
		</if>
		<if test="endTime != null">
			<![CDATA[ and bi.create_time <=#{endTime} ]]>
		</if>
		<if test="contactName != null and contactName !=''">
			and bi.contact_name like concat('%',#{contactName},'%')
		</if>
		<if test="contactMobile != null and contactMobile != ''">
			and bi.contact_mobile=#{contactMobile}
		</if>
		<if test="submitType != null">
			and bi.submit_type=#{submitType}
		</if>
		<if test="submitTypeList != null and submitTypeList.size() != 0">
			and bi.submit_type in
			<foreach collection="submitTypeList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="payType != null">
			and bi.pay_type=#{payType}
		</if>
		<if test="invoiceType != null">
			and bi.invoice_type=#{invoiceType}
		</if>
		<if test="activId != null">
			and bi.activ_id = #{activId}
		</if>
		<if test="provinceId != null ">and bi.province_id = #{provinceId}</if>
		<if test="cityId != null ">and bi.city_id = #{cityId}</if>
		<if test="areaId != null ">and bi.area_id = #{areaId}</if>
		<if test="cmType != null">
			<if test="cmType == 1">
				and bi.cm is null
			</if>
			<if test="cmType == 2">
				and bi.cm is not null and bi.cm != ''
			</if>
		</if>
		<if test="cmRealName != null and cmRealName != ''">
			and c.real_name like concat('%', #{cmRealName}, '%')
		</if>
		<if test="adminOid != null and adminOid != ''">
			and c.admin_oid = #{adminOid}
		</if>
		group by bi.info_id
		<if test= "pageNo == -1 and orderBy != null and orderBy != ''">
			order by ${orderBy}
		</if>
	</select>

	<select id="getBmInfoListWithOrder" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoVo">
		select
		b.info_id
		,b.province_id
		,b.province_name
		,b.city_id
		,b.city_name
		,b.area_id
		,b.area_name
		,b.address
		,b.depart_name
		,b.period
		,b.join_type
		,b.contact_name
		,b.contact_mobile
		,b.create_time
		,b.create_by
		,b.update_time
		,b.update_by
		,b.is_delete
		,b.submit_type
		,b.pay_type
		,b.invoice_type
		,b.invoice_id
		,b.invoice_url
		,b.invoice_name
		,b.invoice_name_ori
		,b.pay_id
		,b.pay_url
		,b.pay_name
		,b.pay_name_ori
		,b.pay_record
		,if(b.pay_way_type is null and o.transaction_id is not null, 1, b.pay_way_type) payWayType
		,ifnull(b.pay_time, o.pay_time) payTime
		,b.pay_user_oid
		,b.pay_user_name
		,b.activ_id
		,b.sign_in_state
		,b.sign_in_code
		,b.invoicing_type
		,b.invoice_header
	    ,b.invoice_remark
		,b.duty_code
		,b.email
		,b.sub_number
		,b.sub_book_total_price
		,b.sub_book_price
		,b.rec_name
		,b.rec_mobile
		,b.rec_province_id
		,b.rec_province_name
		,b.rec_city_id
		,b.rec_city_name
		,b.rec_area_id
		,b.rec_area_name
		,b.rec_address
		,b.select_material_type
		,b.sub_book_name
		,b.activ_ticket_price
		,b.activ_total_price
		,b.cm
		,b.deliver_state
		,b.logistics_code
		,b.logistics_org
		,b.refund_amount
		,b.refund_way_type
		,b.refund_time
		,b.refund_reason
		,b.refund_id
		,b.refund_url
		,b.refund_name
		,b.refund_name_ori
		,o.order_id orderId
		,o.order_amount orderAmount
		,o.order_number orderNumber
		,o.order_state orderState
		,o.create_time orderCreateTime
		,a.charge_type chargeType
		,a.activ_biz_type activBizType
		,o.transaction_id transactionId
		,c.real_name as cmRealName
		,a.activ_name as activName
		from bm_info b
		left join activ_info a on a.activ_id = b.activ_id and a.is_delete = 0
		left join activ_order o
				  on b.info_id = o.goods_id and o.goods_type = 1 and o.is_delete = 0
		left join cm_info c on b.cm = c.cm and b.activ_id = c.activ_id and c.is_delete = 0
		<where>
			<if test="infoId != null ">and b.info_id = #{infoId}</if>
			<if test="provinceId != null ">and b.province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and b.province_name like concat('%', #{provinceName}, '%')</if>
			<if test="cityId != null ">and b.city_id = #{cityId}</if>
			<if test="cityName != null and cityName != '' ">and b.city_name like concat('%', #{cityName}, '%')</if>
			<if test="areaId != null ">and b.area_id = #{areaId}</if>
			<if test="areaName != null and areaName != '' ">and b.area_name like concat('%', #{areaName}, '%')</if>
			<if test="address != null and address != '' ">and b.address like concat('%', #{address}, '%')</if>
			<if test="departName != null and departName != '' ">and b.depart_name like concat('%', #{departName}, '%')</if>
			<if test="period != null and period != '' ">and b.period like concat('%', #{period}, '%')</if>
			<if test="joinType != null ">and b.join_type = #{joinType}</if>
			<if test="contactName != null and contactName != '' ">and b.contact_name like concat('%', #{contactName}, '%')</if>
			<if test="contactMobile != null and contactMobile != '' ">and b.contact_mobile = #{contactMobile}</if>
			<if test="isDelete != null ">and b.is_delete = #{isDelete}</if>
			<if test="submitType != null ">and b.submit_type = #{submitType}</if>
			<if test="payType != null ">and b.pay_type = #{payType}</if>
			<if test="invoiceType != null ">and b.invoice_type = #{invoiceType}</if>
			<if test="invoiceId != null and invoiceId !=''">and b.invoice_id = #{invoiceId}</if>
			<if test="invoiceUrl != null and invoiceUrl !=''">and b.invoice_url = #{invoiceUrl}</if>
			<if test="invoiceName != null and invoiceName !=''">and b.invoice_name = #{invoiceName}</if>
			<if test="invoiceNameOri != null and invoiceNameOri !=''">and b.invoice_name_ori = #{invoiceNameOri}</if>
			<if test="payId != null and payId !=''">and b.pay_id = #{payId}</if>
			<if test="payUrl != null and payUrl !=''">and b.pay_url = #{payUrl}</if>
			<if test="payName != null and payName !=''">and b.pay_name = #{payName}</if>
			<if test="payNameOri != null and payNameOri !=''">and b.pay_name_ori = #{payNameOri}</if>

			<if test="startTime != null">
				and b.create_time <![CDATA[  >= ]]> #{startTime}
			</if>
			<if test="endTime != null">
				and b.create_time <![CDATA[  <= ]]> #{endTime}
			</if>
			<if test="notEqInfoId != null">
				and b.info_id != #{notEqInfoId}
			</if>
			<if test="activId != null">and b.activ_id = #{activId}</if>
			<if test="signInState != null">and b.sign_in_state = #{signInState}</if>
			<if test="submitTypeList != null and submitTypeList.size() != 0">
				and b.submit_type in
				<foreach collection="submitTypeList" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="signInCode != null and signInCode != ''">
				and b.sign_in_code = #{signInCode}
			</if>
			<if test="joinType != null">and b.join_type = #{joinType}</if>
			<if test="invoicingType != null">and b.invoicing_type = #{invoicingType}</if>
			<if test="invoiceHeader != null and invoiceHeader != ''">and b.invoice_header like concat('%', #{invoiceHeader}, '%')</if>
			<if test="dutyCode != null and dutyCode != ''">and b.duty_code like concat('%', #{dutyCode}, '%')</if>
			<if test="email != null and email != ''">and b.email like concat('%', #{email}, '%')</if>
			<if test="payTime != null">and b.pay_time = #{payTime}</if>
			<if test="payUserOid != null and payUserOid != ''">and b.pay_user_oid = #{payUserOid}</if>
			<if test="payUserName != null and payUserName != ''">and b.pay_user_name like concat('%', #{payUserName}, '%')</if>
			<if test="subNumber != null">and b.sub_number = #{subNumber}</if>
			<if test="subBookTotalPrice != null">and b.sub_book_total_price = #{subBookTotalPrice}</if>
			<if test="subBookPrice != null">and b.sub_book_price = #{subBookPrice}</if>
			<if test="recName != null and recName != ''">and b.rec_name like concat('%', #{recName}, '%')</if>
			<if test="recMobile != null and recMobile != ''">and b.rec_mobile like concat('%', #{recMobile}, '%')</if>
			<if test="recProvinceId != null ">and b.rec_province_id = #{recProvinceId}</if>
			<if test="recProvinceName != null and recProvinceName != ''">and b.rec_province_name like concat('%', #{recProvinceName}, '%')</if>
			<if test="recCityId != null">and b.rec_city_id = #{recCityId}</if>
			<if test="recCityName != null and recCityName != ''">and b.rec_city_name like concat('%', #{recCityName}, '%')</if>
			<if test="recAreaId != null">and b.rec_area_id = #{recAreaId}</if>
			<if test="recAreaName != null and recAreaName != ''">and b.rec_area_name like concat('%', #{recAreaName}, '%')</if>
			<if test="recAddress != null and recAddress != ''">and b.rec_address like concat('%', #{recAddress}, '%')</if>
			<if test="payWayType != null">and b.pay_way_type=#{payWayType}</if>
			<if test="payRecord != null">and b.pay_record=#{payRecord}</if>
			<if test="selectMaterialType != null">and b.select_material_type=#{selectMaterialType}</if>
			<if test="subBookName != null and subBookName != ''">and b.sub_book_name=#{subBookName}</if>
			<if test="activTicketPrice != null">and b.activ_ticket_price=#{activTicketPrice}</if>
			<if test="activTotalPrice != null">and b.activ_total_price=#{activTotalPrice}</if>
			<if test="cmType != null">
				<if test="cmType == 1">
					and b.cm is null
				</if>
				<if test="cmType == 2">
					and b.cm is not null and b.cm != ''
				</if>
			</if>
			<if test="cmRealName != null and cmRealName != ''">
				and c.real_name like concat('%', #{cmRealName}, '%')
			</if>
			<if test="adminOid != null and adminOid != ''">
				and c.admin_oid = #{adminOid}
			</if>
			<if test="deliverState != null">and b.deliver_state = #{deliverState}</if>
			<if test="logisticsCode != null and logisticsCode != ''">and b.logistics_code like concat('%', #{logisticsCode}, '%')</if>
			<if test="logisticsOrg != null and logisticsOrg != ''">and b.logistics_org like concat('%', #{logisticsOrg}, '%')</if>
			<if test="activBizType != null">and a.activ_biz_type = #{activBizType}</if>
			<if test="orderState != null">and o.order_state = #{orderState}</if>
			<if test="queryNotSmsRemindRecord">
				and not exists (
					select order_id
					from `activ_order_sms_remind_record`
					where is_delete = 0
					and order_id = o.order_id
				)
			</if>
			<if test="payStartTime != null">
				and b.pay_time <![CDATA[  >= ]]> #{payStartTime}
			</if>
			<if test="payEndTime != null">
				and b.pay_time <![CDATA[  <= ]]> #{payEndTime}
			</if>
			<if test="refundStartTime != null">
				and b.refund_time <![CDATA[  >= ]]> #{refundStartTime}
			</if>
			<if test="refundEndTime != null">
				and b.refund_time <![CDATA[  <= ]]> #{refundEndTime}
			</if>
		</where>
		<if test= "pageNo == -1 and orderBy != null and orderBy != ''">
			order by ${orderBy}
		</if>
	</select>

	<select id="getBmInfoStatistics" resultType="com.fh.yanx.service.bm.entity.vo.BmInfoStatisticsVo">
		select
		SUM(b.sub_number) as totalSubNumber,
		SUM(b.sub_book_total_price) as totalSubBookPrice
		from bm_info b
		left join activ_info a on a.activ_id = b.activ_id and a.is_delete = 0
		left join activ_order o
		on b.info_id = o.goods_id and o.goods_type = 1 and o.is_delete = 0
		left join cm_info c on b.cm = c.cm and b.activ_id = c.activ_id and c.is_delete = 0
		<where>
			<if test="infoId != null ">and b.info_id = #{infoId}</if>
			<if test="provinceId != null ">and b.province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and b.province_name like concat('%', #{provinceName}, '%')</if>
			<if test="cityId != null ">and b.city_id = #{cityId}</if>
			<if test="cityName != null and cityName != '' ">and b.city_name like concat('%', #{cityName}, '%')</if>
			<if test="areaId != null ">and b.area_id = #{areaId}</if>
			<if test="areaName != null and areaName != '' ">and b.area_name like concat('%', #{areaName}, '%')</if>
			<if test="address != null and address != '' ">and b.address like concat('%', #{address}, '%')</if>
			<if test="departName != null and departName != '' ">and b.depart_name like concat('%', #{departName}, '%')</if>
			<if test="period != null and period != '' ">and b.period like concat('%', #{period}, '%')</if>
			<if test="joinType != null ">and b.join_type = #{joinType}</if>
			<if test="contactName != null and contactName != '' ">and b.contact_name like concat('%', #{contactName}, '%')</if>
			<if test="contactMobile != null and contactMobile != '' ">and b.contact_mobile = #{contactMobile}</if>
			<if test="isDelete != null ">and b.is_delete = #{isDelete}</if>
			<if test="submitType != null ">and b.submit_type = #{submitType}</if>
			<if test="payType != null ">and b.pay_type = #{payType}</if>
			<if test="invoiceType != null ">and b.invoice_type = #{invoiceType}</if>
			<if test="invoiceId != null and invoiceId !=''">and b.invoice_id = #{invoiceId}</if>
			<if test="invoiceUrl != null and invoiceUrl !=''">and b.invoice_url = #{invoiceUrl}</if>
			<if test="invoiceName != null and invoiceName !=''">and b.invoice_name = #{invoiceName}</if>
			<if test="invoiceNameOri != null and invoiceNameOri !=''">and b.invoice_name_ori = #{invoiceNameOri}</if>
			<if test="payId != null and payId !=''">and b.pay_id = #{payId}</if>
			<if test="payUrl != null and payUrl !=''">and b.pay_url = #{payUrl}</if>
			<if test="payName != null and payName !=''">and b.pay_name = #{payName}</if>
			<if test="payNameOri != null and payNameOri !=''">and b.pay_name_ori = #{payNameOri}</if>

			<if test="startTime != null">
				and b.create_time <![CDATA[  >= ]]> #{startTime}
			</if>
			<if test="endTime != null">
				and b.create_time <![CDATA[  <= ]]> #{endTime}
			</if>
			<if test="notEqInfoId != null">
				and b.info_id != #{notEqInfoId}
			</if>
			<if test="activId != null">and b.activ_id = #{activId}</if>
			<if test="signInState != null">and b.sign_in_state = #{signInState}</if>
			<if test="submitTypeList != null and submitTypeList.size() != 0">
				and b.submit_type in
				<foreach collection="submitTypeList" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="signInCode != null and signInCode != ''">
				and b.sign_in_code = #{signInCode}
			</if>
			<if test="joinType != null">and b.join_type = #{joinType}</if>
			<if test="invoicingType != null">and b.invoicing_type = #{invoicingType}</if>
			<if test="invoiceHeader != null and invoiceHeader != ''">and b.invoice_header like concat('%', #{invoiceHeader}, '%')</if>
			<if test="dutyCode != null and dutyCode != ''">and b.duty_code like concat('%', #{dutyCode}, '%')</if>
			<if test="email != null and email != ''">and b.email like concat('%', #{email}, '%')</if>
			<if test="payTime != null">and b.pay_time = #{payTime}</if>
			<if test="payUserOid != null and payUserOid != ''">and b.pay_user_oid = #{payUserOid}</if>
			<if test="payUserName != null and payUserName != ''">and b.pay_user_name like concat('%', #{payUserName}, '%')</if>
			<if test="subNumber != null">and b.sub_number = #{subNumber}</if>
			<if test="subBookTotalPrice != null">and b.sub_book_total_price = #{subBookTotalPrice}</if>
			<if test="subBookPrice != null">and b.sub_book_price = #{subBookPrice}</if>
			<if test="recName != null and recName != ''">and b.rec_name like concat('%', #{recName}, '%')</if>
			<if test="recMobile != null and recMobile != ''">and b.rec_mobile like concat('%', #{recMobile}, '%')</if>
			<if test="recProvinceId != null ">and b.rec_province_id = #{recProvinceId}</if>
			<if test="recProvinceName != null and recProvinceName != ''">and b.rec_province_name like concat('%', #{recProvinceName}, '%')</if>
			<if test="recCityId != null">and b.rec_city_id = #{recCityId}</if>
			<if test="recCityName != null and recCityName != ''">and b.rec_city_name like concat('%', #{recCityName}, '%')</if>
			<if test="recAreaId != null">and b.rec_area_id = #{recAreaId}</if>
			<if test="recAreaName != null and recAreaName != ''">and b.rec_area_name like concat('%', #{recAreaName}, '%')</if>
			<if test="recAddress != null and recAddress != ''">and b.rec_address like concat('%', #{recAddress}, '%')</if>
			<if test="payWayType != null">and b.pay_way_type=#{payWayType}</if>
			<if test="payRecord != null">and b.pay_record=#{payRecord}</if>
			<if test="cmType != null">
				<if test="cmType == 1">
					and b.cm is null
				</if>
				<if test="cmType == 2">
					and b.cm is not null and b.cm != ''
				</if>
			</if>
			<if test="cmRealName != null and cmRealName != ''">
				and c.real_name like concat('%', #{cmRealName}, '%')
			</if>
			<if test="adminOid != null and adminOid != ''">
				and c.admin_oid = #{adminOid}
			</if>
			<if test="deliverState != null">and b.deliver_state = #{deliverState}</if>
			<if test="logisticsCode != null and logisticsCode != ''">and b.logistics_code like concat('%', #{logisticsCode}, '%')</if>
			<if test="logisticsOrg != null and logisticsOrg != ''">and b.logistics_org like concat('%', #{logisticsOrg}, '%')</if>
		</where>
	</select>

	<select id="checkPhoneForLogin" resultType="java.lang.String">
		select bij.joiner_mobile
		from bm_info bi
		join bm_info_joiner bij on bi.info_id = bij.info_id and bij.is_delete = 0
		where
			bij.joiner_mobile = #{phone}
			and bi.submit_type = 3
			and bi.pay_type = 2
	</select>

	<select id="countBmInfoJoiner" resultType="java.lang.Integer">
		select
			ifnull(count(1), 0)
		from bm_info b
		join bm_info_joiner bij on b.info_id = bij.info_id and bij.is_delete = 0
		<where>
			<if test="infoId != null ">and b.info_id = #{infoId}</if>
			<if test="provinceId != null ">and b.province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and b.province_name like concat('%', #{provinceName}, '%')</if>
			<if test="cityId != null ">and b.city_id = #{cityId}</if>
			<if test="cityName != null and cityName != '' ">and b.city_name like concat('%', #{cityName}, '%')</if>
			<if test="areaId != null ">and b.area_id = #{areaId}</if>
			<if test="areaName != null and areaName != '' ">and b.area_name like concat('%', #{areaName}, '%')</if>
			<if test="address != null and address != '' ">and b.address like concat('%', #{address}, '%')</if>
			<if test="departName != null and departName != '' ">and b.depart_name like concat('%', #{departName}, '%')</if>
			<if test="period != null and period != '' ">and b.period like concat('%', #{period}, '%')</if>
			<if test="joinType != null ">and b.join_type = #{joinType}</if>
			<if test="contactName != null and contactName != '' ">and b.contact_name like concat('%', #{contactName}, '%')</if>
			<if test="contactMobile != null and contactMobile != '' ">and b.contact_mobile = #{contactMobile}</if>
			<if test="isDelete != null ">and b.is_delete = #{isDelete}</if>
			<if test="submitType != null ">and b.submit_type = #{submitType}</if>
			<if test="payType != null ">and b.pay_type = #{payType}</if>
			<if test="invoiceType != null ">and b.invoice_type = #{invoiceType}</if>
			<if test="invoiceId != null and invoiceId !=''">and b.invoice_id = #{invoiceId}</if>
			<if test="invoiceUrl != null and invoiceUrl !=''">and b.invoice_url = #{invoiceUrl}</if>
			<if test="invoiceName != null and invoiceName !=''">and b.invoice_name = #{invoiceName}</if>
			<if test="invoiceNameOri != null and invoiceNameOri !=''">and b.invoice_name_ori = #{invoiceNameOri}</if>
			<if test="payId != null and payId !=''">and b.pay_id = #{payId}</if>
			<if test="payUrl != null and payUrl !=''">and b.pay_url = #{payUrl}</if>
			<if test="payName != null and payName !=''">and b.pay_name = #{payName}</if>
			<if test="payNameOri != null and payNameOri !=''">and b.pay_name_ori = #{payNameOri}</if>

			<if test="startTime != null">
				and b.create_time <![CDATA[  >= ]]> #{startTime}
			</if>
			<if test="endTime != null">
				and b.create_time <![CDATA[  <= ]]> #{endTime}
			</if>
			<if test="notEqInfoId != null">
				and b.info_id != #{notEqInfoId}
			</if>
			<if test="activId != null">and b.activ_id = #{activId}</if>
			<if test="signInState != null">and b.sign_in_state = #{signInState}</if>
			<if test="submitTypeList != null and submitTypeList.size() != 0">
				and b.submit_type in
				<foreach collection="submitTypeList" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="signInCode != null and signInCode != ''">
				and b.sign_in_code = #{signInCode}
			</if>
			<if test="joinType != null">and b.join_type = #{joinType}</if>
			<if test="invoicingType != null">and b.invoicing_type = #{invoicingType}</if>
			<if test="invoiceHeader != null and invoiceHeader != ''">and b.invoice_header like concat('%', #{invoiceHeader}, '%')</if>
			<if test="dutyCode != null and dutyCode != ''">and b.duty_code like concat('%', #{dutyCode}, '%')</if>
			<if test="email != null and email != ''">and b.email like concat('%', #{email}, '%')</if>
			<if test="payTime != null">and b.pay_time = #{payTime}</if>
			<if test="payUserOid != null and payUserOid != ''">and b.pay_user_oid = #{payUserOid}</if>
			<if test="payUserName != null and payUserName != ''">and b.pay_user_name like concat('%', #{payUserName}, '%')</if>
			<if test="subNumber != null">and b.sub_number = #{subNumber}</if>
			<if test="subBookTotalPrice != null">and b.sub_book_total_price = #{subBookTotalPrice}</if>
			<if test="subBookPrice != null">and b.sub_book_price = #{subBookPrice}</if>
			<if test="recName != null and recName != ''">and b.rec_name like concat('%', #{recName}, '%')</if>
			<if test="recMobile != null and recMobile != ''">and b.rec_mobile like concat('%', #{recMobile}, '%')</if>
			<if test="recProvinceId != null ">and b.rec_province_id = #{recProvinceId}</if>
			<if test="recProvinceName != null and recProvinceName != ''">and b.rec_province_name like concat('%', #{recProvinceName}, '%')</if>
			<if test="recCityId != null">and b.rec_city_id = #{recCityId}</if>
			<if test="recCityName != null and recCityName != ''">and b.rec_city_name like concat('%', #{recCityName}, '%')</if>
			<if test="recAreaId != null">and b.rec_area_id = #{recAreaId}</if>
			<if test="recAreaName != null and recAreaName != ''">and b.rec_area_name like concat('%', #{recAreaName}, '%')</if>
			<if test="recAddress != null and recAddress != ''">and b.rec_address like concat('%', #{recAddress}, '%')</if>
			<if test="selectMaterialType != null">and b.select_material_type=#{selectMaterialType}</if>
		</where>
	</select>

    <update id="updatePayTypeBatch">
		update bm_info
		set pay_type = #{payType}
		where info_id in
		<foreach collection="infoIds" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</update>
</mapper>