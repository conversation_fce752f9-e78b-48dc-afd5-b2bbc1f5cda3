<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.jz.mapper.JzCourseCasesTypeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.jz.entity.dto.JzCourseCasesTypeDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="casesId" column="cases_id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="casesId != null ">and cases_id = #{casesId}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.cases_id
	 		,t.name
	 		,t.type
	 		,t.is_delete
	 		,t.create_date
	 		,t.update_date
		from (
			 select a.* from jz_course_cases_type a
		 ) t

	</sql>

	<select id="getJzCourseCasesTypeListByCondition" resultType="com.fh.yanx.service.jz.entity.vo.JzCourseCasesTypeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getJzCourseCasesTypeByCondition" resultType="com.fh.yanx.service.jz.entity.vo.JzCourseCasesTypeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>