<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.jz.mapper.JzCourseCasesMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.jz.entity.dto.JzCourseCasesDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="schoolName" column="school_name"/>
        <result property="courseName" column="course_name"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="countyId" column="county_id"/>
        <result property="phase" column="phase"/>
        <result property="year" column="year"/>
        <result property="picture" column="picture"/>
        <result property="status" column="status"/>
        <result property="operation" column="operation"/>
        <result property="phone" column="phone"/>
        <result property="introduction" column="introduction"/>
        <result property="views" column="views"/>
        <result property="isAuth" column="is_auth"/>
        <result property="homeType" column="home_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="filePath" column="file_path"/>
        <result property="wordPath" column="word_path"/>
        <result property="isUpload" column="is_upload"/>
        <result property="customerId" column="customer_id"/>
        <result property="isExcellent" column="is_excellent"/>
        <result property="userOid" column="user_oid"/>
        <result property="sourceType" column="source_type"/>
        <result property="bestVerifyType" column="best_verify_type"/>
        <result property="templateType" column="template_type"/>
        <result property="templateLayoutOne" column="template_layout_one"/>
        <result property="templateLayoutTwo" column="template_layout_two"/>
        <result property="templateLayoutThree" column="template_layout_three"/>
        <result property="holdType" column="hold_type"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userId != null ">and user_id = #{userId}</if>
			<if test="schoolName != null and schoolName != '' ">and school_name like concat('%', #{schoolName}, '%')</if>
			<if test="courseName != null and courseName != '' ">and course_name like concat('%', #{courseName}, '%')</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="countyId != null ">and county_id = #{countyId}</if>
			<if test="phase != null ">and phase = #{phase}</if>
			<if test="year != null ">and year = #{year}</if>
			<if test="picture != null and picture != '' ">and picture like concat('%', #{picture}, '%')</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="operation != null and operation != '' ">and operation like concat('%', #{operation}, '%')</if>
			<if test="phone != null and phone != '' ">and phone like concat('%', #{phone}, '%')</if>
			<if test="introduction != null and introduction != '' ">and introduction like concat('%', #{introduction}, '%')</if>
			<if test="views != null ">and views = #{views}</if>
			<if test="isAuth != null ">and is_auth = #{isAuth}</if>
			<if test="homeType != null ">and home_type = #{homeType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createDate != null ">and create_date = #{createDate}</if>
			<if test="updateDate != null ">and update_date = #{updateDate}</if>
			<if test="filePath != null and filePath != '' ">and file_path like concat('%', #{filePath}, '%')</if>
			<if test="wordPath != null and wordPath != '' ">and word_path like concat('%', #{wordPath}, '%')</if>
			<if test="isUpload != null ">and is_upload = #{isUpload}</if>
			<if test="customerId != null ">and customer_id = #{customerId}</if>
			<if test="isExcellent != null ">and is_excellent = #{isExcellent}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="bestVerifyType != null ">and best_verify_type = #{bestVerifyType}</if>
			<if test="templateType != null ">and template_type = #{templateType}</if>
			<if test="templateLayoutOne != null and templateLayoutOne != '' ">and template_layout_one like concat('%', #{templateLayoutOne}, '%')</if>
			<if test="templateLayoutTwo != null and templateLayoutTwo != '' ">and template_layout_two like concat('%', #{templateLayoutTwo}, '%')</if>
			<if test="templateLayoutThree != null and templateLayoutThree != '' ">and template_layout_three like concat('%', #{templateLayoutThree}, '%')</if>
			<if test="holdType != null ">and hold_type = #{holdType}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.user_id
	 		,t.school_name
	 		,t.course_name
	 		,t.province_id
	 		,t.city_id
	 		,t.county_id
	 		,t.phase
	 		,t.year
	 		,t.picture
	 		,t.status
	 		,t.operation
	 		,t.phone
	 		,t.introduction
	 		,t.views
	 		,t.is_auth
	 		,t.home_type
	 		,t.is_delete
	 		,t.create_date
	 		,t.update_date
	 		,t.file_path
	 		,t.word_path
	 		,t.is_upload
	 		,t.customer_id
	 		,t.is_excellent
	 		,t.user_oid
	 		,t.source_type
	 		,t.best_verify_type
	 		,t.template_type
	 		,t.template_layout_one
	 		,t.template_layout_two
	 		,t.template_layout_three
	 		,t.hold_type
		from (
			 select a.* from jz_course_cases a
		 ) t

	</sql>

	<select id="getJzCourseCasesListByCondition" resultType="com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getJzCourseCasesByCondition" resultType="com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getHomeList" resultType="com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo">
		SELECT
		t1.id,
		t1.course_name as courseName,
		t1.school_name as schoolName,
		t1.operation,
		t1.phase,
		t1.year,
		t1.introduction,
		t1.views,
		t1.picture,
		t1.home_type,
		t1.is_excellent,
		t1.hold_type,
		t1.user_oid,
		t1.source_type,
		t1.best_verify_type,
		t1.template_type,
		t1.template_layout_one,
		t1.template_layout_two,
		t1.template_layout_three
		FROM
		jz_course_cases t1
		<if test="grade != null">
			JOIN jz_course_cases_grade t2 ON t1.id = t2.cases_id
		</if>
		<if test="type != null">
			JOIN jz_course_cases_type t3 ON t1.id = t3.cases_id
		</if>
		WHERE
		t1.is_delete = 0 and t1.hold_type=1
		<!--AND t1.is_auth = 1-->
		<if test="phase != null">AND t1.phase = #{phase}</if>
		<if test="grade != null">AND t2.is_delete = 0 AND t2.grade = #{grade}</if>
		<if test="provinceId != null">AND t1.province_id = #{provinceId}</if>
		<if test="type != null">AND t3.is_delete = 0 AND t3.type = #{type}</if>
		<if test="year != null">AND t1.year = #{year}</if>
		<if test="homeType != null"> AND t1.home_type=#{homeType}</if>
		<if test="isExcellent != null"> AND t1.is_excellent=#{isExcellent}</if>
		<if test="courseName != null and courseName !=''"> AND t1.course_name like concat('%',#{courseName},'%')</if>
		<if test="userOid != null and userOid !=''"> AND t1.user_oid = #{userOid}</if>
		<if test="sourceType != null"> AND t1.source_type = #{sourceType}</if>
		<if test="bestVerifyType != null"> AND t1.best_verify_type = #{bestVerifyType}</if>
		<if test="templateType != null"> AND t1.template_type = #{templateType}</if>
		<if test="templateLayoutOne != null"> AND t1.template_layout_one like concat('%', #{templateLayoutOne},'%')</if>
		<if test="templateLayoutTwo != null"> AND t1.template_layout_two like concat('%', #{templateLayoutTwo},'%')</if>
		<if test="templateLayoutThree != null"> AND t1.template_layout_three like concat('%', #{templateLayoutThree},'%')</if>
		and (source_type=1 or (source_type=2 and best_verify_type=2))
	</select>
</mapper>