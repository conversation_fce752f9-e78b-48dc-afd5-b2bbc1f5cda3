<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.cm.mapper.CmInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.cm.entity.dto.CmInfoDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="activId" column="activ_id"/>
        <result property="activName" column="activ_name"/>
        <result property="adminOid" column="admin_oid"/>
        <result property="realName" column="real_name"/>
        <result property="phone" column="phone"/>
        <result property="cm" column="cm"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="activId != null ">and activ_id = #{activId}</if>
			<if test="activName != null and activName != '' ">and activ_name like concat('%', #{activName}, '%')</if>
			<if test="adminOid != null and adminOid != '' ">and admin_oid like concat('%', #{adminOid}, '%')</if>
			<if test="realName != null and realName != '' ">and real_name like concat('%', #{realName}, '%')</if>
			<if test="phone != null and phone != '' ">and phone like concat('%', #{phone}, '%')</if>
			<if test="cm != null and cm != '' ">and cm like concat('%', #{cm}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.activ_id
	 		,t.activ_name
	 		,t.admin_oid
	 		,t.real_name
	 		,t.phone
	 		,t.cm
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from cm_info a
		 ) t

	</sql>

	<select id="getCmInfoListByCondition" resultType="com.fh.yanx.service.cm.entity.vo.CmInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCmInfoByCondition" resultType="com.fh.yanx.service.cm.entity.vo.CmInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>