<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.org.mapper.OrganizationAuthLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.org.entity.dto.OrganizationAuthLogDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="authType" column="auth_type"/>
        <result property="authEndDay" column="auth_end_day"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="authType != null ">and auth_type = #{authType}</if>
			<if test="authEndDay != null ">and auth_end_day = #{authEndDay}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="authType != null ">and auth_type = #{authType}</if>
			<if test="authEndDay != null ">and auth_end_day = #{authEndDay}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.organization_id
	 		,t.auth_type
	 		,t.auth_end_day
	 		,t.is_delete
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
		from (
			select a.* from organization_auth_log a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrganizationAuthLogListByCondition" resultType="com.fh.yanx.service.org.entity.vo.OrganizationAuthLogVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by create_time desc
	</select>

	<select id="getOrganizationAuthLogByCondition" resultType="com.fh.yanx.service.org.entity.vo.OrganizationAuthLogVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>