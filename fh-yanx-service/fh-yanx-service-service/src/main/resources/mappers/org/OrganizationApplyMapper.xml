<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.org.mapper.OrganizationApplyMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.org.entity.dto.OrganizationApplyDto" id="BaseResultMap">
        <result property="organizationApplyId" column="organization_apply_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="name" column="name"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="areaId" column="area_id"/>
        <result property="contact" column="contact"/>
        <result property="concatName" column="concat_name"/>
        <result property="type" column="type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="organizationApplyId != null ">and organization_apply_id = #{organizationApplyId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="areaId != null ">and area_id = #{areaId}</if>
			<if test="contact != null and contact != '' ">and contact like concat('%', #{contact}, '%')</if>
			<if test="concatName != null and concatName != '' ">and concat_name like concat('%', #{concatName}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="organizationApplyId != null ">and organization_apply_id = #{organizationApplyId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="areaId != null ">and area_id = #{areaId}</if>
			<if test="contact != null and contact != '' ">and contact like concat('%', #{contact}, '%')</if>
			<if test="concatName != null and concatName != '' ">and concat_name like concat('%', #{concatName}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.organization_apply_id
	 		,t.organization_id
	 		,t.name
	 		,t.province_id
	 		,t.city_id
	 		,t.area_id
	 		,t.contact
	 		,t.concat_name
	 		,t.type
	 		,t.is_delete
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
		from (
			select a.* from organization_apply a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrganizationApplyListByCondition" resultType="com.fh.yanx.service.org.entity.vo.OrganizationApplyVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by create_time desc
	</select>

	<select id="getOrganizationApplyByCondition" resultType="com.fh.yanx.service.org.entity.vo.OrganizationApplyVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>