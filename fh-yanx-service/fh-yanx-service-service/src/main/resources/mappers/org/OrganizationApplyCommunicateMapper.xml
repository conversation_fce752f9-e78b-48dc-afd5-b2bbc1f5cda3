<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.org.mapper.OrganizationApplyCommunicateMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.org.entity.dto.OrganizationApplyCommunicateDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="organizationApplyId" column="organization_apply_id"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationApplyId != null ">and organization_apply_id = #{organizationApplyId}</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationApplyId != null ">and organization_apply_id = #{organizationApplyId}</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.organization_apply_id
	 		,t.content
	 		,t.type
	 		,t.is_delete
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
		from (
			select a.* from organization_apply_communicate a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrganizationApplyCommunicateListByCondition" resultType="com.fh.yanx.service.org.entity.vo.OrganizationApplyCommunicateVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by create_time desc
	</select>

	<select id="getOrganizationApplyCommunicateByCondition" resultType="com.fh.yanx.service.org.entity.vo.OrganizationApplyCommunicateVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>