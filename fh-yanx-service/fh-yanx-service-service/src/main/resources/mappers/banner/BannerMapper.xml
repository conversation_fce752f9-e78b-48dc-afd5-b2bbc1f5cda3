<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.banner.mapper.BannerInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.banner.entity.dto.BannerInfoDto" id="BaseResultMap">
        <result property="bannerInfoId" column="banner_info_id"/>
        <result property="bannerShowType" column="banner_show_type"/>
        <result property="bannerImageId" column="banner_image_id"/>
        <result property="bannerImageUrl" column="banner_image_url"/>
        <result property="jumpUrl" column="jump_url"/>
        <result property="startTime" column="start_time"/>
		<result property="endType" column="end_type"/>
        <result property="endTime" column="end_time"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="bannerInfoId != null">and banner_info_id = #{bannerInfoId}</if>
			<if test="bannerShowType != null ">and banner_show_type = #{bannerShowType}</if>
			<if test="bannerImageId != null and bannerImageId != '' ">and banner_image_id like concat('%', #{bannerImageId}, '%')</if>
			<if test="bannerImageUrl != null and bannerImageUrl != '' ">and banner_image_url like concat('%', #{bannerImageUrl}, '%')</if>
			<if test="jumpUrl != null and jumpUrl != '' ">and jump_url like concat('%', #{jumpUrl}, '%')</if>
			<if test="startTime != null ">and start_time = #{startTime}</if>
			<if test="endType != null">and end_type = #{endType}</if>
			<if test="endTime != null ">and end_time = #{endTime}</if>
			<if test="state != null ">and state = #{state}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="bannerInfoId != null ">and banner_info_id = #{bannerId}</if>
			<if test="bannerShowType != null ">and banner_show_type = #{bannerShowType}</if>
			<if test="bannerImageId != null and bannerImageId != '' ">and banner_image_id like concat('%', #{bannerImageId}, '%')</if>
			<if test="bannerImageUrl != null and bannerImageUrl != '' ">and banner_image_url like concat('%', #{bannerImageUrl}, '%')</if>
			<if test="jumpUrl != null and jumpUrl != '' ">and jump_url like concat('%', #{jumpUrl}, '%')</if>
			<if test="startTime != null ">and start_time = #{startTime}</if>
			<if test="endType != null">and end_type = #{endType}</if>
			<if test="endTime != null ">and end_time = #{endTime}</if>
			<if test="state != null ">and state = #{state}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.banner_info_id
	 		,t.banner_show_type
	 		,t.banner_image_id
	 		,t.banner_image_url
	 		,t.jump_url
	 		,t.start_time
			,t.end_type
	 		,t.end_time
	 		,t.state
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from banner a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getBannerListByCondition" resultType="com.fh.yanx.service.banner.entity.vo.BannerInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by update_time desc
	</select>

	<select id="getBannerByCondition" resultType="com.fh.yanx.service.banner.entity.vo.BannerInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>