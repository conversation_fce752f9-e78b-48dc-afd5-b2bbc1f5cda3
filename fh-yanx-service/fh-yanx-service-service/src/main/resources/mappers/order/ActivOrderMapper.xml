<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.order.mapper.ActivOrderMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.order.entity.dto.ActivOrderDto" id="BaseResultMap">
        <result property="orderId" column="order_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="orderNumber" column="order_number"/>
        <result property="orderName" column="order_name"/>
        <result property="orderType" column="order_type"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="orderState" column="order_state"/>
        <result property="payMode" column="pay_mode"/>
        <result property="goodsType" column="goods_type"/>
        <result property="goodsId" column="goods_id"/>
        <result property="goodsQuantity" column="goods_quantity"/>
        <result property="addressInfo" column="address_info"/>
        <result property="terminalType" column="terminal_type"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="payScene" column="pay_scene"/>
        <result property="endTime" column="end_time"/>
        <result property="payTime" column="pay_time"/>
        <result property="applyRefundMoney" column="apply_refund_money"/>
        <result property="applyRefundReason" column="apply_refund_reason"/>
        <result property="applyRefundTime" column="apply_refund_time"/>
        <result property="refundTime" column="refund_time"/>
        <result property="refundMoney" column="refund_money"/>
        <result property="rejectTime" column="reject_time"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="firstPay" column="first_pay"/>
        <result property="note" column="note"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="orderId != null ">and order_id = #{orderId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="orderNumber != null and orderNumber != '' ">and order_number like concat('%', #{orderNumber}, '%')</if>
			<if test="orderName != null and orderName != '' ">and order_name like concat('%', #{orderName}, '%')</if>
			<if test="orderType != null ">and order_type = #{orderType}</if>
			<if test="transactionId != null and transactionId != '' ">and transaction_id like concat('%', #{transactionId}, '%')</if>
			<if test="orderState != null ">and order_state = #{orderState}</if>
			<if test="payMode != null ">and pay_mode = #{payMode}</if>
			<if test="goodsType != null ">and goods_type = #{goodsType}</if>
			<if test="goodsId != null ">and goods_id = #{goodsId}</if>
			<if test="goodsQuantity != null ">and goods_quantity = #{goodsQuantity}</if>
			<if test="addressInfo != null and addressInfo != '' ">and address_info like concat('%', #{addressInfo}, '%')</if>
			<if test="terminalType != null ">and terminal_type = #{terminalType}</if>
			<if test="payAmount != null ">and pay_amount = #{payAmount}</if>
			<if test="orderAmount != null ">and order_amount = #{orderAmount}</if>
			<if test="payScene != null ">and pay_scene = #{payScene}</if>
			<if test="endTime != null ">and end_time = #{endTime}</if>
			<if test="payTime != null ">and pay_time = #{payTime}</if>
			<if test="applyRefundMoney != null ">and apply_refund_money = #{applyRefundMoney}</if>
			<if test="applyRefundReason != null and applyRefundReason != '' ">and apply_refund_reason like concat('%', #{applyRefundReason}, '%')</if>
			<if test="applyRefundTime != null ">and apply_refund_time = #{applyRefundTime}</if>
			<if test="refundTime != null ">and refund_time = #{refundTime}</if>
			<if test="refundMoney != null ">and refund_money = #{refundMoney}</if>
			<if test="rejectTime != null ">and reject_time = #{rejectTime}</if>
			<if test="cancelTime != null ">and cancel_time = #{cancelTime}</if>
			<if test="firstPay != null ">and first_pay = #{firstPay}</if>
			<if test="note != null and note != '' ">and note like concat('%', #{note}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="orderId != null ">and order_id = #{orderId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="orderNumber != null and orderNumber != '' ">and order_number like concat('%', #{orderNumber}, '%')</if>
			<if test="orderName != null and orderName != '' ">and order_name like concat('%', #{orderName}, '%')</if>
			<if test="orderType != null ">and order_type = #{orderType}</if>
			<if test="transactionId != null and transactionId != '' ">and transaction_id like concat('%', #{transactionId}, '%')</if>
			<if test="orderState != null ">and order_state = #{orderState}</if>
			<if test="payMode != null ">and pay_mode = #{payMode}</if>
			<if test="goodsType != null ">and goods_type = #{goodsType}</if>
			<if test="goodsId != null ">and goods_id = #{goodsId}</if>
			<if test="goodsQuantity != null ">and goods_quantity = #{goodsQuantity}</if>
			<if test="addressInfo != null and addressInfo != '' ">and address_info like concat('%', #{addressInfo}, '%')</if>
			<if test="terminalType != null ">and terminal_type = #{terminalType}</if>
			<if test="payAmount != null ">and pay_amount = #{payAmount}</if>
			<if test="orderAmount != null ">and order_amount = #{orderAmount}</if>
			<if test="payScene != null ">and pay_scene = #{payScene}</if>
			<if test="endTime != null ">and end_time = #{endTime}</if>
			<if test="payTime != null ">and pay_time = #{payTime}</if>
			<if test="applyRefundMoney != null ">and apply_refund_money = #{applyRefundMoney}</if>
			<if test="applyRefundReason != null and applyRefundReason != '' ">and apply_refund_reason like concat('%', #{applyRefundReason}, '%')</if>
			<if test="applyRefundTime != null ">and apply_refund_time = #{applyRefundTime}</if>
			<if test="refundTime != null ">and refund_time = #{refundTime}</if>
			<if test="refundMoney != null ">and refund_money = #{refundMoney}</if>
			<if test="rejectTime != null ">and reject_time = #{rejectTime}</if>
			<if test="cancelTime != null ">and cancel_time = #{cancelTime}</if>
			<if test="firstPay != null ">and first_pay = #{firstPay}</if>
			<if test="note != null and note != '' ">and note like concat('%', #{note}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.order_id
	 		,t.user_oid
	 		,t.order_number
	 		,t.order_name
	 		,t.order_type
	 		,t.transaction_id
	 		,t.order_state
	 		,t.pay_mode
	 		,t.goods_type
	 		,t.goods_id
	 		,t.goods_quantity
	 		,t.address_info
	 		,t.terminal_type
	 		,t.pay_amount
	 		,t.order_amount
	 		,t.pay_scene
	 		,t.end_time
	 		,t.pay_time
	 		,t.apply_refund_money
	 		,t.apply_refund_reason
	 		,t.apply_refund_time
	 		,t.refund_time
	 		,t.refund_money
	 		,t.reject_time
	 		,t.cancel_time
	 		,t.first_pay
	 		,t.note
	 		,t.is_delete
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
		from (
			select a.* from activ_order a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getActivOrderListByCondition" resultType="com.fh.yanx.service.order.entity.vo.ActivOrderVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getActivOrderByCondition" resultType="com.fh.yanx.service.order.entity.vo.ActivOrderVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

    <update id="changeActivOrderTimeout">
		update activ_order
		set order_state = 8,
			update_time = now()
		<where>
			order_id in
			<foreach collection="orderIds" index="index" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</where>
    </update>
</mapper>