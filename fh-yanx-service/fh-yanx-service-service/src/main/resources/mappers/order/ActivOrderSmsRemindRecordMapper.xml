<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.yanx.service.order.mapper.ActivOrderSmsRemindRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.yanx.service.order.entity.dto.ActivOrderSmsRemindRecordDto" id="BaseResultMap">
		<result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null">and id = #{id}</if>
			<if test="orderId != null ">and order_id = #{orderId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null">and id = #{id}</if>
			<if test="orderId != null ">and order_id = #{orderId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.order_id
	 		,t.is_delete
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
		from (
			select a.* from activ_order_sms_remind_record a
			<include refid="table_where"></include>
		 ) t

	</sql>

    <select id="getActivOrderSmsRemindRecordListByCondition"
			resultType="com.fh.yanx.service.order.entity.vo.ActivOrderSmsRemindRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

    <select id="getActivOrderSmsRemindRecordByCondition"
			resultType="com.fh.yanx.service.order.entity.vo.ActivOrderSmsRemindRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>