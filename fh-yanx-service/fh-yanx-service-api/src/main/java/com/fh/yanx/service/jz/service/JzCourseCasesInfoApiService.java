package com.fh.yanx.service.jz.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.jz.api.JzCourseCasesInfoApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 金中-校本课程案例详细信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@FeignClient(contextId = "jzCourseCasesInfoApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = JzCourseCasesInfoApiService.JzCourseCasesInfoApiFallbackFactory.class)
@Component
public interface JzCourseCasesInfoApiService extends JzCourseCasesInfoApi {

    @Component
    class JzCourseCasesInfoApiFallbackFactory implements FallbackFactory<JzCourseCasesInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(JzCourseCasesInfoApiFallbackFactory.class);
        @Override
        public JzCourseCasesInfoApiService create(Throwable cause) {
            JzCourseCasesInfoApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new JzCourseCasesInfoApiService() {
                public AjaxResult getJzCourseCasesInfoPageListByCondition(JzCourseCasesInfoConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getJzCourseCasesInfoListByCondition(JzCourseCasesInfoConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addJzCourseCasesInfo(JzCourseCasesInfoBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateJzCourseCasesInfo(JzCourseCasesInfoBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}