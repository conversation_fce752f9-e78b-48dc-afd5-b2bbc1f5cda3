package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStuAttachmentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课题研究附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicStuAttachmentApi {

    /**
     * 查询课题研究附件表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/stu/attachment/page/list")
    public AjaxResult<PageInfo<ResTopicStuAttachmentVo>> getResTopicStuAttachmentPageListByCondition(@RequestBody ResTopicStuAttachmentConditionBo condition);

    /**
     * 查询课题研究附件表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/stu/attachment/list")
    public AjaxResult<List<ResTopicStuAttachmentVo>> getResTopicStuAttachmentListByCondition(@RequestBody ResTopicStuAttachmentConditionBo condition);


    /**
     * 新增课题研究附件表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping(value = "res/topic/stu/attachment/add")
    public AjaxResult addResTopicStuAttachment(@RequestBody ResTopicStuAttachmentBo resTopicStuAttachmentBo);

    /**
     * 修改课题研究附件表
     * @param resTopicStuAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/stu/attachment/update")
    public AjaxResult updateResTopicStuAttachment(@Validated @RequestBody ResTopicStuAttachmentBo resTopicStuAttachmentBo);

    /**
     * 批量修改附件
     *
     * @param resTopicStuAttachmentBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/8 14:54
     */
    @PostMapping("res/topic/stu/attachment/update-batch")
    AjaxResult updateBatchResTopicStuAttachment(@RequestBody List<ResTopicStuAttachmentBo> resTopicStuAttachmentBos);

    /**
     * 查询课题研究附件表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/stu/attachment/detail")
    public AjaxResult<ResTopicStuAttachmentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课题研究附件表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/stu/attachment/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
    /**
     * 删除课题研究附件表-根据fileOid
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/stu/attachment/delete-oid")
    public AjaxResult deleteByFileOid(@NotNull(message = "请选择数据") @RequestParam("fileOid") String fileOid);
}
