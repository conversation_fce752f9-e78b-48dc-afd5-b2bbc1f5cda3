package com.fh.yanx.service.courseReviewExpertConfig.entity.vo;

import com.fh.yanx.service.org.entity.vo.UserVoExt;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 课程审核专家配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@Data
public class CourseReviewExpertConfigVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 校本课程案例ID
     */
    @ApiModelProperty("校本课程案例ID")
    private Long casesId;

    /**
     * 专家用户的oid
     */
    @ApiModelProperty("专家用户的oid")
    private String userOid;

    /**
     * 专家用户
     */
    @ApiModelProperty("专家用户")
    private UserVoExt userVoExt;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 审核进度类型，默认空：1初审，2终审
     */
    @ApiModelProperty("审核进度类型")
    private Integer verifyProcessType;

    /**
     * 是否需要评分：1不需要评分,2需要评分
     */
    @ApiModelProperty("是否需要评分：1不需要评分,2需要评分")
    private Integer needScore;

    /**
     * 1.审核专家，2指导专家，3专家助理
     */
    @ApiModelProperty("1.审核专家，2指导专家，3专家助理")
    private Integer expertType;

    /**
     * 是否评分
     */
    @ApiModelProperty("是否评分")
    private Boolean isScore;

    /*
     * 方便steam流存入自身
     * */
    public CourseReviewExpertConfigVo returnOwn() {
        return this;
    }

}
