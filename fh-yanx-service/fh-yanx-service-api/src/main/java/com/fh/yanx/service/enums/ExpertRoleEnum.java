package com.fh.yanx.service.enums;

/**
 * 专家角色
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-11-27  14:24
 */
public enum ExpertRoleEnum {
    AUDIT_EXPERT("审核专家", "yanx_audit_expert", 22L),
    GUIDE_EXPERT("指导专家", "yanx_guide_expert", 33L),
    COMPREHENSIVE_AUDIT("综评专家", "yanx_comprehensive_audit", 29L),
    EXPERT_ASSISTANT("专家助理", "yanx_expert_assistant", 28L),
    ;

    private String name;
    private String code;
    private Long roleId;

    ExpertRoleEnum(String name, String code, Long roleId) {
        this.name = name;
        this.code = code;
        this.roleId = roleId;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public Long getRoleId() {
        return this.roleId;
    }

    public static ExpertRoleEnum getByRoleId(Long roleId){
        for(ExpertRoleEnum roleEnum:ExpertRoleEnum.values()){
            if(roleEnum.getRoleId().equals(roleId)){
                return roleEnum;
            }
        }
        //默认课班报名
        return null;
    }

}
