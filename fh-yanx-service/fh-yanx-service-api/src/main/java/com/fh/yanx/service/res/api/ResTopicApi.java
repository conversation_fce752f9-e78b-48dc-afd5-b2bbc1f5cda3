package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课题表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicApi {

    /**
     * 查询课题表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/page/list")
    public AjaxResult<PageInfo<ResTopicVo>> getResTopicPageListByCondition(@RequestBody ResTopicConditionBo condition);

    /**
     * 查询课题表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/list")
    public AjaxResult<List<ResTopicVo>> getResTopicListByCondition(@RequestBody ResTopicConditionBo condition);


    /**
     * 新增课题表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/add")
    public AjaxResult addResTopic(@Validated @RequestBody ResTopicBo resTopicBo);

    /**
     * 修改课题表
     * @param resTopicBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/update")
    public AjaxResult updateResTopicStatus(@RequestBody ResTopicBo resTopicBo);

    /**
     * 查询课题表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/detail")
    public AjaxResult<ResTopicVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     *获取当前用户的课题详情
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/1 17:41
     */
    @GetMapping("res/topic/user-detail")
    public AjaxResult getCurrentUserTopicDetail(@RequestParam("organizationId")Long organizationId,@RequestParam("currentUserOid")String currentUserOid);

    /**
     * 删除课题表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
