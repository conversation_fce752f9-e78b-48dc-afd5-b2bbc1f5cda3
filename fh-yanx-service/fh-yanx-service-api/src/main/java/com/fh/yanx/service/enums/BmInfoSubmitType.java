package com.fh.yanx.service.enums;

/**
 * 报名确认状态
 *
 * <AUTHOR>
 * @date 2023-07-31 9:28
 */
public enum BmInfoSubmitType {
    TO_CONFIRM(1, "待确认"),
    COMMUNICATION(2, "沟通中"),
    ATTEND(3, "确认参会"),
    REFUSE(4, "已拒绝");

    private Integer code;
    private String value;

    BmInfoSubmitType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
