package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 结题答辩附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
public class ResTopicRepAttachmentBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 课题id
     */
    @ApiModelProperty("课题id")
    private Long topicId;

    /**
     * 研究附件名称
     */
    @ApiModelProperty("研究附件名称")
    private String repName;

    /**
     * 论文摘要
     */
    @ApiModelProperty("论文摘要")
    private String repDesc;

    /**
     * 附件文件原始名称-带后缀
     */
    @ApiModelProperty("附件文件原始名称-带后缀")
    private String repFileNameOri;

    /**
     * 附件文件名称-不带后缀
     */
    @ApiModelProperty("附件文件名称-不带后缀")
    private String repFileName;

    /**
     * 附件文件地址
     */
    @ApiModelProperty("附件文件地址")
    private String repFileUrl;


    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;


    /**
     * 附件类型，1：结题论文，2：答辩材料
     */
	@ApiModelProperty("附件类型，1：结题论文，2：答辩材料")
    private Integer repType;

    /**
     * 文件oid
     */
    private String repFileOid;

    /**
     * 当前登陆用户oid
     */
    @ApiModelProperty("当前登陆用户oid")
    private String currentUserOid;

}
