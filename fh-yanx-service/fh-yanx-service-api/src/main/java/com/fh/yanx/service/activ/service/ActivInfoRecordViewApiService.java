package com.fh.yanx.service.activ.service;


import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.yanx.service.activ.api.ActivInfoRecordViewApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

import java.util.List;


/**
 * 新时代文化校园活动内容观看记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
@FeignClient(contextId = "activInfoRecordViewApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ActivInfoRecordViewApiService.ActivInfoRecordViewApiFallbackFactory.class)
@Component
public interface ActivInfoRecordViewApiService extends ActivInfoRecordViewApi {

    @Component
    class ActivInfoRecordViewApiFallbackFactory implements FallbackFactory<ActivInfoRecordViewApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ActivInfoRecordViewApiFallbackFactory.class);
        @Override
        public ActivInfoRecordViewApiService create(Throwable cause) {
            ActivInfoRecordViewApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new ActivInfoRecordViewApiService() {
                public AjaxResult getActivInfoRecordViewPageListByCondition(ActivInfoRecordViewConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getActivInfoRecordViewListByCondition(ActivInfoRecordViewConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addActivInfoRecordView(ActivInfoRecordViewBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateActivInfoRecordView(ActivInfoRecordViewBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<List<ActivInfoRecordVo>> getActivInfoRecordViewStatistics(ActivInfoRecordViewConditionBo condition) {
                    return AjaxResult.fail("查询统计信息失败");
                }
            };
        }
    }
}