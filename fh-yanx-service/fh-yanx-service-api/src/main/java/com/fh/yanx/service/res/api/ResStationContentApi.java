package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResStationContentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 工作站具体内容
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationContentApi {

    /**
     * 查询工作站具体内容分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/page/list")
    public AjaxResult<PageInfo<ResStationContentVo>> getResStationContentPageListByCondition(@RequestBody ResStationContentConditionBo condition);

    /**
     * 查询工作站具体内容列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/list")
    public AjaxResult<List<ResStationContentVo>> getResStationContentListByCondition(@RequestBody ResStationContentConditionBo condition);


    /**
     * 新增工作站具体内容
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/add")
    public AjaxResult addResStationContent(@Validated @RequestBody ResStationContentBo resStationContentBo);

    /**
     * 修改工作站具体内容
     * @param resStationContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/update")
    public AjaxResult updateResStationContent(@Validated @RequestBody ResStationContentBo resStationContentBo);

    /**
     * 查询工作站具体内容详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/content/detail")
    public AjaxResult<ResStationContentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除工作站具体内容
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/content/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
