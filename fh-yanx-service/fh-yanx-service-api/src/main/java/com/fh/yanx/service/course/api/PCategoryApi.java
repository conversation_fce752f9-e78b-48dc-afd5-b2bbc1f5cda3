package com.fh.yanx.service.course.api;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.fh.yanx.service.course.entity.bo.PCategoryBo;
import com.fh.yanx.service.course.entity.bo.PCategoryConditionBo;
import com.fh.yanx.service.course.entity.vo.PCategoryVo;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.entity.PageLimitBo;

/**
 * 资源类别
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCategoryApi {

    /**
     * 查询资源类别分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/category/page/list")
    public AjaxResult<PageInfo<PCategoryVo>>
        getPCategoryPageListByCondition(@RequestBody PCategoryConditionBo condition);

    /**
     * 查询资源类别列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/category/list")
    public AjaxResult<List<PCategoryVo>> getPCategoryListByCondition(@RequestBody PCategoryConditionBo condition);

    /**
     * 新增资源类别
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/category/add")
    public AjaxResult addPCategory(@Validated @RequestBody PCategoryBo pCategoryBo);

    /**
     * 修改资源类别
     * 
     * @param pCategoryBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/category/update")
    public AjaxResult updatePCategory(@Validated @RequestBody PCategoryBo pCategoryBo);

    /**
     * 查询资源类别详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/category/detail")
    public AjaxResult<PCategoryVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除资源类别
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/category/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 类别分页展示
     *
     * @param pageLimitBo
     * @return
     */
    @PostMapping("/p/category/getPageTreeList")
    AjaxResult getPageTreeList(@RequestBody PCategoryConditionBo condition);

    /**
     * 通过组织id 获得其分类
     *
     * @param pageLimitBo
     * @return
     */
    @PostMapping("/p/category/getCategoryTreeList")
    AjaxResult<List<PCategoryVo>> getCategoryTreeList(@RequestBody PCategoryConditionBo condition);

    /**
     * 通过组织id 获得其分类
     *
     * @param pageLimitBo
     * @return
     */
    @PostMapping("/p/category/getCategoryList")
    AjaxResult<List<PCategoryVo>> getCategoryList(@RequestBody PCategoryConditionBo condition);

    /**
     * 添加分类
     *
     * @param categoryVo
     * @return
     */
    @PostMapping("/p/category/addCategory")
    AjaxResult addCategory(@RequestBody PCategoryBo categoryBo);

    /**
     * 修改分类
     *
     * @param categoryVo
     * @return
     */
    @PostMapping("/p/category/updateCategory")
    AjaxResult updateCategory(@RequestBody PCategoryBo categoryBo);

    /**
     * 删除分类
     *
     * @param ids
     * @return
     */
    @PostMapping("/p/category/deleteCategory")
    AjaxResult<Boolean> deleteCategory(@RequestBody List<Long> ids);

    /**
     * 启用/禁用
     *
     * @param id
     * @return
     */
    @GetMapping("/p/category/editStatus")
    AjaxResult editStatus(@RequestParam("state") Integer state, @RequestParam("id") Long id);

    /**
     * 通过类别ids获取类别对象
     *
     * @param ids
     * @return
     */
    @GetMapping("/p/category/getListByIds")
    AjaxResult<List<PCategoryVo>> getListByIds(@RequestParam("ids") String ids);

    /**
     * 通过类别ids获取类别对象
     *
     * @param ids
     * @return
     */
    @GetMapping("/p/category/getListByIdsList")
    AjaxResult<List<PCategoryVo>> getListByIdsList(@RequestParam("ids") List<Long> ids);

    /**
     * 通过ids获得父级id
     */
    @GetMapping("/p/category/getParentIdsByIds")
    AjaxResult<List<Map<String, Long>>> getCategoryMap(@RequestParam("ids") List<Long> ids);

    /**
     * 通过name获取类别对象
     */
    @GetMapping("/p/category/getOneByName")
    AjaxResult<PCategoryVo> getOneByName1(@RequestParam("name") String name, @RequestParam("parentId") Integer parentId,
        @RequestParam("organizationId") Long organizationId);

    /**
     * 迁移过来的方法
     * 
     * @param targetIds
     * @return
     */
    @GetMapping("/p/category/getMapByIds")
    AjaxResult<Map<Long, String>> getMapByIds(@RequestParam("targetIds") List<Long> targetIds);

    /**
     * 迁移过来的方法
     * 
     * @param organizationId
     * @return
     */
    @GetMapping("/p/category/getAcademicSectionList")
    AjaxResult<List<PCategoryVo>> getAcademicSectionList(@RequestParam("organizationId") Long organizationId);
}
