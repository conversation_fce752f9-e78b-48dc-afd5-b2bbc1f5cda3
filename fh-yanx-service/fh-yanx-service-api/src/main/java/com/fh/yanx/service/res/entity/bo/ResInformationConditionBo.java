package com.fh.yanx.service.res.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 资讯
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
@Data
public class ResInformationConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long informationId;

	/**
	 * 资讯名称
	 */
	@ApiModelProperty("资讯名称")
	private String informationName;

	/**
	 * 资讯内容
	 */
	@ApiModelProperty("资讯内容")
	private String informationDesc;

	/**
	 * 资讯顺序
	 */
	@ApiModelProperty("资讯顺序")
	private Long informationIndex;

	/**
	 * 资讯图片文件原始名称-带后缀
	 */
	@ApiModelProperty("资讯图片文件原始名称-带后缀")
	private String imgFileNameOri;

	/**
	 * 资讯图片文件名称-不带后缀
	 */
	@ApiModelProperty("资讯图片文件名称-不带后缀")
	private String imgFileName;

	/**
	 * 资讯图片文件地址
	 */
	@ApiModelProperty("资讯图片文件地址")
	private String imgFileUrl;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
