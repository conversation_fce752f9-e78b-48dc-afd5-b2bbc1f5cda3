package com.fh.yanx.service.order.api;

import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
public interface ActivOrderApi {

    /**
     * 查询订单表分页列表
     * 
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @PostMapping("/activ/order/page/list")
    public AjaxResult<PageInfo<ActivOrderVo>>
        getActivOrderPageListByCondition(@RequestBody ActivOrderConditionBo condition);

    /**
     * 查询订单表列表
     * 
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @PostMapping("/activ/order/list")
    public AjaxResult<List<ActivOrderVo>> getActivOrderListByCondition(@RequestBody ActivOrderConditionBo condition);

    /**
     * 新增订单表
     * 
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @PostMapping("/activ/order/add")
    public AjaxResult addActivOrder(@Validated @RequestBody ActivOrderBo activOrderBo);

    /**
     * 修改订单表
     * 
     * @param activOrderBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @PostMapping("/activ/order/update")
    public AjaxResult updateActivOrder(@Validated @RequestBody ActivOrderBo activOrderBo);

    /**
     * 查询订单表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @GetMapping("/activ/order/detail")
    public AjaxResult<ActivOrderVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询订单表详情-根据订单号查询订单
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @GetMapping("/activ/order/detail-by-order-number")
    public AjaxResult<ActivOrderVo>
        getDetailByOrderNumber(@NotBlank(message = "订单号不能为空") @RequestParam("orderNumber") String orderNumber);

    /**
     * 删除订单表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 09:29:54
     */
    @GetMapping("/activ/order/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 修改订单金额
     *
     * @param orderBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/2 16:12
     **/
    @PostMapping("/activ/order/change-order-amount")
    public AjaxResult changeOrderAmount(@RequestBody ActivOrderBo orderBo);

    /**
     * 完成订单且同步微信流水号
     *
     * @param orderBo the order bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -09-22 15:02:43
     */
    @PostMapping("/activ/order/complete-order")
    public AjaxResult completeOrder(@RequestBody ActivOrderBo orderBo);

    /**
     * 获取用户订单列表
     *
     * @param orderBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/25 15:12
     **/
    @PostMapping("/activ/order/user/order-list")
    public AjaxResult getUserOrderList(@RequestBody ActivOrderBo orderBo);
}
