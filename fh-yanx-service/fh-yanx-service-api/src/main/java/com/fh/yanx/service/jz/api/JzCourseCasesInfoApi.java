package com.fh.yanx.service.jz.api;


import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesInfoBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesInfoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 金中-校本课程案例详细信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesInfoApi {

    /**
     * 查询金中-校本课程案例详细信息分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/info/page/list")
    public AjaxResult<PageInfo<JzCourseCasesInfoVo>> getJzCourseCasesInfoPageListByCondition(@RequestBody JzCourseCasesInfoConditionBo condition);

    /**
     * 查询金中-校本课程案例详细信息列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/info/list")
    public AjaxResult<List<JzCourseCasesInfoVo>> getJzCourseCasesInfoListByCondition(@RequestBody JzCourseCasesInfoConditionBo condition);


    /**
     * 新增金中-校本课程案例详细信息
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/info/add")
    public AjaxResult addJzCourseCasesInfo(@Validated @RequestBody JzCourseCasesInfoBo jzCourseCasesInfoBo);

    /**
     * 修改金中-校本课程案例详细信息
     * @param jzCourseCasesInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/info/update")
    public AjaxResult updateJzCourseCasesInfo(@Validated @RequestBody JzCourseCasesInfoBo jzCourseCasesInfoBo);

    /**
     * 查询金中-校本课程案例详细信息详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/info/detail")
    public AjaxResult<JzCourseCasesInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除金中-校本课程案例详细信息
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
