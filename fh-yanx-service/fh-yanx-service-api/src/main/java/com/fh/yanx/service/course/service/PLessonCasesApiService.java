package com.fh.yanx.service.course.service;


import com.fh.yanx.service.course.api.PLessonCasesApi;
import com.fh.yanx.service.course.entity.bo.PLessonCasesBo;
import com.fh.yanx.service.course.entity.bo.PLessonCasesConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课例表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@FeignClient(contextId = "pLessonCasesApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PLessonCasesApiService.PLessonCasesApiFallbackFactory.class)
@Component
public interface PLessonCasesApiService extends PLessonCasesApi {

    @Component
    class PLessonCasesApiFallbackFactory implements FallbackFactory<PLessonCasesApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PLessonCasesApiFallbackFactory.class);
        @Override
        public PLessonCasesApiService create(Throwable cause) {
            PLessonCasesApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PLessonCasesApiService() {
                public AjaxResult getPLessonCasesPageListByCondition(PLessonCasesConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPLessonCasesListByCondition(PLessonCasesConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPLessonCases(PLessonCasesBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePLessonCases(PLessonCasesBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}