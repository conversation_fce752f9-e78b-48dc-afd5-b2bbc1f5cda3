package com.fh.yanx.service.activ.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 新时代文化校园活动内容表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
@Data
public class ActivInfoRecordVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long recordId;

    /**
     * FK新时代文化校园活动信息表的id
     */
    @ApiModelProperty("FK新时代文化校园活动信息表的id")
    private Long activId;

    /**
     * 内容类型：1标题，2图文
     */
    @ApiModelProperty("内容类型：1标题，2图文")
    private Integer recordType;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    private Long recordSort;

    /**
     * 介绍标题
     */
    @ApiModelProperty("介绍标题")
    private String recordTitle;

    /**
     * 内容图文
     */
    @ApiModelProperty("内容图文")
    private String recordContent;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ActivInfoRecordVo returnOwn() {
        return this;
    }

    /**
     * 总观看数量
     */
    public Long totalViewCount;
    /**
     * 按照日期的观看数量
     */
    public List<ActivInfoRecordViewVo> activInfoRecordViewVoList;
}
