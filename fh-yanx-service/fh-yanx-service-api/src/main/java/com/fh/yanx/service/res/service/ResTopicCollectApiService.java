package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicCollectApi;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectBo;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 用户收藏的课题
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resTopicCollectApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicCollectApiService.ResTopicCollectApiFallbackFactory.class)
@Component
public interface ResTopicCollectApiService extends ResTopicCollectApi {

    @Component
    class ResTopicCollectApiFallbackFactory implements FallbackFactory<ResTopicCollectApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicCollectApiFallbackFactory.class);

        @Override
        public ResTopicCollectApiService create(Throwable cause) {
            ResTopicCollectApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicCollectApiService() {
                public AjaxResult getResTopicCollectPageListByCondition(ResTopicCollectConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicCollectListByCondition(ResTopicCollectConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicCollect(ResTopicCollectBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicCollect(ResTopicCollectBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult deleteByTopicAndUser(Long topicId, String userOid) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}