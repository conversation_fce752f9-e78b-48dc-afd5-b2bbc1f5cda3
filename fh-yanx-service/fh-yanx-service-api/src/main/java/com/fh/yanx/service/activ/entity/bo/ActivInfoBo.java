package com.fh.yanx.service.activ.entity.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 新时代文化校园活动信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
@Data
public class ActivInfoBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long activId;

	/**
	 * 学校id,0表示是全网的活动
	 */
	@ApiModelProperty("学校id,0表示是全网的活动")
	private Long organizationId;

	/**
	 * 活动名称
	 */
	@ApiModelProperty("活动名称")
	private String activName;

	/**
	 * 活动描述
	 */
	@ApiModelProperty("活动描述")
	private String activDescription;

	/**
	 * 活动封面文件oid
	 */
	@ApiModelProperty("活动封面文件oid")
	private String activCoverId;

	/**
	 * 活动封面文件url
	 */
	@ApiModelProperty("活动封面文件url")
	private String activCoverUrl;

	/**
	 * 活动开始时间：yyyy-MM-dd
	 */
	@ApiModelProperty("活动开始时间：yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date activStartTime;

	/**
	 * 活动结束时间：yyyy-MM-dd
	 */
	@ApiModelProperty("活动结束时间：yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date activEndTime;

	/**
	 * 活动形式：1线上，2线下，3线上线下
	 */
	@ApiModelProperty("活动形式：1线上，2线下，3线上线下")
	private Integer activFormType;

	/**
	 * 活动上下架：1上架，2下架
	 */
	@ApiModelProperty("活动上下架：1上架，2下架")
	private Integer activType;

	/**
	 * 活动介绍
	 */
	@ApiModelProperty("活动介绍")
	private List<ActivInfoIntroduceBo> activInfoIntroduces;

	/**
	 * 活动内容
	 */
	@ApiModelProperty("活动回看")
	private List<ActivInfoRecordBo> activInfoRecords;

	/**
	 * 活动日程
	 */
	@ApiModelProperty("活动日程")
	private List<ActivInfoScheduleBo> schedules;

	/**
	 * 观看权限
	 */
	@ApiModelProperty("观看权限")
	private List<ActivInfoViewPermissionBo> viewPermissions;

	/**
	 * 活动内容 1-活动日程（直播） 2-内容回看
	 */
	@ApiModelProperty("活动内容 1-活动日程（直播） 2-内容回看")
	private Integer viewType;
	

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 活动展示类型：1默认无特殊含义，2在教师发展页展示
	 */
	@ApiModelProperty("活动展示类型：1默认无特殊含义，2在教师发展页展示")
	private Integer showType;

	/**
	 * 展示顺序
	 */
	@ApiModelProperty("展示顺序")
	private Long showIndex;

	/**
	 * 活动业务类型：1普通活动，2征订活动
	 */
	@ApiModelProperty("活动业务类型：1普通活动，2征订活动")
	private Integer activBizType;
	/**
	 * 征订书籍名称
	 */
	@ApiModelProperty("征订书籍名称")
	private String subBookName;
	/**
	 * 征订最大可征订数量
	 */
	@ApiModelProperty("征订最大可征订数量")
	private Long subMaxNumber;


	/**
	 * 报名名称
	 */
	@ApiModelProperty("报名名称")
	private String bmName;

	/**
	 * 报名说明
	 */
	@ApiModelProperty("报名说明")
	private String bmContent;

	/**
	 * 是否缴费 1-无需缴费 2-需要缴费
	 */
	@ApiModelProperty("是否缴费 1-无需缴费 2-需要缴费")
	private Integer chargeType;

	/**
	 * 缴费金额 元/人
	 */
	@ApiModelProperty("缴费金额 元/人")
	private BigDecimal chargeAmount;

	/**
	 * web报名入口展示开始时间
	 */
	@ApiModelProperty("web报名入口展示开始时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date webBmStartTime;

	/**
	 * web报名入口展示结束时间
	 */
	@ApiModelProperty("web报名入口展示结束时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date webBmEndTime;

	/**
	 * 报名开始时间
	 */
	@ApiModelProperty("报名开始时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date bmStartTime;

	/**
	 * 报名结束时间
	 */
	@ApiModelProperty("报名结束时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date bmEndTime;

	/**
	 * 线下活动地址
	 */
	@ApiModelProperty("线下活动地址")
	private String activAddress;
}
