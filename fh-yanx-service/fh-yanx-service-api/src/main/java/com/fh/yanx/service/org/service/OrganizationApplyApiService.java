package com.fh.yanx.service.org.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.org.api.OrganizationApplyApi;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 组织申请表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
@FeignClient(contextId = "organizationApplyApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = OrganizationApplyApiService.OrganizationApplyApiFallbackFactory.class)
@Component
public interface OrganizationApplyApiService extends OrganizationApplyApi {

    @Component
    class OrganizationApplyApiFallbackFactory implements FallbackFactory<OrganizationApplyApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(OrganizationApplyApiFallbackFactory.class);
        @Override
        public OrganizationApplyApiService create(Throwable cause) {
            OrganizationApplyApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new OrganizationApplyApiService() {
                public AjaxResult getOrganizationApplyPageListByCondition(OrganizationApplyConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getOrganizationApplyListByCondition(OrganizationApplyConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addOrganizationApply(OrganizationApplyBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateOrganizationApply(OrganizationApplyBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}