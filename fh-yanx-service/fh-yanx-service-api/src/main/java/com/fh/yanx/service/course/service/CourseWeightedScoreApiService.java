package com.fh.yanx.service.course.service;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseWeightedScoreApi;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreBo;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;


/**
 * 课程加权分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
@FeignClient(contextId = "courseWeightedScoreApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseWeightedScoreApiService.CourseWeightedScoreApiFallbackFactory.class)
@Component
public interface CourseWeightedScoreApiService extends CourseWeightedScoreApi {

    @Component
    class CourseWeightedScoreApiFallbackFactory implements FallbackFactory<CourseWeightedScoreApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseWeightedScoreApiFallbackFactory.class);
        @Override
        public CourseWeightedScoreApiService create(Throwable cause) {
            CourseWeightedScoreApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new CourseWeightedScoreApiService() {
                public AjaxResult getCourseWeightedScorePageListByCondition(CourseWeightedScoreConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseWeightedScoreListByCondition(CourseWeightedScoreConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseWeightedScore(CourseWeightedScoreBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseWeightedScore(CourseWeightedScoreBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}