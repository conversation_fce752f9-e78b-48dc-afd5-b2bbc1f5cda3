package com.fh.yanx.service.activ.service;


import com.fh.yanx.service.activ.api.ActivInfoIntroduceApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 新时代文化校园活动介绍表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
@FeignClient(contextId = "activInfoIntroduceApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ActivInfoIntroduceApiService.ActivInfoIntroduceApiFallbackFactory.class)
@Component
public interface ActivInfoIntroduceApiService extends ActivInfoIntroduceApi {

    @Component
    class ActivInfoIntroduceApiFallbackFactory implements FallbackFactory<ActivInfoIntroduceApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ActivInfoIntroduceApiFallbackFactory.class);
        @Override
        public ActivInfoIntroduceApiService create(Throwable cause) {
            ActivInfoIntroduceApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new ActivInfoIntroduceApiService() {
                public AjaxResult getActivInfoIntroducePageListByCondition(ActivInfoIntroduceConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getActivInfoIntroduceListByCondition(ActivInfoIntroduceConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addActivInfoIntroduce(ActivInfoIntroduceBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateActivInfoIntroduce(ActivInfoIntroduceBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}