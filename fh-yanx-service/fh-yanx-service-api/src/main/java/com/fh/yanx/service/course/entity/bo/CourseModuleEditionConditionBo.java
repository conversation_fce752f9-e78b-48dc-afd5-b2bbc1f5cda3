package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 课程模块版本记录表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:17
 */
@Data
public class CourseModuleEditionConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long courseModuleEditionId;

	/**
	 * 案例版本id
	 */
	@ApiModelProperty("案例版本id")
	private Long casesEditionId;

	/**
	 * 课程模块类型：1课程资源，2成果样例
	 */
	@ApiModelProperty("课程模块类型：1课程资源，2成果样例")
	private Integer courseModuleType;

	/**
	 * 标题
	 */
	@ApiModelProperty("标题")
	private String courseModuleTitle;

	/**
	 * 副标题
	 */
	@ApiModelProperty("副标题")
	private String courseModuleSubTitle;

	/**
	 * 课程资源排列方式：1列表展示，2块状展示
	 */
	@ApiModelProperty("课程资源排列方式：1列表展示，2块状展示")
	private Integer courseModuleResStyle;

	/**
	 * 课程模块排序，默认1
	 */
	@ApiModelProperty("课程模块排序，默认1")
	private Long courseModuleIndex;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 前端生成提交存储，用于前端和布局里面的信息做映射
	 */
	@ApiModelProperty("前端生成提交存储，用于前端和布局里面的信息做映射")
	private String uuid;

}
