package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseModuleAttachmentEditionApi;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程资源或成果样例附件表-模块附件版本表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:25
 */
@FeignClient(contextId = "courseModuleAttachmentEditionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseModuleAttachmentEditionApiService.CourseModuleAttachmentEditionApiFallbackFactory.class)
@Component
public interface CourseModuleAttachmentEditionApiService extends CourseModuleAttachmentEditionApi {

    @Component
    class CourseModuleAttachmentEditionApiFallbackFactory implements FallbackFactory<CourseModuleAttachmentEditionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseModuleAttachmentEditionApiFallbackFactory.class);
        @Override
        public CourseModuleAttachmentEditionApiService create(Throwable cause) {
            CourseModuleAttachmentEditionApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseModuleAttachmentEditionApiService() {
                public AjaxResult getCourseModuleAttachmentEditionPageListByCondition(CourseModuleAttachmentEditionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseModuleAttachmentEditionListByCondition(CourseModuleAttachmentEditionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseModuleAttachmentEdition(CourseModuleAttachmentEditionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseModuleAttachmentEdition(CourseModuleAttachmentEditionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}