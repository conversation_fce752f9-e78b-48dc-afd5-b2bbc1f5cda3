package com.fh.yanx.service.user.entity.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-12-09  14:36
 */
@Data
public class ExpertImportExcelBo extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = {"新时代专家导入模板", "*姓名"}, index = 0)
    private String realName;

    @ExcelProperty(value = {"新时代专家导入模板", "*手机号"}, index = 1)
    private String phone;

    @ExcelProperty(value = {"新时代专家导入模板", "*角色"}, index = 2)
    private String role;

    @ExcelProperty(value = {"新时代专家导入模板", "*职务"}, index = 3)
    private String position;

}
