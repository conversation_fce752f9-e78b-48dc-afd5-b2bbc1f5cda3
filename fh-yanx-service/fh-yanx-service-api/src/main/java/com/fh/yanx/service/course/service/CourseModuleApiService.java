package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseModuleApi;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程模块
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@FeignClient(contextId = "courseModuleApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseModuleApiService.CourseModuleApiFallbackFactory.class)
@Component
public interface CourseModuleApiService extends CourseModuleApi {

    @Component
    class CourseModuleApiFallbackFactory implements FallbackFactory<CourseModuleApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseModuleApiFallbackFactory.class);
        @Override
        public CourseModuleApiService create(Throwable cause) {
            CourseModuleApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new CourseModuleApiService() {
                public AjaxResult getCourseModulePageListByCondition(CourseModuleConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseModuleListByCondition(CourseModuleConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseModule(CourseModuleBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseModule(CourseModuleBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}