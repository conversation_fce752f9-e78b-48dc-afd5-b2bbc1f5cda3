package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseVerifyLogApi;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程审核流水表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@FeignClient(contextId = "courseVerifyLogApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseVerifyLogApiService.CourseVerifyLogApiFallbackFactory.class)
@Component
public interface CourseVerifyLogApiService extends CourseVerifyLogApi {

    @Component
    class CourseVerifyLogApiFallbackFactory implements FallbackFactory<CourseVerifyLogApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseVerifyLogApiFallbackFactory.class);
        @Override
        public CourseVerifyLogApiService create(Throwable cause) {
            CourseVerifyLogApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new CourseVerifyLogApiService() {
                public AjaxResult getCourseVerifyLogPageListByCondition(CourseVerifyLogConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseVerifyLogListByCondition(CourseVerifyLogConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseVerifyLog(CourseVerifyLogBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseVerifyLog(CourseVerifyLogBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult deleteAndSave(CourseVerifyLogBo courseVerifyLogBo) {
                    return AjaxResult.fail("删除新增失败");
                }
            };
        }
    }
}