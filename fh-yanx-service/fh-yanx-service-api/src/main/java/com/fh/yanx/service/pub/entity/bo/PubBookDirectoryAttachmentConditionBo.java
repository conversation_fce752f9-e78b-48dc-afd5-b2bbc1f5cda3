package com.fh.yanx.service.pub.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 融合出版书目录附件
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
public class PubBookDirectoryAttachmentConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long bookDirectoryAttachmentId;

	/**
	 * 冗余，FK出版书id，pub_book表逐渐
	 */
	@ApiModelProperty("冗余，FK出版书id，pub_book表逐渐")
	private Long bookId;

	/**
	 * 融合出版书目录id,pub_book_directory的主键
	 */
	@ApiModelProperty("融合出版书目录id,pub_book_directory的主键")
	private Long bookDirectoryId;

	/**
	 * 目录对应的文件名称
	 */
	@ApiModelProperty("目录对应的文件名称")
	private String bookDirectoryFileName;

	/**
	 * 目录对应的文件url地址
	 */
	@ApiModelProperty("目录对应的文件url地址")
	private String bookDirectoryFileUrl;

	/**
	 * 目录对应的文件的oid
	 */
	@ApiModelProperty("目录对应的文件的oid")
	private String bookDirectoryFileOid;

	/**
	 * 目录对应的文件的顺序
	 */
	@ApiModelProperty("目录对应的文件的顺序")
	private String bookDirectoryFileIndex;



	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
