package com.fh.yanx.service.course.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 课程模块
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Data
public class CourseModuleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long courseModuleId;

    /**
     * 案例id
     */
    @ApiModelProperty("案例id")
    private Long casesId;

    /**
     * 课程模块类型：1课程资源，2成果样例
     */
    @ApiModelProperty("课程模块类型：1课程资源，2成果样例")
    private Integer courseModuleType;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String courseModuleTitle;

    /**
     * 副标题
     */
    @ApiModelProperty("副标题")
    private String courseModuleSubTitle;

    /**
     * 课程资源排列方式：1列表展示，2块状展示
     */
    @ApiModelProperty("课程资源排列方式：1列表展示，2块状展示")
    private Integer courseModuleResStyle;

    /**
     * 课程模块排序，默认1
     */
    @ApiModelProperty("课程模块排序，默认1")
    private Long courseModuleIndex;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public CourseModuleVo returnOwn() {
        return this;
    }

    /**
     * 内容管理-内容模块的附件列表
     */
    private List<CourseModuleAttachmentVo> courseModuleAttachmentVos;

    /**
     * 前端生成提交存储，用于前端和布局里面的信息做映射
     */
    @ApiModelProperty("前端生成提交存储，用于前端和布局里面的信息做映射")
    private String uuid;
}
