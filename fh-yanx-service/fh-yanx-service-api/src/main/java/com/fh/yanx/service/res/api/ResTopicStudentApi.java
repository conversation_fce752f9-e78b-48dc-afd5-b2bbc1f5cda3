package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;

import com.light.core.entity.AjaxResult;
import com.light.user.student.entity.bo.StudentConditionBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课题组成员表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicStudentApi {

    /**
     * 查询课题组成员表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/student/page/list")
    public AjaxResult<PageInfo<ResTopicStudentVo>> getResTopicStudentPageListByCondition(@RequestBody ResTopicStudentConditionBo condition);

    /**
     * 查询课题组成员表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/student/list")
    public AjaxResult<List<ResTopicStudentVo>> getResTopicStudentListByCondition(@RequestBody ResTopicStudentConditionBo condition);


    /**
     * 新增课题组成员表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/student/add")
    public AjaxResult addResTopicStudent(@Validated @RequestBody ResTopicStudentBo resTopicStudentBo);

    /**
     *
     * @param resTopicStudentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/student/update")
    public AjaxResult updateResTopicStudent(@Validated @RequestBody ResTopicStudentBo resTopicStudentBo);

    /**
     * 查询课题组成员表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/student/detail")
    public AjaxResult<ResTopicStudentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课题组成员表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/student/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    @PostMapping("res/topic/student/student-list")
    AjaxResult getStudentListByCondition(@RequestBody StudentConditionBo condition);
}
