package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResStationImgApi;
import com.fh.yanx.service.res.entity.bo.ResStationImgBo;
import com.fh.yanx.service.res.entity.bo.ResStationImgConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 工作站介绍图片表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resStationImgApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResStationImgApiService.ResStationImgApiFallbackFactory.class)
@Component
public interface ResStationImgApiService extends ResStationImgApi {

    @Component
    class ResStationImgApiFallbackFactory implements FallbackFactory<ResStationImgApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResStationImgApiFallbackFactory.class);

        @Override
        public ResStationImgApiService create(Throwable cause) {
            ResStationImgApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResStationImgApiService() {
                public AjaxResult getResStationImgPageListByCondition(ResStationImgConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResStationImgListByCondition(ResStationImgConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResStationImg(ResStationImgBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResStationImg(ResStationImgBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}