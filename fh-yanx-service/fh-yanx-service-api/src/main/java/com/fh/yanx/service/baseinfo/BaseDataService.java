package com.fh.yanx.service.baseinfo;

import com.fh.yanx.service.enums.OrganizationAuthType;
import com.fh.yanx.service.org.entity.bo.OrganizationBoExt;
import com.fh.yanx.service.org.entity.bo.OrganizationExtConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationVoExt;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.service.org.entity.vo.TeacherVoExt;
import com.google.common.collect.Lists;
import com.fh.yanx.service.org.entity.vo.UserRoleVoExt;
import com.fh.yanx.service.org.entity.vo.UserVoExt;
import com.light.base.attachment.entity.bo.MultipartFormData;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.category.entity.vo.CategoryVo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.core.entity.AjaxResult;
import com.light.user.account.entity.bo.AccountBo;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.admin.entity.bo.AdminBo;
import com.light.user.admin.entity.bo.AdminConditionBo;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.organization.entity.bo.*;
import com.light.user.organization.entity.vo.OrganizationSettingVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;

import com.google.common.collect.Maps;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.bo.UserTransferBo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 基础数据调用service(解耦基础信息库)
 */
public interface BaseDataService {

    /**
     * 一个spi实现的名称，通常可以用于业务方传入一个name的配置来选择启用哪一个实现
     *
     * @return
     */
    default String name() {
        return "name";
    }

    /**
     * 测试方法。仅用于输出
     *
     * @return
     */
    default String echo() {
        return "echo";
    }

    /**
     * 根据userOid查询教师信息
     *
     * @param userOid the user oid
     * @return teacher vo by user oid
     */
    default TeacherVo getTeacherVoByUserOid(String userOid) {
        return null;
    }

    /**
     * 根据用户oid集合查询用户真实姓名
     *
     * @param userOids 用户oid集合
     * @return real name by user oids
     */
    default Map<String, String> getRealNameByUserOids(List<String> userOids) {
        return Maps.newHashMap();
    }

    /**
     * 根据用户oid 获取用户的班级id及年级
     *
     * @param userOid
     * @return com.light.user.student.entity.vo.StudentVo
     */
    default StudentVo getStudentVoByUserOid(String userOid) {
        return null;
    }

    /**
     * 获取当前用户Oid
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/9/27 11:42
     */
    default String getCurrentUserOid() {
        return null;
    }

    /**
     * 获取当前登录用户
     *
     * @return com.light.user.account.entity.vo.LoginAccountVo
     * <AUTHOR>
     * @date 2023/8/3 11:30
     **/
    default LoginAccountVo getCurrentUser() {
        return null;
    }

    /**
     * 获取研学科目列表
     *
     * @return java.util.List<com.light.base.category.entity.vo.CategoryVo>
     * <AUTHOR>
     * @date 2023/2/2 14:34
     */
    default List<CategoryVo> getSubjectList() {
        return Lists.newArrayList();
    }

    /**
     * 根据条件获取老师基础信息
     *
     * @param teacherConditionBo
     * @return
     */
    default List<TeacherVo> getTeacherVoList(TeacherConditionBo teacherConditionBo) {
        return Lists.newArrayList();
    }

    /**
     * 根据条件获取学生信息
     *
     * @param studentConditionBo
     * @return
     */
    default List<StudentVo> getStudentVoList(StudentConditionBo studentConditionBo) {
        return Lists.newArrayList();
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictTypes
     * @return
     */
    default List<DictionaryDataVo> listValueByTypes(List<String> dictTypes) {
        return Lists.newArrayList();
    }

    /**
     * 根据用户oids 获取学生列表
     *
     * @param userOids
     * @return com.light.user.student.entity.vo.StudentVo
     */
    default List<StudentVo> getStudentVolistByUserOids(List<String> userOids) {
        return null;
    }

    /**
     * 获取组织机构列表
     *
     * @param orgIds
     * @return
     */
    default List<OrganizationVo> getOrganizationVoList(List<Long> orgIds) {
        return null;
    }

    /**
     * 上传文件
     *
     * @param multipartFormData file
     * @return com.light.base.attachment.entity.vo.AttachmentVo
     */
    default AttachmentVo upload(MultipartFormData multipartFormData) {
        return null;
    }

    /**
     * 上传文件
     *
     * @param file
     * @return com.light.base.attachment.entity.vo.AttachmentVo
     */
    default AttachmentVo upload(MultipartFile file) {
        return null;
    }

    /**
     * 获取组织列表
     *
     * @param conditionBo
     * @return java.util.List<com.light.user.organization.entity.vo.OrganizationVo>
     * <AUTHOR>
     * @date 2023/7/17 11:28
     **/
    default Map<String, Object> getOrganizationListByCondition(OrganizationExtConditionBo conditionBo) {
        return null;
    }

    /**
     * 根据组织名称查询组织信息
     *
     * @param conditionBo
     * @return com.light.user.organization.entity.vo.OrganizationVo
     * <AUTHOR>
     * @date 2023/7/17 14:59
     **/
    default AjaxResult getOrganizationByCondition(OrganizationConditionBo conditionBo) {
        return null;
    }

    /**
     * 新增组织机构
     *
     * @param organizationBo
     * @return
     */
    default Long addOrganization(OrganizationBo organizationBo) {
        return null;
    }

    /**
     * 编辑组织信息
     *
     * @param organizationBo
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/12 10:30
     **/
    default boolean updateOrganization(OrganizationBoExt organizationBo) {
        return false;
    }

    /**
     * 获取组织详情
     *
     * @param organizationId
     * @return com.light.user.organization.entity.vo.OrganizationVo
     * <AUTHOR>
     * @date 2023/7/19 17:59
     **/
    default OrganizationVoExt getOrganizationDetail(Long organizationId) {
        return null;
    }

    /**
     * 新增组织平台设置信息
     *
     * @param organizationSettingBo
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/18 17:22
     **/
    default boolean addOrUpdateOrganizationSetting(OrganizationSettingBo organizationSettingBo) {
        return false;
    }

    /**
     * 查询组织平台设置信息列表
     *
     * @param conditionBo
     * @return java.util.List<com.light.user.organization.entity.vo.OrganizationSettingVo>
     * <AUTHOR>
     * @date 2023/7/18 17:31
     **/
    default OrganizationSettingVo getOrganizationSetting(OrganizationSettingConditionBo conditionBo) {
        return null;
    }

    /**
     * 修改密码
     *
     * @param oldPassword
     * @param password
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/11 18:09
     **/
    default AjaxResult changePassword(String oldPassword, String password) {
        return AjaxResult.fail();
    }

    /**
     * 更新账号信息
     *
     * @param accountBo
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/24 11:19
     **/
    default AjaxResult updateAccount(AccountBo accountBo) {
        return AjaxResult.fail();
    }

    /**
     * 更新用户信息
     *
     * @param userBo
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/19 9:59
     **/
    default boolean updateUser(UserBo userBo) {
        return false;
    }

    /**
     * 重置密码
     *
     * @param accountId
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/20 10:47
     **/
    default boolean resetPassword(Long accountId) {
        return false;
    }

    /**
     * 新增教师
     *
     * @param teacherBo
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/20 14:36
     **/
    default AjaxResult addTeacher(TeacherBo teacherBo) {
        return AjaxResult.fail();
    }

    /**
     * 新增教师-批量
     *
     * @param teacherBos
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/20 14:36
     **/
    default AjaxResult addBathTeacher(List<TeacherBo> teacherBos) {
        return AjaxResult.fail();
    }

    /**
     * 编辑教师
     *
     * @param teacherBo
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/20 14:42
     **/
    default AjaxResult updateTeacher(TeacherBo teacherBo) {
        return AjaxResult.fail();
    }

    /**
     * 获取教师详情
     *
     * @param userOid
     * @return com.light.user.teacher.entity.vo.TeacherVo
     * <AUTHOR>
     * @date 2023/7/20 15:05
     **/
    default TeacherVoExt getTeacherDetail(String userOid) {
        return null;
    }

    /**
     * 删除教师
     *
     * @param teacherId
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/20 15:14
     **/
    default boolean delTeacher(Long teacherId) {
        return false;
    }

    /**
     * 获取教师列表
     *
     * @param conditionBo
     * @return java.util.List<com.fh.yanx.service.org.entity.vo.TeacherVoExt>
     * <AUTHOR>
     * @date 2023/7/21 10:42
     **/
    default Map<String, Object> getTeacherList(TeacherConditionBo conditionBo) {
        return null;
    }

    /**
     * 根据userOid重置密码
     *
     * @param userOid
     * @return boolean
     * <AUTHOR>
     * @date 2023/7/21 13:42
     **/
    default boolean resetPasswordByUserOid(String userOid) {
        return false;
    }

    /**
     * 传递所有手机号，返回未注册的手机号码
     *
     * @param allMobiles the all mobiles
     * @return list list
     * <AUTHOR>
     * @date 2023 -08-09 14:35:05
     */
    default List<String> listUnregisterMobiles(List<String> allMobiles) {
        return null;
    }

    /**
     * 根据id查询基础信息area信息
     * 
     * @return
     */
    default String getAreaNameFromCache(Long id) {
        return null;
    }

    /**
     * 获取组织认证状态
     *
     * @param organizationId
     * @return
     */
    default Integer getOrgAuthType(Long organizationId) {return OrganizationAuthType.UN_AUTH.getCode();}

    /**
     * 新增用户转出到其他学校操作
     */
    default AjaxResult addUserTransfer(UserTransferBo userTransferBo){
        return null;
    }

    /**
     * 后台账号管理（查询人员列表）
     */
    default List<AdminVo> getAdminListByCondition(AdminConditionBo conditionBo){
        return Lists.newArrayList();
    }
    /**
     * 后台账号管理（查询人员列表-分页）
     */
    default AjaxResult getAdminPageListByCondition(AdminConditionBo conditionBo){
        return AjaxResult.fail();
    }

    /**
     * 后台添加人员
     */
    default AjaxResult addAdmin(AdminBo adminBo){
        return AjaxResult.fail();
    }

    /**
     * 后台修改人员
     */
    default AjaxResult updateAdmin(AdminBo adminBo){
        return AjaxResult.fail();
    }

    /**
     * 后台删除人员
     */
    default AjaxResult delAdmin(Long adminId){
        return AjaxResult.fail();
    }

    /**
     * 后台查询人员
     */
    default AjaxResult<AdminVo> getAdminDetail(Long adminId){
        return AjaxResult.fail();
    }

    /**
     * 启用禁用后台人员
     *
     * @param adminId the admin id
     * @param status 0-没有被锁定，1被锁定
     * @return the ajax result
     * <AUTHOR>
     * @date 2024 -07-16 14:14:24
     */
    default AjaxResult enableAdmin(Long adminId, Integer status){
        return AjaxResult.fail();
    }

    /**
     * 后台重置密码
     *
     * @param adminId the admin id
     * @param initPwd 默认密码，如果为空则使用和前台一样的默认密码
     * @return the ajax result
     * <AUTHOR>
     * @date 2024 -07-16 14:14:44
     */
    default AjaxResult resetAdminPassword(Long adminId, String initPwd){
        return AjaxResult.fail();
    }

    /**
     * 新增用户并创建账号
     *
     * @param userBo
     * @return
     */
    default AjaxResult addUserAndGeneratorAccount(UserBo userBo) {
        return AjaxResult.fail();
    }

    /**
     * 新时代编辑专家、专家助理
     *
     * @param userBo
     * @return
     */
    default AjaxResult updateExpertUser(UserBo userBo) {
        return AjaxResult.fail();
    }

    /**
     * 查询用户列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 16:08
     **/
    default AjaxResult getUserList(UserConditionBoExt conditionBo) {
        return AjaxResult.fail();
    }

    /**
     * 获取用户详情
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/29 14:18
     **/
    default AjaxResult getUserDetail(String userOid) {
        return AjaxResult.fail();
    }

    /**
     * 删除用户失败
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/2 14:38
     **/
    default AjaxResult delUser(String userOid) {
        return AjaxResult.fail();
    }

    /**
     * 删除组织
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/3 10:13
     **/
    default AjaxResult delOrganization(Long organizationId) {
        return AjaxResult.fail();
    }

    /**
     * 修改手机号
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/6 10:52
     **/
    default AjaxResult updatePhoneByUserOid(UserBo userBo) {
        return AjaxResult.fail();
    }

    /**
     * 批量新增教师
     *
     * @param teacherBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/9 13:46
     **/
    default AjaxResult addBatchTeacher(List<TeacherBo> teacherBos) {
        return AjaxResult.fail();
    }

    /**
     * 批量创建用户并生成账号
     *
     * @param userBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/9 14:32
     **/
    default AjaxResult addUserAndGeneratorAccountBatch(List<UserBo> userBos) {
        return AjaxResult.fail();
    }


    /**
     *  根据用户 OID 集合 获取用户列表
     * @param userOidList the user oid list 用户 OID 集合
     * @return {@link AjaxResult }<{@link List }<{@link UserVoExt }>>
     */
    default List<UserVoExt> queryByUserOidList(List<String> userOidList) {
        return null;
    }

    /**
     * 根据用户 OID 集合 获取用户map
     *
     * @param userOidList the user oid list 用户 OID 集合
     * @return {@link Map }<{@link String }, {@link UserVoExt }>
     */
    default Map<String, UserVoExt> queryMapByUserOidList(List<String> userOidList) {
        return null;
    }

    /**
     *  根据用户 OID 获取角色 map ： key-> user oid  Value: user role vo list
     * @param userOidList the user oid list 用户 OID 集合
     * @return {@link Map }<{@link String }, {@link List }<{@link UserRoleVoExt }>>
     */
    default Map<String, List<UserRoleVoExt>> queryUserRoleMapByUserOidList(List<String> userOidList){
        return null;
    }
}
