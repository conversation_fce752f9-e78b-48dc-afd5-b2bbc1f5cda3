package com.fh.yanx.service.course.service;


import com.fh.yanx.service.course.api.PCourseCasesInfoApi;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本课程案例详细信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@FeignClient(contextId = "pCourseCasesInfoApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PCourseCasesInfoApiService.PCourseCasesInfoApiFallbackFactory.class)
@Component
public interface PCourseCasesInfoApiService extends PCourseCasesInfoApi {

    @Component
    class PCourseCasesInfoApiFallbackFactory implements FallbackFactory<PCourseCasesInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PCourseCasesInfoApiFallbackFactory.class);
        @Override
        public PCourseCasesInfoApiService create(Throwable cause) {
            PCourseCasesInfoApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PCourseCasesInfoApiService() {
                public AjaxResult getPCourseCasesInfoPageListByCondition(PCourseCasesInfoConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPCourseCasesInfoListByCondition(PCourseCasesInfoConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPCourseCasesInfo(PCourseCasesInfoBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePCourseCasesInfo(PCourseCasesInfoBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}