package com.fh.yanx.service.pub.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 融合出版书目录附件
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
public class PubBookDirectoryAttachmentVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long bookDirectoryAttachmentId;

    /**
     * 冗余，FK出版书id，pub_book表逐渐
     */
    @ApiModelProperty("冗余，FK出版书id，pub_book表逐渐")
    private Long bookId;

    /**
     * 融合出版书目录id,pub_book_directory的主键
     */
    @ApiModelProperty("融合出版书目录id,pub_book_directory的主键")
    private Long bookDirectoryId;

    /**
     * 目录对应的文件名称
     */
    @ApiModelProperty("目录对应的文件名称")
    private String bookDirectoryFileName;

    /**
     * 目录对应的文件url地址
     */
    @ApiModelProperty("目录对应的文件url地址")
    private String bookDirectoryFileUrl;

    /**
     * 目录对应的文件的oid
     */
    @ApiModelProperty("目录对应的文件的oid")
    private String bookDirectoryFileOid;

    /**
     * 目录对应的文件的顺序
     */
    @ApiModelProperty("目录对应的文件的顺序")
    private String bookDirectoryFileIndex;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public PubBookDirectoryAttachmentVo returnOwn() {
        return this;
    }

}
