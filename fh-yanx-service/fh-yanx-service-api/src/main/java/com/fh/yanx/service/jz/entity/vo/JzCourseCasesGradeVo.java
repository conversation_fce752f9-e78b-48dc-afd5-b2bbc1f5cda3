package com.fh.yanx.service.jz.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 金中-校本课程案例年级
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@Data
public class JzCourseCasesGradeVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 案例ID
     */
    @ApiModelProperty("案例ID")
    private Long casesId;

    /**
     * 年级名称
     */
    @ApiModelProperty("年级名称")
    private String name;

    /**
     * 年级
     */
    @ApiModelProperty("年级")
    private Long grade;

    /**
     * 0-未删除 1-已删除
     */
    @ApiModelProperty("0-未删除 1-已删除")
    private Integer isDelete;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date createDate;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date updateDate;

    /*
     * 方便steam流存入自身
     * */
    public JzCourseCasesGradeVo returnOwn() {
        return this;
    }

}
