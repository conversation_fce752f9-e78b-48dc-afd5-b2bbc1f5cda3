package com.fh.yanx.service.activ.service;


import com.fh.yanx.service.activ.api.ActivInfoViewPermissionApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 活动内容观看权限表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
@FeignClient(contextId = "activInfoViewPermissionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ActivInfoViewPermissionApiService.ActivInfoViewPermissionApiFallbackFactory.class)
@Component
public interface ActivInfoViewPermissionApiService extends ActivInfoViewPermissionApi {

    @Component
    class ActivInfoViewPermissionApiFallbackFactory implements FallbackFactory<ActivInfoViewPermissionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ActivInfoViewPermissionApiFallbackFactory.class);
        @Override
        public ActivInfoViewPermissionApiService create(Throwable cause) {
            ActivInfoViewPermissionApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new ActivInfoViewPermissionApiService() {
                public AjaxResult getActivInfoViewPermissionPageListByCondition(ActivInfoViewPermissionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getActivInfoViewPermissionListByCondition(ActivInfoViewPermissionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addActivInfoViewPermission(ActivInfoViewPermissionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateActivInfoViewPermission(ActivInfoViewPermissionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}