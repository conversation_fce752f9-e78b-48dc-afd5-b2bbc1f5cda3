package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicTeacherApi;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课题指导老师表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@FeignClient(contextId = "resTopicTeacherApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicTeacherApiService.ResTopicTeacherApiFallbackFactory.class)
@Component
public interface ResTopicTeacherApiService extends ResTopicTeacherApi {

    @Component
    class ResTopicTeacherApiFallbackFactory implements FallbackFactory<ResTopicTeacherApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicTeacherApiFallbackFactory.class);

        @Override
        public ResTopicTeacherApiService create(Throwable cause) {
            ResTopicTeacherApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicTeacherApiService() {
                public AjaxResult getResTopicTeacherPageListByCondition(ResTopicTeacherConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicTeacherListByCondition(ResTopicTeacherConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicTeacher(ResTopicTeacherBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicTeacher(ResTopicTeacherBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getTeacherListByCondition(TeacherConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }
            };
        }
    }
}