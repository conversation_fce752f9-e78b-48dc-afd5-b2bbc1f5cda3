package com.fh.yanx.service.enums;

/**
 * 缴费类型
 *
 * <AUTHOR>
 * @date 2023-08-01 14:29
 */
public enum ChargeType {
    FREE(1, "免费"),
    CHARGE(2, "收费");

    private Integer code;
    private String value;

    ChargeType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
