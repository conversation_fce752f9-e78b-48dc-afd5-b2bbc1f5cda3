package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicApi;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课题表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resTopicApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicApiService.ResTopicApiFallbackFactory.class)
@Component
public interface ResTopicApiService extends ResTopicApi {

    @Component
    class ResTopicApiFallbackFactory implements FallbackFactory<ResTopicApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicApiFallbackFactory.class);

        @Override
        public ResTopicApiService create(Throwable cause) {
            ResTopicApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicApiService() {
                public AjaxResult getResTopicPageListByCondition(ResTopicConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicListByCondition(ResTopicConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopic(ResTopicBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicStatus(ResTopicBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }
                public AjaxResult getCurrentUserTopicDetail(Long organizationId,String currentUserOid) {
                    return AjaxResult.fail("查询当前用户课题详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getSubjectList() {
                    return AjaxResult.fail("获取学科列表失败");
                }
            };
        }
    }
}