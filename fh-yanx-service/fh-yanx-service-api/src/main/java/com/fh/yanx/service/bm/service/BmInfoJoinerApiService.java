package com.fh.yanx.service.bm.service;

import com.fh.yanx.service.bm.api.BmInfoJoinerApi;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 新时代文化校园报名活动申请表-参与人信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@FeignClient(contextId = "bmInfoJoinerApiService", value = ConstServiceName.FH_YANX_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = BmInfoJoinerApiService.BmInfoJoinerApiFallbackFactory.class)
@Component
public interface BmInfoJoinerApiService extends BmInfoJoinerApi {

    @Component
    class BmInfoJoinerApiFallbackFactory implements FallbackFactory<BmInfoJoinerApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(BmInfoJoinerApiFallbackFactory.class);

        @Override
        public BmInfoJoinerApiService create(Throwable cause) {
            BmInfoJoinerApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new BmInfoJoinerApiService() {
                public AjaxResult getBmInfoJoinerPageListByCondition(BmInfoJoinerConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getBmInfoJoinerListByCondition(BmInfoJoinerConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addBmInfoJoiner(BmInfoJoinerBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateBmInfoJoiner(BmInfoJoinerBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult updateByMobile(BmInfoJoinerBo bmInfoJoinerBo) {
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult getByMobile(BmInfoJoinerBo bmInfoJoinerBo) {
                    return AjaxResult.fail("查询信息失败");
                }

            };
        }
    }
}