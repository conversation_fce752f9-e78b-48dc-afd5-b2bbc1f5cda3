package com.fh.yanx.service.order.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.order.api.ActivOrderApi;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 订单表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
@FeignClient(contextId = "activOrderApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ActivOrderApiService.ActivOrderApiFallbackFactory.class)
@Component
public interface ActivOrderApiService extends ActivOrderApi {

    @Component
    class ActivOrderApiFallbackFactory implements FallbackFactory<ActivOrderApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ActivOrderApiFallbackFactory.class);
        @Override
        public ActivOrderApiService create(Throwable cause) {
            ActivOrderApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new ActivOrderApiService() {
                public AjaxResult getActivOrderPageListByCondition(ActivOrderConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getActivOrderListByCondition(ActivOrderConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addActivOrder(ActivOrderBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateActivOrder(ActivOrderBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                @Override
                public AjaxResult<ActivOrderVo> getDetailByOrderNumber(String orderNumber) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult changeOrderAmount(ActivOrderBo orderBo) {
                    return AjaxResult.fail("修改订单金额失败");
                }

                @Override
                public AjaxResult completeOrder(ActivOrderBo orderBo) {
                    return AjaxResult.fail("修改订单状态失败");
                }

                @Override
                public AjaxResult getUserOrderList(ActivOrderBo orderBo) {
                    return AjaxResult.fail("获取用户订单列表");
                }
            };
        }
    }
}