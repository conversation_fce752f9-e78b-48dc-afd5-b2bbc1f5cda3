package com.fh.yanx.service.pub.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.pub.api.PubBookDirectoryAttachmentApi;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 融合出版书目录附件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@FeignClient(contextId = "pubBookDirectoryAttachmentApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PubBookDirectoryAttachmentApiService.PubBookDirectoryAttachmentApiFallbackFactory.class)
@Component
public interface PubBookDirectoryAttachmentApiService extends PubBookDirectoryAttachmentApi {

    @Component
    class PubBookDirectoryAttachmentApiFallbackFactory implements FallbackFactory<PubBookDirectoryAttachmentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PubBookDirectoryAttachmentApiFallbackFactory.class);
        @Override
        public PubBookDirectoryAttachmentApiService create(Throwable cause) {
            PubBookDirectoryAttachmentApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PubBookDirectoryAttachmentApiService() {
                public AjaxResult getPubBookDirectoryAttachmentPageListByCondition(PubBookDirectoryAttachmentConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPubBookDirectoryAttachmentListByCondition(PubBookDirectoryAttachmentConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPubBookDirectoryAttachment(PubBookDirectoryAttachmentBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePubBookDirectoryAttachment(PubBookDirectoryAttachmentBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}