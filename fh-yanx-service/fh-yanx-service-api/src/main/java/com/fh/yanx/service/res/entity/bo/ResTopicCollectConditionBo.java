package com.fh.yanx.service.res.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 用户收藏的课题
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicCollectConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long collectId;

	/**
	 * 课题id
	 */
	@ApiModelProperty("课题id")
	private Long topicId;

	/**
	 * 学生用户oid
	 */
	@ApiModelProperty("学生用户oid")
	private String userOid;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private Long organizationId;

	/**
	 * 当前登陆用户oid
	 */
	@ApiModelProperty("当前登陆用户oid")
	private String currentUserOid;
}
