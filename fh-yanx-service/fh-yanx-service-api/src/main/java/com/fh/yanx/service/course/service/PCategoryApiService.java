package com.fh.yanx.service.course.service;


import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.PCategoryApi;
import com.fh.yanx.service.course.entity.bo.PCategoryBo;
import com.fh.yanx.service.course.entity.bo.PCategoryConditionBo;
import com.fh.yanx.service.course.entity.vo.PCategoryVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;


/**
 * 资源类别
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@FeignClient(contextId = "pCategoryApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PCategoryApiService.PCategoryApiFallbackFactory.class)
@Component
public interface PCategoryApiService extends PCategoryApi {

    @Component
    class PCategoryApiFallbackFactory implements FallbackFactory<PCategoryApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PCategoryApiFallbackFactory.class);
        @Override
        public PCategoryApiService create(Throwable cause) {
            PCategoryApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PCategoryApiService() {
                public AjaxResult getPCategoryPageListByCondition(PCategoryConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPCategoryListByCondition(PCategoryConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPCategory(PCategoryBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePCategory(PCategoryBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult getPageTreeList(PCategoryConditionBo condition) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<List<PCategoryVo>> getCategoryTreeList(PCategoryConditionBo condition) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<List<PCategoryVo>> getCategoryList(PCategoryConditionBo condition) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult addCategory(PCategoryBo categoryVo) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult updateCategory(PCategoryBo categoryVo) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<Boolean> deleteCategory(List<Long> ids) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult editStatus(Integer state, Long id) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<List<PCategoryVo>> getListByIds(String ids) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<List<PCategoryVo>> getListByIdsList(List<Long> ids) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<List<Map<String, Long>>> getCategoryMap(List<Long> ids) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<PCategoryVo> getOneByName1(String name, Integer parentId, Long organizationId) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<Map<Long, String>> getMapByIds(List<Long> targetIds) {
                    return AjaxResult.fail("调用失败");
                }

                @Override
                public AjaxResult<List<PCategoryVo>> getAcademicSectionList(Long organizationId) {
                    return AjaxResult.fail("调用失败");
                }
            };
        }
    }
}