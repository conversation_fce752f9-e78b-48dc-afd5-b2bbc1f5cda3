package com.fh.yanx.service.jz.api;


import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesGradeVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 金中-校本课程案例年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesGradeApi {

    /**
     * 查询金中-校本课程案例年级分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/grade/page/list")
    public AjaxResult<PageInfo<JzCourseCasesGradeVo>> getJzCourseCasesGradePageListByCondition(@RequestBody JzCourseCasesGradeConditionBo condition);

    /**
     * 查询金中-校本课程案例年级列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/grade/list")
    public AjaxResult<List<JzCourseCasesGradeVo>> getJzCourseCasesGradeListByCondition(@RequestBody JzCourseCasesGradeConditionBo condition);


    /**
     * 新增金中-校本课程案例年级
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/grade/add")
    public AjaxResult addJzCourseCasesGrade(@Validated @RequestBody JzCourseCasesGradeBo jzCourseCasesGradeBo);

    /**
     * 修改金中-校本课程案例年级
     * @param jzCourseCasesGradeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/grade/update")
    public AjaxResult updateJzCourseCasesGrade(@Validated @RequestBody JzCourseCasesGradeBo jzCourseCasesGradeBo);

    /**
     * 查询金中-校本课程案例年级详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/grade/detail")
    public AjaxResult<JzCourseCasesGradeVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除金中-校本课程案例年级
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/grade/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
