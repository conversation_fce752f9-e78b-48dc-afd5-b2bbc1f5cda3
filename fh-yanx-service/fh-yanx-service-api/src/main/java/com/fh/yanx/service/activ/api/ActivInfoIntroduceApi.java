package com.fh.yanx.service.activ.api;


import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoIntroduceBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoIntroduceVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 新时代文化校园活动介绍表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
public interface ActivInfoIntroduceApi {

    /**
     * 查询新时代文化校园活动介绍表分页列表
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
    @PostMapping("/activ/info/introduce/page/list")
    public AjaxResult<PageInfo<ActivInfoIntroduceVo>> getActivInfoIntroducePageListByCondition(@RequestBody ActivInfoIntroduceConditionBo condition);

    /**
     * 查询新时代文化校园活动介绍表列表
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
    @PostMapping("/activ/info/introduce/list")
    public AjaxResult<List<ActivInfoIntroduceVo>> getActivInfoIntroduceListByCondition(@RequestBody ActivInfoIntroduceConditionBo condition);


    /**
     * 新增新时代文化校园活动介绍表
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
    @PostMapping("/activ/info/introduce/add")
    public AjaxResult addActivInfoIntroduce(@Validated @RequestBody ActivInfoIntroduceBo activInfoIntroduceBo);

    /**
     * 修改新时代文化校园活动介绍表
     * @param activInfoIntroduceBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
    @PostMapping("/activ/info/introduce/update")
    public AjaxResult updateActivInfoIntroduce(@Validated @RequestBody ActivInfoIntroduceBo activInfoIntroduceBo);

    /**
     * 查询新时代文化校园活动介绍表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
    @GetMapping("/activ/info/introduce/detail")
    public AjaxResult<ActivInfoIntroduceVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除新时代文化校园活动介绍表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:43
     */
    @GetMapping("/activ/info/introduce/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
