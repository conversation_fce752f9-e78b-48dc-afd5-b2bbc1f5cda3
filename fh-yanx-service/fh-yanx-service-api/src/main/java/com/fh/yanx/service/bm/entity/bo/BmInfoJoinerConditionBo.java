package com.fh.yanx.service.bm.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 新时代文化校园报名活动申请表-参与人信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Data
public class BmInfoJoinerConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 申请记录主表id
	 */
	@ApiModelProperty("申请记录主表id")
	private Long infoId;

	/**
	 * 姓名
	 */
	@ApiModelProperty("姓名")
	private String joinerName;

	/**
	 * 性别：1男，2女
	 */
	@ApiModelProperty("性别：1男，2女")
	private Integer joinerGender;

	/**
	 * 民族
	 */
	@ApiModelProperty("民族")
	private String joinerNation;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String joinerDuties;

	/**
	 * 教学学科/工作
	 */
	@ApiModelProperty("教学学科/工作")
	private String joinerTeach;

	/**
	 * 邮箱
	 */
	@ApiModelProperty("邮箱")
	private String joinerMail;

	/**
	 * 手机号码
	 */
	@ApiModelProperty("手机号码")
	private String joinerMobile;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
