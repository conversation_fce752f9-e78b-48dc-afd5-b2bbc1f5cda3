package com.fh.yanx.service.org.api;


import com.fh.yanx.service.org.entity.bo.OrganizationApplyConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyBo;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 组织申请表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
public interface OrganizationApplyApi {

    /**
     * 查询组织申请表分页列表
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @PostMapping("/organization/apply/page/list")
    public AjaxResult<PageInfo<OrganizationApplyVo>> getOrganizationApplyPageListByCondition(@RequestBody OrganizationApplyConditionBo condition);

    /**
     * 查询组织申请表列表
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @PostMapping("/organization/apply/list")
    public AjaxResult<List<OrganizationApplyVo>> getOrganizationApplyListByCondition(@RequestBody OrganizationApplyConditionBo condition);


    /**
     * 新增组织申请表
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @PostMapping("/organization/apply/add")
    public AjaxResult addOrganizationApply(@Validated @RequestBody OrganizationApplyBo organizationApplyBo);

    /**
     * 修改组织申请表
     * @param organizationApplyBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @PostMapping("/organization/apply/update")
    public AjaxResult updateOrganizationApply(@Validated @RequestBody OrganizationApplyBo organizationApplyBo);

    /**
     * 查询组织申请表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @GetMapping("/organization/apply/detail")
    public AjaxResult<OrganizationApplyVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除组织申请表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:50:13
     */
    @GetMapping("/organization/apply/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
