package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 课程版本表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
@Data
public class CourseCasesEditionConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 案例id
	 */
	@ApiModelProperty("案例id")
	private Long casesId;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long userId;

	/**
	 * 学校名称
	 */
	@ApiModelProperty("学校名称")
	private String schoolName;

	/**
	 * 课程名称
	 */
	@ApiModelProperty("课程名称")
	private String courseName;

	/**
	 * 省份ID
	 */
	@ApiModelProperty("省份ID")
	private Long provinceId;

	/**
	 * 市区ID
	 */
	@ApiModelProperty("市区ID")
	private Long cityId;

	/**
	 * 区县ID
	 */
	@ApiModelProperty("区县ID")
	private Long countyId;

	/**
	 * 学段
	 */
	@ApiModelProperty("学段")
	private Long phase;

	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private Long year;

	/**
	 * 图片
	 */
	@ApiModelProperty("图片")
	private String picture;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Integer status;

	/**
	 * 实施者
	 */
	@ApiModelProperty("实施者")
	private String operation;

	/**
	 * 联系方式
	 */
	@ApiModelProperty("联系方式")
	private String phone;

	/**
	 * 简介
	 */
	@ApiModelProperty("简介")
	private String introduction;

	/**
	 * 浏览量
	 */
	@ApiModelProperty("浏览量")
	private Long views;

	/**
	 * 是否授权 0 否 1是
	 */
	@ApiModelProperty("是否授权 0 否 1是")
	private Integer isAuth;

	/**
	 * 展示首页的类型，1：大首页，2：校本课程平台首页
	 */
	@ApiModelProperty("展示首页的类型，1：大首页，2：校本课程平台首页")
	private Integer homeType;

	/**
	 * 0-未删除 1-已删除
	 */
	@ApiModelProperty("0-未删除 1-已删除")
	private Integer isDelete;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createDate;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateDate;

	/**
	 * 文件地址
	 */
	@ApiModelProperty("文件地址")
	private String filePath;

	/**
	 * word文档地址
	 */
	@ApiModelProperty("word文档地址")
	private String wordPath;

	/**
	 * 是否上传文件 ： 0 否 1是
	 */
	@ApiModelProperty("是否上传文件 ： 0 否 1是")
	private Integer isUpload;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long customerId;

	/**
	 * 0课程案例，1典型案例，2精品课程
	 */
	@ApiModelProperty("0课程案例，1典型案例，2精品课程")
	private Integer isExcellent;

	/**
	 * 新时代用户的oid
	 */
	@ApiModelProperty("新时代用户的oid")
	private String userOid;

	/**
	 * 来源类型：1资源网导入，2新时代新增
	 */
	@ApiModelProperty("来源类型：1资源网导入，2新时代新增")
	private Integer sourceType;

	/**
	 * 精品课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存，5初审通过，6初审不通过
	 */
	@ApiModelProperty("精品课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存，5初审通过，6初审不通过")
	private Integer bestVerifyType;

	/**
	 * 模板类型：默认空，1特色课程模板，2整体课程模板
	 */
	@ApiModelProperty("模板类型：默认空，1特色课程模板，2整体课程模板")
	private Integer templateType;

	/**
	 * 精品课程的模板内容布局-tab1的布局，存储布局json信息
	 */
	@ApiModelProperty("精品课程的模板内容布局-tab1的布局，存储布局json信息")
	private String templateLayoutOne;

	/**
	 * 精品课程的模板内容布局-tab2的布局，存储布局json信息
	 */
	@ApiModelProperty("精品课程的模板内容布局-tab2的布局，存储布局json信息")
	private String templateLayoutTwo;

	/**
	 * 精品课程的模板内容布局-tab3的布局，存储布局json信息
	 */
	@ApiModelProperty("精品课程的模板内容布局-tab3的布局，存储布局json信息")
	private String templateLayoutThree;

	/**
	 * 课程上下架：1上架，2下架
	 */
	@ApiModelProperty("课程上下架：1上架，2下架")
	private Integer holdType;

	/**
	 * 普通课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存，5初审通过，6初审不通过
	 */
	@ApiModelProperty("普通课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存，5初审通过，6初审不通过")
	private Integer normalVerifyType;

	/**
	 * 审核进度类型，默认空：1初审，2终审
	 */
	@ApiModelProperty("审核进度类型，默认空：1初审，2终审")
	private Integer verifyProcessType;

	/**
	 * 课程类型标签，曾经是这个类型的都会被记录，类型同is_excellent，多个使用英文逗号分割
	 */
	@ApiModelProperty("课程类型标签，曾经是这个类型的都会被记录，类型同is_excellent，多个使用英文逗号分割")
	private String isExcellentLabel;

	/**
	 * 观看权限（数字,分隔） 2-未认证学校用户 3-已认证学校用户 4-案例持有者
	 */
	@ApiModelProperty("观看权限（数字,分隔） 2-未认证学校用户 3-已认证学校用户 4-案例持有者")
	private String viewPermission;

	@ApiModelProperty("课程案例ids")
	private List<Long> casesIds;

}
