package com.fh.yanx.service.activ.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 新时代文化校园活动介绍表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
@Data
public class ActivInfoIntroduceConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long introduceId;

	/**
	 * FK新时代文化校园活动信息表的id
	 */
	@ApiModelProperty("FK新时代文化校园活动信息表的id")
	private Long activId;

	/**
	 * 介绍类型：1标题，2图片，3文字
	 */
	@ApiModelProperty("介绍类型：1标题，2图片，3文字")
	private Integer introduceType;

	/**
	 * 顺序
	 */
	@ApiModelProperty("顺序")
	private Long introduceSort;

	/**
	 * 活动标题
	 */
	@ApiModelProperty("活动标题")
	private String introduceTitle;

	/**
	 * 活动介绍图片文件oid
	 */
	@ApiModelProperty("活动介绍图片文件oid")
	private String introduceImageId;

	/**
	 * 活动介绍图片文件地址
	 */
	@ApiModelProperty("活动介绍图片文件地址")
	private String introduceImageUrl;

	/**
	 * 活动介绍内容
	 */
	@ApiModelProperty("活动介绍内容")
	private String introduceContent;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
