package com.fh.yanx.service.bm.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动报名校验
 *
 * <AUTHOR>
 * @date 2023-08-04 10:18
 */
@Data
public class BmInfoCheckVo {
    /**
     * 校验状态 1-成功 2-作为联系人已提交 3-作为参会人被提交
     */
    @ApiModelProperty("校验状态 1-成功 2-作为联系人已提交 3-作为参会人被提交")
    private Integer type;

    /**
     * 报名联系人
     */
    @ApiModelProperty("报名联系人")
    private String contactName;

    /**
     * 报名联系人手机号
     */
    @ApiModelProperty("报名联系人手机号")
    private String contactMobile;

    /**
     * 重复参与人
     */
    @ApiModelProperty("重复参与人")
    private List<BmInfoJoinerVo> joiners;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    /**
     * 参会人数量
     */
    @ApiModelProperty("参会人数量")
    private Integer joinerCount;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 报名id
     */
    @ApiModelProperty("报名id")
    private Long infoId;
}
