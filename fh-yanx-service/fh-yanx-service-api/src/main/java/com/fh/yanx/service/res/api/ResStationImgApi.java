package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResStationImgConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationImgBo;
import com.fh.yanx.service.res.entity.vo.ResStationImgVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 工作站介绍图片表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationImgApi {

    /**
     * 查询工作站介绍图片表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/img/page/list")
    public AjaxResult<PageInfo<ResStationImgVo>> getResStationImgPageListByCondition(@RequestBody ResStationImgConditionBo condition);

    /**
     * 查询工作站介绍图片表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/img/list")
    public AjaxResult<List<ResStationImgVo>> getResStationImgListByCondition(@RequestBody ResStationImgConditionBo condition);


    /**
     * 新增工作站介绍图片表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/img/add")
    public AjaxResult addResStationImg(@Validated @RequestBody ResStationImgBo resStationImgBo);

    /**
     * 修改工作站介绍图片表
     * @param resStationImgBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/img/update")
    public AjaxResult updateResStationImg(@Validated @RequestBody ResStationImgBo resStationImgBo);

    /**
     * 查询工作站介绍图片表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/img/detail")
    public AjaxResult<ResStationImgVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除工作站介绍图片表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/img/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
