package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResStationContentApi;
import com.fh.yanx.service.res.entity.bo.ResStationContentBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 工作站具体内容
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resStationContentApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResStationContentApiService.ResStationContentApiFallbackFactory.class)
@Component
public interface ResStationContentApiService extends ResStationContentApi {

    @Component
    class ResStationContentApiFallbackFactory implements FallbackFactory<ResStationContentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResStationContentApiFallbackFactory.class);

        @Override
        public ResStationContentApiService create(Throwable cause) {
            ResStationContentApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResStationContentApiService() {
                public AjaxResult getResStationContentPageListByCondition(ResStationContentConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResStationContentListByCondition(ResStationContentConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResStationContent(ResStationContentBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResStationContent(ResStationContentBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}