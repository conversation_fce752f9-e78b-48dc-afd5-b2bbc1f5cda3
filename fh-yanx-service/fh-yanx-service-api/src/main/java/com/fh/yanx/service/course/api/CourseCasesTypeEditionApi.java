package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesTypeEditionVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 校本课程案例类型版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
public interface CourseCasesTypeEditionApi {

    /**
     * 查询校本课程案例类型版本记录分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
    @PostMapping("/course/cases/type/edition/page/list")
    public AjaxResult<PageInfo<CourseCasesTypeEditionVo>> getCourseCasesTypeEditionPageListByCondition(@RequestBody CourseCasesTypeEditionConditionBo condition);

    /**
     * 查询校本课程案例类型版本记录列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
    @PostMapping("/course/cases/type/edition/list")
    public AjaxResult<List<CourseCasesTypeEditionVo>> getCourseCasesTypeEditionListByCondition(@RequestBody CourseCasesTypeEditionConditionBo condition);


    /**
     * 新增校本课程案例类型版本记录
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
    @PostMapping("/course/cases/type/edition/add")
    public AjaxResult addCourseCasesTypeEdition(@Validated @RequestBody CourseCasesTypeEditionBo courseCasesTypeEditionBo);

    /**
     * 修改校本课程案例类型版本记录
     * @param courseCasesTypeEditionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
    @PostMapping("/course/cases/type/edition/update")
    public AjaxResult updateCourseCasesTypeEdition(@Validated @RequestBody CourseCasesTypeEditionBo courseCasesTypeEditionBo);

    /**
     * 查询校本课程案例类型版本记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
    @GetMapping("/course/cases/type/edition/detail")
    public AjaxResult<CourseCasesTypeEditionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除校本课程案例类型版本记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:20
     */
    @GetMapping("/course/cases/type/edition/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
