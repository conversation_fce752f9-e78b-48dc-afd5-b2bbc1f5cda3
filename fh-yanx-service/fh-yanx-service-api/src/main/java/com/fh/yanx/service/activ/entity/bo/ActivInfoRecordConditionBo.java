package com.fh.yanx.service.activ.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 新时代文化校园活动内容表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
@Data
public class ActivInfoRecordConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long recordId;

	/**
	 * FK新时代文化校园活动信息表的id
	 */
	@ApiModelProperty("FK新时代文化校园活动信息表的id")
	private Long activId;

	/**
	 * 内容类型：1标题，2图文
	 */
	@ApiModelProperty("内容类型：1标题，2图文")
	private Integer recordType;

	/**
	 * 顺序
	 */
	@ApiModelProperty("顺序")
	private Long recordSort;

	/**
	 * 介绍标题
	 */
	@ApiModelProperty("介绍标题")
	private String recordTitle;

	/**
	 * 内容图文
	 */
	@ApiModelProperty("内容图文")
	private String recordContent;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
