package com.fh.yanx.service.res.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 课题审批意见表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicApprovalVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long topicApprovalId;

    /**
     * 课题id
     */
    @ApiModelProperty("课题id")
    private Long topicId;

    /**
     * 审批老师的userOid
     */
    @ApiModelProperty("审批老师的userOid")
    private String verifyTeacher;

    /**
     * 课题名称建议
     */
    @ApiModelProperty("课题名称建议")
    private String topicNameSug;

    /**
     * 课题类别建议
     */
    @ApiModelProperty("课题类别建议")
    private String topicTypeSug;

    /**
     * 关联学科建议
     */
    @ApiModelProperty("关联学科建议")
    private String relSubjectSug;

    /**
     * 课题界定建议
     */
    @ApiModelProperty("课题界定建议")
    private String topicDescSug;

    /**
     * 课题背景建议
     */
    @ApiModelProperty("课题背景建议")
    private String topicBackSug;

    /**
     * 课题目的建议
     */
    @ApiModelProperty("课题目的建议")
    private String topicGoalSug;

    /**
     * 研究内容建议
     */
    @ApiModelProperty("研究内容建议")
    private String topicContentSug;

    /**
     * 研究方法建议
     */
    @ApiModelProperty("研究方法建议")
    private String topicMethodSug;

    /**
     * 研究条件建议
     */
    @ApiModelProperty("研究条件建议")
    private String topicConditionSug;

    /**
     * 研究计划建议
     */
    @ApiModelProperty("研究计划建议")
    private String topicPlanSug;

    /**
     * 研究预期成果建议
     */
    @ApiModelProperty("研究预期成果建议")
    private String topicExpectedSug;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 审批老师的名称
     */
    @ApiModelProperty("审批老师的userOid")
    private String verifyTeacherName;

}
