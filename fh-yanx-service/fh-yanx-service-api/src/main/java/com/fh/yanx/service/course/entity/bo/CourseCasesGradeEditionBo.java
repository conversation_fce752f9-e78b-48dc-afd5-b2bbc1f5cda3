package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 校本课程案例年级版本记录
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:00
 */
@Data
public class CourseCasesGradeEditionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 案例版本表ID
	 */
	@ApiModelProperty("案例版本表ID")
	private Long casesEditionId;

	/**
	 * 年级名称
	 */
	@ApiModelProperty("年级名称")
	private String name;

	/**
	 * 年级
	 */
	@ApiModelProperty("年级")
	private Long grade;

	/**
	 * 0-未删除 1-已删除
	 */
	@ApiModelProperty("0-未删除 1-已删除")
	private Integer isDelete;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createDate;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateDate;

}
