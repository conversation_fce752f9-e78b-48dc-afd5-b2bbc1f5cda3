package com.fh.yanx.service.enums;

/**
 * <AUTHOR>
 * @date 2023-08-02 10:23
 */
public enum TeacherType {
    TEACH_USER(1L, "教学人员"),
    NOT_TEACHE_USER(2L, "非教学人员");

    private Long code;
    private String value;

    TeacherType(Long code, String value) {
        this.code = code;
        this.value = value;
    }

    public Long getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
