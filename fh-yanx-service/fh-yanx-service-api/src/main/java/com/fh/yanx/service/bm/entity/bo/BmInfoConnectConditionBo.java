package com.fh.yanx.service.bm.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 报名活动沟通确认表（本表只有新增记录）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
@Data
public class BmInfoConnectConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 活动报名记录id
	 */
	@ApiModelProperty("活动报名记录id")
	private Long infoId;

	/**
	 * 确认状态：1待确认(本表不会出现该状态的数据)，2沟通中，3已确认，4已拒绝
	 */
	@ApiModelProperty("确认状态：1待确认(本表不会出现该状态的数据)，2沟通中，3已确认，4已拒绝")
	private Integer submitType;

	/**
	 * 沟通内容
	 */
	@ApiModelProperty("沟通内容")
	private String connectContent;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
