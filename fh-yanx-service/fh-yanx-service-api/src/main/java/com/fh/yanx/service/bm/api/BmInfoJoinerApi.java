package com.fh.yanx.service.bm.api;

import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerConditionBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoJoinerVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新时代文化校园报名活动申请表-参与人信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
public interface BmInfoJoinerApi {

    /**
     * 查询新时代文化校园报名活动申请表-参与人信息表分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/joiner/page/list")
    public AjaxResult<PageInfo<BmInfoJoinerVo>>
        getBmInfoJoinerPageListByCondition(@RequestBody BmInfoJoinerConditionBo condition);

    /**
     * 查询新时代文化校园报名活动申请表-参与人信息表列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/joiner/list")
    public AjaxResult<List<BmInfoJoinerVo>>
        getBmInfoJoinerListByCondition(@RequestBody BmInfoJoinerConditionBo condition);

    /**
     * 新增新时代文化校园报名活动申请表-参与人信息表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/joiner/add")
    public AjaxResult addBmInfoJoiner(@Validated @RequestBody BmInfoJoinerBo bmInfoJoinerBo);

    /**
     * 修改新时代文化校园报名活动申请表-参与人信息表
     * 
     * @param bmInfoJoinerBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/joiner/update")
    public AjaxResult updateBmInfoJoiner(@Validated @RequestBody BmInfoJoinerBo bmInfoJoinerBo);

    /**
     * 查询新时代文化校园报名活动申请表-参与人信息表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @GetMapping("/bm/info/joiner/detail")
    public AjaxResult<BmInfoJoinerVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除新时代文化校园报名活动申请表-参与人信息表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @GetMapping("/bm/info/joiner/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
    
    /**
     * 根据手机号和InfoId更新参会人信息表
     *
     * @param bmInfoJoinerBo 
     * @return com.light.core.entity.AjaxResult 
     * <AUTHOR>
     * @date 2024/4/18 10:46
     **/
    @PostMapping("/bm/info/joiner/update-by-mobile")
    public AjaxResult updateByMobile(@RequestBody BmInfoJoinerBo bmInfoJoinerBo);

    /**
     * 根据infoId和手机号获取参会人信息
     *
     * @param bmInfoJoinerBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/4/18 16:02
     **/
    @PostMapping("/bm/info/joiner/get-by-mobile")
    public AjaxResult getByMobile(@RequestBody BmInfoJoinerBo bmInfoJoinerBo);

}
