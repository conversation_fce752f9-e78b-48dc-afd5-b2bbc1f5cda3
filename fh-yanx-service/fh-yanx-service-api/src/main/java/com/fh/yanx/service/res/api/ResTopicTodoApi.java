package com.fh.yanx.service.res.api;

import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTodoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户待办事项
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicTodoApi {

    /**
     * 查询用户待办事项分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/todo/page/list")
    public AjaxResult<PageInfo<ResTopicTodoVo>>
        getResTopicTodoPageListByCondition(@RequestBody ResTopicTodoConditionBo condition);

    /**
     * 查询用户待办事项列表
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/todo/list")
    public AjaxResult<List<ResTopicTodoVo>>
        getResTopicTodoListByCondition(@RequestBody ResTopicTodoConditionBo condition);

    /**
     * 新增用户待办事项
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/todo/add")
    public AjaxResult addResTopicTodo(@Validated @RequestBody ResTopicTodoBo resTopicTodoBo);

    /**
     * 修改用户待办事项
     * 
     * @param resTopicTodoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/todo/update")
    public AjaxResult updateResTopicTodo(@Validated @RequestBody ResTopicTodoBo resTopicTodoBo);

    /**
     * 查询用户待办事项详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/todo/detail")
    public AjaxResult<ResTopicTodoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除用户待办事项
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/todo/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    @GetMapping("res/topic/todo/complete")
    public AjaxResult completeTodo(@RequestParam("id") Long id, @RequestParam("student") boolean student);
}
