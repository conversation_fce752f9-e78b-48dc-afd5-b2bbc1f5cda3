package com.fh.yanx.service.course.service;


import com.fh.yanx.service.course.api.PCourseCasesApi;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 校本课程案例
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@FeignClient(contextId = "pCourseCasesApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PCourseCasesApiService.PCourseCasesApiFallbackFactory.class)
@Component
public interface PCourseCasesApiService extends PCourseCasesApi {

    @Component
    class PCourseCasesApiFallbackFactory implements FallbackFactory<PCourseCasesApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PCourseCasesApiFallbackFactory.class);
        @Override
        public PCourseCasesApiService create(Throwable cause) {
            PCourseCasesApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PCourseCasesApiService() {
                public AjaxResult getPCourseCasesPageListByCondition(PCourseCasesConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                @Override
                public AjaxResult<PageInfo<PCourseCasesVo>> getStoreCourseCasesPageListByCondition(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询我的收藏列表失败");
                }

                public AjaxResult getPCourseCasesListByCondition(PCourseCasesConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPCourseCases(PCourseCasesBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePCourseCases(PCourseCasesBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult getHomeList(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询首页数据失败");
                }

                @Override
                public AjaxResult homeDetail(Long casesId, Boolean queryEdition) {
                    return AjaxResult.fail("查询首页课程详情数据失败");
                }

                @Override
                public AjaxResult topList(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询首页课程推荐数据失败");
                }

                @Override
                public AjaxResult<PCourseCasesVo> getContentModule(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询内容管理数据失败");
                }

                @Override
                public AjaxResult saveContentModule(PCourseCasesBo pCourseCasesBo) {
                    return AjaxResult.fail("保存内容管理数据失败");
                }

                @Override
                public AjaxResult getTeacherApplyCourseList(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("获取教师申报课程列表失败");
                }

                @Override
                public AjaxResult updateExpertCommentaryShowcase(List<CourseVerifyLogBo> courseVerifyLogList) {
                    return AjaxResult.fail("更新专家评论展示失败");
                }

                @Override
                public AjaxResult updateSupplementState(PCourseCasesBo pCourseCasesBo) {
                    return AjaxResult.fail("更新课程增补失败");
                }

                @Override
                public AjaxResult getExpertVerifyCourseList(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询专家审核课程列表失败");
                }

                @Override
                public AjaxResult<PageInfo<PCourseCasesVo>> getComprehensiveCourseList(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询待综评列表失败");
                }

                @Override
                public AjaxResult<PageInfo<PCourseCasesVo>> getExpertAssistantCourseList(PCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询专家助理课程列表失败");
                }
            };
        }
    }
}