package com.fh.yanx.service.courseReviewExpertConfig.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;

/**
 * 课程审核专家配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@Data
public class CourseReviewExpertConfigConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;

	/**
	 * 专家用户的oid
	 */
	@ApiModelProperty("专家用户的oid")
	private String userOid;

	/**
	 * 审核进度类型，默认空：1初审，2终审
	 */
	@ApiModelProperty("审核进度类型")
	private Integer verifyProcessType;


	@ApiModelProperty("课程案例id列表")
	private List<Long> casesIds;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 是否需要评分：1不需要评分,2需要评分
	 */
	@ApiModelProperty("是否需要评分：1不需要评分,2需要评分")
	private Integer needScore;

	/**
	 * 1.审核专家，2指导专家，3专家助理
	 */
	@ApiModelProperty("1.审核专家，2指导专家，3专家助理")
	private Integer expertType;

}
