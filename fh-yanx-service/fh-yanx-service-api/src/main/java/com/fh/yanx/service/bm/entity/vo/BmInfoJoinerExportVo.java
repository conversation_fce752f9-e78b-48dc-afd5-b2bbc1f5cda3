package com.fh.yanx.service.bm.entity.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 参与人导出信息
 * 
 * <AUTHOR>
 * @date 2023/6/16 14:21
 */
@Data
public class BmInfoJoinerExportVo implements Serializable {
    /**
     * 姓名
     */
    @Excel(name = "参会人员", width = 10)
    private String joinerName;

    /**
     * 性别
     */
    @Excel(name = "性别", width = 10)
    private String joinerGender;

    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 20)
    private String joinerMobile;

    /**
     * 民族
     */
    @Excel(name = "民族", width = 10)
    private String joinerNation;

    /**
     * 职务
     */
    @Excel(name = "职务", width = 10)
    private String joinerDuties;

    /**
     * 工作单位/教学科目
     */
    @Excel(name = "教学学科/工作", width = 15)
    private String joinerTeach;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱", width = 20)
    private String joinerMail;
}
