package com.fh.yanx.service.org.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 组织认证日志记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
@Data
public class OrganizationAuthLogConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 认证状态 1-已认证 2-未认证
	 */
	@ApiModelProperty("认证状态 1-已认证 2-未认证")
	private Integer authType;

	/**
	 * 认证截止时间
	 */
	@ApiModelProperty("认证截止时间")
	private Date authEndDay;

	/**
	 * 是否删除：0：否，1：是
	 */
	@ApiModelProperty("是否删除：0：否，1：是")
	private Integer isDelete;





}
