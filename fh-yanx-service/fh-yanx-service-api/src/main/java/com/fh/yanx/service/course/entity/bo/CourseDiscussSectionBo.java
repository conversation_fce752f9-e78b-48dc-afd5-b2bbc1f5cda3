package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课程讨论区表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
@Data
public class CourseDiscussSectionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;

	/**
	 * 0-一级评论 不为0为对某条评论的回复
	 */
	@ApiModelProperty("0-一级评论 不为0为对某条评论的回复")
	private Long parentId;

	/**
	 * 所有父级id英文逗号拼接
	 */
	@ApiModelProperty("所有父级id英文逗号拼接")
	private String superiorIds;

	/**
	 * 评论用户oid
	 */
	@ApiModelProperty("评论用户oid")
	private String userOid;

	/**
	 * 评论内容
	 */
	@ApiModelProperty("评论内容")
	private String content;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
