package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 关联科目表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
public class ResTopicSubjectBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 课题id
	 */
	@ApiModelProperty("课题id")
	private Long topicId;

	/**
	 * 关联学科：待定（可能是type或者字典值）
	 */
	@ApiModelProperty("关联学科：待定（可能是type或者字典值）")
	private Long relSubject;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
