package com.fh.yanx.service.cm.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 代理商信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
@Data
public class CmInfoBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 活动id
	 */
	@ApiModelProperty("活动id")
	private Long activId;

	/**
	 * 活动名称
	 */
	@ApiModelProperty("活动名称")
	private String activName;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String adminOid;

	/**
	 * 代理商名称
	 */
	@ApiModelProperty("代理商名称")
	private String realName;

	/**
	 * 手机号
	 */
	@ApiModelProperty("手机号")
	private String phone;

	/**
	 * 代理商参数
	 */
	@ApiModelProperty("代理商参数")
	private String cm;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
