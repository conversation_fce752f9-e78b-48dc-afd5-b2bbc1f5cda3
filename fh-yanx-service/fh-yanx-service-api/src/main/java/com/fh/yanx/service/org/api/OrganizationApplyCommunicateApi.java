package com.fh.yanx.service.org.api;


import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateBo;
import com.fh.yanx.service.org.entity.vo.OrganizationApplyCommunicateVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 组织申请沟通记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
public interface OrganizationApplyCommunicateApi {

    /**
     * 查询组织申请沟通记录表分页列表
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @PostMapping("/organization/apply/communicate/page/list")
    public AjaxResult<PageInfo<OrganizationApplyCommunicateVo>> getOrganizationApplyCommunicatePageListByCondition(@RequestBody OrganizationApplyCommunicateConditionBo condition);

    /**
     * 查询组织申请沟通记录表列表
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @PostMapping("/organization/apply/communicate/list")
    public AjaxResult<List<OrganizationApplyCommunicateVo>> getOrganizationApplyCommunicateListByCondition(@RequestBody OrganizationApplyCommunicateConditionBo condition);


    /**
     * 新增组织申请沟通记录表
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @PostMapping("/organization/apply/communicate/add")
    public AjaxResult addOrganizationApplyCommunicate(@Validated @RequestBody OrganizationApplyCommunicateBo organizationApplyCommunicateBo);

    /**
     * 修改组织申请沟通记录表
     * @param organizationApplyCommunicateBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @PostMapping("/organization/apply/communicate/update")
    public AjaxResult updateOrganizationApplyCommunicate(@Validated @RequestBody OrganizationApplyCommunicateBo organizationApplyCommunicateBo);

    /**
     * 查询组织申请沟通记录表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @GetMapping("/organization/apply/communicate/detail")
    public AjaxResult<OrganizationApplyCommunicateVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除组织申请沟通记录表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 10:49:54
     */
    @GetMapping("/organization/apply/communicate/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
