package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseCasesGradeEditionApi;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本课程案例年级版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:00
 */
@FeignClient(contextId = "courseCasesGradeEditionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseCasesGradeEditionApiService.CourseCasesGradeEditionApiFallbackFactory.class)
@Component
public interface CourseCasesGradeEditionApiService extends CourseCasesGradeEditionApi {

    @Component
    class CourseCasesGradeEditionApiFallbackFactory implements FallbackFactory<CourseCasesGradeEditionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseCasesGradeEditionApiFallbackFactory.class);
        @Override
        public CourseCasesGradeEditionApiService create(Throwable cause) {
            CourseCasesGradeEditionApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseCasesGradeEditionApiService() {
                public AjaxResult getCourseCasesGradeEditionPageListByCondition(CourseCasesGradeEditionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseCasesGradeEditionListByCondition(CourseCasesGradeEditionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseCasesGradeEdition(CourseCasesGradeEditionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseCasesGradeEdition(CourseCasesGradeEditionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}