package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 工作站具体内容
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResStationContentBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long stationContentId;

	/**
	 * 工作站id
	 */
	@ApiModelProperty("工作站id")
	private Long stationId;

	/**
	 * 内容名称
	 */
	@ApiModelProperty("内容名称")
	private String stationContentName;

	/**
	 * 内容类型：1核心能力展示，2优秀课题展示，3热点资讯，4赛事服务
	 */
	@ApiModelProperty("内容类型：1核心能力展示，2优秀课题展示，3热点资讯，4赛事服务")
	private Integer stationContentType;

	/**
	 * 内容顺序
	 */
	@ApiModelProperty("内容顺序")
	private Long stationContentIndex;

	/**
	 * 内容图片文件原始名称-带后缀，内容只有一张图片时生效
	 */
	@ApiModelProperty("内容图片文件原始名称-带后缀，内容只有一张图片时生效")
	private String contentFileNameOri;

	/**
	 * 内容图片文件名称-不带后缀
	 */
	@ApiModelProperty("内容图片文件名称-不带后缀")
	private String contentFileName;

	/**
	 * 内容图片文件地址
	 */
	@ApiModelProperty("内容图片文件地址")
	private String contentFileUrl;

	/**
	 * 内容图片点击链接
	 */
	@ApiModelProperty("内容图片点击链接")
	private String contentFileUrlLink;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
