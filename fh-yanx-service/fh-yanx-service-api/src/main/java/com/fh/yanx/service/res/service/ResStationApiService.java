package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResStationApi;
import com.fh.yanx.service.res.entity.bo.ResStationBo;
import com.fh.yanx.service.res.entity.bo.ResStationConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 工作站和学校表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resStationApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResStationApiService.ResStationApiFallbackFactory.class)
@Component
public interface ResStationApiService extends ResStationApi {

    @Component
    class ResStationApiFallbackFactory implements FallbackFactory<ResStationApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResStationApiFallbackFactory.class);

        @Override
        public ResStationApiService create(Throwable cause) {
            ResStationApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResStationApiService() {
                public AjaxResult getResStationPageListByCondition(ResStationConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResStationListByCondition(ResStationConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResStation(ResStationBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResStation(ResStationBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}