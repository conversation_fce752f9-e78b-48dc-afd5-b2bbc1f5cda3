package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 校本课程案例
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
public class PCourseCasesConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long userId;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String schoolName;

    /**
     * 课程名称
     */
    @ApiModelProperty("课程名称")
    private String courseName;

    /**
     * 省份ID
     */
    @ApiModelProperty("省份ID")
    private Long provinceId;

    /**
     * 市区ID
     */
    @ApiModelProperty("市区ID")
    private Long cityId;

    /**
     * 区县ID
     */
    @ApiModelProperty("区县ID")
    private Long countyId;

    /**
     * 学段
     */
    @ApiModelProperty("学段")
    private Long phase;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private Long year;

    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String picture;

    /**
     * 
     */
    @ApiModelProperty("")
    private Integer status;

    /**
     * 实施者
     */
    @ApiModelProperty("实施者")
    private String operation;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String phone;

    /**
     * 简介
     */
    @ApiModelProperty("简介")
    private String introduction;

    /**
     * 浏览量
     */
    @ApiModelProperty("浏览量")
    private Long views;

    /**
     * 是否授权 0 否 1是
     */
    @ApiModelProperty("是否授权 0 否 1是")
    private Integer isAuth;

    /**
     * 展示首页的类型，1：大首页，2：校本课程平台首页
     */
    @ApiModelProperty("展示首页的类型，1：大首页，2：校本课程平台首页")
    private Integer homeType;

    /**
     * 0-未删除 1-已删除
     */
    @ApiModelProperty("0-未删除 1-已删除")
    private Integer isDelete;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date createDate;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date updateDate;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String filePath;

    /**
     * word文档地址
     */
    @ApiModelProperty("word文档地址")
    private String wordPath;

    /**
     * 是否上传文件 ： 0 否 1是
     */
    @ApiModelProperty("是否上传文件 ： 0 否 1是")
    private Integer isUpload;

    /**
     * 圆梦表的课程上传人id
     */
    @ApiModelProperty("")
    private Long customerId;

    /**
     * 校本课程案例类型：0课程案例，1典型案例，2精品课程-特色课程，3精品课程-整体课程
     */
    @ApiModelProperty("校本课程案例类型：0课程案例，1典型案例，2精品课程")
    private Integer isExcellent;

    /**
     * 课程上下架：1上架，2下架
     */
    private Integer holdType;

    /**
     * 年级
     */
    private Long grade;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 用户id集合查询条件
     */
    private List<Long> userIds;

    /**
     * 新时代用户的oid
     */
    @ApiModelProperty("新时代用户的oid")
    private String userOid;
    /**
     * 来源类型：1资源网导入，2新时代新增
     */
    @ApiModelProperty("来源类型：1资源网导入，2新时代新增")
    private Integer sourceType;
    /**
     * 精品课程审核状态：1待审核，2审核通过，3审核不通过
     */
    @ApiModelProperty("精品课程审核状态：1待审核，2审核通过，3审核不通过")
    private Integer bestVerifyType;
    /**
     * 模板类型：默认空，1特色课程模板，2整体课程模板
     */
    @ApiModelProperty("模板类型：默认空，1特色课程模板，2整体课程模板")
    private Integer templateType;
    /**
     * 精品课程的模板内容布局-tab1的布局，存储布局json信息
     */
    @ApiModelProperty("精品课程的模板内容布局-tab1的布局，存储布局json信息")
    private String templateLayoutOne;
    /**
     * 精品课程的模板内容布局-tab2的布局，存储布局json信息
     */
    @ApiModelProperty("精品课程的模板内容布局-tab2的布局，存储布局json信息")
    private String templateLayoutTwo;
    /**
     * 精品课程的模板内容布局-tab3的布局，存储布局json信息
     */
    @ApiModelProperty("精品课程的模板内容布局-tab3的布局，存储布局json信息")
    private String templateLayoutThree;

    /**
     * 普通课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存
     */
    @ApiModelProperty("普通课程审核状态")
    private Integer normalVerifyType;

    /**
     * 审核进度类型，默认空：1初审，2二审，3运营审核
     */
    @ApiModelProperty("审核进度类型")
    private Integer verifyProcessType;
    /**
     * 课程类型标签，曾经是这个类型的都会被记录，类型同is_excellent，多个使用英文逗号分割
     */
    @ApiModelProperty("课程类型标签，曾经是这个类型的都会被记录，类型同is_excellent，多个使用英文逗号分割")
    private String isExcellentLabel;

    /**
     * 查询参数：精品审核状态数组查询
     */
    private List<Integer> bestVerifyTypes;

    /**
     * 查询参数：普通审核状态数组查询
     */
    private List<Integer> normalVerifyTypes;

    /**
     * 是否查询历史数据，需要前端传参（目前仅有前台，后台2个老的校本课程列表需要查询）
     */
    private boolean queryHis = false;

    /**
     * 默认bestVerifyTypes和normalVerifyTypes是and查询，如果queryOrTypes=true则转换为or查询，此时normalVerifyType和bestVerifyTypes必须同时有值
     */
    private boolean queryOrTypes = false;

    /**
     * 是否计算总分/平均分，计算一页每个课程的总分。
     */
    private boolean calculateScore = false;

    /**
     * 审核专家的oid
     */
    private String adminOid;

    /**
     * 当前登录人oid，用于查询操作状态
     */
    private String currentUserOid;

    /**
     * 审核操作状态(查询条件使用) 1-未审核 2-已审核
     */
    private Integer verifyOperateType;

    /**
     * 搜索key，用于模糊查询：课程名称、学校名称、教师姓名
     */
    private String searchKey;

    /**
     * 配置审核进度，默认空：1初审，2二审，3运营审核
     */
    @ApiModelProperty("配置审核进度")
    private Integer reviewConfigVerifyProcessType;

    /**
     * 课程列表排序类型：0按照默认排序字段排序（更新时间倒序），1按照加权1升序，2按照加权1降序，3按照加权2升序，4按照加权2降序，5按照加权3升序，6按照加权3降序，7按照平均分升序，8按照平均分降序9按照收藏时间降序
     */
    private Integer courseSortType = 0;

    /**
     * 是否综评（查询条件使用，注意这个综评只是殷总综评，非专家），1未综评，2已综评
     */
    private Integer comprehensiveOperateType;

    /**
     * 是否派单（查询条件使用） 1-未派单 2-已派单
     */
    private Integer dispatchOperateType;

    /**
     * 观看权限（数字,分隔） 2-未认证学校用户 3-一认证学校用户 4-案例持有者
     */
    @ApiModelProperty("观看权限")
    private String viewPermission;

    /**
     * 专家助理审核进度类型 1-初审 2-终审
     */
    private Integer assistantVerifyProcessType;

    /**
     * 是否查询采纳意见 默认false不查询
     */
    private Boolean queryAdoptType = false;

    /**
     * 采纳意见 1-建议采纳 2-建议不采纳
     */
    private Integer adoptType;

    /**
     * 专家审核状态 1-未审核 2-已审核
     */
    private Integer expertVerifyType;

    /**
     * 审核用户oid
     */
    private String verifyUserOid;

    /**
     * 是否需要展示到前台条件
     */
    private Boolean queryVerifyShowType = false;

    /**
     * 查询版本记录
     */
    private Boolean queryEdition = false;

    /**
     * 审核记录和版本记录是否合并返回  默认false不处理
     */
    private Boolean handleVerifyLogAndEdition = false;

    /**
     * 是否查询收藏课本 默认false不查询
     */
    private Boolean queryStoreType = false;

    /**
     * 是否查询收藏课本 默认false不查询
     */
    private String storeUserOid;


    /**
     * 是否增补，0：不增补，1：增补
     */
    @ApiModelProperty("是否增补，0：不增补，1：增补")
    private Integer isSupplement;

    @ApiModelProperty("育人理念")
    private String eduPhilosophy;

    /**
     * 推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）
     */
    @ApiModelProperty("推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）")
    private Integer recommendType;

    /**
     * 专家助理分配状态（1，未分配，2已分配）
     */
    @ApiModelProperty("专家助理分配状态（1，未分配，2已分配）")
    private Integer dispatchType;

    /**
     * 指导专家分配状态（1，未分配，2已分配）
     */
    @ApiModelProperty("指导专家分配状态（1，未分配，2已分配）")
    private Integer guideDispatchOperateType;

    /**
     * 审核专家分配状态（1，未分配，2已分配）
     */
    @ApiModelProperty("审核专家分配状态（1，未分配，2已分配）")
    private Integer auditDispatchOperateType;

    /**
     * 查询是否被专家推荐过
     */
    @ApiModelProperty("查询是否被专家推荐过")
    private Boolean queryIsRecommend=false;

    /**
     * 是否综评专家推荐课程 true:推荐 false:不推荐 null不查询
     */
    @ApiModelProperty("是否综评专家推荐课程 true:推荐 false:不推荐")
    private Boolean queryRecommendType;

    @ApiModelProperty("是否返回配置专家信息，默认false不返回")
    private Boolean showExpertUsers = false;

    @ApiModelProperty("课程案例ids")
    private List<Long> courseCasesIds;

    /**
     * 查询模块信息 默认false不查询 导出需要查询
     */
    private Boolean queryCourseCasesModule = false;

    /**
     * 跳过处理返回结果 默认不跳过
     */
    private Boolean skipConvertHomeResult = false;

    /**
     * 课程案例学校名称
     */
    private String courseCaseSchoolName;

    /**
     * 类型
     */
    private List<Integer> types;
}
