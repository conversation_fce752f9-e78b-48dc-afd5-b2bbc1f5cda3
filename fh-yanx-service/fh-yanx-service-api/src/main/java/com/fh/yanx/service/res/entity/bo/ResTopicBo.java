package com.fh.yanx.service.res.entity.bo;

import com.light.user.student.entity.bo.StudentBo;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.user.entity.bo.UserBo;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 课题表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long topicId;

	/**
	 * 课题名称
	 */
	@ApiModelProperty("课题名称")
	private String topicName;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private Long organizationId;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classesId;

	/**
	 * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定
	 */
	@ApiModelProperty("课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定")
	private Integer topicProcess;

	/**
	 * 当前提交人
	 */
	@ApiModelProperty("当前提交人")
	private String submitUser;

	/**
	 * 当前审批人
	 */
	@ApiModelProperty("当前审批人")
	private String verifyUser;

	/**
	 * 课题类别：待定
	 */
	@ApiModelProperty("课题类别：待定")
	private Integer topicType;

	/**
	 * 关联学科：待定（可能是type或者字典值），多个使用逗号分割
	 */
	@ApiModelProperty("关联学科：待定（可能是type或者字典值），多个使用逗号分割")
	private String relSubject;

	/**
	 * 课题介绍/界定
	 */
	@ApiModelProperty("课题介绍/界定")
	private String topicDesc;

	/**
	 * 课题背景
	 */
	@ApiModelProperty("课题背景")
	private String topicBack;

	/**
	 * 课题目的
	 */
	@ApiModelProperty("课题目的")
	private String topicGoal;

	/**
	 * 研究内容
	 */
	@ApiModelProperty("研究内容")
	private String topicContent;

	/**
	 * 研究方法
	 */
	@ApiModelProperty("研究方法")
	private String topicMethod;

	/**
	 * 研究条件
	 */
	@ApiModelProperty("研究条件")
	private String topicCondition;

	/**
	 * 研究计划
	 */
	@ApiModelProperty("研究计划")
	private String topicPlan;

	/**
	 * 研究预期成果
	 */
	@ApiModelProperty("研究预期成果")
	private String topicExpected;

	/**
	 * 最终评定结果：1通过，2不通过
	 */
	@ApiModelProperty("最终评定结果：1通过，2不通过")
	private Integer evaluateResult;

	/**
	 * 优秀课题类型：1不是优秀课题，2是优秀课题
	 */
	@ApiModelProperty("优秀课题类型：1不是优秀课题，2是优秀课题")
	private Integer excellentType;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 课题来源：1校内自建，2校外导入
	 */
	@ApiModelProperty("课题来源：1校内自建，2校外导入")
	private Integer topicSource;

	/**
	 * 校外课题指导老师信息
	 */
	@ApiModelProperty("校外课题指导老师信息")
	private String topicTutor;

	/**
	 * 校外课题id,第三方id
	 */
	@ApiModelProperty("校外课题id,第三方id")
	private String topicThirdId;

	/**
	 * 课题组成员oid列表
	 */
	@NotEmpty(message = "小组成员不能为空")
	private List<UserBo> studentUserBos;

	/**
	 * 课题指导老师oid列表
	 */
	@NotEmpty(message = "课题指导老师不能为空")
	private List<UserBo> teacherUserBos;

	/**
	 * 关联学科字典值,逗号分隔
	 */
	@ApiModelProperty("关联学科字典值,逗号分隔")
	private String subjectCode;

	/**
	 * 当前登陆用户oid
	 */
	@ApiModelProperty("当前登陆用户oid")
	private String currentUserOid;
}
