package com.fh.yanx.service.course.api;

import com.fh.yanx.service.course.entity.bo.CourseHomeConfigConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigBo;
import com.fh.yanx.service.course.entity.vo.CourseHomeConfigVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 推荐展示位
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface CourseHomeConfigApi {

    /**
     * 查询推荐展示位分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/home/<USER>/page/list")
    public AjaxResult<PageInfo<CourseHomeConfigVo>>
        getCourseHomeConfigPageListByCondition(@RequestBody CourseHomeConfigConditionBo condition);

    /**
     * 查询推荐展示位列表
     * 
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/home/<USER>/list")
    public AjaxResult<List<CourseHomeConfigVo>>
        getCourseHomeConfigListByCondition(@RequestBody CourseHomeConfigConditionBo condition);

    /**
     * 新增推荐展示位
     * 
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/home/<USER>/add")
    public AjaxResult addCourseHomeConfig(@Validated @RequestBody CourseHomeConfigBo courseHomeConfigBo);

    /**
     * 修改推荐展示位
     * 
     * @param courseHomeConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/home/<USER>/update")
    public AjaxResult updateCourseHomeConfig(@Validated @RequestBody CourseHomeConfigBo courseHomeConfigBo);

    /**
     * 查询推荐展示位详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @GetMapping("/course/home/<USER>/detail")
    public AjaxResult<CourseHomeConfigVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除推荐展示位
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @GetMapping("/course/home/<USER>/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 保存展示位配置，支持删除和新增
     *
     * @param courseHomeConfigBo
     * @return
     */
    @PostMapping("/course/home/<USER>/save")
    public AjaxResult saveCourseHomeConfig(@RequestBody CourseHomeConfigBo courseHomeConfigBo);

}
