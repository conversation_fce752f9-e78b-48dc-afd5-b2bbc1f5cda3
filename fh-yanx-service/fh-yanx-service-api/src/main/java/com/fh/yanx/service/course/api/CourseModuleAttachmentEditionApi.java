package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentEditionVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程资源或成果样例附件表-模块附件版本表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:25
 */
public interface CourseModuleAttachmentEditionApi {

    /**
     * 查询课程资源或成果样例附件表-模块附件版本表分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
    @PostMapping("/course/module/attachment/edition/page/list")
    public AjaxResult<PageInfo<CourseModuleAttachmentEditionVo>> getCourseModuleAttachmentEditionPageListByCondition(@RequestBody CourseModuleAttachmentEditionConditionBo condition);

    /**
     * 查询课程资源或成果样例附件表-模块附件版本表列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
    @PostMapping("/course/module/attachment/edition/list")
    public AjaxResult<List<CourseModuleAttachmentEditionVo>> getCourseModuleAttachmentEditionListByCondition(@RequestBody CourseModuleAttachmentEditionConditionBo condition);


    /**
     * 新增课程资源或成果样例附件表-模块附件版本表
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
    @PostMapping("/course/module/attachment/edition/add")
    public AjaxResult addCourseModuleAttachmentEdition(@Validated @RequestBody CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo);

    /**
     * 修改课程资源或成果样例附件表-模块附件版本表
     * @param courseModuleAttachmentEditionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
    @PostMapping("/course/module/attachment/edition/update")
    public AjaxResult updateCourseModuleAttachmentEdition(@Validated @RequestBody CourseModuleAttachmentEditionBo courseModuleAttachmentEditionBo);

    /**
     * 查询课程资源或成果样例附件表-模块附件版本表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
    @GetMapping("/course/module/attachment/edition/detail")
    public AjaxResult<CourseModuleAttachmentEditionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程资源或成果样例附件表-模块附件版本表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:25
     */
    @GetMapping("/course/module/attachment/edition/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
