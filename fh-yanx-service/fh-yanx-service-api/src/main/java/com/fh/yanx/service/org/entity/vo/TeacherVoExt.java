package com.fh.yanx.service.org.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.entity.vo.UserRoleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-21 10:41
 */
@Data
public class TeacherVoExt extends TeacherVo {

    /**
     * 认证状态 1-已认证 2-未认证
     */
    @ApiModelProperty("认证状态 1-已认证 2-未认证")
    private Integer authType;

    /**
     * 认证截止时间
     */
    @ApiModelProperty(value = "认证截止时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date authEndDay;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    private List<UserRoleVoExt> userRoleList;

    @ApiModelProperty("所属组织市省id")
    private Long orgProvinceId;

    @ApiModelProperty("所属组织省")
    private String orgProvinceName;

    @ApiModelProperty("所属组织市id")
    private Long orgCityId;

    @ApiModelProperty("所属组织市")
    private String orgCityName;

    @ApiModelProperty("所属组织区id")
    private Long orgAreaId;

    @ApiModelProperty("所属组织区")
    private String orgAreaName;

    @ApiModelProperty("职称信息")
    private String titleName;
}
