package com.fh.yanx.service.bm.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.yanx.service.bm.entity.bo.BmInfoJoinerBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 新时代文化校园报名活动申请表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Data
public class BmInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long infoId;

    /**
     * 所属省份ID
     */
    @ApiModelProperty("所属省份ID")
    private Long provinceId;

    /**
     * 所属省份名称
     */
    @ApiModelProperty("所属省份名称")
    private String provinceName;

    /**
     * 所属市ID
     */
    @ApiModelProperty("所属市ID")
    private Long cityId;

    /**
     * 所属市名称
     */
    @ApiModelProperty("所属市名称")
    private String cityName;

    /**
     * 所属县区ID
     */
    @ApiModelProperty("所属县区ID")
    private Long areaId;

    /**
     * 所属县区名称
     */
    @ApiModelProperty("所属县区名称")
    private String areaName;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;

    /**
     * 单位名称
     */
    @ApiModelProperty("单位名称")
    private String departName;

    /**
     * 学段信息
     */
    @ApiModelProperty("学段信息")
    private String period;

    /**
     * 参与类型：1个人，2团体
     */
    @ApiModelProperty("参与类型：1个人，2团体")
    private Integer joinType;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contactName;

    /**
     * 联系人手机号码
     */
    @ApiModelProperty("联系人手机号码")
    private String contactMobile;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public BmInfoVo returnOwn() {
        return this;
    }

    /**
     * 确认状态：1待确认，2沟通中，3已确认，4已拒绝
     */
    private Integer submitType;

    /**
     * 缴费状态：1未缴费，1已缴费
     */
    private Integer payType;

    /**
     * 发票状态：1未开发票，2已开发票
     */
    private Integer invoiceType;

    /**
     * 发票文件oid
     */
    private String invoiceId;

    /**
     * 发票文件地址
     */
    private String invoiceUrl;

    /**
     * 发票名称不带后缀
     */
    private String invoiceName;

    /**
     * 发票名称带后缀
     */
    private String invoiceNameOri;

    /**
     * 支付凭证文件oid
     */
    private String payId;

    /**
     * 支付凭证文件地址
     */
    private String payUrl;

    /**
     * 支付凭证名称不带后缀
     */
    private String payName;

    /**
     * 支付凭证名称带后缀
     */
    private String payNameOri;

    /**
     * 参与人list
     */
    private List<BmInfoJoinerVo> bmInfoJoinerVoList;

    /**
     * 参与人数量
     */
    private Long joinerCount;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activId;

    /**
     * 签到状态 1-未签到 2-已签到
     */
    @ApiModelProperty("签到状态 1-未签到 2-已签到")
    private Integer signInState;

    /**
     * 签到码
     */
    @ApiModelProperty("签到码")
    private String signInCode;

    /**
     * 开票类型 1-个人 2-单位
     */
    @ApiModelProperty("开票类型 1-个人 2-单位")
    private Integer invoicingType;

    /**
     * 发票抬头
     */
    @ApiModelProperty("发票抬头")
    private String invoiceHeader;

    /**
     * 税号
     */
    @ApiModelProperty("税号")
    private String dutyCode;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 付款凭证上传时间
     */
    @ApiModelProperty("付款凭证上传时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 付款凭证上传人oid
     */
    @ApiModelProperty("付款凭证上传人oid")
    private String payUserOid;

    /**
     * 付款凭证上传人
     */
    @ApiModelProperty("付款凭证上传人")
    private String payUserName;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Long orderId;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    /**
     * 订单状态  1：待支付、2：已付款、3：待退款、4：已退款、5：退款驳回、 6：已取消、7：已发货、8：超时未支付
     */
    @ApiModelProperty("订单状态 1：待支付、2：已付款、3：待退款、4：已退款、5：退款驳回、 6：已取消、7：已发货、8：超时未支付")
    private Integer orderState;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 是否缴费 1-无需缴费 2-需要缴费
     */
    @ApiModelProperty("是否缴费 1-无需缴费 2-需要缴费")
    private Integer chargeType;

    /**
     * 征订数量，用于记录显示
     */
    @ApiModelProperty("征订数量，用于记录显示")
    private Long subNumber;
    /**
     * 订单金额，用于记录显示，具体支付金额见订单表
     * 征订总价，改订单金额需同步修改
     */
    @ApiModelProperty("订单金额，用于记录显示，具体支付金额见订单表")
    private BigDecimal subBookTotalPrice;
    /**
     * 商品单价，下单时候的商品价格
     */
    @ApiModelProperty("商品单价，下单时候的商品价格")
    private BigDecimal subBookPrice;
    /**
     * 收件人-联系人
     */
    @ApiModelProperty("收件人-联系人")
    private String recName;
    /**
     * 收件人-联系人手机号码
     */
    @ApiModelProperty("收件人-联系人手机号码")
    private String recMobile;
    /**
     * 收件人-所属省份ID
     */
    @ApiModelProperty("收件人-所属省份ID")
    private Long recProvinceId;
    /**
     * 收件人-所属省份名称
     */
    @ApiModelProperty("收件人-所属省份名称")
    private String recProvinceName;
    /**
     * 收件人-所属市ID
     */
    @ApiModelProperty("收件人-所属市ID")
    private Long recCityId;
    /**
     * 收件人-所属市名称
     */
    @ApiModelProperty("收件人-所属市名称")
    private String recCityName;
    /**
     * 收件人-所属县区ID
     */
    @ApiModelProperty("收件人-所属县区ID")
    private Long recAreaId;
    /**
     * 收件人-所属县区名称
     */
    @ApiModelProperty("收件人-所属县区名称")
    private String recAreaName;
    /**
     * 收件人-地址
     */
    @ApiModelProperty("收件人-地址")
    private String recAddress;

    /**
     * 活动业务类型：1普通活动，2征订活动
     */
    @ApiModelProperty("活动业务类型：1普通活动，2征订活动")
    private Integer activBizType;

    /**
     * 交易流水号
     */
    @ApiModelProperty("交易流水号")
    private String transactionId;

    /**
     * 支付方式类型
     */
    @ApiModelProperty("支付方式类型")
    private Integer payWayType;
    /**
     * 转账记录
     */
    @ApiModelProperty("转账记录")
    private String payRecord;
    /**
     * 发票备注
     *
     */
    @ApiModelProperty("发票备注")
    private String invoiceRemark;

    /**
     * 是否选择材料默认空，1不选择材料，2选择材料
     */
    @ApiModelProperty("否选择材料")
    private Integer selectMaterialType;

    /**
     * 征订书籍名称
     */
    @ApiModelProperty("征订书籍名称")
    private String subBookName;

    /**
     * 活动的门票价格，即活动价格
     */
    @ApiModelProperty("活动的门票价格，即活动价格")
    private BigDecimal activTicketPrice;

    /**
     * 活动的总价格（活动+材料）
     */
    @ApiModelProperty("活动的总价格（活动+材料）")
    private BigDecimal activTotalPrice;

    /**
     * 代理厂商
     */
    @ApiModelProperty("代理厂商")
    private String cm;

    /**
     * 代理厂商名称
     */
    private String cmRealName;

    /**
     * 代理厂商类型 {@link com.fh.yanx.service.enums.CmType}
     */
    private Integer cmType;

    /**
     * 发货状态 1-未发货 2-已发货
     */
    @ApiModelProperty("发货状态")
    private Integer deliverState;

    /**
     * 物流单号
     */
    @ApiModelProperty("物流单号")
    private String logisticsCode;

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    private String logisticsOrg;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activName;

    /**
     * 订单创建时间
     */
    @ApiModelProperty("订单创建时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;

    @ApiModelProperty("支付调整记录")
    private List<BmPayAdjustRecordVo> bmPayAdjustRecordList;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款方式")
    private Integer refundWayType;

    @ApiModelProperty("退款时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date refundTime;

    @ApiModelProperty("退款原因")
    private String refundReason;

    @ApiModelProperty("退款凭证oid")
    private String refundId;

    @ApiModelProperty("退款凭证url")
    private String refundUrl;

    @ApiModelProperty("退款凭证名不带后缀")
    private String refundName;

    @ApiModelProperty("退款凭证名带后缀")
    private String refundNameOri;
}
