package com.fh.yanx.service.enums;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-09-09  09:20
 */
public enum ReceptionRoleEnum {
    SCHOOL_TEACHER("教职工", "school_teacher", 8L),
    CASE_OWNER("案例持有者", "case_owner", 31L);

    private String name;
    private String code;
    private Long roleId;

    ReceptionRoleEnum(String name, String code, Long roleId) {
        this.name = name;
        this.code = code;
        this.roleId = roleId;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public Long getRoleId() {
        return this.roleId;
    }

    public static ReceptionRoleEnum getByRoleId(Long roleId){
        for(ReceptionRoleEnum roleEnum:ReceptionRoleEnum.values()){
            if(roleEnum.getRoleId().equals(roleId)){
                return roleEnum;
            }
        }
        //默认课班报名
        return null;
    }
}
