package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseRecommendAddBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendBo;
import com.fh.yanx.service.course.entity.vo.CourseRecommendVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程推荐表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
public interface CourseRecommendApi {

    /**
     * 查询课程推荐表分页列表
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @PostMapping("/course/recommend/page/list")
    public AjaxResult<PageInfo<CourseRecommendVo>> getCourseRecommendPageListByCondition(@RequestBody CourseRecommendConditionBo condition);

    /**
     * 查询课程推荐表列表
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @PostMapping("/course/recommend/list")
    public AjaxResult<List<CourseRecommendVo>> getCourseRecommendListByCondition(@RequestBody CourseRecommendConditionBo condition);


    /**
     * 新增课程推荐表
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @PostMapping("/course/recommend/add")
    public AjaxResult addCourseRecommend(@Validated @RequestBody CourseRecommendBo courseRecommendBo);

    /**
     * 新增课程推荐表
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @PostMapping("/course/recommend/addBatch")
    public AjaxResult addCourseRecommend(@Validated @RequestBody CourseRecommendAddBo conditionBo);

    /**
     * 修改课程推荐表
     * @param courseRecommendBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @PostMapping("/course/recommend/update")
    public AjaxResult updateCourseRecommend(@Validated @RequestBody CourseRecommendBo courseRecommendBo);

    /**
     * 查询课程推荐表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @GetMapping("/course/recommend/detail")
    public AjaxResult<CourseRecommendVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程推荐表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-06 10:47:11
     */
    @GetMapping("/course/recommend/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
