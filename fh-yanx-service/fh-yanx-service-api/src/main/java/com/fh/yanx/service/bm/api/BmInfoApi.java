package com.fh.yanx.service.bm.api;

import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportZdVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoStatisticsVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;

import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新时代文化校园报名活动申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
public interface BmInfoApi {

    /**
     * 查询新时代文化校园报名活动申请表分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/page/list")
    public AjaxResult<PageInfo<BmInfoVo>> getBmInfoPageListByCondition(@RequestBody BmInfoConditionBo condition);

    /**
     * 查询报名统计信息
     *
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/statistics")
    public AjaxResult<BmInfoStatisticsVo> getBmInfoStatistics(@RequestBody BmInfoConditionBo condition);

    /**
     * 查询新时代文化校园报名名单人数
     *
     * @param conditionBo
     * @return
     */
    @PostMapping("/bm/info/count-joiner")
    public AjaxResult<Integer> countBmInfoJoiner(@RequestBody BmInfoConditionBo conditionBo);

    /**
     * 查询新时代文化校园报名活动申请表列表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/list")
    public AjaxResult<List<BmInfoVo>> getBmInfoListByCondition(@RequestBody BmInfoConditionBo condition);

    /**
     * 新增新时代文化校园报名活动申请表
     * 
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/add")
    public AjaxResult addBmInfo(@Validated @RequestBody BmInfoBo bmInfoBo);

    /**
     * 修改新时代文化校园报名活动申请表
     * 
     * @param bmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/update")
    public AjaxResult updateBmInfo(@Validated @RequestBody BmInfoBo bmInfoBo);

    /**
     * 修改新时代文化校园报名活动申请表-同时更新参与人
     *
     * @param bmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/update-wbij")
    public AjaxResult updateBmInfoWbij(@Validated @RequestBody BmInfoBo bmInfoBo);

    /**
     * 查询新时代文化校园报名活动申请表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @GetMapping("/bm/info/detail")
    public AjaxResult<BmInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询新时代文化校园报名活动申请表详-带参与人信息
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @GetMapping("/bm/info/detail-wj")
    public AjaxResult<BmInfoVo> getDetailWithJoiner(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除新时代文化校园报名活动申请表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @GetMapping("/bm/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 新增新时代文化校园报名活动申请表(unregister用户的申请添加记录)
     *
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/add-ug")
    public AjaxResult addBmInfoUg(@Validated @RequestBody BmInfoBo bmInfoBo);

    /**
     * 新增活动报名申请（包含参会人信息）
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/4 14:32
     **/
    @PostMapping("/bm/info/add-with-joiner")
    public AjaxResult addBmInfoWithJoiner(@Validated @RequestBody BmInfoBo bmInfoBo);

    /**
     * 查询新时代文化校园报名活动导出的列表
     *
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/list-export")
    public AjaxResult<List<BmInfoExportVo>> getBmInfoListByConditionExport(@RequestBody BmInfoConditionBo condition);

    /**
     * 查询新时代文化校园报名活动导出的列表-征订
     *
     * <AUTHOR>
     * @date 2023-06-08 14:35:03
     */
    @PostMapping("/bm/info/list-export/zd")
    public AjaxResult<List<BmInfoExportZdVo>>
        getBmInfoListByConditionExportZd(@RequestBody BmInfoConditionBo condition);

    /**
     * 校验是否已报名
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/3 18:07
     **/
    @PostMapping("/bm/info/check/activ-contact")
    public AjaxResult checkActivContact(@RequestBody BmInfoBo bmInfoBo);

    /**
     * 根据联系人手机号查询报名记录
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/4 15:17
     **/
    @PostMapping("/bm/info/detail-by-mobile")
    public AjaxResult bmInfoDetailByMobile(@RequestBody BmInfoConditionBo condition);

    /**
     * 生成不重复的报名签到码
     *
     * @param bmInfoBo 需要参数：infoId和activId
     * @return
     */
    @PostMapping("/bm/info/sign-code")
    public AjaxResult<String> generateNotRepeatSignInCode(@RequestBody BmInfoBo bmInfoBo);

    /**
     * 生成本次报名活动的所有已确认用户的账号
     *
     * @param bmInfoBo the bm info bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-09 10:19:15
     */
    @PostMapping("/bm/info/generate-account")
    public AjaxResult generateAccount(@RequestBody BmInfoBo bmInfoBo);

    /**
     * 签到
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/8 10:02
     **/
    @PostMapping("/bm/info/sign-in")
    public AjaxResult bmInfoSignIn(@RequestBody BmInfoBo bmInfoBo);

    /**
     * 校验手机号是否在已确认参会人名单里
     *
     * @param phone
     * @return
     */
    @GetMapping("/bm/info/check-phone-for-login")
    public AjaxResult<Boolean> checkPhoneForLogin(@RequestParam("phone") String phone);

    /**
     * 根据手机号查询我的订单
     *
     * @param activId the activ id
     * @param phone the phone
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2023 -10-27 11:39:01
     */
    @GetMapping("/bm/info/my-order")
    @ApiOperation(value = "根据手机号查询我的订单", notes = "根据手机号查询我的订单")
    public AjaxResult myOrder(@RequestParam("activId") Long activId, @RequestParam("phone") String phone);


    /**
     * 更新报名表信息
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/3/26 14:54
     **/
    @PostMapping("/update-by-id")
    @ApiOperation(value = "更新报名表信息", notes = "更新报名表信息")
    public AjaxResult updateBmInfoByInfoId(@RequestBody BmInfoBo bmInfoBo);

//    @GetMapping("/bm/info/asposeWord")
//    @ApiOperation(value = "测试", notes = "测试")
//    public byte[] asposeWord();
}
