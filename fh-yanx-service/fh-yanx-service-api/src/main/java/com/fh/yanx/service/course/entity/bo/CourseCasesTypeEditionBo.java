package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 校本课程案例类型版本记录
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
@Data
public class CourseCasesTypeEditionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 校本课程案例版本表ID
	 */
	@ApiModelProperty("校本课程案例版本表ID")
	private Long casesEditionId;

	/**
	 * 类型名称
	 */
	@ApiModelProperty("类型名称")
	private String name;

	/**
	 * 类型
	 */
	@ApiModelProperty("类型")
	private Long type;

	/**
	 * 0-未删除 1-已删除
	 */
	@ApiModelProperty("0-未删除 1-已删除")
	private Integer isDelete;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createDate;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateDate;

}
