package com.fh.yanx.service.banner.service;


import com.fh.yanx.service.banner.api.BannerInfoApi;
import com.fh.yanx.service.banner.entity.bo.BannerInfoBo;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 新时代文化校园banner信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
@FeignClient(contextId = "bannerInfoApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = BannerInfoApiService.BannerApiFallbackFactory.class)
@Component
public interface BannerInfoApiService extends BannerInfoApi {

    @Component
    class BannerApiFallbackFactory implements FallbackFactory<BannerInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(BannerApiFallbackFactory.class);
        @Override
        public BannerInfoApiService create(Throwable cause) {
            BannerApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new BannerInfoApiService() {
                public AjaxResult getBannerPageListByCondition(BannerInfoConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getBannerListByCondition(BannerInfoConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addBanner(BannerInfoBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateBanner(BannerInfoBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult changeState(BannerInfoBo bannerInfoBo) {
                    return AjaxResult.fail("修改banner状态失败");
                }
            };
        }
    }
}