package com.fh.yanx.service.res.service;

import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicTodoApi;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 用户待办事项
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@FeignClient(contextId = "resTopicTodoApiService", value = ConstServiceName.FH_YANX_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ResTopicTodoApiService.ResTopicTodoApiFallbackFactory.class)
@Component
public interface ResTopicTodoApiService extends ResTopicTodoApi {

    @Component
    class ResTopicTodoApiFallbackFactory implements FallbackFactory<ResTopicTodoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicTodoApiFallbackFactory.class);

        @Override
        public ResTopicTodoApiService create(Throwable cause) {
            ResTopicTodoApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicTodoApiService() {
                public AjaxResult getResTopicTodoPageListByCondition(ResTopicTodoConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicTodoListByCondition(ResTopicTodoConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicTodo(ResTopicTodoBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicTodo(ResTopicTodoBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult completeTodo(Long id, boolean student) {
                    return AjaxResult.fail("completeTodo失败");
                }
            };
        }
    }
}