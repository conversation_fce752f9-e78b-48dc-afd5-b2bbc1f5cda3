package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicStudentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.student.entity.bo.StudentConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课题组成员表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@FeignClient(contextId = "resTopicStudentApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicStudentApiService.ResTopicStudentApiFallbackFactory.class)
@Component
public interface ResTopicStudentApiService extends ResTopicStudentApi {

    @Component
    class ResTopicStudentApiFallbackFactory implements FallbackFactory<ResTopicStudentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicStudentApiFallbackFactory.class);

        @Override
        public ResTopicStudentApiService create(Throwable cause) {
            ResTopicStudentApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicStudentApiService() {
                public AjaxResult getResTopicStudentPageListByCondition(ResTopicStudentConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicStudentListByCondition(ResTopicStudentConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicStudent(ResTopicStudentBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicStudent(ResTopicStudentBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getStudentListByCondition(StudentConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }
            };
        }
    }
}