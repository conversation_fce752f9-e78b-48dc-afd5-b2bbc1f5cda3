package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesInfoBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesInfoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 校本课程案例详细信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesInfoApi {

    /**
     * 查询校本课程案例详细信息分页列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/info/page/list")
    public AjaxResult<PageInfo<PCourseCasesInfoVo>> getPCourseCasesInfoPageListByCondition(@RequestBody PCourseCasesInfoConditionBo condition);

    /**
     * 查询校本课程案例详细信息列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/info/list")
    public AjaxResult<List<PCourseCasesInfoVo>> getPCourseCasesInfoListByCondition(@RequestBody PCourseCasesInfoConditionBo condition);


    /**
     * 新增校本课程案例详细信息
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/info/add")
    public AjaxResult addPCourseCasesInfo(@Validated @RequestBody PCourseCasesInfoBo pCourseCasesInfoBo);

    /**
     * 修改校本课程案例详细信息
     * @param pCourseCasesInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/info/update")
    public AjaxResult updatePCourseCasesInfo(@Validated @RequestBody PCourseCasesInfoBo pCourseCasesInfoBo);

    /**
     * 查询校本课程案例详细信息详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/info/detail")
    public AjaxResult<PCourseCasesInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除校本课程案例详细信息
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
