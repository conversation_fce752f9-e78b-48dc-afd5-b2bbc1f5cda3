package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 课程模块
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Data
public class CourseModuleBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long courseModuleId;

	/**
	 * 课程案例id
	 */
	@ApiModelProperty("课程案例id")
	private Long casesId;

	/**
	 * 课程模块类型：1课程资源，2成果样例
	 */
	@Deprecated
	private Integer courseModuleType;

	/**
	 * 标题
	 */
	@ApiModelProperty("标题")
	private String courseModuleTitle;

	/**
	 * 副标题
	 */
	@ApiModelProperty("副标题")
	private String courseModuleSubTitle;

	/**
	 * 课程资源排列方式：1列表展示，2块状展示
	 */
	@ApiModelProperty("课程资源排列方式：1列表展示，2块状展示")
	private Integer courseModuleResStyle;

	/**
	 * 课程模块排序，默认1
	 */
	@ApiModelProperty("课程模块排序，默认1")
	private Long courseModuleIndex;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 课程案例模块附件
	 */
	private List<CourseModuleAttachmentBo> courseModuleAttachmentBoList;

	/**
	 * 前端生成提交存储，用于前端和布局里面的信息做映射
	 */
	@ApiModelProperty("前端生成提交存储，用于前端和布局里面的信息做映射")
	private String uuid;
}
