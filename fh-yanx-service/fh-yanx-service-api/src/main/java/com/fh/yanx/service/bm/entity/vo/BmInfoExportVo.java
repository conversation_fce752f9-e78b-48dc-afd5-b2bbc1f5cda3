package com.fh.yanx.service.bm.entity.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 新时代活动列表导出的vo
 *
 * <AUTHOR>
 * @date 2023 /6/16 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BmInfoExportVo implements Serializable {

    /**
     * 报名主键id
     */
    private Long infoId;

    /**
     * 报名时间
     */
    @Excel(name = "报名时间", width = 20, needMerge = true)
    private String time;

    /**
     * 所在地区
     */
    @Excel(name = "所在地区", width = 20, needMerge = true)
    private String address;

    /**
     * 报名单位
     */
    @Excel(name = "报名单位", width = 20, needMerge = true)
    private String depart;

    /**
     * 报名参与类型
     */
    @Excel(name = "参与方式", width = 15, needMerge = true)
    private String joinType;

    /**
     * 联系人名称
     */
    @Excel(name = "联系人", width = 10, needMerge = true)
    private String contactName;

    /**
     * 联系人手机
     */
    @Excel(name = "手机号码", width = 20, needMerge = true)
    private String contactMobile;

    /**
     * 确认状态
     */
    @Excel(name = "确认状态", width = 10, needMerge = true)
    private String submit;

    /**
     * 支付状态
     */
    @Excel(name = "缴费状态", width = 10, needMerge = true)
    private String pay;

    /**
     * 开票状态
     */
    @Excel(name = "开票状态", width = 10, needMerge = true)
    private String invoice;

    /**
     * 购买会议材料
     */
    @Excel(name = "购买会议材料", width = 8, needMerge = true)
    private String selectMaterialType;

    /**
     * 报名参与人信息
     */
    private BmInfoJoinerExportVo bmInfoJoinerExportVo;

    /**
     * 报名参与人信息
     */
    @ExcelCollection(name = "参与人信息")
    private List<BmInfoJoinerExportVo> bmInfoJoinerExportVos;
}
