package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicPvApi;
import com.fh.yanx.service.res.entity.bo.ResTopicPvBo;
import com.fh.yanx.service.res.entity.bo.ResTopicPvConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课题pv记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resTopicPvApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicPvApiService.ResTopicPvApiFallbackFactory.class)
@Component
public interface ResTopicPvApiService extends ResTopicPvApi {

    @Component
    class ResTopicPvApiFallbackFactory implements FallbackFactory<ResTopicPvApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicPvApiFallbackFactory.class);

        @Override
        public ResTopicPvApiService create(Throwable cause) {
            ResTopicPvApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicPvApiService() {
                public AjaxResult getResTopicPvPageListByCondition(ResTopicPvConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicPvListByCondition(ResTopicPvConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicPv(ResTopicPvBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicPv(ResTopicPvBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}