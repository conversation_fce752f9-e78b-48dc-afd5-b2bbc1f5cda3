package com.fh.yanx.service.res.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 课题组成员表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
public class ResTopicStudentConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 课题id
	 */
	@ApiModelProperty("课题id")
	private Long topicId;

	/**
	 * 学生用户oid
	 */
	@ApiModelProperty("学生用户oid")
	private String userOid;

	/**
	 * 课题评价
	 */
	@ApiModelProperty("课题评价")
	private String topicEvaluate;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
