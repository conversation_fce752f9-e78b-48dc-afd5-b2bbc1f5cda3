package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseCasesInfoEditionApi;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本课程案例详细信息版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
@FeignClient(contextId = "courseCasesInfoEditionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseCasesInfoEditionApiService.CourseCasesInfoEditionApiFallbackFactory.class)
@Component
public interface CourseCasesInfoEditionApiService extends CourseCasesInfoEditionApi {

    @Component
    class CourseCasesInfoEditionApiFallbackFactory implements FallbackFactory<CourseCasesInfoEditionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseCasesInfoEditionApiFallbackFactory.class);
        @Override
        public CourseCasesInfoEditionApiService create(Throwable cause) {
            CourseCasesInfoEditionApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseCasesInfoEditionApiService() {
                public AjaxResult getCourseCasesInfoEditionPageListByCondition(CourseCasesInfoEditionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseCasesInfoEditionListByCondition(CourseCasesInfoEditionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseCasesInfoEdition(CourseCasesInfoEditionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseCasesInfoEdition(CourseCasesInfoEditionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}