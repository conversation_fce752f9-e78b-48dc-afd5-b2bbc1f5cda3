package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleAttachmentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程资源或成果样例附件表-模块附件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface CourseModuleAttachmentApi {

    /**
     * 查询课程资源或成果样例附件表-模块附件分页列表
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/attachment/page/list")
    public AjaxResult<PageInfo<CourseModuleAttachmentVo>> getCourseModuleAttachmentPageListByCondition(@RequestBody CourseModuleAttachmentConditionBo condition);

    /**
     * 查询课程资源或成果样例附件表-模块附件列表
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/attachment/list")
    public AjaxResult<List<CourseModuleAttachmentVo>> getCourseModuleAttachmentListByCondition(@RequestBody CourseModuleAttachmentConditionBo condition);


    /**
     * 新增课程资源或成果样例附件表-模块附件
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/attachment/add")
    public AjaxResult addCourseModuleAttachment(@Validated @RequestBody CourseModuleAttachmentBo courseModuleAttachmentBo);

    /**
     * 修改课程资源或成果样例附件表-模块附件
     * @param courseModuleAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/attachment/update")
    public AjaxResult updateCourseModuleAttachment(@Validated @RequestBody CourseModuleAttachmentBo courseModuleAttachmentBo);

    /**
     * 查询课程资源或成果样例附件表-模块附件详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @GetMapping("/course/module/attachment/detail")
    public AjaxResult<CourseModuleAttachmentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程资源或成果样例附件表-模块附件
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @GetMapping("/course/module/attachment/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
