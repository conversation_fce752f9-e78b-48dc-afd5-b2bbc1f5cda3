package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资源类别
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
public class PCategoryBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 父级ID
	 */
	@ApiModelProperty("父级ID")
	private Long parentId;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String name;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String description;

	/**
	 * 所属模块ID
	 */
	@ApiModelProperty("所属模块ID")
	private Integer moduleId;

	/**
	 * 层级
	 */
	@ApiModelProperty("层级")
	private Integer level;

	/**
	 * 类型
	 */
	@ApiModelProperty("类型")
	private Integer typeId;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String remark;

	/**
	 * 状态（1、正常，2、禁用 0 删除）
	 */
	@ApiModelProperty("状态（1、正常，2、禁用 0 删除）")
	private Integer state;



	/**
	 * 
	 */
	@ApiModelProperty("")
	private String createUserOid;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 顺序
	 */
	@ApiModelProperty("顺序")
	private Integer sequence;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long edusoaId;

}
