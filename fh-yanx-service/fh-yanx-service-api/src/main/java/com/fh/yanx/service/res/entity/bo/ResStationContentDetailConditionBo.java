package com.fh.yanx.service.res.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 工作站具体内容详情
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResStationContentDetailConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long stationContentDetailId;

	/**
	 * 工作站具体内容id
	 */
	@ApiModelProperty("工作站具体内容id")
	private Long stationContentId;

	/**
	 * 工作站id
	 */
	@ApiModelProperty("工作站id")
	private Long stationId;

	/**
	 * 内容详情名称
	 */
	@ApiModelProperty("内容详情名称")
	private String stationContentDetailName;

	/**
	 * 详细内容分组：1丘成桐赛事，2ISEF赛事（可能从字典获取）
	 */
	@ApiModelProperty("详细内容分组：1丘成桐赛事，2ISEF赛事（可能从字典获取）")
	private Integer stationContentDetailGroup;

	/**
	 * 内容详情顺序
	 */
	@ApiModelProperty("内容详情顺序")
	private Long stationContentDetailIndex;

	/**
	 * 内容详情图片文件原始名称-带后缀，内容只有一张图片时生效
	 */
	@ApiModelProperty("内容详情图片文件原始名称-带后缀，内容只有一张图片时生效")
	private String contentDetailFileNameOri;

	/**
	 * 内容详情图片文件名称-不带后缀
	 */
	@ApiModelProperty("内容详情图片文件名称-不带后缀")
	private String contentDetailFileName;

	/**
	 * 内容详情图片文件地址
	 */
	@ApiModelProperty("内容详情图片文件地址")
	private String contentDetailFileUrl;

	/**
	 * 内容详情图片点击链接
	 */
	@ApiModelProperty("内容详情图片点击链接")
	private String contentDetailFileUrlLink;

	/**
	 * 内容详情副名称
	 */
	@ApiModelProperty("内容详情副名称")
	private String contentDetailSubname;

	/**
	 * 内容详情标签，比如：环境学、统计学
	 */
	@ApiModelProperty("内容详情标签，比如：环境学、统计学")
	private String contentDetailLabel;

	/**
	 * 内容详情成员，比如：张晓琴、冒小冒、曹小明
	 */
	@ApiModelProperty("内容详情成员，比如：张晓琴、冒小冒、曹小明")
	private String contentDetailMember;

	/**
	 * 收藏数
	 */
	@ApiModelProperty("收藏数")
	private Long contentDetailCollect;

	/**
	 * 查看数
	 */
	@ApiModelProperty("查看数")
	private Long contentDetailPv;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
