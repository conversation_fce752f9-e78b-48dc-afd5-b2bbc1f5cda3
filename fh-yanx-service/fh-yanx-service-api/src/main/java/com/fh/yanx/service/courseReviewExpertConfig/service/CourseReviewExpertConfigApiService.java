package com.fh.yanx.service.courseReviewExpertConfig.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.courseReviewExpertConfig.api.CourseReviewExpertConfigApi;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 课程审核专家配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@FeignClient(contextId = "courseReviewExpertConfigApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseReviewExpertConfigApiService.CourseReviewExpertConfigApiFallbackFactory.class)
@Component
public interface CourseReviewExpertConfigApiService extends CourseReviewExpertConfigApi {

    @Component
    class CourseReviewExpertConfigApiFallbackFactory implements FallbackFactory<CourseReviewExpertConfigApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseReviewExpertConfigApiFallbackFactory.class);
        @Override
        public CourseReviewExpertConfigApiService create(Throwable cause) {
            CourseReviewExpertConfigApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new CourseReviewExpertConfigApiService() {
                public AjaxResult getCourseReviewExpertConfigPageListByCondition(CourseReviewExpertConfigConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseReviewExpertConfigListByCondition(CourseReviewExpertConfigConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseReviewExpertConfig(List<CourseReviewExpertConfigBo> Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseReviewExpertConfig(CourseReviewExpertConfigBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}