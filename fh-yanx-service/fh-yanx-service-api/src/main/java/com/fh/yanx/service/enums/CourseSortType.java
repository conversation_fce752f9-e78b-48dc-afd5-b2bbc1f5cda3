package com.fh.yanx.service.enums;

/**
 * 课程排序类型
 *
 * <AUTHOR>
 */
public enum CourseSortType {
    /**
     * 更新时间倒序
     */
    UPDATE_TIME_DESC(0),
    /**
     * 加权1-升序
     */
    JQ1_ASC(1),
    /**
     * 加权1-降序
     */
    JQ1_DESC(2),
    /**
     * 加权2-升序
     */
    JQ2_ASC(3),
    /**
     * 加权2-降序
     */
    JQ2_DESC(4),
    /**
     * 加权3-升序
     */
    JQ3_ASC(5),
    /**
     * 加权3-降序
     */
    JQ3_DESC(6),
    /**
     * 平均分-升序
     */
    AVG_SCORE_ASC(7),
    /**
     * 平均分-降序
     */
    AVG_SCORE_DESC(8)
    ;

    private Integer value;

    CourseSortType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}