package com.fh.yanx.service.org.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 组织申请沟通记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
@Data
public class OrganizationApplyCommunicateBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 组织申请id
	 */
	@ApiModelProperty("组织申请id")
	private Long organizationApplyId;

	/**
	 * 沟通内容
	 */
	@ApiModelProperty("沟通内容")
	private String content;

	/**
	 * 处理状态：1：处理中，2：已处理
	 */
	@ApiModelProperty("处理状态：1：处理中，2：已处理")
	private Integer type;

	/**
	 * 是否删除：0：否，1：是
	 */
	@ApiModelProperty("是否删除：0：否，1：是")
	private Integer isDelete;





}
