package com.fh.yanx.service.bm.service;

import com.fh.yanx.service.bm.entity.vo.BmInfoExportZdVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoStatisticsVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.yanx.service.bm.api.BmInfoApi;
import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

import java.util.List;

/**
 * 新时代文化校园报名活动申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@FeignClient(contextId = "bmInfoApiService", value = ConstServiceName.FH_YANX_SERVICE,
    configuration = FeignClientInterceptor.class, fallbackFactory = BmInfoApiService.BmInfoApiFallbackFactory.class)
@Component
public interface BmInfoApiService extends BmInfoApi {

    @Component
    class BmInfoApiFallbackFactory implements FallbackFactory<BmInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(BmInfoApiFallbackFactory.class);

        @Override
        public BmInfoApiService create(Throwable cause) {
            BmInfoApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new BmInfoApiService() {
                public AjaxResult getBmInfoPageListByCondition(BmInfoConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                @Override
                public AjaxResult<BmInfoStatisticsVo> getBmInfoStatistics(BmInfoConditionBo condition) {
                    return AjaxResult.fail("查询报名统计信息失败");
                }

                @Override
                public AjaxResult countBmInfoJoiner(BmInfoConditionBo conditionBo) {
                    return AjaxResult.fail("查询新时代文化校园报名名单人数失败");
                }

                public AjaxResult getBmInfoListByCondition(BmInfoConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addBmInfo(BmInfoBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateBmInfo(BmInfoBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult addBmInfoUg(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult addBmInfoWithJoiner(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult<BmInfoVo> getDetailWithJoiner(Long id) {
                    return AjaxResult.fail("查询详情携带参与人信息失败");
                }

                @Override
                public AjaxResult updateBmInfoWbij(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult<List<BmInfoExportVo>> getBmInfoListByConditionExport(BmInfoConditionBo condition) {
                    return AjaxResult.fail("导出失败");
                }

                @Override
                public AjaxResult<List<BmInfoExportZdVo>>
                    getBmInfoListByConditionExportZd(BmInfoConditionBo condition) {
                    return AjaxResult.fail("导出失败");
                }

                @Override
                public AjaxResult<String> generateNotRepeatSignInCode(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("生成签到码失败");
                }

                @Override
                public AjaxResult checkActivContact(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("校验失败");
                }

                @Override
                public AjaxResult bmInfoDetailByMobile(BmInfoConditionBo condition) {
                    return AjaxResult.fail("查询报名信息失败");
                }

                @Override
                public AjaxResult generateAccount(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("生成账号失败");
                }

                @Override
                public AjaxResult bmInfoSignIn(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("签到失败");
                }

                @Override
                public AjaxResult checkPhoneForLogin(String phone) {
                    return AjaxResult.fail("校验手机号登录失败");
                }

                @Override
                public AjaxResult myOrder(Long activId, String phone) {
                    return AjaxResult.fail("获取我的订单失败");
                }

                @Override
                public AjaxResult updateBmInfoByInfoId(BmInfoBo bmInfoBo) {
                    return AjaxResult.fail("更新报名表信息失败");
                }

//                @Override
//                public byte[] asposeWord() {
//                    return null;
//
//                }

            };
        }
    }
}