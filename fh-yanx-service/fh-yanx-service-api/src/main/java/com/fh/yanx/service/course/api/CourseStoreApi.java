package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseStoreConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseStoreBo;
import com.fh.yanx.service.course.entity.vo.CourseStoreVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程收藏表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-04 18:33:34
 */
public interface CourseStoreApi {

    /**
     * 查询课程收藏表分页列表
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @PostMapping("/course/store/page/list")
    public AjaxResult<PageInfo<CourseStoreVo>> getCourseStorePageListByCondition(@RequestBody CourseStoreConditionBo condition);

    /**
     * 查询课程收藏表列表
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @PostMapping("/course/store/list")
    public AjaxResult<List<CourseStoreVo>> getCourseStoreListByCondition(@RequestBody CourseStoreConditionBo condition);


    /**
     * 新增课程收藏表
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @PostMapping("/course/store/add")
    public AjaxResult addCourseStore(@Validated @RequestBody CourseStoreBo courseStoreBo);

    /**
     * 修改课程收藏表
     * @param courseStoreBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @PostMapping("/course/store/update")
    public AjaxResult updateCourseStore(@Validated @RequestBody CourseStoreBo courseStoreBo);

    /**
     * 删除课程收藏表
     * @param courseStoreBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @PostMapping("/course/store/deleteByCasesIdAndUserOid")
    public AjaxResult deleteCourseStore(@Validated @RequestBody CourseStoreBo courseStoreBo);

    /**
     * 查询课程收藏表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @GetMapping("/course/store/detail")
    public AjaxResult<CourseStoreVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程收藏表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-04 18:33:34
     */
    @GetMapping("/course/store/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
