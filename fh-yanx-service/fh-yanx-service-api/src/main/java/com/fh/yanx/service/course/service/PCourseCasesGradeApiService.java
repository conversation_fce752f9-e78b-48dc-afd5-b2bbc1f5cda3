package com.fh.yanx.service.course.service;


import com.fh.yanx.service.course.api.PCourseCasesGradeApi;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本课程案例年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@FeignClient(contextId = "pCourseCasesGradeApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PCourseCasesGradeApiService.PCourseCasesGradeApiFallbackFactory.class)
@Component
public interface PCourseCasesGradeApiService extends PCourseCasesGradeApi {

    @Component
    class PCourseCasesGradeApiFallbackFactory implements FallbackFactory<PCourseCasesGradeApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PCourseCasesGradeApiFallbackFactory.class);
        @Override
        public PCourseCasesGradeApiService create(Throwable cause) {
            PCourseCasesGradeApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PCourseCasesGradeApiService() {
                public AjaxResult getPCourseCasesGradePageListByCondition(PCourseCasesGradeConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPCourseCasesGradeListByCondition(PCourseCasesGradeConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPCourseCasesGrade(PCourseCasesGradeBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePCourseCasesGrade(PCourseCasesGradeBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}