package com.fh.yanx.service.bm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.yanx.service.bm.api.BmInfoConnectApi;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

/**
 * 报名活动沟通确认表（本表只有新增记录）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
@FeignClient(contextId = "bmInfoConnectApiService", value = ConstServiceName.FH_YANX_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = BmInfoConnectApiService.BmInfoConnectApiFallbackFactory.class)
@Component
public interface BmInfoConnectApiService extends BmInfoConnectApi {

    @Component
    class BmInfoConnectApiFallbackFactory implements FallbackFactory<BmInfoConnectApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(BmInfoConnectApiFallbackFactory.class);

        @Override
        public BmInfoConnectApiService create(Throwable cause) {
            BmInfoConnectApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new BmInfoConnectApiService() {
                public AjaxResult getBmInfoConnectPageListByCondition(BmInfoConnectConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getBmInfoConnectListByCondition(BmInfoConnectConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addBmInfoConnect(BmInfoConnectBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateBmInfoConnect(BmInfoConnectBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult addBmInfoConnectWithUpdateBmInfo(BmInfoConnectBo bmInfoConnectBo) {
                    return AjaxResult.fail("新增失败");
                }
            };
        }
    }
}