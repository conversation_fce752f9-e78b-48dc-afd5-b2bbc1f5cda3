package com.fh.yanx.service.jz.api;


import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 金中-校本课程案例
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesApi {

    /**
     * 查询金中-校本课程案例分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/page/list")
    public AjaxResult<PageInfo<JzCourseCasesVo>> getJzCourseCasesPageListByCondition(@RequestBody JzCourseCasesConditionBo condition);

    /**
     * 查询金中-校本课程案例列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/list")
    public AjaxResult<List<JzCourseCasesVo>> getJzCourseCasesListByCondition(@RequestBody JzCourseCasesConditionBo condition);


    /**
     * 新增金中-校本课程案例
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/add")
    public AjaxResult addJzCourseCases(@Validated @RequestBody JzCourseCasesBo jzCourseCasesBo);

    /**
     * 修改金中-校本课程案例
     * @param jzCourseCasesBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/update")
    public AjaxResult updateJzCourseCases(@Validated @RequestBody JzCourseCasesBo jzCourseCasesBo);

    /**
     * 查询金中-校本课程案例详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/detail")
    public AjaxResult<JzCourseCasesVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除金中-校本课程案例
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询校本课程案例首页列表
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/jz/course/cases/page/home-list")
    public AjaxResult getHomeList(@RequestBody JzCourseCasesConditionBo condition);

    /**
     * 查询校本课程案例首页列表
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/jz/course/cases/page/home-detail")
    public AjaxResult homeDetail(@RequestParam("casesId") Long casesId);
}
