package com.fh.yanx.service.cm.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 代理商信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
@Data
public class CmInfoVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activId;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activName;

    /**
     * 用户oid
     */
    @ApiModelProperty("用户oid")
    private String adminOid;

    /**
     * 代理商名称
     */
    @ApiModelProperty("代理商名称")
    private String realName;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 代理商参数
     */
    @ApiModelProperty("代理商参数")
    private String cm;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public CmInfoVo returnOwn() {
        return this;
    }

}
