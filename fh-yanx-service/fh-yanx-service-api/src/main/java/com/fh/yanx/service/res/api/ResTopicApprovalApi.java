package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicApprovalConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalBo;
import com.fh.yanx.service.res.entity.vo.ResTopicApprovalVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课题审批意见表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicApprovalApi {

    /**
     * 查询课题审批意见表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/approval/page/list")
    public AjaxResult<PageInfo<ResTopicApprovalVo>> getResTopicApprovalPageListByCondition(@RequestBody ResTopicApprovalConditionBo condition);

    /**
     * 查询课题审批意见表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/approval/list")
    public AjaxResult<List<ResTopicApprovalVo>> getResTopicApprovalListByCondition(@RequestBody ResTopicApprovalConditionBo condition);


    /**
     * 新增课题审批意见表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/approval/add")
    public AjaxResult addResTopicApproval(@Validated @RequestBody ResTopicApprovalBo resTopicApprovalBo);

    /**
     * 修改课题审批意见表
     * @param resTopicApprovalBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/approval/update")
    public AjaxResult updateResTopicApproval(@Validated @RequestBody ResTopicApprovalBo resTopicApprovalBo);

    /**
     * 查询课题审批意见表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/approval/detail")
    public AjaxResult<ResTopicApprovalVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课题审批意见表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/approval/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
