package com.fh.yanx.service.enums;

/**
 * <AUTHOR>
 * @date 2023-08-08 10:18
 */
public enum BmInfoSignInState {
    BM_INFO_NOT_SIGN(1, "未签到"),
    BM_INFO_SIGN(2, "已签到");

    private Integer code;
    private String value;

    BmInfoSignInState(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
