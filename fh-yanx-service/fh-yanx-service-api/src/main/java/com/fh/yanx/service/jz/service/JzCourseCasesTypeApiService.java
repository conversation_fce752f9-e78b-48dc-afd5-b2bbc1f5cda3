package com.fh.yanx.service.jz.service;

import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.jz.api.JzCourseCasesTypeApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 金中-校本课程案例类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@FeignClient(contextId = "jzCourseCasesTypeApiService", value = ConstServiceName.FH_YANX_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = JzCourseCasesTypeApiService.JzCourseCasesTypeApiFallbackFactory.class)
@Component
public interface JzCourseCasesTypeApiService extends JzCourseCasesTypeApi {

    @Component
    class JzCourseCasesTypeApiFallbackFactory implements FallbackFactory<JzCourseCasesTypeApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(JzCourseCasesTypeApiFallbackFactory.class);

        @Override
        public JzCourseCasesTypeApiService create(Throwable cause) {
            JzCourseCasesTypeApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new JzCourseCasesTypeApiService() {
                public AjaxResult getJzCourseCasesTypePageListByCondition(JzCourseCasesTypeConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getJzCourseCasesTypeListByCondition(JzCourseCasesTypeConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addJzCourseCasesType(JzCourseCasesTypeBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateJzCourseCasesType(JzCourseCasesTypeBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}