package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResStationContentDetailApi;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 工作站具体内容详情
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resStationContentDetailApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResStationContentDetailApiService.ResStationContentDetailApiFallbackFactory.class)
@Component
public interface ResStationContentDetailApiService extends ResStationContentDetailApi {

    @Component
    class ResStationContentDetailApiFallbackFactory implements FallbackFactory<ResStationContentDetailApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResStationContentDetailApiFallbackFactory.class);

        @Override
        public ResStationContentDetailApiService create(Throwable cause) {
            ResStationContentDetailApiFallbackFactory.LOGGER.error("yanx-service}服务调用失败:{}", cause.getMessage());
            return new ResStationContentDetailApiService() {
                public AjaxResult getResStationContentDetailPageListByCondition(ResStationContentDetailConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResStationContentDetailListByCondition(ResStationContentDetailConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResStationContentDetail(ResStationContentDetailBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResStationContentDetail(ResStationContentDetailBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}