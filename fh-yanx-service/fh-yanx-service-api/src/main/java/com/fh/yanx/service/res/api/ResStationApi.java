package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResStationConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationBo;
import com.fh.yanx.service.res.entity.vo.ResStationVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 工作站和学校表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationApi {

    /**
     * 查询工作站和学校表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/page/list")
    public AjaxResult<PageInfo<ResStationVo>> getResStationPageListByCondition(@RequestBody ResStationConditionBo condition);

    /**
     * 查询工作站和学校表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/list")
    public AjaxResult<List<ResStationVo>> getResStationListByCondition(@RequestBody ResStationConditionBo condition);


    /**
     * 新增工作站和学校表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/add")
    public AjaxResult addResStation(@Validated @RequestBody ResStationBo resStationBo);

    /**
     * 修改工作站和学校表
     * @param resStationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/update")
    public AjaxResult updateResStation(@Validated @RequestBody ResStationBo resStationBo);

    /**
     * 查询工作站和学校表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/detail")
    public AjaxResult<ResStationVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除工作站和学校表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
