package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseRecommendApi;
import com.fh.yanx.service.course.entity.bo.CourseRecommendAddBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 课程推荐表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
@FeignClient(contextId = "courseRecommendApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseRecommendApiService.CourseRecommendApiFallbackFactory.class)
@Component
public interface CourseRecommendApiService extends CourseRecommendApi {

    @Component
    class CourseRecommendApiFallbackFactory implements FallbackFactory<CourseRecommendApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseRecommendApiFallbackFactory.class);
        @Override
        public CourseRecommendApiService create(Throwable cause) {
            CourseRecommendApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseRecommendApiService() {
                public AjaxResult getCourseRecommendPageListByCondition(CourseRecommendConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseRecommendListByCondition(CourseRecommendConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseRecommend(CourseRecommendBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult addCourseRecommend(CourseRecommendAddBo conditionBo) {
                    return AjaxResult.fail("新增失败");
                }


                public AjaxResult updateCourseRecommend(CourseRecommendBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}