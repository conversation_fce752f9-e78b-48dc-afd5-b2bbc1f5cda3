package com.fh.yanx.service.enums;

/**
 * 活动展示
 *
 * <AUTHOR>
 * @date 2023-08-01 11:39
 */
public enum ActivInfoShowType {
    DEFAULT(1, "默认"),
    TEACHER_DEVELOP(2, "教师发展页");

    private Integer code;
    private String value;

    ActivInfoShowType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
