package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicCollectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicCollectVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 用户收藏的课题
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicCollectApi {

    /**
     * 查询用户收藏的课题分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/collect/page/list")
    public AjaxResult getResTopicCollectPageListByCondition(@RequestBody ResTopicCollectConditionBo condition);

    /**
     * 查询用户收藏的课题列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/collect/list")
    public AjaxResult<List<ResTopicCollectVo>> getResTopicCollectListByCondition(@RequestBody ResTopicCollectConditionBo condition);


    /**
     * 新增用户收藏的课题
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/collect/add")
    public AjaxResult addResTopicCollect(@Validated @RequestBody ResTopicCollectBo resTopicCollectBo);

    /**
     * 修改用户收藏的课题
     * @param resTopicCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/collect/update")
    public AjaxResult updateResTopicCollect(@Validated @RequestBody ResTopicCollectBo resTopicCollectBo);

    /**
     * 查询用户收藏的课题详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/collect/detail")
    public AjaxResult<ResTopicCollectVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除用户收藏的课题
     * @param topicId,userOid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/collect/delete")
    public AjaxResult deleteByTopicAndUser(@RequestParam("topicId") Long topicId,@RequestParam("userOid") String userOid);
}
