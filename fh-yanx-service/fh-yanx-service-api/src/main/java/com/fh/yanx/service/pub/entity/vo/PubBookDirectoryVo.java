package com.fh.yanx.service.pub.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 融合出版书目录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
public class PubBookDirectoryVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long bookDirectoryId;

    /**
     * FK出版书id，pub_book表逐渐
     */
    @ApiModelProperty("FK出版书id，pub_book表逐渐")
    private Long bookId;

    /**
     * 目录名称
     */
    @ApiModelProperty("目录名称")
    private String bookDirectoryName;

    /**
     * 作者名称
     */
    @ApiModelProperty("作者名称")
    private String authorName;

    /**
     * 作者学校
     */
    @ApiModelProperty("作者学校")
    private String authorSchool;

    /**
     * 书籍目录顺序
     */
    @ApiModelProperty("书籍目录顺序")
    private String bookDirectoryIndex;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public PubBookDirectoryVo returnOwn() {
        return this;
    }

}
