package com.fh.yanx.service.cm.service;


import com.fh.yanx.service.cm.api.CmInfoApi;
import com.fh.yanx.service.cm.entity.bo.CmInfoBo;
import com.fh.yanx.service.cm.entity.bo.CmInfoConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 代理商信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
@FeignClient(contextId = "cmInfoApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CmInfoApiService.CmInfoApiFallbackFactory.class)
@Component
public interface CmInfoApiService extends CmInfoApi {

    @Component
    class CmInfoApiFallbackFactory implements FallbackFactory<CmInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CmInfoApiFallbackFactory.class);
        @Override
        public CmInfoApiService create(Throwable cause) {
            CmInfoApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new CmInfoApiService() {
                public AjaxResult getCmInfoPageListByCondition(CmInfoConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCmInfoListByCondition(CmInfoConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCmInfo(CmInfoBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult addCmInfoBatch(List<CmInfoBo> cmInfoBos) {
                    return AjaxResult.fail("批量新增失败");
                }

                public AjaxResult updateCmInfo(CmInfoBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult updateCmInfoByAdmin(CmInfoBo cmInfoBo) {
                    return AjaxResult.fail("更新失败Admin");
                }
            };
        }
    }
}