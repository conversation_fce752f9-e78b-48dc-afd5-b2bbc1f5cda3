package com.fh.yanx.service.pub.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 融合出版书
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
public class PubBookVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long bookId;

    /**
     * 书籍名称
     */
    @ApiModelProperty("书籍名称")
    private String bookName;

    /**
     * 标签字符串，多个则使用英文逗号分割
     */
    @ApiModelProperty("标签字符串，多个则使用英文逗号分割")
    private String labelNames;

    /**
     * 出版社名称
     */
    @ApiModelProperty("出版社名称")
    private String pressName;

    /**
     * 书号
     */
    @ApiModelProperty("书号")
    private String bookNo;

    /**
     * 阅读次数
     */
    @ApiModelProperty("阅读次数")
    private Long readTimes;

    /**
     * 书籍顺序
     */
    @ApiModelProperty("书籍顺序")
    private Integer bookIndex;

    /**
     * 封面文件oid
     */
    @ApiModelProperty("封面文件oid")
    private String coverFileOid;

    /**
     * 封面文件预览地址
     */
    @ApiModelProperty("封面文件预览地址")
    private String coverFileUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public PubBookVo returnOwn() {
        return this;
    }

}
