package com.fh.yanx.service.pub.api;


import com.fh.yanx.service.pub.entity.bo.PubBookConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookBo;
import com.fh.yanx.service.pub.entity.vo.PubBookVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 融合出版书
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface PubBookApi {

    /**
     * 查询融合出版书分页列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/page/list")
    public AjaxResult<PageInfo<PubBookVo>> getPubBookPageListByCondition(@RequestBody PubBookConditionBo condition);

    /**
     * 查询融合出版书列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/list")
    public AjaxResult<List<PubBookVo>> getPubBookListByCondition(@RequestBody PubBookConditionBo condition);


    /**
     * 新增融合出版书
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/add")
    public AjaxResult addPubBook(@Validated @RequestBody PubBookBo pubBookBo);

    /**
     * 修改融合出版书
     * @param pubBookBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/update")
    public AjaxResult updatePubBook(@Validated @RequestBody PubBookBo pubBookBo);

    /**
     * 查询融合出版书详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @GetMapping("/pub/book/detail")
    public AjaxResult<PubBookVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除融合出版书
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @GetMapping("/pub/book/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 增加书籍阅读次数
     * @param id
     * @return
     * @returnType AjaxResult
     */
    @GetMapping("/pub/book/incr-read-times")
    public AjaxResult incrBookReadTimes(@NotNull(message = "请选择数据") @RequestParam("bookId") Long bookId);
}
