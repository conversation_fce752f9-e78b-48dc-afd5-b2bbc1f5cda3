package com.fh.yanx.service.res.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 课题研究附件表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
public class ResTopicStuAttachmentVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 课题id
     */
    @ApiModelProperty("课题id")
    private Long topicId;

    /**
     * 研究附件名称
     */
    @ApiModelProperty("研究附件名称")
    private String stuName;

    /**
     * 附件文件原始名称-带后缀
     */
    @ApiModelProperty("附件文件原始名称-带后缀")
    private String stuFileNameOri;

    /**
     * 附件文件名称-不带后缀
     */
    @ApiModelProperty("附件文件名称-不带后缀")
    private String stuFileName;

    /**
     * 附件文件地址
     */
    @ApiModelProperty("附件文件地址")
    private String stuFileUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 文件oid
     */
    private String stuFileOid;
}
