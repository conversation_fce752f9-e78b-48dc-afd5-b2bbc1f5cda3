package com.fh.yanx.service.activ.api;


import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 新时代活动日程表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
public interface ActivInfoScheduleApi {

    /**
     * 查询新时代活动日程表分页列表
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
    @PostMapping("/activ/info/schedule/page/list")
    public AjaxResult<PageInfo<ActivInfoScheduleVo>> getActivInfoSchedulePageListByCondition(@RequestBody ActivInfoScheduleConditionBo condition);

    /**
     * 查询新时代活动日程表列表
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
    @PostMapping("/activ/info/schedule/list")
    public AjaxResult<List<ActivInfoScheduleVo>> getActivInfoScheduleListByCondition(@RequestBody ActivInfoScheduleConditionBo condition);


    /**
     * 新增新时代活动日程表
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
    @PostMapping("/activ/info/schedule/add")
    public AjaxResult addActivInfoSchedule(@Validated @RequestBody ActivInfoScheduleBo activInfoScheduleBo);

    /**
     * 修改新时代活动日程表
     * @param activInfoScheduleBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
    @PostMapping("/activ/info/schedule/update")
    public AjaxResult updateActivInfoSchedule(@Validated @RequestBody ActivInfoScheduleBo activInfoScheduleBo);

    /**
     * 查询新时代活动日程表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
    @GetMapping("/activ/info/schedule/detail")
    public AjaxResult<ActivInfoScheduleVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除新时代活动日程表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:29:00
     */
    @GetMapping("/activ/info/schedule/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 保存活动日程
     *
     * @param activInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/28 15:23
     **/
    @PostMapping("/activ/info/schedule/save")
    public AjaxResult saveActivSchedules(@RequestBody ActivInfoBo activInfoBo);

}
