package com.fh.yanx.service.enums;

/**
 * 活动内容类型
 *
 * <AUTHOR>
 * @date 2023-07-31 17:02
 */
public enum ActivInfoViewType {
    ACTIV_SCHEDULE(1, "活动日程"),
    ACTIV_RECORD(2, "活动回看");

    private Integer code;
    private String value;

    ActivInfoViewType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
