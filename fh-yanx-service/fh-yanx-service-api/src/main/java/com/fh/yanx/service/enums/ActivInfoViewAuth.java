package com.fh.yanx.service.enums;

/**
 * 观看权限
 *
 * <AUTHOR>
 * @date 2023-08-14 15:57
 */
public enum ActivInfoViewAuth {
    CAN_VIEW(1, "可以观看"),
    NO_VIEW_AUTH(2, "无权限观看");

    private Integer code;
    private String value;

    ActivInfoViewAuth(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
