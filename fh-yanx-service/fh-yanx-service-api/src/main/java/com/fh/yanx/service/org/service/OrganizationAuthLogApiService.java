package com.fh.yanx.service.org.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.org.api.OrganizationAuthLogApi;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogBo;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 组织认证日志记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
@FeignClient(contextId = "organizationAuthLogApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = OrganizationAuthLogApiService.OrganizationAuthLogApiFallbackFactory.class)
@Component
public interface OrganizationAuthLogApiService extends OrganizationAuthLogApi {

    @Component
    class OrganizationAuthLogApiFallbackFactory implements FallbackFactory<OrganizationAuthLogApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(OrganizationAuthLogApiFallbackFactory.class);
        @Override
        public OrganizationAuthLogApiService create(Throwable cause) {
            OrganizationAuthLogApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new OrganizationAuthLogApiService() {
                public AjaxResult getOrganizationAuthLogPageListByCondition(OrganizationAuthLogConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getOrganizationAuthLogListByCondition(OrganizationAuthLogConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addOrganizationAuthLog(OrganizationAuthLogBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateOrganizationAuthLog(OrganizationAuthLogBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}