package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.PLessonCasesConditionBo;
import com.fh.yanx.service.course.entity.bo.PLessonCasesBo;
import com.fh.yanx.service.course.entity.vo.PLessonCasesVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课例表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PLessonCasesApi {

    /**
     * 查询课例表分页列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/lesson/cases/page/list")
    public AjaxResult<PageInfo<PLessonCasesVo>> getPLessonCasesPageListByCondition(@RequestBody PLessonCasesConditionBo condition);

    /**
     * 查询课例表列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/lesson/cases/list")
    public AjaxResult<List<PLessonCasesVo>> getPLessonCasesListByCondition(@RequestBody PLessonCasesConditionBo condition);


    /**
     * 新增课例表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/lesson/cases/add")
    public AjaxResult addPLessonCases(@Validated @RequestBody PLessonCasesBo pLessonCasesBo);

    /**
     * 修改课例表
     * @param pLessonCasesBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/lesson/cases/update")
    public AjaxResult updatePLessonCases(@Validated @RequestBody PLessonCasesBo pLessonCasesBo);

    /**
     * 查询课例表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/lesson/cases/detail")
    public AjaxResult<PLessonCasesVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课例表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/lesson/cases/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
