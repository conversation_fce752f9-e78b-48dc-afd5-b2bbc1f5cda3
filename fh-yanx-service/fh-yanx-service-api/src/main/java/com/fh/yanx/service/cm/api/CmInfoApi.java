package com.fh.yanx.service.cm.api;

import com.fh.yanx.service.cm.entity.bo.CmInfoConditionBo;
import com.fh.yanx.service.cm.entity.bo.CmInfoBo;
import com.fh.yanx.service.cm.entity.vo.CmInfoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 代理商信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-16 11:35:44
 */
public interface CmInfoApi {

    /**
     * 查询代理商信息表分页列表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/cm/info/page/list")
    public AjaxResult<PageInfo<CmInfoVo>> getCmInfoPageListByCondition(@RequestBody CmInfoConditionBo condition);

    /**
     * 查询代理商信息表列表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/cm/info/list")
    public AjaxResult<List<CmInfoVo>> getCmInfoListByCondition(@RequestBody CmInfoConditionBo condition);

    /**
     * 新增代理商信息表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/cm/info/add")
    public AjaxResult addCmInfo(@Validated @RequestBody CmInfoBo cmInfoBo);

    /**
     * 新增代理商信息表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/cm/info/add-batch")
    public AjaxResult addCmInfoBatch(@RequestBody List<CmInfoBo> cmInfoBos);

    /**
     * 修改代理商信息表
     * 
     * @param cmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/cm/info/update")
    public AjaxResult updateCmInfo(@Validated @RequestBody CmInfoBo cmInfoBo);

    /**
     * 查询代理商信息表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @GetMapping("/cm/info/detail")
    public AjaxResult<CmInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除代理商信息表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @GetMapping("/cm/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 根据adminOid修改：姓名和手机号
     * 
     * @param cmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/cm/info/update-byadmin")
    public AjaxResult updateCmInfoByAdmin(@RequestBody CmInfoBo cmInfoBo);
}
