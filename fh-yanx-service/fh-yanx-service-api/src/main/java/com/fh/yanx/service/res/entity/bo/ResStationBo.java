package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 工作站和学校表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResStationBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long stationId;

	/**
	 * 工作站名称
	 */
	@ApiModelProperty("工作站名称")
	private String stationName;

	/**
	 * 工作站介绍
	 */
	@ApiModelProperty("工作站介绍")
	private String stationDesc;

	/**
	 * logo图片文件原始名称-带后缀
	 */
	@ApiModelProperty("logo图片文件原始名称-带后缀")
	private String logoFileNameOri;

	/**
	 * logo图片文件名称-不带后缀
	 */
	@ApiModelProperty("logo图片文件名称-不带后缀")
	private String logoFileName;

	/**
	 * logo图片文件地址
	 */
	@ApiModelProperty("logo图片文件地址")
	private String logoFileUrl;

	/**
	 * 工作站类型：1工作站，2学校
	 */
	@ApiModelProperty("工作站类型：1工作站，2学校")
	private Integer stationType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
