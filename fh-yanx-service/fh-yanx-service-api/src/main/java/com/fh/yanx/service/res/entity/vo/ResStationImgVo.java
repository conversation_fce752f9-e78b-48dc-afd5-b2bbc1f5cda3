package com.fh.yanx.service.res.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 工作站介绍图片表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResStationImgVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 工作站id
     */
    @ApiModelProperty("工作站id")
    private Long stationId;

    /**
     * 图片顺序
     */
    @ApiModelProperty("图片顺序")
    private Long imgIndex;

    /**
     * 工作站介绍图片文件原始名称-带后缀
     */
    @ApiModelProperty("工作站介绍图片文件原始名称-带后缀")
    private String imgFileNameOri;

    /**
     * 工作站介绍图片文件名称-不带后缀
     */
    @ApiModelProperty("工作站介绍图片文件名称-不带后缀")
    private String imgFileName;

    /**
     * 工作站介绍图片文件地址
     */
    @ApiModelProperty("工作站介绍图片文件地址")
    private String imgFileUrl;

    /**
     * 工作站介绍图片点击链接
     */
    @ApiModelProperty("工作站介绍图片点击链接")
    private String imgFileUrlLink;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

}
