package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 用户待办事项
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
public class ResTopicTodoBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long todoId;

    /**
     * 待办事项类型：
     */
    @ApiModelProperty("待办事项类型：")
    private Integer todoType;

    /**
     * 待办事项名称或操作：
     */
    @ApiModelProperty("待办事项名称或操作：")
    private String todoName;

    /**
     * 待办事项key：
     */
    @ApiModelProperty("待办事项key：")
    private String todoKey;

    /**
     * 待办事项消息
     */
    @ApiModelProperty("待办事项消息")
    private String todoMsg;

    /**
     * 待办事项业务类型：1课题
     */
    @ApiModelProperty("待办事项业务类型：1课题")
    private Integer todoBusinessType;

    /**
     * 待办事项业务主键id：例如课题id，和上面的todo_business_type对应
     */
    @ApiModelProperty("待办事项业务主键id：例如课题id，和上面的todo_business_type对应")
    private Long todoBusinessId;

    /**
     * 待办事项内容，json字符串
     */
    @ApiModelProperty("待办事项内容，json字符串")
    private String todoJson;

    /**
     * 待办事项用户
     */
    @ApiModelProperty("待办事项用户")
    private String userOid;

    /**
     * 待办状态：1待办，2已办理
     */
    @ApiModelProperty("待办状态：1待办，2已办理")
    private Integer todoStatus;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 待办数量，用于合并展示时候使用
     */
    @ApiModelProperty("待办数量，用于合并展示时候使用")
    private Integer todoNum;

    /**
     * todoTypes
     */
    private List<Integer> todoTypes;

}
