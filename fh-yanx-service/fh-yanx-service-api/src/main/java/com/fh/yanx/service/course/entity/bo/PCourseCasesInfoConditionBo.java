package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 校本课程案例详细信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
public class PCourseCasesInfoConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;

	/**
	 * 背景
	 */
	@ApiModelProperty("背景")
	private String background;

	/**
	 * 目标
	 */
	@ApiModelProperty("目标")
	private String goal;

	/**
	 * 内容
	 */
	@ApiModelProperty("内容")
	private String content;

	/**
	 * 评价
	 */
	@ApiModelProperty("评价")
	private String eval;

	/**
	 * 实施信息
	 */
	@ApiModelProperty("实施信息")
	private String operationInfo;

	/**
	 * 校本课程建设经验
	 */
	@ApiModelProperty("校本课程建设经验")
	private String exp;

	/**
	 * 校本课程建设成效
	 */
	@ApiModelProperty("校本课程建设成效")
	private String effect;

	/**
	 * 问题困难
	 */
	@ApiModelProperty("问题困难")
	private String problem;

	/**
	 * 课程框架图
	 */
	@ApiModelProperty("课程框架图")
	private String structure;

	/**
	 * 实施案例
	 */
	@ApiModelProperty("实施案例")
	private String teacherCaseInfo;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String teacherCaseName;

	/**
	 * 0-未删除 1-已删除
	 */
	@ApiModelProperty("0-未删除 1-已删除")
	private Integer isDelete;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createDate;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateDate;

	/**
	 * 观看权限（数字,分隔） 2-未认证学校用户 3-一认证学校用户 4-案例持有者
	 */
	@ApiModelProperty("观看权限")
	private String viewPermission;


	/**
	 * 是否增补，0：不增补，1：增补
	 */
	@ApiModelProperty("是否增补，0：不增补，1：增补")
	private Integer isSupplement;

	@ApiModelProperty("育人理念")
	private String eduPhilosophy;
}
