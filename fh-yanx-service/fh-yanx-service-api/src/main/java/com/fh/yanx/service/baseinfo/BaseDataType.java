package com.fh.yanx.service.baseinfo;

/**
 * 基础服务名称类型（基础服务指：组织机构、校区、省市区、年级、班级、用户、教师、学生、科目、任教等）
 */
public enum BaseDataType {

    /**
     * 公共cloud服务
     */
    CLOUD_BASE("cloud_base"),

    /**
     * 研学里面的基础服务
     */
    YANX_BASE("yanx_base");

    private String name;

    BaseDataType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
