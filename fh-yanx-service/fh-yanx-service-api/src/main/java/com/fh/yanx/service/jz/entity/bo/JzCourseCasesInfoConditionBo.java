package com.fh.yanx.service.jz.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 金中-校本课程案例详细信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@Data
public class JzCourseCasesInfoConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;

	/**
	 * 背景
	 */
	@ApiModelProperty("背景")
	private String background;

	/**
	 * 目标
	 */
	@ApiModelProperty("目标")
	private String goal;

	/**
	 * 内容
	 */
	@ApiModelProperty("内容")
	private String content;

	/**
	 * 评价
	 */
	@ApiModelProperty("评价")
	private String eval;

	/**
	 * 实施信息
	 */
	@ApiModelProperty("实施信息")
	private String operationInfo;

	/**
	 * 校本课程建设经验
	 */
	@ApiModelProperty("校本课程建设经验")
	private String exp;

	/**
	 * 校本课程建设成效
	 */
	@ApiModelProperty("校本课程建设成效")
	private String effect;

	/**
	 * 问题困难
	 */
	@ApiModelProperty("问题困难")
	private String problem;

	/**
	 * 课程框架图
	 */
	@ApiModelProperty("课程框架图")
	private String structure;

	/**
	 * 实施案例
	 */
	@ApiModelProperty("实施案例")
	private String teacherCaseInfo;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String teacherCaseName;

	/**
	 * 0-未删除 1-已删除
	 */
	@ApiModelProperty("0-未删除 1-已删除")
	private Integer isDelete;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createDate;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateDate;

}
