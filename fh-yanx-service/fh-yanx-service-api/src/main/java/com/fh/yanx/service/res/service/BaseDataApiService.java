package com.fh.yanx.service.res.service;

import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.org.entity.bo.OrganizationBoExt;
import com.fh.yanx.service.org.entity.bo.OrganizationExtConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationVoExt;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.account.entity.bo.AccountBo;
import com.light.user.admin.entity.bo.AdminBo;
import com.light.user.admin.entity.bo.AdminConditionBo;
import com.light.user.organization.entity.bo.*;
import com.light.user.organization.entity.vo.OrganizationSettingVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.bo.UserTransferBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/12 19:24
 */
@FeignClient(contextId = "baseDataApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = BaseDataApiService.BaseDataApiFallbackFactory.class)
@Component
public interface BaseDataApiService extends BaseDataApi {

    @Component
    class BaseDataApiFallbackFactory implements FallbackFactory<BaseDataApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(BaseDataApiService.BaseDataApiFallbackFactory.class);

        @Override
        public BaseDataApiService create(Throwable cause) {
            BaseDataApiService.BaseDataApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new BaseDataApiService() {
                @Override
                public AjaxResult<TeacherVo> getTeacherVoByUserOid(String userOid) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult<StudentVo> getStudentVoByUserOid(String userOid) {
                    return  AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult getSubjectList() {
                    return  AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult listValueByTypes(List<String> dictTypes) {
                    return  AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult<Long> addOrganization(OrganizationBo organizationBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult updateOrganization(OrganizationBoExt organizationBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult<OrganizationVoExt> getOrganizationDetail(Long organizaitonId) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult getOrganizationListByCondition(OrganizationExtConditionBo conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult<OrganizationVo> getOrganizationByCondition(OrganizationConditionBo conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult addOrUpdateOrganizationSetting(OrganizationSettingBo organizationSettingBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult<OrganizationSettingVo> getOrganizationSetting(OrganizationSettingConditionBo conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult changePassword(String oldPassword, String password) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult updateAccount(AccountBo accountBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult updateUser(UserBo userBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult resetPassword(Long accountId) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult addTeacher(TeacherBo teacherBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult updateTeacher(TeacherBo teacherBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult getTeacherDetail(String userOid) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult delTeacher(Long teacherId) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult getTeacherList(TeacherConditionBo conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult resetPasswordByUserOid(String userOid) {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult getCurrentUser() {
                    return AjaxResult.fail("基础服务service调用失败");
                }

                @Override
                public AjaxResult removeOutTeacher(UserTransferBo userTransferBo) {
                    return AjaxResult.fail("基础服务service调用失败-转校");
                }

                @Override
                public AjaxResult getAdminListByCondition(AdminConditionBo conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败-获取管理员列表");
                }

                @Override
                public AjaxResult getAdminPageListByCondition(AdminConditionBo conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败-获取管理员分页列表");
                }

                @Override
                public AjaxResult addAdmin(AdminBo adminBo) {
                    return AjaxResult.fail("基础服务service调用失败-添加管理员");
                }

                @Override
                public AjaxResult updateAdmin(AdminBo adminBo) {
                    return AjaxResult.fail("基础服务service调用失败-修改管理员");
                }

                @Override
                public AjaxResult delAdmin(Long adminId) {
                    return AjaxResult.fail("基础服务service调用失败-删除管理员");
                }

                @Override
                public AjaxResult getAdminDetail(Long adminId) {
                    return AjaxResult.fail("基础服务service调用失败-获取管理员详情");
                }

                @Override
                public AjaxResult enableAdmin(Long adminId, Integer status) {
                    return AjaxResult.fail("基础服务service调用失败-启用禁用管理员");
                }

                @Override
                public AjaxResult resetAdminPassword(Long adminId, String initPwd) {
                    return AjaxResult.fail("基础服务service调用失败-重置管理员密码");
                }

                @Override
                public AjaxResult getAllYanxRoles() {
                    return AjaxResult.fail("基础服务service调用失败-获取所有新时代运营的角色");
                }

                @Override
                public AjaxResult receptionRoles() {
                    return AjaxResult.fail("基础服务service调用失败-获取新时代案例持有者角色");
                }

                @Override
                public AjaxResult expertRoles() {
                    return AjaxResult.fail("基础服务service调用失败-获取新时代专家角色");
                }

                @Override
                public AjaxResult expertAssistantRoles() {
                    return AjaxResult.fail("基础服务service调用失败-获取新时代专家助理角色");
                }

                @Override
                public AjaxResult addUserAndGeneratorAccount(UserBo userBo) {
                    return AjaxResult.fail("基础服务service调用失败-新增用户并创建装好失败");
                }

                @Override
                public AjaxResult updateExpertUser(UserBo userBo) {
                    return AjaxResult.fail("基础服务service调用失败-编辑专家失败");
                }

                @Override
                public AjaxResult userList(UserConditionBoExt conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败-获取用户列表失败");
                }

                @Override
                public AjaxResult getUserDetail(String userOid) {
                    return AjaxResult.fail("基础服务service调用失败-获取用户详情失败");
                }

                @Override
                public AjaxResult expertAssistantList(UserConditionBoExt conditionBo) {
                    return AjaxResult.fail("基础服务service调用失败-获取专家助理列表失败");
                }

                @Override
                public AjaxResult delUser(String userOid) {
                    return AjaxResult.fail("基础服务service调用失败-删除用户失败");
                }

                @Override
                public AjaxResult delOrganization(Long organizationId) {
                    return AjaxResult.fail("基础服务service调用失败-删除组织失败");
                }

                @Override
                public AjaxResult changePhone(UserBo userBo) {
                    return AjaxResult.fail("基础服务service调用失败-修改手机号失败");
                }

                @Override
                public AjaxResult addBatchTeacher(List<TeacherBo> teacherBos) {
                    return AjaxResult.fail("基础服务service调用失败-批量新增教师失败");
                }

                @Override
                public AjaxResult addBatchUser(List<UserBo> userBos) {
                    return AjaxResult.fail("基础服务service调用失败-批量新增用户失败");
                }

                @Override
                public AjaxResult expertAndCaseOwnerRoles() {
                    return AjaxResult.fail("基础服务service调用失败-获取专家和案例持有者角色列表失败");
                }
            };
        }
    }
}
