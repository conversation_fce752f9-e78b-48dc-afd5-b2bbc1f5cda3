package com.fh.yanx.service.activ.api;


import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoShowBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 新时代文化校园活动信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
public interface ActivInfoApi {

    /**
     * 查询新时代文化校园活动信息表分页列表
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
    @PostMapping("/activ/info/page/list")
    public AjaxResult<PageInfo<ActivInfoVo>> getActivInfoPageListByCondition(@RequestBody ActivInfoConditionBo condition);

    /**
     * 查询新时代文化校园活动信息表列表
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
    @PostMapping("/activ/info/list")
    public AjaxResult<List<ActivInfoVo>> getActivInfoListByCondition(@RequestBody ActivInfoConditionBo condition);


    /**
     * 新增新时代文化校园活动信息表
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
    @PostMapping("/activ/info/add")
    public AjaxResult addActivInfo(@Validated @RequestBody ActivInfoBo activInfoBo);

    /**
     * 修改新时代文化校园活动信息表
     * @param activInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
    @PostMapping("/activ/info/update")
    public AjaxResult updateActivInfo(@Validated @RequestBody ActivInfoBo activInfoBo);

    /**
     * 查询新时代文化校园活动信息表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
    @GetMapping("/activ/info/detail")
    public AjaxResult<ActivInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id,
                                             @RequestParam(value = "activType", required = false) Integer activType);


    /**
     * 删除新时代文化校园活动信息表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:34:04
     */
    @GetMapping("/activ/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 上下架
     *
     * @param activInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/5 9:36
     **/
    @PostMapping("/activ/info/update-activ-type")
    public AjaxResult updateActivType(@RequestBody ActivInfoBo activInfoBo);

    /**
     * 设置活动展示
     *
     * @param activInfoShowBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/1 10:33
     **/
    @PostMapping("/activ/info/set-show")
    public AjaxResult setActivInfoShow(@RequestBody ActivInfoShowBo activInfoShowBo);

    /**
     * 用户活动列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/8 14:22
     **/
    @PostMapping("/activ/info/user-activ-list")
    public AjaxResult<PageInfo<ActivInfoVo>> getUserActivList(@RequestBody ActivInfoConditionBo conditionBo);
}
