package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesGradeEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesGradeEditionVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 校本课程案例年级版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:00
 */
public interface CourseCasesGradeEditionApi {

    /**
     * 查询校本课程案例年级版本记录分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
    @PostMapping("/course/cases/grade/edition/page/list")
    public AjaxResult<PageInfo<CourseCasesGradeEditionVo>> getCourseCasesGradeEditionPageListByCondition(@RequestBody CourseCasesGradeEditionConditionBo condition);

    /**
     * 查询校本课程案例年级版本记录列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
    @PostMapping("/course/cases/grade/edition/list")
    public AjaxResult<List<CourseCasesGradeEditionVo>> getCourseCasesGradeEditionListByCondition(@RequestBody CourseCasesGradeEditionConditionBo condition);


    /**
     * 新增校本课程案例年级版本记录
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
    @PostMapping("/course/cases/grade/edition/add")
    public AjaxResult addCourseCasesGradeEdition(@Validated @RequestBody CourseCasesGradeEditionBo courseCasesGradeEditionBo);

    /**
     * 修改校本课程案例年级版本记录
     * @param courseCasesGradeEditionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
    @PostMapping("/course/cases/grade/edition/update")
    public AjaxResult updateCourseCasesGradeEdition(@Validated @RequestBody CourseCasesGradeEditionBo courseCasesGradeEditionBo);

    /**
     * 查询校本课程案例年级版本记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
    @GetMapping("/course/cases/grade/edition/detail")
    public AjaxResult<CourseCasesGradeEditionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除校本课程案例年级版本记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:00
     */
    @GetMapping("/course/cases/grade/edition/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
