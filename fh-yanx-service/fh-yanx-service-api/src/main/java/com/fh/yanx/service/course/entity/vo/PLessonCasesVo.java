package com.fh.yanx.service.course.entity.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 课例表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
public class PLessonCasesVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 封面
     */
    @ApiModelProperty("封面")
    private String cover;

    /**
     * 课例实验区id
     */
    @ApiModelProperty("课例实验区id")
    private Long lcZone;

    /**
     * 课例科目
     */
    @ApiModelProperty("课例科目")
    private Long lcSubject;

    /**
     * 媒体id
     */
    @ApiModelProperty("媒体id")
    private String mediaId;

    /**
     * 数据来源：1汇景数据
     */
    @ApiModelProperty("数据来源：1汇景数据")
    private Integer sourceType;

    /**
     * 
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("")
    private Date createTime;

    /**
     * 
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("")
    private Date updateTime;

    /**
     * 
     */
    @ApiModelProperty("")
    private String createUserOid;

    /**
     * 
     */
    @ApiModelProperty("")
    private String remark;

    /**
     * 组织ID
     */
    @ApiModelProperty("组织ID")
    private Long organizationId;

    /**
     * 删除状态
     */
    @ApiModelProperty("删除状态")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public PLessonCasesVo returnOwn() {
        return this;
    }


    @ApiModelProperty(value = "课例实验区名称")
    private String lcZoneName;

    @ApiModelProperty(value = "课例科目名称")
    private String lcSubjectName;
}
