package com.fh.yanx.service.course.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 课程推荐表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
@Data
public class CourseRecommendVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 校本课程案例ID
     */
    @ApiModelProperty("校本课程案例ID")
    private Long casesId;

    /**
     * 用户oid
     */
    @ApiModelProperty("用户oid")
    private String userOid;

    /**
     * 推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）
     */
    @ApiModelProperty("推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）")
    private Integer recommendType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public CourseRecommendVo returnOwn() {
        return this;
    }

}
