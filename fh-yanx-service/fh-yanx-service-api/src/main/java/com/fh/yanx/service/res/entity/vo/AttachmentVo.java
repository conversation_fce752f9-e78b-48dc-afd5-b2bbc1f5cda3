package com.fh.yanx.service.res.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/5
 */
@Data
public class AttachmentVo implements Serializable {
    /**
     * 课题id
     */
    @ApiModelProperty("课题id")
    private Long topicId;

    /**
     * 附件名称
     */
    @ApiModelProperty("附件名称")
    private String name;

    /**
     * 附件文件原始名称-带后缀
     */
    @ApiModelProperty("附件文件原始名称-带后缀")
    private String fileNameOri;

    /**
     * 附件文件名称-不带后缀
     */
    @ApiModelProperty("附件文件名称-不带后缀")
    private String fileName;

    /**
     * 附件文件地址
     */
    @ApiModelProperty("附件文件地址")
    private String fileUrl;

    /**
     * 结题答辩附件类型，1：结题论文，2：答辩材料
     */
    @ApiModelProperty("结题答辩附件类型")
    private Integer repType;
}
