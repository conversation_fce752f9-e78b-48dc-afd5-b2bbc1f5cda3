package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResInformationApi;
import com.fh.yanx.service.res.entity.bo.ResInformationBo;
import com.fh.yanx.service.res.entity.bo.ResInformationConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 资讯
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
@FeignClient(contextId = "resInformationApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResInformationApiService.ResInformationApiFallbackFactory.class)
@Component
public interface ResInformationApiService extends ResInformationApi {

    @Component
    class ResInformationApiFallbackFactory implements FallbackFactory<ResInformationApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResInformationApiFallbackFactory.class);

        @Override
        public ResInformationApiService create(Throwable cause) {
            ResInformationApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResInformationApiService() {
                public AjaxResult getResInformationPageListByCondition(ResInformationConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResInformationListByCondition(ResInformationConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResInformation(ResInformationBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResInformation(ResInformationBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}