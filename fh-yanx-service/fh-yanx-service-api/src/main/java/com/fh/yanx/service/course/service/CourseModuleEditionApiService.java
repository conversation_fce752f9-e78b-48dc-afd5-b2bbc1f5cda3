package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseModuleEditionApi;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程模块版本记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:17
 */
@FeignClient(contextId = "courseModuleEditionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseModuleEditionApiService.CourseModuleEditionApiFallbackFactory.class)
@Component
public interface CourseModuleEditionApiService extends CourseModuleEditionApi {

    @Component
    class CourseModuleEditionApiFallbackFactory implements FallbackFactory<CourseModuleEditionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseModuleEditionApiFallbackFactory.class);
        @Override
        public CourseModuleEditionApiService create(Throwable cause) {
            CourseModuleEditionApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseModuleEditionApiService() {
                public AjaxResult getCourseModuleEditionPageListByCondition(CourseModuleEditionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseModuleEditionListByCondition(CourseModuleEditionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseModuleEdition(CourseModuleEditionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseModuleEdition(CourseModuleEditionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}