package com.fh.yanx.service.pub.api;


import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryBo;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 融合出版书目录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface PubBookDirectoryApi {

    /**
     * 查询融合出版书目录分页列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/page/list")
    public AjaxResult<PageInfo<PubBookDirectoryVo>> getPubBookDirectoryPageListByCondition(@RequestBody PubBookDirectoryConditionBo condition);

    /**
     * 查询融合出版书目录列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/list")
    public AjaxResult<List<PubBookDirectoryVo>> getPubBookDirectoryListByCondition(@RequestBody PubBookDirectoryConditionBo condition);


    /**
     * 新增融合出版书目录
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/add")
    public AjaxResult addPubBookDirectory(@Validated @RequestBody PubBookDirectoryBo pubBookDirectoryBo);

    /**
     * 修改融合出版书目录
     * @param pubBookDirectoryBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/update")
    public AjaxResult updatePubBookDirectory(@Validated @RequestBody PubBookDirectoryBo pubBookDirectoryBo);

    /**
     * 查询融合出版书目录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @GetMapping("/pub/book/directory/detail")
    public AjaxResult<PubBookDirectoryVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除融合出版书目录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @GetMapping("/pub/book/directory/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
