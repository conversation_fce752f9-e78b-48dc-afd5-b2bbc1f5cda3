package com.fh.yanx.service.activ.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 新时代活动日程表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
@Data
public class ActivInfoScheduleVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long scheduleId;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activId;

    /**
     * 日程名称
     */
    @ApiModelProperty("日程名称")
    private String scheduleName;

    /**
     * 拉流地址
     */
    @ApiModelProperty("拉流地址")
    private String scheduleUrl;

    /**
     * 活动日程顺序
     */
    @ApiModelProperty("活动日程顺序")
    private Long scheduleIndex;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ActivInfoScheduleVo returnOwn() {
        return this;
    }

}
