package com.fh.yanx.service.activ.api;


import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 活动内容观看权限表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
public interface ActivInfoViewPermissionApi {

    /**
     * 查询活动内容观看权限表分页列表
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
    @PostMapping("/activ/info/view/permission/page/list")
    public AjaxResult<PageInfo<ActivInfoViewPermissionVo>> getActivInfoViewPermissionPageListByCondition(@RequestBody ActivInfoViewPermissionConditionBo condition);

    /**
     * 查询活动内容观看权限表列表
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
    @PostMapping("/activ/info/view/permission/list")
    public AjaxResult<List<ActivInfoViewPermissionVo>> getActivInfoViewPermissionListByCondition(@RequestBody ActivInfoViewPermissionConditionBo condition);


    /**
     * 新增活动内容观看权限表
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
    @PostMapping("/activ/info/view/permission/add")
    public AjaxResult addActivInfoViewPermission(@Validated @RequestBody ActivInfoViewPermissionBo activInfoViewPermissionBo);

    /**
     * 修改活动内容观看权限表
     * @param activInfoViewPermissionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
    @PostMapping("/activ/info/view/permission/update")
    public AjaxResult updateActivInfoViewPermission(@Validated @RequestBody ActivInfoViewPermissionBo activInfoViewPermissionBo);

    /**
     * 查询活动内容观看权限表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
    @GetMapping("/activ/info/view/permission/detail")
    public AjaxResult<ActivInfoViewPermissionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除活动内容观看权限表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-27 17:28:46
     */
    @GetMapping("/activ/info/view/permission/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
