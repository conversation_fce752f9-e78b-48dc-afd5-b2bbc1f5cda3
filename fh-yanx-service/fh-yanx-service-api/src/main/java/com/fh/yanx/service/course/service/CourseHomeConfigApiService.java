package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseHomeConfigApi;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigBo;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 推荐展示位
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@FeignClient(contextId = "courseHomeConfigApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseHomeConfigApiService.CourseHomeConfigApiFallbackFactory.class)
@Component
public interface CourseHomeConfigApiService extends CourseHomeConfigApi {

    @Component
    class CourseHomeConfigApiFallbackFactory implements FallbackFactory<CourseHomeConfigApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseHomeConfigApiFallbackFactory.class);
        @Override
        public CourseHomeConfigApiService create(Throwable cause) {
            CourseHomeConfigApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new CourseHomeConfigApiService() {
                public AjaxResult getCourseHomeConfigPageListByCondition(CourseHomeConfigConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseHomeConfigListByCondition(CourseHomeConfigConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseHomeConfig(CourseHomeConfigBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseHomeConfig(CourseHomeConfigBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult saveCourseHomeConfig(CourseHomeConfigBo courseHomeConfigBo) {
                    return AjaxResult.fail("保存失败");
                }
            };
        }
    }
}