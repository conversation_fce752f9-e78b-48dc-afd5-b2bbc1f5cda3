package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseModuleEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleEditionVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程模块版本记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:17
 */
public interface CourseModuleEditionApi {

    /**
     * 查询课程模块版本记录表分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
    @PostMapping("/course/module/edition/page/list")
    public AjaxResult<PageInfo<CourseModuleEditionVo>> getCourseModuleEditionPageListByCondition(@RequestBody CourseModuleEditionConditionBo condition);

    /**
     * 查询课程模块版本记录表列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
    @PostMapping("/course/module/edition/list")
    public AjaxResult<List<CourseModuleEditionVo>> getCourseModuleEditionListByCondition(@RequestBody CourseModuleEditionConditionBo condition);


    /**
     * 新增课程模块版本记录表
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
    @PostMapping("/course/module/edition/add")
    public AjaxResult addCourseModuleEdition(@Validated @RequestBody CourseModuleEditionBo courseModuleEditionBo);

    /**
     * 修改课程模块版本记录表
     * @param courseModuleEditionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
    @PostMapping("/course/module/edition/update")
    public AjaxResult updateCourseModuleEdition(@Validated @RequestBody CourseModuleEditionBo courseModuleEditionBo);

    /**
     * 查询课程模块版本记录表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
    @GetMapping("/course/module/edition/detail")
    public AjaxResult<CourseModuleEditionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程模块版本记录表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:17
     */
    @GetMapping("/course/module/edition/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
