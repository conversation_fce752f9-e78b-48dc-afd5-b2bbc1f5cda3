package com.fh.yanx.service.course.service;


import com.fh.yanx.service.course.api.PCourseCasesTypeApi;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本课程案例类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@FeignClient(contextId = "pCourseCasesTypeApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PCourseCasesTypeApiService.PCourseCasesTypeApiFallbackFactory.class)
@Component
public interface PCourseCasesTypeApiService extends PCourseCasesTypeApi {

    @Component
    class PCourseCasesTypeApiFallbackFactory implements FallbackFactory<PCourseCasesTypeApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PCourseCasesTypeApiFallbackFactory.class);
        @Override
        public PCourseCasesTypeApiService create(Throwable cause) {
            PCourseCasesTypeApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PCourseCasesTypeApiService() {
                public AjaxResult getPCourseCasesTypePageListByCondition(PCourseCasesTypeConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPCourseCasesTypeListByCondition(PCourseCasesTypeConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPCourseCasesType(PCourseCasesTypeBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePCourseCasesType(PCourseCasesTypeBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}