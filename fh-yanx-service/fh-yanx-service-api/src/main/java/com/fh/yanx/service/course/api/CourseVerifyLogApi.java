package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程审核流水表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
public interface CourseVerifyLogApi {

    /**
     * 查询课程审核流水表分页列表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/verify/log/page/list")
    public AjaxResult<PageInfo<CourseVerifyLogVo>> getCourseVerifyLogPageListByCondition(@RequestBody CourseVerifyLogConditionBo condition);

    /**
     * 查询课程审核流水表列表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/verify/log/list")
    public AjaxResult<List<CourseVerifyLogVo>> getCourseVerifyLogListByCondition(@RequestBody CourseVerifyLogConditionBo condition);


    /**
     * 新增课程审核流水表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/verify/log/add")
    public AjaxResult addCourseVerifyLog(@Validated @RequestBody CourseVerifyLogBo courseVerifyLogBo);

    /**
     * 修改课程审核流水表
     * @param courseVerifyLogBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/verify/log/update")
    public AjaxResult updateCourseVerifyLog(@Validated @RequestBody CourseVerifyLogBo courseVerifyLogBo);

    /**
     * 查询课程审核流水表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @GetMapping("/course/verify/log/detail")
    public AjaxResult<CourseVerifyLogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程审核流水表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @GetMapping("/course/verify/log/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课程审核流水表并保存（根据：cases_id，verify_process_type，user_oid删除再增加）
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -03-11 13:53:16
     */
    @PostMapping("/course/verify/log/delete-save")
    public AjaxResult deleteAndSave(@RequestBody CourseVerifyLogBo courseVerifyLogBo);

}
