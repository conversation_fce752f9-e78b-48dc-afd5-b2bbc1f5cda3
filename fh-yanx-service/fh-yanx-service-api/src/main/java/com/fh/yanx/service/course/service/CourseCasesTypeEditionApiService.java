package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseCasesTypeEditionApi;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesTypeEditionConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本课程案例类型版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:20
 */
@FeignClient(contextId = "courseCasesTypeEditionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseCasesTypeEditionApiService.CourseCasesTypeEditionApiFallbackFactory.class)
@Component
public interface CourseCasesTypeEditionApiService extends CourseCasesTypeEditionApi {

    @Component
    class CourseCasesTypeEditionApiFallbackFactory implements FallbackFactory<CourseCasesTypeEditionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseCasesTypeEditionApiFallbackFactory.class);
        @Override
        public CourseCasesTypeEditionApiService create(Throwable cause) {
            CourseCasesTypeEditionApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseCasesTypeEditionApiService() {
                public AjaxResult getCourseCasesTypeEditionPageListByCondition(CourseCasesTypeEditionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseCasesTypeEditionListByCondition(CourseCasesTypeEditionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseCasesTypeEdition(CourseCasesTypeEditionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseCasesTypeEdition(CourseCasesTypeEditionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}