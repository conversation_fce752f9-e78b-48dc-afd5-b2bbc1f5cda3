package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseModuleConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleBo;
import com.fh.yanx.service.course.entity.vo.CourseModuleVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程模块
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
public interface CourseModuleApi {

    /**
     * 查询课程模块分页列表
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/page/list")
    public AjaxResult<PageInfo<CourseModuleVo>> getCourseModulePageListByCondition(@RequestBody CourseModuleConditionBo condition);

    /**
     * 查询课程模块列表
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/list")
    public AjaxResult<List<CourseModuleVo>> getCourseModuleListByCondition(@RequestBody CourseModuleConditionBo condition);


    /**
     * 新增课程模块
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/add")
    public AjaxResult addCourseModule(@Validated @RequestBody CourseModuleBo courseModuleBo);

    /**
     * 修改课程模块
     * @param courseModuleBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @PostMapping("/course/module/update")
    public AjaxResult updateCourseModule(@Validated @RequestBody CourseModuleBo courseModuleBo);

    /**
     * 查询课程模块详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @GetMapping("/course/module/detail")
    public AjaxResult<CourseModuleVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程模块
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-15 09:49:48
     */
    @GetMapping("/course/module/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
