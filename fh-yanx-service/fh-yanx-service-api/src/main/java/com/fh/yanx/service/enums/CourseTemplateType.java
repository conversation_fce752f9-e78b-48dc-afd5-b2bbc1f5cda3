package com.fh.yanx.service.enums;

/**
 * 模板类型：默认空，1特色课程模板，2整体课程模板
 *
 * <AUTHOR>
 */
public enum CourseTemplateType {
    /**
     * 普通活动
     */
    DEFAULT(null),
    /**
     * 特色课程
     */
    CHARACTERISTIC(1),
    /**
     * 整体课程
     */
    INTEGRAL(2),
    ;

    private Integer value;

    CourseTemplateType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}