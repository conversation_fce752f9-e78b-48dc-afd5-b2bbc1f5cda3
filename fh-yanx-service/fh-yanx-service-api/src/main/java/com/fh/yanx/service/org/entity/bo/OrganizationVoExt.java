package com.fh.yanx.service.org.entity.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.light.user.organization.entity.bo.OrganizationBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Data
public class OrganizationVoExt extends OrganizationBo {

    /**
     * 平台名称  (副标题)
     */
    @ApiModelProperty("平台名称 (副标题)")
    private String webName;
    /**
     * logo
     */
    @ApiModelProperty("logo")
    private String logo;

    /**
     * 其他配置（建校日期）
     */
    @ApiModelProperty("其他配置（建校日期）")
    private String otherConfig;

    /**
     * 超级管理员账户名
     */
    @ApiModelProperty("超级管理员账户名")
    private String accountName;

    /**
     * 超级管理员账户id
     */
    @ApiModelProperty("超级管理员账户id")
    private Long accountId;

    /**
     * 设备密码
     */
    @ApiModelProperty("设备密码")
    private String screenPwdValue;

    /**
     * 认证状态 1-已认证 2-未认证
     */
    @ApiModelProperty("认证状态 1-已认证 2-未认证")
    private Integer authType;

    /**
     * 认证截止时间
     */
    @ApiModelProperty("认证截止时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date authEndDay;

    /**
     * 父机构名称
     */
    private String parentName;
}
