package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicProcessRecordApi;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课题流程记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resTopicProcessRecordApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicProcessRecordApiService.ResTopicProcessRecordApiFallbackFactory.class)
@Component
public interface ResTopicProcessRecordApiService extends ResTopicProcessRecordApi {

    @Component
    class ResTopicProcessRecordApiFallbackFactory implements FallbackFactory<ResTopicProcessRecordApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicProcessRecordApiFallbackFactory.class);

        @Override
        public ResTopicProcessRecordApiService create(Throwable cause) {
            ResTopicProcessRecordApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicProcessRecordApiService() {
                public AjaxResult getResTopicProcessRecordPageListByCondition(ResTopicProcessRecordConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicProcessRecordListByCondition(ResTopicProcessRecordConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicProcessRecord(ResTopicProcessRecordBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicProcessRecord(ResTopicProcessRecordBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}