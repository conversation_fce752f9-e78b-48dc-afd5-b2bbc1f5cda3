package com.fh.yanx.service.course.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: liu<PERSON>yu
 * @CreateTime: 2024-12-05  15:10
 */
@Data
public class CourseCasesVerifyLogAndEditionVo {

    @ApiModelProperty("业务id")
    private Long businessId;

    @ApiModelProperty("业务类型 1-专家审核 2-版本提交")
    private Integer businessType;

    @ApiModelProperty("审核进度类型")
    private Integer verifyProcessType;

    @ApiModelProperty("专家oid")
    private String expertUserOid;

    @ApiModelProperty("专家姓名")
    private String expertUserName;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    /**
     * 精品审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过
     */
    @ApiModelProperty("精品审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过")
    private Integer bestVerifyType;

    /**
     * 普通课程审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过
     */
    @ApiModelProperty("普通课程审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过")
    private Integer normalVerifyType;

    /**
     * 课程流水来源：1校本课程审核，2精品课程审核
     */
    private Integer courseVerifySource;

    /**
     * 申报人oid
     */
    private String applyUserOid;

    /**
     * 申报人姓名
     */
    private String applyUserName;

}
