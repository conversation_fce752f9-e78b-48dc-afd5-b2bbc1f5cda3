package com.fh.yanx.service.res.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 公开课表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
@Data
public class ResOpenCourseVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long openCourseId;

    /**
     * 研究附件名称
     */
    @ApiModelProperty("研究附件名称")
    private String openCourseName;

    /**
     * 论文摘要
     */
    @ApiModelProperty("论文摘要")
    private String openCourseDesc;

    /**
     * pv学习数，多少个人点开过
     */
    @ApiModelProperty("pv学习数，多少个人点开过")
    private Long openCourseView;

    /**
     * 公开课顺序
     */
    @ApiModelProperty("公开课顺序")
    private Long openCourseIndex;

    /**
     * 学校id
     */
    @ApiModelProperty("学校id")
    private Long organizationId;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String organizationName;

    /**
     * 上传人userOid
     */
    @ApiModelProperty("上传人userOid")
    private String userOid;

    /**
     * 上传人姓名
     */
    @ApiModelProperty("上传人姓名")
    private String realName;

    /**
     * 视频文件原始名称-带后缀
     */
    @ApiModelProperty("视频文件原始名称-带后缀")
    private String mediaFileNameOri;

    /**
     * 视频文件名称-不带后缀
     */
    @ApiModelProperty("视频文件名称-不带后缀")
    private String mediaFileName;

    /**
     * 视频文件地址
     */
    @ApiModelProperty("视频文件地址")
    private String mediaFileUrl;

    /**
     * 图片文件原始名称-带后缀
     */
    @ApiModelProperty("图片文件原始名称-带后缀")
    private String imgFileNameOri;

    /**
     * 图片文件名称-不带后缀
     */
    @ApiModelProperty("图片文件名称-不带后缀")
    private String imgFileName;

    /**
     * 图片文件地址
     */
    @ApiModelProperty("图片文件地址")
    private String imgFileUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

}
