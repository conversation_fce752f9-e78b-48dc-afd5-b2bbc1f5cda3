package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 课程推荐表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
@Data
public class CourseRecommendConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private List<Long> casesIdList;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 * 推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）
	 */
	@ApiModelProperty("推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）")
	private Integer recommendType;


	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
