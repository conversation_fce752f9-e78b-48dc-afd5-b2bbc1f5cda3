package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;

import com.light.core.entity.AjaxResult;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课题指导老师表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicTeacherApi {

    /**
     * 查询课题指导老师表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/teacher/page/list")
    public AjaxResult<PageInfo<ResTopicTeacherVo>> getResTopicTeacherPageListByCondition(@RequestBody ResTopicTeacherConditionBo condition);

    /**
     * 查询课题指导老师表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/teacher/list")
    public AjaxResult<List<ResTopicTeacherVo>> getResTopicTeacherListByCondition(@RequestBody ResTopicTeacherConditionBo condition);


    /**
     * 新增课题指导老师表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/teacher/add")
    public AjaxResult addResTopicTeacher(@Validated @RequestBody ResTopicTeacherBo resTopicTeacherBo);

    /**
     * 修改课题指导老师表
     * @param resTopicTeacherBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/teacher/update")
    public AjaxResult updateResTopicTeacher(@Validated @RequestBody ResTopicTeacherBo resTopicTeacherBo);

    /**
     * 查询课题指导老师表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/teacher/detail")
    public AjaxResult<ResTopicTeacherVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课题指导老师表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/teacher/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    @PostMapping("res/topic/teacher/teacher-list")
    AjaxResult getTeacherListByCondition(@RequestBody TeacherConditionBo condition);
}
