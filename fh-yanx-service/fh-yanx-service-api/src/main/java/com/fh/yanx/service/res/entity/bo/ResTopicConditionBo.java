package com.fh.yanx.service.res.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 课题表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long topicId;

	/**
	 * 课题名称
	 */
	@ApiModelProperty("课题名称")
	private String topicName;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private Long organizationId;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classesId;

	/**
	 * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定
	 */
	@ApiModelProperty("课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定")
	private Integer topicProcess;

	/**
	 * 当前提交人
	 */
	@ApiModelProperty("当前提交人")
	private String submitUser;

	/**
	 * 当前审批人
	 */
	@ApiModelProperty("当前审批人")
	private String verifyUser;

	/**
	 * 课题类别：待定
	 */
	@ApiModelProperty("课题类别：待定")
	private Integer topicType;

	/**
	 * 关联学科：待定（可能是type或者字典值），多个使用逗号分割
	 */
	@ApiModelProperty("关联学科：待定（可能是type或者字典值），多个使用逗号分割")
	private String relSubject;

	/**
	 * 课题介绍/界定
	 */
	@ApiModelProperty("课题介绍/界定")
	private String topicDesc;

	/**
	 * 课题背景
	 */
	@ApiModelProperty("课题背景")
	private String topicBack;

	/**
	 * 课题目的
	 */
	@ApiModelProperty("课题目的")
	private String topicGoal;

	/**
	 * 研究内容
	 */
	@ApiModelProperty("研究内容")
	private String topicContent;

	/**
	 * 研究方法
	 */
	@ApiModelProperty("研究方法")
	private String topicMethod;

	/**
	 * 研究条件
	 */
	@ApiModelProperty("研究条件")
	private String topicCondition;

	/**
	 * 研究计划
	 */
	@ApiModelProperty("研究计划")
	private String topicPlan;

	/**
	 * 研究预期成果
	 */
	@ApiModelProperty("研究预期成果")
	private String topicExpected;

	/**
	 * 最终评定结果：1通过，2不通过
	 */
	@ApiModelProperty("最终评定结果：1通过，2不通过")
	private Integer evaluateResult;

	/**
	 * 优秀课题类型：1不是优秀课题，2是优秀课题
	 */
	@ApiModelProperty("优秀课题类型：1不是优秀课题，2是优秀课题")
	private Integer excellentType;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 课题来源：1校内自建，2校外导入
	 */
	@ApiModelProperty("课题来源：1校内自建，2校外导入")
	private Integer topicSource;

	/**
	 * 校外课题指导老师信息
	 */
	@ApiModelProperty("校外课题指导老师信息")
	private String topicTutor;

	/**
	 * 校外课题id,第三方id
	 */
	@ApiModelProperty("校外课题id,第三方id")
	private String topicThirdId;

	/**
	 * 首页展示：1展示，2不展示
	 */
	@ApiModelProperty("首页展示：1展示，2不展示")
	private Integer homeShow;

	/**
	 * 课题第三方来源类型：1有方，2汇景
	 */
	@ApiModelProperty("课题第三方来源类型：1有方，2汇景")
	private Integer topicThirdType;

	/**
	 * 搜索关键字  课题名称和背景
	 */
	private String searchKey;

	/**
	 * 课题id集合
	 */
	private List<Long> topicIds;

	/**
	 * 关联学科字典值,逗号分隔
	 */
	@ApiModelProperty("关联学科字典值,逗号分隔")
	private String subjectCode;

	/**
	 * 教师审批列表，教师oid条件。teacherOid不为空则表示只查询该用户审批的数据
	 */
	@ApiModelProperty("教师审批列表，教师oid条件")
	private String teacherOid;

	/**
	 *教师审批列表，状态条件 2提交、教师审批中，3教师审核驳回，4教师审核通过
	 */
	@ApiModelProperty("教师审批列表，状态条件")
	private List topicProcessStatus;

	/**
	 * 根据是否是详情方法调用列表方法，设置检索列表是否检索优秀课题
	 */
	private Integer isDetailQuery;

	/**
	 * 当前登陆用户oid
	 */
	@ApiModelProperty("当前登陆用户oid")
	private String currentUserOid;

	/**
	 * 当前用户管理员标识
	 */
	@ApiModelProperty("当前用户管理员标识")
	private Boolean isAdmin;

}
