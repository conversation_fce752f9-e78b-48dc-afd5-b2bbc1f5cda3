package com.fh.yanx.service.bm.entity.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新时代活动列表导出的vo-征订
 *
 * <AUTHOR>
 * @date 2023 /6/16 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BmInfoExportZdVo implements Serializable {

    /**
     * 报名主键id
     */
    private Long infoId;

    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 20)
    private String orderNumber;

    /**
     * 提交时间
     */
    @Excel(name = "提交时间", width = 20)
    private String time;

    /**
     * 代理类型
     */
    @Excel(name = "代理类型", width = 10)
    private String cmTypeName;

    /**
     * 代理商名称
     */
    @Excel(name = "代理商名称", width = 15)
    private String cmRealName;

    /**
     * 渠道参数
     */
    @Excel(name = "渠道参数", width = 15)
    private String cm;

    /**
     * 联系人名称
     */
    @Excel(name = "联系人", width = 10)
    private String contactName;

    /**
     * 联系人手机
     */
    @Excel(name = "手机号码", width = 20)
    private String contactMobile;

    /**
     * 所在地区
     */
    @Excel(name = "所在地区", width = 20)
    private String address;

    /**
     * 书籍信息
     */
    @Excel(name = "书籍信息", width = 20)
    private String subBookName;

    /**
     * 征订数量
     */
    @Excel(name = "征订数量", width = 20)
    private Long subNumber;

    /**
     * 征订总价
     * 征订总价，改订单金额需同步修改
     */
    @Excel(name = "费用", width = 20)
    private BigDecimal subBookTotalPrice;

    /**
     * 确认状态
     */
    @Excel(name = "沟通状态", width = 10)
    private String submit;

    /**
     * 缴费状态
     */
    @Excel(name = "缴费状态", width = 10)
    private String payType;

    /**
     * 发票状态
     */
    @Excel(name = "发票状态", width = 10)
    private String invoiceType;

    /**
     * 单位
     */
    @Excel(name = "单位", width = 20)
    private String departName;

    /**
     * 收件人
     */
    @Excel(name = "收件人", width = 10)
    private String recName;

    /**
     * 收件人手机号码
     */
    @Excel(name = "收件人手机号码", width = 20)
    private String recMobile;

    /**
     * 配送地址
     */
    @Excel(name = "配送地址", width = 20)
    private String recAddress;

    /**
     * 微信支付流水号
     */
    @Excel(name = "微信支付流水号", width = 20)
    private String transactionId;

    /**
     * 发货状态 1-未发货 2-已发货
     */
    @Excel(name = "发货状态", width = 10)
    private String deliverState;

    /**
     * 物流单号
     */
    @Excel(name = "物流单号", width = 20)
    private String logisticsCode;

    /**
     * 物流公司
     */
    @Excel(name = "物流公司", width = 20)
    private String logisticsOrg;

    @Excel(name = "退款金额", width = 20)
    private BigDecimal refundAmount;

    @Excel(name = "退款方式", width = 20)
    private String refundWayType;

    @Excel(name = "退款时间", width = 20)
    private String refundTime;

    @Excel(name = "退款原因", width = 20)
    private String refundReason;
}
