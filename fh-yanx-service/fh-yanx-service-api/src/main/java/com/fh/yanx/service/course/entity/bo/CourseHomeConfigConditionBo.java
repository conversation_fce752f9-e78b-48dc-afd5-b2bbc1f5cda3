package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 推荐展示位
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Data
public class CourseHomeConfigConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long homeConfigId;

	/**
	 * 案例id
	 */
	@ApiModelProperty("案例id")
	private Long casesId;

	/**
	 * 展示首页的类型，1：大首页，2：校本课程平台首页
	 */
	@ApiModelProperty("展示首页的类型，1：大首页，2：校本课程平台首页")
	private Integer homeType;

	/**
	 * 校本课程案例类型，冗余字段：0课程案例，1典型案例，2精品课程，3样例课程
	 */
	@ApiModelProperty("校本课程案例类型，冗余字段：0课程案例，1典型案例，2精品课程，3样例课程")
	private Integer isExcellent;

	/**
	 * 展示顺序
	 */
	@ApiModelProperty("展示顺序")
	private Long homeIndex;

	/**
	 * 推荐说明
	 */
	@ApiModelProperty("推荐说明")
	private String recommendText;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 课程名称
	 */
	private String courseName;
}
