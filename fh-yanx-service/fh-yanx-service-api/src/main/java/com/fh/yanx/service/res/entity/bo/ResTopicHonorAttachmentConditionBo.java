package com.fh.yanx.service.res.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 课题荣誉表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicHonorAttachmentConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 课题id
	 */
	@ApiModelProperty("课题id")
	private Long topicId;

	/**
	 * 研究附件名称
	 */
	@ApiModelProperty("研究附件名称")
	private String honorName;

	/**
	 * 附件文件原始名称-带后缀
	 */
	@ApiModelProperty("附件文件原始名称-带后缀")
	private String honorFileNameOri;

	/**
	 * 附件文件名称-不带后缀
	 */
	@ApiModelProperty("附件文件名称-不带后缀")
	private String honorFileName;

	/**
	 * 附件文件地址
	 */
	@ApiModelProperty("附件文件地址")
	private String honorFileUrl;

	/**
	 * 文件oid
	 */
	private String honorFileOid;



	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
