package com.fh.yanx.service.activ.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 新时代文化校园活动内容观看记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
@Data
public class ActivInfoRecordViewConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 新时代文化校园活动内容表id
     */
    @ApiModelProperty("新时代文化校园活动内容表id")
    private Long recordId;

    /**
     * FK新时代文化校园活动信息表的id，冗余
     */
    @ApiModelProperty("FK新时代文化校园活动信息表的id，冗余")
    private Long activId;

    /**
     * 资源标题名称
     */
    @ApiModelProperty("资源标题名称")
    private String viewName;

    /**
     * 观看日期(点击了观看的日期)
     */
    @ApiModelProperty("观看日期(点击了观看的日期)")
    private Date viewDate;

    /**
     * 观看行为：1点击观看
     */
    @ApiModelProperty("观看行为：1点击观看")
    private Integer viewActionType;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 统计最近多少天的数据
     */
    private Integer statisticsDateTopCount;

}
