package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源或成果样例附件表-模块附件
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@Data
public class CourseModuleAttachmentConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long courseModuleAttachmentId;

    /**
     * 课程模块id
     */
    @ApiModelProperty("课程模块id")
    private Long courseModuleId;

    /**
     * 案例id，冗余
     */
    @ApiModelProperty("案例id，冗余")
    private Long casesId;

    /**
     * 资源类型：1本地视频文件，2视频地址，3文本
     */
    @ApiModelProperty("资源类型：1本地视频文件，2视频地址，3文本")
    private Integer resourceType;

    /**
     * 文件fileOid
     */
    @ApiModelProperty("文件fileOid")
    private String attachmentMediaId;

    /**
     * 压缩文件的文件fileOid
     */
    @ApiModelProperty("压缩文件的文件fileOid")
    private String attachmentMediaIdCompress;

    /**
     * 附件类型：1文档，2图片，3视频，4音频，5压缩包，6其他
     */
    @ApiModelProperty("附件类型：1文档，2图片，3视频，4音频，5压缩包，6其他")
    private Integer attachmentMediaType;

    /**
     * 媒体文件后缀，小写
     */
    @ApiModelProperty("媒体文件后缀，小写")
    private String attachmentMediaSuffix;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String attachmentMediaUrl;

    /**
     * 文档文件地址-压缩后地址
     */
    @ApiModelProperty("文档文件地址-压缩后地址")
    private String attachmentMediaUrlCompress;

    /**
     * 文件名称，不带后缀
     */
    @ApiModelProperty("文件名称，不带后缀")
    private String attachmentMediaName;

    /**
     * 文件名称，带后缀
     */
    @ApiModelProperty("文件名称，带后缀")
    private String attachmentMediaNameOri;

    /**
     * 文件转码状态：1未转码，2已转码，3转码失败
     */
    @ApiModelProperty("文件转码状态：1未转码，2已转码，3转码失败")
    private Integer attachmentMediaTransferType;

    /**
     * 文件第三方在线地址，可以支持直接填写地址展示
     */
    @ApiModelProperty("文件第三方在线地址，可以支持直接填写地址展示")
    private String attachmentMediaOnlineUrl;

    /**
     * 附件顺序：默认1
     */
    @ApiModelProperty("附件顺序：默认1")
    private Long attachmentMediaSort;

    /**
     * 资源下载类型：1不可下载，2可下载
     */
    @ApiModelProperty("资源下载类型：1不可下载，2可下载")
    private Integer downloadType;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 内容模块ids
     */
    private List<Long> courseModuleIds;

    /**
     * 前端生成提交存储，用于前端和布局里面的信息做映射
     */
    @ApiModelProperty("前端生成提交存储，用于前端和布局里面的信息做映射")
    private String uuid;
    /**
     * 用于多组文字显示
     */
    @ApiModelProperty("用于多组文字显示")
    private String content;
}
