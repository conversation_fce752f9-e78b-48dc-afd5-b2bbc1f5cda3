package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseWeightedScoreBo;
import com.fh.yanx.service.course.entity.vo.CourseWeightedScoreVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程加权分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
public interface CourseWeightedScoreApi {

    /**
     * 查询课程加权分表分页列表
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
    @PostMapping("/course/weighted/score/page/list")
    public AjaxResult<PageInfo<CourseWeightedScoreVo>> getCourseWeightedScorePageListByCondition(@RequestBody CourseWeightedScoreConditionBo condition);

    /**
     * 查询课程加权分表列表
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
    @PostMapping("/course/weighted/score/list")
    public AjaxResult<List<CourseWeightedScoreVo>> getCourseWeightedScoreListByCondition(@RequestBody CourseWeightedScoreConditionBo condition);


    /**
     * 新增课程加权分表
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
    @PostMapping("/course/weighted/score/add")
    public AjaxResult addCourseWeightedScore(@Validated @RequestBody CourseWeightedScoreBo courseWeightedScoreBo);

    /**
     * 修改课程加权分表
     * @param courseWeightedScoreBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
    @PostMapping("/course/weighted/score/update")
    public AjaxResult updateCourseWeightedScore(@Validated @RequestBody CourseWeightedScoreBo courseWeightedScoreBo);

    /**
     * 查询课程加权分表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
    @GetMapping("/course/weighted/score/detail")
    public AjaxResult<CourseWeightedScoreVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程加权分表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-08-29 11:07:05
     */
    @GetMapping("/course/weighted/score/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
