package com.fh.yanx.service.org.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 组织申请表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:50:13
 */
@Data
public class OrganizationApplyVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 组织申请id
     */
    @ApiModelProperty("组织申请id")
    private Long organizationApplyId;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 组织机构名称
     */
    @ApiModelProperty("组织机构名称")
    private String name;

    /**
     * 所属省份
     */
    @ApiModelProperty("所属省份")
    private Long provinceId;

    /**
     * 所属市
     */
    @ApiModelProperty("所属市")
    private Long cityId;

    /**
     * 所属县区
     */
    @ApiModelProperty("所属县区")
    private Long areaId;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contact;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    private String concatName;

    /**
     * 处理状态：1：处理中，2：已处理
     */
    @ApiModelProperty("处理状态：1：处理中，2：已处理")
    private Integer type;

    /**
     * 是否删除：0：否，1：是
     */
    @ApiModelProperty("是否删除：0：否，1：是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /*
     * 方便steam流存入自身
     * */
    public OrganizationApplyVo returnOwn() {
        return this;
    }

}
