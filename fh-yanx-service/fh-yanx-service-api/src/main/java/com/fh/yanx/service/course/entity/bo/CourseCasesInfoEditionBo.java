package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 校本课程案例详细信息版本记录
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
@Data
public class CourseCasesInfoEditionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 校本课程案例版本表ID
	 */
	@ApiModelProperty("校本课程案例版本表ID")
	private Long casesEditionId;

	/**
	 * 背景
	 */
	@ApiModelProperty("背景")
	private String background;

	/**
	 * 目标
	 */
	@ApiModelProperty("目标")
	private String goal;

	/**
	 * 内容
	 */
	@ApiModelProperty("内容")
	private String content;

	/**
	 * 评价
	 */
	@ApiModelProperty("评价")
	private String eval;

	/**
	 * 实施信息
	 */
	@ApiModelProperty("实施信息")
	private String operationInfo;

	/**
	 * 校本课程建设经验
	 */
	@ApiModelProperty("校本课程建设经验")
	private String exp;

	/**
	 * 校本课程建设成效
	 */
	@ApiModelProperty("校本课程建设成效")
	private String effect;

	/**
	 * 校本课程建设成效
	 */
	@ApiModelProperty("校本课程建设成效")
	private String problem;

	/**
	 * 校本课程建设成效
	 */
	@ApiModelProperty("校本课程建设成效")
	private String structure;

	/**
	 * 实施案例
	 */
	@ApiModelProperty("实施案例")
	private String teacherCaseInfo;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String teacherCaseName;

	/**
	 * 0-未删除 1-已删除
	 */
	@ApiModelProperty("0-未删除 1-已删除")
	private Integer isDelete;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createDate;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateDate;

}
