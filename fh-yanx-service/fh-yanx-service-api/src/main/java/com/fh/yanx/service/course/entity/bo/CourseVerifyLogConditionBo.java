package com.fh.yanx.service.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 课程审核流水表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
@Data
public class CourseVerifyLogConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;

	/**
	 * 审核进度类型，默认空：1初审，2二审，3运营审核。4综合评分
	 */
	@ApiModelProperty("审核进度类型，默认空：1初审，2二审，3运营审核 4综合评语，5派单评语。")
	private Integer verifyProcessType;

	/**
	 * 精品审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过
	 */
	@ApiModelProperty("精品审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过")
	private Integer bestVerifyType;

	/**
	 * 普通课程审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过
	 */
	@ApiModelProperty("普通课程审核状态：1未审核，2通过，3不通过，4仅保存，5初审通过，6初审不通过")
	private Integer normalVerifyType;

	/**
	 * 课程流水来源：1校本课程审核，2精品课程审核
	 */
	private Integer courseVerifySource;

	/**
	 * 审核备注
	 */
	@ApiModelProperty("审核备注")
	private String verifyRemark;


	/**
	 * 审核打分逐项-前端记录显示
	 */
	@ApiModelProperty("审核打分逐项-前端记录显示")
	private String verifyScoreDetailJson;

	/**
	 * 审核打分总分
	 */
	@ApiModelProperty("审核打分总分")
	private String verifyScoreTotal;

	/**
	 * 审核人
	 */
	@ApiModelProperty("审核人")
	private String userOid;

	/**
	 * 审核人姓名
	 */
	@ApiModelProperty("审核人姓名")
	private String realName;

	/**
	 * 审核人角色
	 */
	@ApiModelProperty("审核人角色")
	private Long roleId;

	/**
	 * 审核人角色名称
	 */
	@ApiModelProperty("审核人角色名称")
	private String roleName;


	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 课程ids集合
	 */
	private List<Long> casesIds;

	/**
	 * 审核进度类型，默认空：1初审，2二审，3运营审核。
	 */
	@ApiModelProperty("审核进度类型，默认空：1初审，2二审，3运营审核 4综合评语，5派单评语。")
	private List<Integer> verifyProcessTypes;

	/**
	 * 采纳意见 1-建议采纳 2-建议不采纳
	 */
	@ApiModelProperty("采纳意见 1-建议采纳 2-建议不采纳")
	private Integer adoptType;

	/**
	 * 是否展示到前台，1展示，2不展示
	 */
	@ApiModelProperty("是否展示到前台，1展示，2不展示")
	private Integer showType;
}
