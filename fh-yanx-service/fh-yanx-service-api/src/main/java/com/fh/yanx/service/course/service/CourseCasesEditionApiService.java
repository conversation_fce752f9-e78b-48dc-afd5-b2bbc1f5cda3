package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseCasesEditionApi;
import com.fh.yanx.service.course.entity.bo.CourseCasesEditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleEditionConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程版本表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
@FeignClient(contextId = "courseCasesEditionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseCasesEditionApiService.CourseCasesEditionApiFallbackFactory.class)
@Component
public interface CourseCasesEditionApiService extends CourseCasesEditionApi {

    @Component
    class CourseCasesEditionApiFallbackFactory implements FallbackFactory<CourseCasesEditionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseCasesEditionApiFallbackFactory.class);
        @Override
        public CourseCasesEditionApiService create(Throwable cause) {
            CourseCasesEditionApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseCasesEditionApiService() {
                public AjaxResult getCourseCasesEditionPageListByCondition(CourseCasesEditionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseCasesEditionListByCondition(CourseCasesEditionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseCasesEdition(CourseCasesEditionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseCasesEdition(CourseCasesEditionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getContentModuleEdition(CourseCasesEditionConditionBo conditionBo) {
                    return AjaxResult.fail("查询版本信息失败");
                }

            };
        }
    }
}