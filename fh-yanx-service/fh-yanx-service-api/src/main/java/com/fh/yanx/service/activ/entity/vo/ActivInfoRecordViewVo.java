package com.fh.yanx.service.activ.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 新时代文化校园活动内容观看记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
@Data
public class ActivInfoRecordViewVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 新时代文化校园活动内容表id
     */
    @ApiModelProperty("新时代文化校园活动内容表id")
    private Long recordId;

    /**
     * FK新时代文化校园活动信息表的id，冗余
     */
    @ApiModelProperty("FK新时代文化校园活动信息表的id，冗余")
    private Long activId;

    /**
     * 资源标题名称
     */
    @ApiModelProperty("资源标题名称")
    private String viewName;

    /**
     * 观看日期(点击了观看的日期)
     */
    @ApiModelProperty("观看日期(点击了观看的日期)")
    private Date viewDate;

    /**
     * 观看行为：1点击观看
     */
    @ApiModelProperty("观看行为：1点击观看")
    private Integer viewActionType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ActivInfoRecordViewVo returnOwn() {
        return this;
    }

    /**
     * 一个资源一天的观看次数
     */
    private Long viewCountDay;
    /**
     * 对应的活动名称
     */
    private String activName;
}
