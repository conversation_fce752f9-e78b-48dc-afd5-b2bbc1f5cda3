package com.fh.yanx.service.course.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 课程加权分表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
@Data
public class CourseWeightedScoreVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 校本课程案例ID
     */
    @ApiModelProperty("校本课程案例ID")
    private Long casesId;

    /**
     * 校本或精品审核的加权分（暂时这么设计）：1、校本，2精品
     */
    @ApiModelProperty("校本或精品审核的加权分（暂时这么设计）：1、校本，2精品")
    private Integer normalBestType;

    /**
     * 加权分1
     */
    @ApiModelProperty("加权分1")
    private BigDecimal weightedScoreOne;

    /**
     * 加权分2
     */
    @ApiModelProperty("加权分2")
    private BigDecimal weightedScoreTwo;

    /**
     * 加权分3
     */
    @ApiModelProperty("加权分3")
    private BigDecimal weightedScoreThree;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public CourseWeightedScoreVo returnOwn() {
        return this;
    }

}
