package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课题组成员表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
public class ResTopicStudentBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 课题id
	 */
	@ApiModelProperty("课题id")
	private Long topicId;

	/**
	 * 学生用户oid
	 */
	@ApiModelProperty("学生用户oid")
	private String userOid;

	/**
	 * 课题评价
	 */
	@ApiModelProperty("课题评价")
	private String topicEvaluate;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 添加评语接口标识
	 */
	@ApiModelProperty("添加评语接口标识，0：否，1：是")
	private Integer isAddEvaluate;

	/**
	 * 当前登陆用户oid
	 */
	@ApiModelProperty("当前登陆用户oid")
	private String currentUserOid;
}
