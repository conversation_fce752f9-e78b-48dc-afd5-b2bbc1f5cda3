package com.fh.yanx.service.pub.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 融合出版书
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
public class PubBookBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long bookId;

	/**
	 * 书籍名称
	 */
	@ApiModelProperty("书籍名称")
	private String bookName;

	/**
	 * 标签字符串，多个则使用英文逗号分割
	 */
	@ApiModelProperty("标签字符串，多个则使用英文逗号分割")
	private String labelNames;

	/**
	 * 出版社名称
	 */
	@ApiModelProperty("出版社名称")
	private String pressName;

	/**
	 * 书号
	 */
	@ApiModelProperty("书号")
	private String bookNo;

	/**
	 * 阅读次数
	 */
	@ApiModelProperty("阅读次数")
	private Long readTimes;

	/**
	 * 书籍顺序
	 */
	@ApiModelProperty("书籍顺序")
	private Integer bookIndex;

	/**
	 * 封面文件oid
	 */
	@ApiModelProperty("封面文件oid")
	private String coverFileOid;

	/**
	 * 封面文件预览地址
	 */
	@ApiModelProperty("封面文件预览地址")
	private String coverFileUrl;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
