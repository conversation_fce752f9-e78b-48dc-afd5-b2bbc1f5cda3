package com.fh.yanx.service.res.entity.vo;

import com.light.base.category.entity.vo.CategoryVo;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 课题表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long topicId;

    /**
     * 课题名称
     */
    @ApiModelProperty("课题名称")
    private String topicName;

    /**
     * 学校id
     */
    @ApiModelProperty("学校id")
    private Long organizationId;

    /**
     * 班级id
     */
    @ApiModelProperty("班级id")
    private Long classesId;

    /**
     * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定
     */
    @ApiModelProperty("课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定")
    private Integer topicProcess;

    /**
     * 当前提交人
     */
    @ApiModelProperty("当前提交人")
    private String submitUser;

    /**
     * 当前审批人
     */
    @ApiModelProperty("当前审批人")
    private String verifyUser;

    /**
     * 课题类别：待定
     */
    @ApiModelProperty("课题类别：待定")
    private Integer topicType;

    /**
     * 关联学科：待定（可能是type或者字典值），多个使用逗号分割
     */
    @ApiModelProperty("关联学科：待定（可能是type或者字典值），多个使用逗号分割")
    private String relSubject;

    /**
     * 课题介绍/界定
     */
    @ApiModelProperty("课题介绍/界定")
    private String topicDesc;

    /**
     * 课题背景
     */
    @ApiModelProperty("课题背景")
    private String topicBack;

    /**
     * 课题目的
     */
    @ApiModelProperty("课题目的")
    private String topicGoal;

    /**
     * 研究内容
     */
    @ApiModelProperty("研究内容")
    private String topicContent;

    /**
     * 研究方法
     */
    @ApiModelProperty("研究方法")
    private String topicMethod;

    /**
     * 研究条件
     */
    @ApiModelProperty("研究条件")
    private String topicCondition;

    /**
     * 研究计划
     */
    @ApiModelProperty("研究计划")
    private String topicPlan;

    /**
     * 研究预期成果
     */
    @ApiModelProperty("研究预期成果")
    private String topicExpected;

    /**
     * 最终评定结果：1通过，2不通过
     */
    @ApiModelProperty("最终评定结果：1通过，2不通过")
    private Integer evaluateResult;

    /**
     * 优秀课题类型：1不是优秀课题，2是优秀课题
     */
    @ApiModelProperty("优秀课题类型：1不是优秀课题，2是优秀课题")
    private Integer excellentType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 课题来源：1校内自建，2校外导入
     */
    @ApiModelProperty("课题来源：1校内自建，2校外导入")
    private Integer topicSource;

    /**
     * 首页展示：1展示
     */
    @ApiModelProperty("首页展示：1展示")
    private Integer homeShow;

    /**
     * 小组学生姓名
     */
    private List<String> topicStudentNames;

    /**
     * 收藏数
     */
    private Integer collectNumber;

    /**
     * 浏览量
     */
    private Integer pvNumber;

    /**
     * 课题老师姓名
     */
    private List<String> topicTeacherNames;

//    /**
//     * 课题结果附件
//     */
//    private List<AttachmentVo> attachmentVos;

    private List<Map<String,Object>> resTopicRepAttachmentVosMap;

    private  List<Map<String,Object>> resTopicStuAttachmentVosMap;

    /**
     * 课题评定 教师评语
     */
    private List<ResTopicApprovalVo> resTopicApprovalVos;

    /**
     * 课题评定 学校荣誉
     */
//    private List<ResTopicHonorAttachmentVo> resTopicHonorAttachmentVos;
    private  List<Map<String,Object>> resTopicHonorAttachmentVosMap;

    /**
     * 流转记录
     */
    private List<ResTopicProcessRecordVo> resTopicProcessRecordVos;

    /**
     * 课题第三方来源类型：1有方，2汇景
     */
    private String topicThirdType;

    /**
     * 关联学科字典值,逗号分隔
     */
    @ApiModelProperty("关联学科字典值,逗号分隔")
    private String subjectCode;

    /**
     * 关联科目对应的字典数组
     */
    private List<CategoryVo> subjectCodes;

    private List<UserVo> studentUserVos;
    private List<UserVo> teacherUserVos;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String organizationName;

    /**
     * 小组成员列表
     */
    @ApiModelProperty("小组成员列表")
    private List<ResTopicStudentVo> resTopicStudentVos;

    /**
     * 指导老师列表
     */
    @ApiModelProperty("指导老师列表")
    private List<ResTopicTeacherVo> resTopicTeacherVos;

    /**
     * 当前用户是否收藏
     */
    @ApiModelProperty("当前用户是否收藏")
    private Boolean isCollect;
}
