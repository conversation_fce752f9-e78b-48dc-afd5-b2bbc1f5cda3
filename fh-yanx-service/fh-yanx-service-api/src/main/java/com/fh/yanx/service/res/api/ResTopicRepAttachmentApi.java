package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicRepAttachmentVo;

import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 结题答辩附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicRepAttachmentApi {

    /**
     * 查询结题答辩附件表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/rep/attachment/page/list")
    public AjaxResult<PageInfo<ResTopicRepAttachmentVo>> getResTopicRepAttachmentPageListByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition);

    /**
     * 查询结题答辩附件表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/rep/attachment/list")
    public AjaxResult<List<ResTopicRepAttachmentVo>> getResTopicRepAttachmentListByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition);


    /**
     * 新增结题答辩附件表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping(value = "res/topic/rep/attachment/add")
    public AjaxResult addResTopicRepAttachment(@RequestBody ResTopicRepAttachmentBo resTopicRepAttachmentBo);

    /**
     * 修改结题答辩附件表
     *
     * @param resTopicRepAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/rep/attachment/update")
    public AjaxResult updateResTopicRepAttachment(@Validated @RequestBody ResTopicRepAttachmentBo resTopicRepAttachmentBo);


    /**
     * 批量修改附件表
     *
     * @param resTopicRepAttachmentBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/8 14:58
     */
    @PostMapping("res/topic/rep/attachment/update-batch")
    AjaxResult updateBatchResTopicRepAttachment(@RequestBody List<ResTopicRepAttachmentBo> resTopicRepAttachmentBos);

    /**
     * 查询结题答辩附件表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/rep/attachment/detail")
    public AjaxResult<ResTopicRepAttachmentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除结题答辩附件表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/rep/attachment/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除结题答辩附件表-根据fileOid
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/rep/attachment/delete-oid")
    public AjaxResult deleteByFileOid(@NotNull(message = "请选择数据") @RequestParam("fileOid") String fileOid);

    /**
     * 查询结题答辩附件表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/rep/attachment/years")
    public AjaxResult<List<String>> getResTopicRepAttachmentYearsByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition);

}
