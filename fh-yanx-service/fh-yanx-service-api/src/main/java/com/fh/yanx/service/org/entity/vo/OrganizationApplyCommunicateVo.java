package com.fh.yanx.service.org.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 组织申请沟通记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
@Data
public class OrganizationApplyCommunicateVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 组织申请id
     */
    @ApiModelProperty("组织申请id")
    private Long organizationApplyId;

    /**
     * 沟通内容
     */
    @ApiModelProperty("沟通内容")
    private String content;

    /**
     * 处理状态：1：处理中，2：已处理
     */
    @ApiModelProperty("处理状态：1：处理中，2：已处理")
    private Integer type;

    /**
     * 是否删除：0：否，1：是
     */
    @ApiModelProperty("是否删除：0：否，1：是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /*
     * 方便steam流存入自身
     * */
    public OrganizationApplyCommunicateVo returnOwn() {
        return this;
    }

}
