package com.fh.yanx.service.enums;

/**
 * 课程内容修改操作类型
 *
 * <AUTHOR>
 */
public enum CourseContentOperateType {
    /**
     * 普通课程内容（tab1），基础介绍内容
     */
    NORMAL(1),
    /**
     * 课程的精品内容（tab2，tab3，tab4），模块和附件内容
     */
    BOUTIQUE(2),

    ;

    private Integer value;

    CourseContentOperateType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}