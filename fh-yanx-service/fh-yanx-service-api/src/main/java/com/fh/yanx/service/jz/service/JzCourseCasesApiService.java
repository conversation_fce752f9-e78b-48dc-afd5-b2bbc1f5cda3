package com.fh.yanx.service.jz.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.jz.api.JzCourseCasesApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 金中-校本课程案例
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@FeignClient(contextId = "jzCourseCasesApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = JzCourseCasesApiService.JzCourseCasesApiFallbackFactory.class)
@Component
public interface JzCourseCasesApiService extends JzCourseCasesApi {

    @Component
    class JzCourseCasesApiFallbackFactory implements FallbackFactory<JzCourseCasesApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(JzCourseCasesApiFallbackFactory.class);
        @Override
        public JzCourseCasesApiService create(Throwable cause) {
            JzCourseCasesApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new JzCourseCasesApiService() {
                public AjaxResult getJzCourseCasesPageListByCondition(JzCourseCasesConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getJzCourseCasesListByCondition(JzCourseCasesConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addJzCourseCases(JzCourseCasesBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateJzCourseCases(JzCourseCasesBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult getHomeList(JzCourseCasesConditionBo condition) {
                    return AjaxResult.fail("查询首页数据失败");
                }

                @Override
                public AjaxResult homeDetail(Long casesId) {
                    return AjaxResult.fail("查询首页课程详情数据失败");
                }
            };
        }
    }
}