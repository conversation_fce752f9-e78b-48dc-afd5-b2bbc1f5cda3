package com.fh.yanx.service.res.api;

import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentBo;
import com.fh.yanx.service.res.entity.vo.ResTopicHonorAttachmentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 课题荣誉表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicHonorAttachmentApi {

    /**
     * 查询课题荣誉表分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/honor/attachment/page/list")
    public AjaxResult<PageInfo<ResTopicHonorAttachmentVo>>
        getResTopicHonorAttachmentPageListByCondition(@RequestBody ResTopicHonorAttachmentConditionBo condition);

    /**
     * 查询课题荣誉表列表
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/honor/attachment/list")
    public AjaxResult<List<ResTopicHonorAttachmentVo>>
        getResTopicHonorAttachmentListByCondition(@RequestBody ResTopicHonorAttachmentConditionBo condition);

    /**
     * 新增课题荣誉表
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/honor/attachment/add")
    public AjaxResult
        addResTopicHonorAttachment(@Validated @RequestBody ResTopicHonorAttachmentBo resTopicHonorAttachmentBo);

    /**
     * 修改课题荣誉表
     * 
     * @param resTopicHonorAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/honor/attachment/update")
    public AjaxResult
        updateResTopicHonorAttachment(@Validated @RequestBody ResTopicHonorAttachmentBo resTopicHonorAttachmentBo);

    /**
     * 查询课题荣誉表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/honor/attachment/detail")
    public AjaxResult<ResTopicHonorAttachmentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课题荣誉表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/honor/attachment/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除和批量新增
     * 
     * @param resTopicHonorAttachmentBos
     * @return
     */
    @PostMapping("res/topic/honor/attachment/delete-save")
    public AjaxResult
        deleteAndSaveTopicHonorAttachmentBatch(@RequestBody List<ResTopicHonorAttachmentBo> resTopicHonorAttachmentBos);
}
