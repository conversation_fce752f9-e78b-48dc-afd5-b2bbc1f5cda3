package com.fh.yanx.service.res.api;

import com.fh.yanx.service.org.entity.bo.OrganizationBoExt;
import com.fh.yanx.service.org.entity.bo.OrganizationExtConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationVoExt;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.light.core.entity.AjaxResult;
import com.light.user.account.entity.bo.AccountBo;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.admin.entity.bo.AdminBo;
import com.light.user.admin.entity.bo.AdminConditionBo;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.organization.entity.bo.*;
import com.light.user.organization.entity.vo.OrganizationSettingVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.bo.UserTransferBo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 基础数据调用api
 */
public interface BaseDataApi {

    /**
     * 查询合作意向申请表分页列表
     *
     * @param userOid the user oid
     * @return the teacher vo by user oid
     * <AUTHOR>
     * @date 2022 -10-27 16:19:35
     */
    @GetMapping("res/base/teacher/detail")
    AjaxResult<TeacherVo> getTeacherVoByUserOid(@RequestParam("userOid") String userOid);

    /**
     * 获取学生详情
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult<com.light.user.student.entity.vo.StudentVo>
     * <AUTHOR>
     * @date 2022/12/12 20:14
     */
    @GetMapping("res/base/student/detail")
    AjaxResult<StudentVo> getStudentVoByUserOid(@RequestParam("userOid") String userOid);

    /**
     * 获取研学学科列表
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/2 14:19
     */
    @GetMapping("res/base/data/subject/list")
    public AjaxResult getSubjectList();

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictTypes
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/6 11:08
     */
    @PostMapping(value = "res/base/data/dicts")
    public AjaxResult listValueByTypes(@RequestBody List<String> dictTypes);

    /**
     * 新增组织信息
     *
     * @param organizationBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/17 15:32
     **/
    @PostMapping("res/base/data/add-organization")
    public AjaxResult<Long> addOrganization(@RequestBody OrganizationBo organizationBo);

    /**
     * 编辑组织信息
     *
     * @param organizationBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/17 15:32
     **/
    @PostMapping("res/base/data/update-organization")
    public AjaxResult updateOrganization(@RequestBody OrganizationBoExt organizationBo);

    /**
     * 获取组织详情
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 18:04
     **/
    @GetMapping("res/base/data/organization-detail")
    public AjaxResult<OrganizationVoExt> getOrganizationDetail(@RequestParam("organizationId") Long organizationId);

    /**
     * 获取组织信息列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/17 15:33
     **/
    @PostMapping("res/base/data/organization-list")
    public AjaxResult getOrganizationListByCondition(@RequestBody OrganizationExtConditionBo conditionBo);

    /**
     * 查询组织信息
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/17 15:34
     **/
    @PostMapping("res/base/data/get-organization")
    public AjaxResult<OrganizationVo> getOrganizationByCondition(@RequestBody OrganizationConditionBo conditionBo);

    /**
     * 新增or更新组织设置信息
     *
     * @param organizationSettingBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 17:33
     **/
    @PostMapping("res/base/data/add-or-update-organization-setting")
    public AjaxResult addOrUpdateOrganizationSetting(@RequestBody OrganizationSettingBo organizationSettingBo);

    /**
     * 获取组织设置信息
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/24 8:55
     **/
    @PostMapping("res/base/data/get-organization-setting")
    public AjaxResult<OrganizationSettingVo>
        getOrganizationSetting(@RequestBody OrganizationSettingConditionBo conditionBo);

    /**
     * 修改密码
     *
     * @param oldPassword
     * @param password
     * @return com.light.core.entity.AjaxResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2023/7/7 16:47
     **/
    @GetMapping("res/base/data/change-password")
    public AjaxResult changePassword(@RequestParam("oldPassword") String oldPassword,
        @RequestParam("password") String password);

    /**
     * 修改账号信息
     *
     * @param accountBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/24 11:17
     **/
    @PostMapping("res/base/data/update-account")
    public AjaxResult updateAccount(@RequestBody AccountBo accountBo);

    /**
     * 更新用户信息
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 9:57
     **/
    @PostMapping("res/base/data/update-user")
    public AjaxResult updateUser(@RequestBody UserBo userBo);

    /**
     * 重置密码
     *
     * @param accountId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 10:45
     **/
    @GetMapping("res/base/data/reset-password")
    public AjaxResult resetPassword(@RequestParam("accountId") Long accountId);

    /**
     * 新增教师
     *
     * @param teacherBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 14:35
     **/
    @PostMapping("res/base/data/add-teacher")
    public AjaxResult addTeacher(@RequestBody TeacherBo teacherBo);

    /**
     * 编辑教师
     *
     * @param teacherBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 14:40
     **/
    @PostMapping("res/base/data/update-teacher")
    public AjaxResult updateTeacher(@RequestBody TeacherBo teacherBo);

    /**
     * 获取教师详情
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 15:03
     **/
    @GetMapping("res/base/data/teacher-detail")
    public AjaxResult getTeacherDetail(@RequestParam("userOid") String userOid);

    /**
     * 删除教师
     *
     * @param teacherId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 15:17
     **/
    @GetMapping("res/base/data/del-teacher")
    public AjaxResult delTeacher(@RequestParam("teacherId") Long teacherId);

    /**
     * 获取教师列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/21 10:40
     **/
    @PostMapping("res/base/data/list-teacher")
    public AjaxResult getTeacherList(@RequestBody TeacherConditionBo conditionBo);

    /**
     * 根据userOid重置密码
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/21 13:44
     **/
    @GetMapping("res/base/data/reset-password-by-oid")
    public AjaxResult resetPasswordByUserOid(@RequestParam("userOid") String userOid);

    /**
     * 获取当前登录用户
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/8 10:52
     **/
    @GetMapping("res/base/data/current-user")
    public AjaxResult<LoginAccountVo> getCurrentUser();

    /**
     * 用户转校
     * 
     * @param userTransferBo
     * @return
     */
    @RequestMapping(value = "/remove-out", method = RequestMethod.POST)
    public AjaxResult removeOutTeacher(@RequestBody UserTransferBo userTransferBo);

    /**
     * 后台账号管理（查询人员列表）
     */
    @PostMapping(value = "res/base/data/admin/list")
    public AjaxResult<List<AdminVo>> getAdminListByCondition(@RequestBody AdminConditionBo conditionBo);

    /**
     * 后台账号管理（查询人员列表-分页）
     */
    @RequestMapping(value = "res/base/data/admin/list-page", method = RequestMethod.POST)
    public AjaxResult getAdminPageListByCondition(@RequestBody AdminConditionBo conditionBo);

    /**
     * 后台添加人员
     */
    @PostMapping(value = "res/base/data/admin/add")
    public AjaxResult addAdmin(@RequestBody AdminBo adminBo);

    /**
     * 后台修改人员
     */
    @PostMapping(value = "res/base/data/admin/update")
    public AjaxResult updateAdmin(@RequestBody AdminBo adminBo);

    /**
     * 后台删除人员
     */
    @GetMapping(value = "res/base/data/admin/delete")
    public AjaxResult delAdmin(@RequestParam("adminId") Long adminId);

    /**
     * 后台查询人员
     */
    @GetMapping(value = "res/base/data/admin/detail")
    public AjaxResult<AdminVo> getAdminDetail(@RequestParam("adminId") Long adminId);

    /**
     * 启用禁用后台人员
     */
    @GetMapping(value = "res/base/data/admin/lock")
    public AjaxResult enableAdmin(@RequestParam("adminId") Long adminId, @RequestParam("status") Integer status);

    /**
     * 后台重置密码
     */
    @GetMapping(value = "res/base/data/admin/reset-pwd")
    public AjaxResult resetAdminPassword(@RequestParam("adminId") Long adminId,
        @RequestParam("initPwd") String initPwd);

    /**
     * 查询所有研学的角色
     * @return
     */
    @GetMapping("res/base/data/yanx-roles")
    public AjaxResult getAllYanxRoles();

    /**
     * 获取案例持有者角色
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/9 9:40
     **/
    @GetMapping("/res/base/data/reception-roles")
    public AjaxResult receptionRoles();

    /**
     * 新时代专家角色
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/27 15:02
     **/
    @GetMapping("/res/base/data/expert-roles")
    public AjaxResult expertRoles();

    /**
     * 获取专家助理角色
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/27 15:09
     **/
    @GetMapping("/res/base/data/expert-assistant-roles")
    public AjaxResult expertAssistantRoles();

    /**
     * 新增用户并创建账号
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 14:33
     **/
    @PostMapping("/res/base/data/add-user-and-generator-account")
    public AjaxResult addUserAndGeneratorAccount(@RequestBody UserBo userBo);

    /**
     * 新时代编辑专家、专家助理
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 15:19
     **/
    @PostMapping("/res/base/data/update-expert-user")
    public AjaxResult updateExpertUser(@RequestBody UserBo userBo);

    /**
     * 用户列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 16:38
     **/
    @PostMapping("/res/base/data/user-list")
    public AjaxResult userList(@RequestBody UserConditionBoExt conditionBo);

    /**
     * 获取用户详情
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/29 14:19
     **/
    @GetMapping("/res/base/data/user-detail")
    public AjaxResult getUserDetail(@RequestParam("userOid") String userOid);

    /**
     * 查询专家助理列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/29 17:32
     **/
    @PostMapping("/res/base/data/expert-assistant-list")
    public AjaxResult expertAssistantList(@RequestBody UserConditionBoExt conditionBo);

    /**
     * 删除用户
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/2 14:36
     **/
    @GetMapping("/res/base/data/del-user")
    public AjaxResult delUser(@RequestParam("userOid") String userOid);

    /**
     * 删除组织  目前只支持删除type=3的学术组织
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/3 10:16
     **/
    @GetMapping("/res/base/data/del-organization")
    public AjaxResult delOrganization(@RequestParam("organizationId") Long organizationId);

    /**
     * 修改手机号
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/6 10:57
     **/
    @PostMapping("/res/base/data/change-phone")
    public AjaxResult changePhone(@RequestBody UserBo userBo);

    /**
     * 批量新增教师
     *
     * @param teacherBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/9 16:15
     **/
    @PostMapping("/res/base/data/add-batch-teacher")
    public AjaxResult addBatchTeacher(@RequestBody List<TeacherBo> teacherBos);

    /**
     * 批量新增用户
     *
     * @param userBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/10 10:18
     **/
    @PostMapping("/res/base/data/add-batch-user")
    public AjaxResult addBatchUser(@RequestBody List<UserBo> userBos);

    /**
     * 获取专家和案例持有者角色列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/16 10:42
     **/
    @GetMapping("/res/base/data/expert-and-owner-roles")
    public AjaxResult expertAndCaseOwnerRoles();
}
