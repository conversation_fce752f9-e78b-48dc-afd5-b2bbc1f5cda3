package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesInfoEditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesInfoEditionVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 校本课程案例详细信息版本记录
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:41:09
 */
public interface CourseCasesInfoEditionApi {

    /**
     * 查询校本课程案例详细信息版本记录分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
    @PostMapping("/course/cases/info/edition/page/list")
    public AjaxResult<PageInfo<CourseCasesInfoEditionVo>> getCourseCasesInfoEditionPageListByCondition(@RequestBody CourseCasesInfoEditionConditionBo condition);

    /**
     * 查询校本课程案例详细信息版本记录列表
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
    @PostMapping("/course/cases/info/edition/list")
    public AjaxResult<List<CourseCasesInfoEditionVo>> getCourseCasesInfoEditionListByCondition(@RequestBody CourseCasesInfoEditionConditionBo condition);


    /**
     * 新增校本课程案例详细信息版本记录
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
    @PostMapping("/course/cases/info/edition/add")
    public AjaxResult addCourseCasesInfoEdition(@Validated @RequestBody CourseCasesInfoEditionBo courseCasesInfoEditionBo);

    /**
     * 修改校本课程案例详细信息版本记录
     * @param courseCasesInfoEditionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
    @PostMapping("/course/cases/info/edition/update")
    public AjaxResult updateCourseCasesInfoEdition(@Validated @RequestBody CourseCasesInfoEditionBo courseCasesInfoEditionBo);

    /**
     * 查询校本课程案例详细信息版本记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
    @GetMapping("/course/cases/info/edition/detail")
    public AjaxResult<CourseCasesInfoEditionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除校本课程案例详细信息版本记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:41:09
     */
    @GetMapping("/course/cases/info/edition/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
