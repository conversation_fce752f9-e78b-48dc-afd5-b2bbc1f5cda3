package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课程加权分表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-29 11:07:05
 */
@Data
public class CourseWeightedScoreBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;

	/**
	 * 校本或精品审核的加权分（暂时这么设计）：1、校本，2精品
	 */
	@ApiModelProperty("校本或精品审核的加权分（暂时这么设计）：1、校本，2精品")
	private Integer normalBestType;

	/**
	 * 加权分1
	 */
	@ApiModelProperty("加权分1")
	private BigDecimal weightedScoreOne;

	/**
	 * 加权分2
	 */
	@ApiModelProperty("加权分2")
	private BigDecimal weightedScoreTwo;

	/**
	 * 加权分3
	 */
	@ApiModelProperty("加权分3")
	private BigDecimal weightedScoreThree;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
