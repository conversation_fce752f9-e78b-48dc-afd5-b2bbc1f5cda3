package com.fh.yanx.service.enums;

/**
 * 审核进度类型
 *
 * <AUTHOR>
 */
public enum VerifyProcessType {
    /**
     * 初审（第一次审核）
     */
//    ONE(1),
    /**
     * 二审（第二次审核）
     */
//    TWO(2),
    /**
     * 三审（第三次审核，运营审核）

     */
//    THREE(3),

    VERIFY(1),// 审核
    COMPREHENSIVE(4),// 综评
    DISPATCH(5),// 派单
    FEEDBACK(7),// 派单反馈
    ADOPT(8), // 采纳
    VERIFY_COMMENT(9), // 审核评语
    GUIDE_COMMENT(10), // 指导评语
    ;

    private Integer value;

    VerifyProcessType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}