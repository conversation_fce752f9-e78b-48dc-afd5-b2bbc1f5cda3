package com.fh.yanx.service.enums;

/**
 * 活动上下架状态
 *
 * <AUTHOR>
 * @date 2023-07-13 10:06
 */
public enum ActivType {
    ACTIV_TYPE_UP(1, "上架"),
    ACTIV_TYPE_DOWN(2, "下架");

    private Integer code;
    private String value;

    ActivType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
