package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicRepAttachmentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 结题答辩附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@FeignClient(contextId = "resTopicRepAttachmentApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicRepAttachmentApiService.ResTopicRepAttachmentApiFallbackFactory.class)
@Component
public interface ResTopicRepAttachmentApiService extends ResTopicRepAttachmentApi {

    @Component
    class ResTopicRepAttachmentApiFallbackFactory implements FallbackFactory<ResTopicRepAttachmentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicRepAttachmentApiFallbackFactory.class);

        @Override
        public ResTopicRepAttachmentApiService create(Throwable cause) {
            ResTopicRepAttachmentApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicRepAttachmentApiService() {
                public AjaxResult getResTopicRepAttachmentPageListByCondition(ResTopicRepAttachmentConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicRepAttachmentListByCondition(ResTopicRepAttachmentConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicRepAttachment(ResTopicRepAttachmentBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicRepAttachment(ResTopicRepAttachmentBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult updateBatchResTopicRepAttachment(List<ResTopicRepAttachmentBo> resTopicRepAttachmentBos) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult deleteByFileOid(String fileOid) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<List<String>> getResTopicRepAttachmentYearsByCondition(ResTopicRepAttachmentConditionBo condition) {
                    return AjaxResult.fail("查询年份失败");
                }
            };
        }
    }
}