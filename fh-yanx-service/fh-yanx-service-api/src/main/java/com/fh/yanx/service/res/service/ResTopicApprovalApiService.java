package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicApprovalApi;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalBo;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课题审批意见表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resTopicApprovalApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicApprovalApiService.ResTopicApprovalApiFallbackFactory.class)
@Component
public interface ResTopicApprovalApiService extends ResTopicApprovalApi {

    @Component
    class ResTopicApprovalApiFallbackFactory implements FallbackFactory<ResTopicApprovalApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicApprovalApiFallbackFactory.class);

        @Override
        public ResTopicApprovalApiService create(Throwable cause) {
            ResTopicApprovalApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicApprovalApiService() {
                public AjaxResult getResTopicApprovalPageListByCondition(ResTopicApprovalConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicApprovalListByCondition(ResTopicApprovalConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicApproval(ResTopicApprovalBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicApproval(ResTopicApprovalBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}