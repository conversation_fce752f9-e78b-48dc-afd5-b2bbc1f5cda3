package com.fh.yanx.service.jz.api;


import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeConditionBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesTypeBo;
import com.fh.yanx.service.jz.entity.vo.JzCourseCasesTypeVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 金中-校本课程案例类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
public interface JzCourseCasesTypeApi {

    /**
     * 查询金中-校本课程案例类型分页列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/type/page/list")
    public AjaxResult<PageInfo<JzCourseCasesTypeVo>> getJzCourseCasesTypePageListByCondition(@RequestBody JzCourseCasesTypeConditionBo condition);

    /**
     * 查询金中-校本课程案例类型列表
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/type/list")
    public AjaxResult<List<JzCourseCasesTypeVo>> getJzCourseCasesTypeListByCondition(@RequestBody JzCourseCasesTypeConditionBo condition);


    /**
     * 新增金中-校本课程案例类型
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/type/add")
    public AjaxResult addJzCourseCasesType(@Validated @RequestBody JzCourseCasesTypeBo jzCourseCasesTypeBo);

    /**
     * 修改金中-校本课程案例类型
     * @param jzCourseCasesTypeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @PostMapping("/jz/course/cases/type/update")
    public AjaxResult updateJzCourseCasesType(@Validated @RequestBody JzCourseCasesTypeBo jzCourseCasesTypeBo);

    /**
     * 查询金中-校本课程案例类型详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/type/detail")
    public AjaxResult<JzCourseCasesTypeVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除金中-校本课程案例类型
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-17 13:49:33
     */
    @GetMapping("/jz/course/cases/type/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
