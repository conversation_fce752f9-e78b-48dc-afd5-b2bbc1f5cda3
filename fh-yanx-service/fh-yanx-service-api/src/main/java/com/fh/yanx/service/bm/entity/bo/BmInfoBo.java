package com.fh.yanx.service.bm.entity.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.yanx.service.enums.ActivBizType;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 新时代文化校园报名活动申请表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Data
public class BmInfoBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long infoId;

    /**
     * 所属省份ID
     */
    @ApiModelProperty("所属省份ID")
    private Long provinceId;

    /**
     * 所属省份名称
     */
    @ApiModelProperty("所属省份名称")
    private String provinceName;

    /**
     * 所属市ID
     */
    @ApiModelProperty("所属市ID")
    private Long cityId;

    /**
     * 所属市名称
     */
    @ApiModelProperty("所属市名称")
    private String cityName;

    /**
     * 所属县区ID
     */
    @ApiModelProperty("所属县区ID")
    private Long areaId;

    /**
     * 所属县区名称
     */
    @ApiModelProperty("所属县区名称")
    private String areaName;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;

    /**
     * 单位名称
     */
    @ApiModelProperty("单位名称")
    private String departName;

    /**
     * 学段信息
     */
    @ApiModelProperty("学段信息")
    private String period;

    /**
     * 参与类型：1个人，2团体
     */
    @ApiModelProperty("参与类型：1个人，2团体")
    private Integer joinType;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contactName;

    /**
     * 联系人手机号码
     */
    @ApiModelProperty("联系人手机号码")
    private String contactMobile;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 确认状态：1待确认，2沟通中，3已确认，4已拒绝
     */
    private Integer submitType;

    /**
     * 缴费状态：1未缴费，2已缴费
     */
    private Integer payType;

    /**
     * 发票状态：1未开发票，2已开发票
     */
    private Integer invoiceType;

    /**
     * 发票文件oid
     */
    private String invoiceId;

    /**
     * 发票文件地址
     */
    private String invoiceUrl;

    /**
     * 发票名称不带后缀
     */
    private String invoiceName;

    /**
     * 发票名称带后缀
     */
    private String invoiceNameOri;

    /**
     * 支付凭证文件oid
     */
    private String payId;

    /**
     * 支付凭证文件地址
     */
    private String payUrl;

    /**
     * 支付凭证名称不带后缀
     */
    private String payName;

    /**
     * 支付凭证名称带后缀
     */
    private String payNameOri;

    /**
     * 参与人list
     */
    private List<BmInfoJoinerBo> bmInfoJoinerBoList;

    /**
     * 短信验证码
     */
    private String code;

    /**
     * 活动id
     */
    private Long activId;

    /**
     * 签到状态 1-未签到 2-已签到
     */
    private Integer signInState;

    /**
     * 签到码
     */
    private String signInCode;

    /**
     * 开票类型 1-个人 2-单位
     */
    private Integer invoicingType;

    /**
     * 发票抬头
     */
    private String invoiceHeader;

    /**
     * 税号
     */
    private String dutyCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 付款凭证上传时间
     */
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 付款凭证上传人oid
     */
    private String payUserOid;

    /**
     * 付款凭证上传人
     */
    private String payUserName;


    /**
     * 费用
     */
    @ApiModelProperty("费用")
    private BigDecimal orderAmount;

    /**
     * 不校验手机号：默认是false，校验
     */
    private boolean notValidateMobile= false;

    /**
     * 是否生成签到码，默认生成，用于复用update方法
     */
    private boolean generateSignCode = false;

    /**
     * 征订数量，用于记录显示
     */
    @ApiModelProperty("征订数量，用于记录显示")
    private Long subNumber;
    /**
     * 订单金额，用于记录显示，具体支付金额见订单表
     * 征订总价，改订单金额需同步修改
     */
    @ApiModelProperty("订单金额，用于记录显示，具体支付金额见订单表")
    private BigDecimal subBookTotalPrice;
    /**
     * 商品单价，下单时候的商品价格
     */
    @ApiModelProperty("商品单价，下单时候的商品价格")
    private BigDecimal subBookPrice;
    /**
     * 收件人-联系人
     */
    @ApiModelProperty("收件人-联系人")
    private String recName;
    /**
     * 收件人-联系人手机号码
     */
    @ApiModelProperty("收件人-联系人手机号码")
    private String recMobile;
    /**
     * 收件人-所属省份ID
     */
    @ApiModelProperty("收件人-所属省份ID")
    private Long recProvinceId;
    /**
     * 收件人-所属省份名称
     */
    @ApiModelProperty("收件人-所属省份名称")
    private String recProvinceName;
    /**
     * 收件人-所属市ID
     */
    @ApiModelProperty("收件人-所属市ID")
    private Long recCityId;
    /**
     * 收件人-所属市名称
     */
    @ApiModelProperty("收件人-所属市名称")
    private String recCityName;
    /**
     * 收件人-所属县区ID
     */
    @ApiModelProperty("收件人-所属县区ID")
    private Long recAreaId;
    /**
     * 收件人-所属县区名称
     */
    @ApiModelProperty("收件人-所属县区名称")
    private String recAreaName;
    /**
     * 收件人-地址
     */
    @ApiModelProperty("收件人-地址")
    private String recAddress;
    /**
     * 支付方式类型
     */
    @ApiModelProperty("支付方式类型")
    private Integer payWayType;
    /**
     * 转账记录
     */
    @ApiModelProperty("转账记录")
    private String payRecord;
    /**
     * 发票备注
     *
     */
    @ApiModelProperty("发票备注")
    private String invoiceRemark;

    /**
     * 是否选择材料默认空，1不选择材料，2选择材料
     */
    @ApiModelProperty("否选择材料")
    private Integer selectMaterialType;

    /**
     * 征订书籍名称
     */
    @ApiModelProperty("征订书籍名称")
    private String subBookName;

    /**
     * 活动的门票价格，即活动价格
     */
    @ApiModelProperty("活动的门票价格，即活动价格")
    private BigDecimal activTicketPrice;

    /**
     * 活动的总价格（活动+材料）
     */
    @ApiModelProperty("活动的总价格（活动+材料）")
    private BigDecimal activTotalPrice;

    /**
     * 活动类型(1普通活动，2征订活动)，{@link ActivBizType}
     */
    private Integer activBizType;

    /**
     * 是否修改订单价格，默认不修改
     */
    private boolean changeOrderPrice = false;
    /**
     * 是否修改订单状态，默认不修改
     */
    private boolean changeOrderState = false;

    /**
     * 跳过校验是否在联系人列表 默认不跳过
     */
    private boolean skipCheckBmContact = false;

    /**
     * 不校验短信 默认校验
     */
    private boolean noValidateSmsCode = false;

    /**
     * 学习时长
     */
    @ApiModelProperty("学习时长")
    private BigDecimal studyHours;

    /**
     * 参会证明文件oid
     */
    @ApiModelProperty("参会证明文件oid")
    private String joinProveId;

    /**
     * 参会证明文件地址
     */
    @ApiModelProperty("参会证明文件地址")
    private String joinProveUrl;

    /**
     * 参会证明文件名不带后缀
     */
    @ApiModelProperty("参会证明文件名不带后缀")
    private String joinProveName;

    /**
     * 参会证明文件名带后缀
     */
    @ApiModelProperty("参会证明文件名带后缀")
    private String joinProveNameOri;

    /**
     * 学时证明生成状态 1-未生成 2-生成中 3-已生成
     */
    @ApiModelProperty("学时证明生成状态 1-未生成 2-生成中 3-已生成")
    private Integer studyProveType;

    /**
     * 学时证明文件oid
     */
    @ApiModelProperty("学时证明文件oid")
    private String studyProveId;

    /**
     * 学时证明文件地址
     */
    @ApiModelProperty("学时证明文件地址")
    private String studyProveUrl;

    /**
     * 学时证明文件名不带后缀
     */
    @ApiModelProperty("学时证明文件名不带后缀")
    private String studyProveName;

    /**
     * 学时证明文件名带后缀
     */
    @ApiModelProperty("学时证明文件名带后缀")
    private String studyProveNameOri;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 代理厂商
     */
    @ApiModelProperty("代理厂商")
    private String cm;

    /**
     * 发货状态 1-未发货 2-已发货
     */
    @ApiModelProperty("发货状态")
    private Integer deliverState;

    /**
     * 物流单号
     */
    @ApiModelProperty("物流单号")
    private String logisticsCode;

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    private String logisticsOrg;

    @ApiModelProperty("支付调整原因")
    private String payAdjustReason;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款方式")
    private Integer refundWayType;

    @ApiModelProperty("退款时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date refundTime;

    @ApiModelProperty("退款原因")
    private String refundReason;

    @ApiModelProperty("退款凭证oid")
    private String refundId;

    @ApiModelProperty("退款凭证url")
    private String refundUrl;

    @ApiModelProperty("退款凭证名不带后缀")
    private String refundName;

    @ApiModelProperty("退款凭证名带后缀")
    private String refundNameOri;

    @ApiModelProperty("是否调整支付方式")
    private Boolean changePayWayType = false;
}
