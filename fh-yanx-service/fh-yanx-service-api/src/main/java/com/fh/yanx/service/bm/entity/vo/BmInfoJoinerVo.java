package com.fh.yanx.service.bm.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 新时代文化校园报名活动申请表-参与人信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-08 14:35:03
 */
@Data
public class BmInfoJoinerVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 申请记录主表id
     */
    @ApiModelProperty("申请记录主表id")
    private Long infoId;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String joinerName;

    /**
     * 性别：1男，2女
     */
    @ApiModelProperty("性别：1男，2女")
    private Integer joinerGender;

    /**
     * 民族
     */
    @ApiModelProperty("民族")
    private String joinerNation;

    /**
     * 
     */
    @ApiModelProperty("")
    private String joinerDuties;

    /**
     * 教学学科/工作
     */
    @ApiModelProperty("教学学科/工作")
    private String joinerTeach;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String joinerMail;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String joinerMobile;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
     * 联系人手机号
     */
    @ApiModelProperty("联系人手机号")
    private String contactMobile;

    /**
     * 学习时长
     */
    @ApiModelProperty("学习时长")
    private BigDecimal studyHours;

    /**
     * 参会证明文件oid
     */
    @ApiModelProperty("参会证明文件oid")
    private String joinProveId;

    /**
     * 参会证明文件地址
     */
    @ApiModelProperty("参会证明文件地址")
    private String joinProveUrl;

    /**
     * 参会证明文件名不带后缀
     */
    @ApiModelProperty("参会证明文件名不带后缀")
    private String joinProveName;

    /**
     * 参会证明文件名带后缀
     */
    @ApiModelProperty("参会证明文件名带后缀")
    private String joinProveNameOri;

    /**
     * 学时证明生成状态 1-未提交 2-未生成 3-生成中 4-已生成
     */
    @ApiModelProperty("学时证明生成状态 1-未提交 2-未生成 3-生成中 4-已生成")
    private Integer studyProveType;

    /**
     * 学时证明文件oid
     */
    @ApiModelProperty("学时证明文件oid")
    private String studyProveId;

    /**
     * 学时证明文件地址
     */
    @ApiModelProperty("学时证明文件地址")
    private String studyProveUrl;

    /**
     * 学时证明文件名不带后缀
     */
    @ApiModelProperty("学时证明文件名不带后缀")
    private String studyProveName;

    /**
     * 学时证明文件名带后缀
     */
    @ApiModelProperty("学时证明文件名带后缀")
    private String studyProveNameOri;

    /*
     * 方便steam流存入自身
     * */
    public BmInfoJoinerVo returnOwn() {
        return this;
    }

}
