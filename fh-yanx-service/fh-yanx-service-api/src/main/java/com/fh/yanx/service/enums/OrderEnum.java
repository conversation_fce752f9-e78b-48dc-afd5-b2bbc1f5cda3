package com.fh.yanx.service.enums;

public enum OrderEnum {

    /**
     * 订单类型：1消费、2充值、3管理员修改
     */
    ORDER_TYPE_CONSUMPTION_ENUM(1, "消费"),
    ORDER_TYPE_RECHARGE_ENUM(2, "充值"),
    ORDER_TYPE_MANAGER_EDIT(3, "管理员修改"),


    /**
     * 订单状态
     * 1：待支付、2：已付款、3：待退款、4：以退款、5：退款驳回、 6：已取消、7：已发货
     */
    ORDER_STATE_PENDING_PAY(1, "待支付"),
    ORDER_STATE_PAY(2, "已付款"),
    ORDER_STATE_PENDING_REFUNDED(3, "待退款"),
    ORDER_STATE_REFUNDED(4, "已退款"),
    ORDER_STATE_REFUNDED_REJECTION(5, "退款驳回"),
    ORDER_STATE_CANCEL_ENUM(6, "已取消"),
    ORDER_STATE_SHIPPED(7, "已发货"),
    ORDER_STATE_TIMEOUT(8, "超时未支付"),

    /**
     * 支付方式：1支付宝、2微信、3苹果、4管理员修改
     */
    PAY_MODE_ALI(1, "支付宝"),
    PAY_MODE_WE_CHAT(2, "微信"),
    PAY_MODE_IOS(3, "苹果"),
    PAY_MODE_MANAGER(4, "管理员修改"),

    PAY_SCENE_FORMAL(1, "正式"),
    PAY_SCENE_SADBOX(2, "沙箱"),

    /**
     * 商品类型（1：培训、2：案例集）
     */
    GOODS_TYPE_PX(1, "培训"),
    GOODS_TYPE_ALJ(2, "案例集"),

    /**
     * 首次支付标识:  1: 是首次支付  2:非首次支付
     */
    FIRST_PAY(1, "是首次支付"),
    NO_FIRST_PAY(2, "非首次支付"),

    /**
     * 商品为培训计划项目时（1：为自己购买、2：为他人购买）
     */
    GOOD_TRAINING_TYPE_SELF(1, "为自己购买"),
    GOOD_TRAINING_TYPE_OTHERS(2, "为他人购买"),

    REMIND_HOURS(48, "48小时未支付短信提醒"),
    TIMEOUT_HOURS(72, "72小时超时未支付取消订单");

    private final Integer code;
    private final String value;

    OrderEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
