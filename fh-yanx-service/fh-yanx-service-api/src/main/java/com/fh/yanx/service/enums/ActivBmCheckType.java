package com.fh.yanx.service.enums;

/**
 * 活动报名校验状态
 *
 * <AUTHOR>
 * @date 2023-08-04 10:23
 */
public enum ActivBmCheckType {
    SUCCESS(1, "校验通过"),
    ALREADY_SUBMIT(2, "已提交"),
    AS_JOINER_SUBMIT(3, "作为参会人已提交");

    private Integer code;
    private String value;

    ActivBmCheckType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
