package com.fh.yanx.service.res.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 结题答辩附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@Data
public class ResTopicRepAttachmentVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 课题id
     */
    @ApiModelProperty("课题id")
    private Long topicId;

    /**
     * 研究附件名称
     */
    @ApiModelProperty("研究附件名称")
    private String repName;

    /**
     * 论文摘要
     */
    @ApiModelProperty("论文摘要")
    private String repDesc;

    /**
     * 附件文件原始名称-带后缀
     */
    @ApiModelProperty("附件文件原始名称-带后缀")
    private String repFileNameOri;

    /**
     * 附件文件名称-不带后缀
     */
    @ApiModelProperty("附件文件名称-不带后缀")
    private String repFileName;

    /**
     * 附件文件地址
     */
    @ApiModelProperty("附件文件地址")
    private String repFileUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 附件类型，1：结题论文，2：答辩材料
     */
    @ApiModelProperty("附件类型，1：结题论文，2：答辩材料")
    private Integer repType;

    /**
     * 组织Id
     */
    @ApiModelProperty("组织Id")
    private Long organizationId;

    /**
     * 课题小组学生名称（逗号分隔）
     */
    @ApiModelProperty("课题小组学生名称（逗号分隔）")
    private List<String> studentNames;

    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    private String organizationName;

    /**
     * 论文作者，多个用顿号
     */
    private String repAuthorName;

    /**
     * 论文所属学校
     */
    private String repOrganizationName;

    /**
     * 发表时间
     */
    private Date repPubDate;

    /**
     * 论文：1校内自建，2校外导入
     */
    private Integer rsSource;

    /**
     * 论文第三方来源类型：1有方，2汇景
     */
    private Integer rsThirdType;

    /**
     * 首页展示：1展示
     */
    private Integer homeShow;


    /**
     * 文件oid
     */
    private String repFileOid;
}
