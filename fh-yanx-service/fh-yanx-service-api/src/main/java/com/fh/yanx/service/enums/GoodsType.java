package com.fh.yanx.service.enums;

/**
 * 订单商品类型
 *
 * <AUTHOR>
 * @date 2023-08-01 14:16
 */
public enum GoodsType {
    ACTIV_BM(1, "活动报名");

    private Integer code;
    private String value;

    GoodsType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
