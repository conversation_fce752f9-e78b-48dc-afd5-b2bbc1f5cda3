package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseCasesEditionConditionBo;
import com.fh.yanx.service.course.entity.bo.CourseCasesEditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseCasesEditionVo;

import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程版本表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-05 10:40:52
 */
public interface CourseCasesEditionApi {

    /**
     * 查询课程版本表分页列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
    @PostMapping("/course/cases/edition/page/list")
    public AjaxResult<PageInfo<CourseCasesEditionVo>> getCourseCasesEditionPageListByCondition(@RequestBody CourseCasesEditionConditionBo condition);

    /**
     * 查询课程版本表列表
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
    @PostMapping("/course/cases/edition/list")
    public AjaxResult<List<CourseCasesEditionVo>> getCourseCasesEditionListByCondition(@RequestBody CourseCasesEditionConditionBo condition);


    /**
     * 新增课程版本表
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
    @PostMapping("/course/cases/edition/add")
    public AjaxResult addCourseCasesEdition(@Validated @RequestBody CourseCasesEditionBo courseCasesEditionBo);

    /**
     * 修改课程版本表
     * @param courseCasesEditionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
    @PostMapping("/course/cases/edition/update")
    public AjaxResult updateCourseCasesEdition(@Validated @RequestBody CourseCasesEditionBo courseCasesEditionBo);

    /**
     * 查询课程版本表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
    @GetMapping("/course/cases/edition/detail")
    public AjaxResult<CourseCasesEditionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程版本表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 10:40:52
     */
    @GetMapping("/course/cases/edition/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 校本课程案例内容管理查看：查询课例+内容管理list
     *
     * @param condition 为了方便后面扩展，参数用对象传递，本期只需要传id
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -08-15 11:09:59
     */
    @PostMapping("/p/course/cases/edition/content-module")
    public AjaxResult<CourseCasesEditionVo> getContentModuleEdition(@RequestBody CourseCasesEditionConditionBo condition);

}
