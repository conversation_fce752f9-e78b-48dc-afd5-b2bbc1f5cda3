package com.fh.yanx.service.activ.service;


import com.fh.yanx.service.activ.api.ActivInfoRecordApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 新时代文化校园活动内容表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
@FeignClient(contextId = "activInfoRecordApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ActivInfoRecordApiService.ActivInfoRecordApiFallbackFactory.class)
@Component
public interface ActivInfoRecordApiService extends ActivInfoRecordApi {

    @Component
    class ActivInfoRecordApiFallbackFactory implements FallbackFactory<ActivInfoRecordApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ActivInfoRecordApiFallbackFactory.class);
        @Override
        public ActivInfoRecordApiService create(Throwable cause) {
            ActivInfoRecordApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new ActivInfoRecordApiService() {
                public AjaxResult getActivInfoRecordPageListByCondition(ActivInfoRecordConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getActivInfoRecordListByCondition(ActivInfoRecordConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addActivInfoRecord(ActivInfoRecordBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateActivInfoRecord(ActivInfoRecordBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult saveActivInfoRecords(ActivInfoBo activInfoBo) {
                    return AjaxResult.fail("保存活动内容失败");
                }

            };
        }
    }
}