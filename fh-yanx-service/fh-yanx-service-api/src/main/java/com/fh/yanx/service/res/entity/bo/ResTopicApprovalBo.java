package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课题审批意见表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicApprovalBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long topicApprovalId;

	/**
	 * 课题id
	 */
	@ApiModelProperty("课题id")
	private Long topicId;

	/**
	 * 审批老师的userOid
	 */
	@ApiModelProperty("审批老师的userOid")
	private String verifyTeacher;

	/**
	 * 课题名称建议
	 */
	@ApiModelProperty("课题名称建议")
	private String topicNameSug;

	/**
	 * 课题类别建议
	 */
	@ApiModelProperty("课题类别建议")
	private String topicTypeSug;

	/**
	 * 关联学科建议
	 */
	@ApiModelProperty("关联学科建议")
	private String relSubjectSug;

	/**
	 * 课题界定建议
	 */
	@ApiModelProperty("课题界定建议")
	private String topicDescSug;

	/**
	 * 课题背景建议
	 */
	@ApiModelProperty("课题背景建议")
	private String topicBackSug;

	/**
	 * 课题目的建议
	 */
	@ApiModelProperty("课题目的建议")
	private String topicGoalSug;

	/**
	 * 研究内容建议
	 */
	@ApiModelProperty("研究内容建议")
	private String topicContentSug;

	/**
	 * 研究方法建议
	 */
	@ApiModelProperty("研究方法建议")
	private String topicMethodSug;

	/**
	 * 研究条件建议
	 */
	@ApiModelProperty("研究条件建议")
	private String topicConditionSug;

	/**
	 * 研究计划建议
	 */
	@ApiModelProperty("研究计划建议")
	private String topicPlanSug;

	/**
	 * 研究预期成果建议
	 */
	@ApiModelProperty("研究预期成果建议")
	private String topicExpectedSug;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;


	/**
	 * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定
	 */
	@ApiModelProperty("课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定")
	private Integer topicProcess;

	/**
	 * 当前登陆用户oid
	 */
	@ApiModelProperty("当前登陆用户oid")
	private String currentUserOid;

	/**
	 * 审核意见是否有值： true:有值，false:无值
	 */
	private boolean hasValue;
}
