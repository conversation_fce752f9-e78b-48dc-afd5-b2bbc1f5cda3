package com.fh.yanx.service.pub.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.pub.api.PubBookDirectoryApi;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 融合出版书目录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@FeignClient(contextId = "pubBookDirectoryApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PubBookDirectoryApiService.PubBookDirectoryApiFallbackFactory.class)
@Component
public interface PubBookDirectoryApiService extends PubBookDirectoryApi {

    @Component
    class PubBookDirectoryApiFallbackFactory implements FallbackFactory<PubBookDirectoryApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PubBookDirectoryApiFallbackFactory.class);
        @Override
        public PubBookDirectoryApiService create(Throwable cause) {
            PubBookDirectoryApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PubBookDirectoryApiService() {
                public AjaxResult getPubBookDirectoryPageListByCondition(PubBookDirectoryConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPubBookDirectoryListByCondition(PubBookDirectoryConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPubBookDirectory(PubBookDirectoryBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePubBookDirectory(PubBookDirectoryBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}