package com.fh.yanx.service.activ.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 新时代活动日程表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
@Data
public class ActivInfoScheduleConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long scheduleId;

	/**
	 * 活动id
	 */
	@ApiModelProperty("活动id")
	private Long activId;

	/**
	 * 日程名称
	 */
	@ApiModelProperty("日程名称")
	private String scheduleName;

	/**
	 * 拉流地址
	 */
	@ApiModelProperty("拉流地址")
	private String scheduleUrl;

	/**
	 * 活动日程顺序
	 */
	@ApiModelProperty("活动日程顺序")
	private Long scheduleIndex;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
