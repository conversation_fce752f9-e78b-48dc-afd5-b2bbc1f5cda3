package com.fh.yanx.service.enums;

/**
 * <AUTHOR>
 * @date 2023-07-19 15:24
 */
public enum OrganizationAuthType {
    AUTH(1, "已认证"),
    UN_AUTH(2, "未认证");

    private Integer code;
    private String value;

    OrganizationAuthType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
