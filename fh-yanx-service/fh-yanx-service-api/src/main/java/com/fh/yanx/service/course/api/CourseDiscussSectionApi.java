package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionBo;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
/**
 * 课程讨论区表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
public interface CourseDiscussSectionApi {

    /**
     * 查询课程讨论区表分页列表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @PostMapping("/course/discuss/section/page/list")
    public AjaxResult<PageInfo<CourseDiscussSectionVo>> getCourseDiscussSectionPageListByCondition(@RequestBody CourseDiscussSectionConditionBo condition);

    /**
     * 查询课程讨论区表列表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @PostMapping("/course/discuss/section/list")
    public AjaxResult<List<CourseDiscussSectionVo>> getCourseDiscussSectionListByCondition(@RequestBody CourseDiscussSectionConditionBo condition);


    /**
     * 新增课程讨论区表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @PostMapping("/course/discuss/section/add")
    public AjaxResult addCourseDiscussSection(@Validated @RequestBody CourseDiscussSectionBo courseDiscussSectionBo);

    /**
     * 修改课程讨论区表
     * @param courseDiscussSectionBo the course discuss section bo 课程 BO
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @PostMapping("/course/discuss/section/update")
    public AjaxResult updateCourseDiscussSection(@RequestBody CourseDiscussSectionBo courseDiscussSectionBo);

    /**
     * 查询课程讨论区表详情
     * @param id the id 讨论 ID
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @GetMapping("/course/discuss/section/detail")
    public AjaxResult<CourseDiscussSectionVo> getDetail(@RequestParam("id") Long id);


    /**
     * 删除课程讨论区表
     * @param id the id 讨论 ID
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @GetMapping("/course/discuss/section/delete")
    public AjaxResult<Boolean> delete( @RequestParam("id") Long id);


    /**
     * 删除课程讨论区表
     * @param id the id 讨论 ID
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @GetMapping("/course/discuss/section/deleteByIdAndUserOid")
    public AjaxResult<Boolean> deleteByIdAndUserOid(@RequestParam("id") Long id, @RequestParam("userOid") String userOid);


}
