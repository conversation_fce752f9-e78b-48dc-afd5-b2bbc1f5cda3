package com.fh.yanx.service.activ.service;


import com.fh.yanx.service.activ.api.ActivInfoScheduleApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleConditionBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 新时代活动日程表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:29:00
 */
@FeignClient(contextId = "activInfoScheduleApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ActivInfoScheduleApiService.ActivInfoScheduleApiFallbackFactory.class)
@Component
public interface ActivInfoScheduleApiService extends ActivInfoScheduleApi {

    @Component
    class ActivInfoScheduleApiFallbackFactory implements FallbackFactory<ActivInfoScheduleApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ActivInfoScheduleApiFallbackFactory.class);
        @Override
        public ActivInfoScheduleApiService create(Throwable cause) {
            ActivInfoScheduleApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new ActivInfoScheduleApiService() {
                public AjaxResult getActivInfoSchedulePageListByCondition(ActivInfoScheduleConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getActivInfoScheduleListByCondition(ActivInfoScheduleConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addActivInfoSchedule(ActivInfoScheduleBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateActivInfoSchedule(ActivInfoScheduleBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult saveActivSchedules(ActivInfoBo activInfoBo) {
                    return AjaxResult.fail("保存活动日程失败");
                }

            };
        }
    }
}