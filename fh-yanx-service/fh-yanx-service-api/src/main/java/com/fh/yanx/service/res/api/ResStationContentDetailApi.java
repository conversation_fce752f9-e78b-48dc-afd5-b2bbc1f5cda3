package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResStationContentDetailConditionBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentDetailVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 工作站具体内容详情
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResStationContentDetailApi {

    /**
     * 查询工作站具体内容详情分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/detail/page/list")
    public AjaxResult<PageInfo<ResStationContentDetailVo>> getResStationContentDetailPageListByCondition(@RequestBody ResStationContentDetailConditionBo condition);

    /**
     * 查询工作站具体内容详情列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/detail/list")
    public AjaxResult<List<ResStationContentDetailVo>> getResStationContentDetailListByCondition(@RequestBody ResStationContentDetailConditionBo condition);


    /**
     * 新增工作站具体内容详情
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/detail/add")
    public AjaxResult addResStationContentDetail(@Validated @RequestBody ResStationContentDetailBo resStationContentDetailBo);

    /**
     * 修改工作站具体内容详情
     * @param resStationContentDetailBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/station/content/detail/update")
    public AjaxResult updateResStationContentDetail(@Validated @RequestBody ResStationContentDetailBo resStationContentDetailBo);

    /**
     * 查询工作站具体内容详情详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/content/detail/detail")
    public AjaxResult<ResStationContentDetailVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除工作站具体内容详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/station/content/detail/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
