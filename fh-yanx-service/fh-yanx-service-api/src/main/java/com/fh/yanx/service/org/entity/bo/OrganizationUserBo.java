package com.fh.yanx.service.org.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 组织用户bo
 *
 * <AUTHOR>
 * @date 2023-07-18 16:38
 */
@Data
public class OrganizationUserBo {

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 账号id
     */
    @ApiModelProperty("账号id")
    private Long accountId;

    /**
     * 联系方式
     */
    @ApiModelProperty("手机号码")
    private String phone;

    /**
     * 验证码
     */
    @ApiModelProperty("验证码")
    private String code;

    /**
     * uuid
     */
    @ApiModelProperty("uuid")
    private String uuid;

    /**
     * 验证码使用类型{@link com.fh.yanx.service.enums.SmsUseType}
     */
    private Integer smsUseType;
}
