package com.fh.yanx.service.course.entity.vo;

import com.fh.yanx.service.org.entity.vo.UserVoExt;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 课程讨论区表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
@Data
public class CourseDiscussSectionVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 校本课程案例ID
     */
    @ApiModelProperty("校本课程案例ID")
    private Long casesId;

    /**
     * 0-一级评论 不为0为对某条评论的回复
     */
    @ApiModelProperty("0-一级评论 不为0为对某条评论的回复")
    private Long parentId;

    /**
     * 所有父级id英文逗号拼接
     */
    @ApiModelProperty("所有父级id英文逗号拼接")
    private String superiorIds;

    /**
     * 评论用户oid
     */
    @ApiModelProperty("评论用户oid")
    private String userOid;

    /**
     * 评论内容
     */
    @ApiModelProperty("评论内容")
    private String content;

    /**
     * 回复数量
     */
    @ApiModelProperty("回复数量")
    private Integer replyNum;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 是否案例持有人 0 否 1 是
     */
    @ApiModelProperty("是否案例持有人 0 否  1 是")
    private Integer isCasesUser;
    /*
     * ########################### 拓展信息字段 ###################
     */
    /**
     * 用户信息
     */
    private UserVoExt user;


    /*
     * 方便steam流存入自身
     * */
    public CourseDiscussSectionVo returnOwn() {
        return this;
    }

}
