package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResInformationConditionBo;
import com.fh.yanx.service.res.entity.bo.ResInformationBo;
import com.fh.yanx.service.res.entity.vo.ResInformationVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 资讯
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
public interface ResInformationApi {

    /**
     * 查询资讯分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("res/information/page/list")
    public AjaxResult<PageInfo<ResInformationVo>> getResInformationPageListByCondition(@RequestBody ResInformationConditionBo condition);

    /**
     * 查询资讯列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("res/information/list")
    public AjaxResult<List<ResInformationVo>> getResInformationListByCondition(@RequestBody ResInformationConditionBo condition);


    /**
     * 新增资讯
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("res/information/add")
    public AjaxResult addResInformation(@Validated @RequestBody ResInformationBo resInformationBo);

    /**
     * 修改资讯
     * @param resInformationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("res/information/update")
    public AjaxResult updateResInformation(@Validated @RequestBody ResInformationBo resInformationBo);

    /**
     * 查询资讯详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @GetMapping("res/information/detail")
    public AjaxResult<ResInformationVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除资讯
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @GetMapping("res/information/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
