package com.fh.yanx.service.activ.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 新时代文化校园活动介绍表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:43
 */
@Data
public class ActivInfoIntroduceVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long introduceId;

    /**
     * FK新时代文化校园活动信息表的id
     */
    @ApiModelProperty("FK新时代文化校园活动信息表的id")
    private Long activId;

    /**
     * 介绍类型：1标题，2图片，3文字
     */
    @ApiModelProperty("介绍类型：1标题，2图片，3文字")
    private Integer introduceType;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    private Long introduceSort;

    /**
     * 活动标题
     */
    @ApiModelProperty("活动标题")
    private String introduceTitle;

    /**
     * 活动介绍图片文件oid
     */
    @ApiModelProperty("活动介绍图片文件oid")
    private String introduceImageId;

    /**
     * 活动介绍图片文件地址
     */
    @ApiModelProperty("活动介绍图片文件地址")
    private String introduceImageUrl;

    /**
     * 活动介绍内容
     */
    @ApiModelProperty("活动介绍内容")
    private String introduceContent;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ActivInfoIntroduceVo returnOwn() {
        return this;
    }

}
