package com.fh.yanx.service.banner.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;

/**
 * 新时代文化校园banner信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
@Data
public class BannerInfoConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long bannerInfoId;

    /**
     * banner位置 1-移动端活动管理
     */
    @ApiModelProperty("banner位置 1-移动端活动管理")
    private Integer bannerShowType;

    /**
     * banner图片文件oid
     */
    @ApiModelProperty("banner图片文件oid")
    private String bannerImageId;

    /**
     * banner图片文件url
     */
    @ApiModelProperty("banner图片文件url")
    private String bannerImageUrl;

    /**
     * banner跳转url
     */
    @ApiModelProperty("banner跳转url")
    private String jumpUrl;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTime;

    /**
     * 截止时间类型 1-永久 2-选定日期
     */
    @ApiModelProperty("截止时间类型 1-永久 2-选定日期")
    private Integer endType;

    /**
     * 截止时间
     */
    @ApiModelProperty("截止时间")
    private Date endTime;

    /**
     * 状态 1-待发布 2-已发布 3-已下架
     */
    @ApiModelProperty("状态 1-待发布 2-已发布 3-已下架")
    private Integer state;


    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

}
