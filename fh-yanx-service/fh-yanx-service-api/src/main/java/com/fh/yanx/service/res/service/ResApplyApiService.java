package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResApplyApi;
import com.fh.yanx.service.res.entity.bo.ResApplyBo;
import com.fh.yanx.service.res.entity.bo.ResApplyConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 合作意向申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-27 16:19:35
 */
@FeignClient(contextId = "resApplyApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResApplyApiService.ResApplyApiFallbackFactory.class)
@Component
public interface ResApplyApiService extends ResApplyApi {

    @Component
    class ResApplyApiFallbackFactory implements FallbackFactory<ResApplyApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResApplyApiFallbackFactory.class);

        @Override
        public ResApplyApiService create(Throwable cause) {
            ResApplyApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new ResApplyApiService() {
                public AjaxResult getResApplyPageListByCondition(ResApplyConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResApplyListByCondition(ResApplyConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResApply(ResApplyBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResApply(ResApplyBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}