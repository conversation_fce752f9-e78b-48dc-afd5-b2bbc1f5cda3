package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicSubjectApi;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectBo;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 关联科目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@FeignClient(contextId = "resTopicSubjectApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicSubjectApiService.ResTopicSubjectApiFallbackFactory.class)
@Component
public interface ResTopicSubjectApiService extends ResTopicSubjectApi {

    @Component
    class ResTopicSubjectApiFallbackFactory implements FallbackFactory<ResTopicSubjectApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicSubjectApiFallbackFactory.class);

        @Override
        public ResTopicSubjectApiService create(Throwable cause) {
            ResTopicSubjectApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicSubjectApiService() {
                public AjaxResult getResTopicSubjectPageListByCondition(ResTopicSubjectConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicSubjectListByCondition(ResTopicSubjectConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicSubject(ResTopicSubjectBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicSubject(ResTopicSubjectBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}