package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicHonorAttachmentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 课题荣誉表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@FeignClient(contextId = "resTopicHonorAttachmentApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicHonorAttachmentApiService.ResTopicHonorAttachmentApiFallbackFactory.class)
@Component
public interface ResTopicHonorAttachmentApiService extends ResTopicHonorAttachmentApi {

    @Component
    class ResTopicHonorAttachmentApiFallbackFactory implements FallbackFactory<ResTopicHonorAttachmentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicHonorAttachmentApiFallbackFactory.class);

        @Override
        public ResTopicHonorAttachmentApiService create(Throwable cause) {
            ResTopicHonorAttachmentApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicHonorAttachmentApiService() {
                public AjaxResult getResTopicHonorAttachmentPageListByCondition(ResTopicHonorAttachmentConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicHonorAttachmentListByCondition(ResTopicHonorAttachmentConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicHonorAttachment(ResTopicHonorAttachmentBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicHonorAttachment(ResTopicHonorAttachmentBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult deleteAndSaveTopicHonorAttachmentBatch(List<ResTopicHonorAttachmentBo> resTopicHonorAttachmentBos) {
                    return AjaxResult.fail("学校评定添加荣誉失败");
                }
            };
        }
    }
}