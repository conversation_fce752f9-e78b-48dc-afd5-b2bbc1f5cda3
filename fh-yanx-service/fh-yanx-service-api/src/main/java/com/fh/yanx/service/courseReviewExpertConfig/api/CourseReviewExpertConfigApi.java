package com.fh.yanx.service.courseReviewExpertConfig.api;


import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程审核专家配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-05 14:13:59
 */
public interface CourseReviewExpertConfigApi {

    /**
     * 查询课程审核专家配置表分页列表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/review/expert/config/page/list")
    public AjaxResult<PageInfo<CourseReviewExpertConfigVo>> getCourseReviewExpertConfigPageListByCondition(@RequestBody CourseReviewExpertConfigConditionBo condition);

    /**
     * 查询课程审核专家配置表列表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/review/expert/config/list")
    public AjaxResult<List<CourseReviewExpertConfigVo>> getCourseReviewExpertConfigListByCondition(@RequestBody CourseReviewExpertConfigConditionBo condition);


    /**
     * 新增课程审核专家配置表
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/review/expert/config/add")
    public AjaxResult addCourseReviewExpertConfig(@Validated @RequestBody List<CourseReviewExpertConfigBo> courseReviewExpertConfigBo);

    /**
     * 修改课程审核专家配置表
     * @param courseReviewExpertConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @PostMapping("/course/review/expert/config/update")
    public AjaxResult updateCourseReviewExpertConfig(@Validated @RequestBody CourseReviewExpertConfigBo courseReviewExpertConfigBo);

    /**
     * 查询课程审核专家配置表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @GetMapping("/course/review/expert/config/detail")
    public AjaxResult<CourseReviewExpertConfigVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除课程审核专家配置表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-05 14:13:59
     */
    @GetMapping("/course/review/expert/config/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
