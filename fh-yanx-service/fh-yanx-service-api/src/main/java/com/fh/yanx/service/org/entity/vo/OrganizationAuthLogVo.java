package com.fh.yanx.service.org.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 组织认证日志记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
@Data
public class OrganizationAuthLogVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 认证状态 1-已认证 2-未认证
     */
    @ApiModelProperty("认证状态 1-已认证 2-未认证")
    private Integer authType;

    /**
     * 认证截止时间
     */
    @ApiModelProperty("认证截止时间")
    private Date authEndDay;

    /**
     * 是否删除：0：否，1：是
     */
    @ApiModelProperty("是否删除：0：否，1：是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /*
     * 方便steam流存入自身
     * */
    public OrganizationAuthLogVo returnOwn() {
        return this;
    }

}
