package com.fh.yanx.service.pub.api;


import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentBo;
import com.fh.yanx.service.pub.entity.vo.PubBookDirectoryAttachmentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 融合出版书目录附件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
public interface PubBookDirectoryAttachmentApi {

    /**
     * 查询融合出版书目录附件分页列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/attachment/page/list")
    public AjaxResult<PageInfo<PubBookDirectoryAttachmentVo>> getPubBookDirectoryAttachmentPageListByCondition(@RequestBody PubBookDirectoryAttachmentConditionBo condition);

    /**
     * 查询融合出版书目录附件列表
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/attachment/list")
    public AjaxResult<List<PubBookDirectoryAttachmentVo>> getPubBookDirectoryAttachmentListByCondition(@RequestBody PubBookDirectoryAttachmentConditionBo condition);


    /**
     * 新增融合出版书目录附件
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/attachment/add")
    public AjaxResult addPubBookDirectoryAttachment(@Validated @RequestBody PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo);

    /**
     * 修改融合出版书目录附件
     * @param pubBookDirectoryAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @PostMapping("/pub/book/directory/attachment/update")
    public AjaxResult updatePubBookDirectoryAttachment(@Validated @RequestBody PubBookDirectoryAttachmentBo pubBookDirectoryAttachmentBo);

    /**
     * 查询融合出版书目录附件详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @GetMapping("/pub/book/directory/attachment/detail")
    public AjaxResult<PubBookDirectoryAttachmentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除融合出版书目录附件
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-19 18:40:43
     */
    @GetMapping("/pub/book/directory/attachment/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
