package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 课程推荐表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-12-06 10:47:11
 */
@Data
public class CourseRecommendAddBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 校本课程案例ID
	 */
	@ApiModelProperty("校本课程案例ID")
	private Long casesId;
	/**
	 * 推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）
	 */
	@ApiModelProperty("推荐类型（1：首页典型案例，2：频道页精品课程，3：频道页校本课程,4:频道页精品课程）")
	private List<Integer> recommendTypeList;

}
