package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResApplyConditionBo;
import com.fh.yanx.service.res.entity.bo.ResApplyBo;
import com.fh.yanx.service.res.entity.vo.ResApplyVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 合作意向申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-27 16:19:35
 */
public interface ResApplyApi {

    /**
     * 查询合作意向申请表分页列表
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
    @PostMapping("res/apply/page/list")
    public AjaxResult<PageInfo<ResApplyVo>> getResApplyPageListByCondition(@RequestBody ResApplyConditionBo condition);

    /**
     * 查询合作意向申请表列表
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
    @PostMapping("res/apply/list")
    public AjaxResult<List<ResApplyVo>> getResApplyListByCondition(@RequestBody ResApplyConditionBo condition);


    /**
     * 新增合作意向申请表
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
    @PostMapping("res/apply/add")
    public AjaxResult addResApply(@Validated @RequestBody ResApplyBo resApplyBo);

    /**
     * 修改合作意向申请表
     * @param resApplyBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
    @PostMapping("res/apply/update")
    public AjaxResult updateResApply(@Validated @RequestBody ResApplyBo resApplyBo);

    /**
     * 查询合作意向申请表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
    @GetMapping("res/apply/detail")
    public AjaxResult<ResApplyVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除合作意向申请表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-27 16:19:35
     */
    @GetMapping("res/apply/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
