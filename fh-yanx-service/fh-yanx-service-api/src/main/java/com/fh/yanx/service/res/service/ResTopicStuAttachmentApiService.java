package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResTopicStuAttachmentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 课题研究附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@FeignClient(contextId = "resTopicStuAttachmentApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResTopicStuAttachmentApiService.ResTopicStuAttachmentApiFallbackFactory.class)
@Component
public interface ResTopicStuAttachmentApiService extends ResTopicStuAttachmentApi {

    @Component
    class ResTopicStuAttachmentApiFallbackFactory implements FallbackFactory<ResTopicStuAttachmentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResTopicStuAttachmentApiFallbackFactory.class);

        @Override
        public ResTopicStuAttachmentApiService create(Throwable cause) {
            ResTopicStuAttachmentApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResTopicStuAttachmentApiService() {
                public AjaxResult getResTopicStuAttachmentPageListByCondition(ResTopicStuAttachmentConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResTopicStuAttachmentListByCondition(ResTopicStuAttachmentConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResTopicStuAttachment(ResTopicStuAttachmentBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResTopicStuAttachment(ResTopicStuAttachmentBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult updateBatchResTopicStuAttachment(List<ResTopicStuAttachmentBo> resTopicStuAttachmentBos) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult deleteByFileOid(String fileOid) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}