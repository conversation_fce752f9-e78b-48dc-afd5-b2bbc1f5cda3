package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicPvConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicPvBo;
import com.fh.yanx.service.res.entity.vo.ResTopicPvVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课题pv记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicPvApi {

    /**
     * 查询课题pv记录分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/pv/page/list")
    public AjaxResult<PageInfo<ResTopicPvVo>> getResTopicPvPageListByCondition(@RequestBody ResTopicPvConditionBo condition);

    /**
     * 查询课题pv记录列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/pv/list")
    public AjaxResult<List<ResTopicPvVo>> getResTopicPvListByCondition(@RequestBody ResTopicPvConditionBo condition);


    /**
     * 新增课题pv记录
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/pv/add")
    public AjaxResult addResTopicPv(@Validated @RequestBody ResTopicPvBo resTopicPvBo);

    /**
     * 修改课题pv记录
     * @param resTopicPvBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/pv/update")
    public AjaxResult updateResTopicPv(@Validated @RequestBody ResTopicPvBo resTopicPvBo);

    /**
     * 查询课题pv记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/pv/detail")
    public AjaxResult<ResTopicPvVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课题pv记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/pv/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
