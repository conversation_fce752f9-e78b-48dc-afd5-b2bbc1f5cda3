package com.fh.yanx.service.course.api;

import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesBo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 校本课程案例
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesApi {

    /**
     * 查询校本课程案例分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/page/list")
    public AjaxResult<PageInfo<PCourseCasesVo>>
        getPCourseCasesPageListByCondition(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 查询我的收藏校本课程案例分页列表
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/store-list-page")
    public AjaxResult<PageInfo<PCourseCasesVo>>
        getStoreCourseCasesPageListByCondition(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 查询校本课程案例列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/list")
    public AjaxResult<List<PCourseCasesVo>>
        getPCourseCasesListByCondition(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 新增校本课程案例
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/add")
    public AjaxResult addPCourseCases(@Validated @RequestBody PCourseCasesBo pCourseCasesBo);

    /**
     * 修改校本课程案例
     * 
     * @param pCourseCasesBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/update")
    public AjaxResult updatePCourseCases(@Validated @RequestBody PCourseCasesBo pCourseCasesBo);

    /**
     * 查询校本课程案例详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/detail")
    public AjaxResult<PCourseCasesVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除校本课程案例
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询校本课程案例首页列表
     * 
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/page/home-list")
    public AjaxResult getHomeList(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 查询校本课程案例首页列表
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/page/home-detail")
    public AjaxResult homeDetail(@RequestParam("casesId") Long casesId, @RequestParam(value = "queryEdition", required = false) Boolean queryEdition);

    /**
     * 查询校本课程案例首页推荐数据列表
     *
     * <AUTHOR>
     * @date 2023-08-15 11:09:59
     */
    @PostMapping("/p/course/cases/page/top-list")
    public AjaxResult topList(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 校本课程案例内容管理查看：查询课例+内容管理list
     *
     * @param condition 为了方便后面扩展，参数用对象传递，本期只需要传id
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -08-15 11:09:59
     */
    @PostMapping("/p/course/cases/content-module")
    public AjaxResult<PCourseCasesVo> getContentModule(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 校本课程案例内容管理保存
     *
     * @param pCourseCasesBo the p course cases bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -08-15 11:09:59
     */
    @PostMapping("/p/course/cases/content-module/save")
    public AjaxResult saveContentModule(@RequestBody PCourseCasesBo pCourseCasesBo);

    /**
     * 查询校本课程案例列表
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/teacher/apply-course-list")
    public AjaxResult getTeacherApplyCourseList(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 更新专家评论展示
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/updateExpertCommentaryShowcase")
    public AjaxResult
    updateExpertCommentaryShowcase(@RequestBody List<CourseVerifyLogBo> courseVerifyLogList);

    /**
     * 更新专家评论展示
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/updateSupplementState")
    public AjaxResult
    updateSupplementState(@RequestBody PCourseCasesBo pCourseCasesBo);

    /**
     * 专家审核列表
     *
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/expert/verify-course-list")
    public AjaxResult<PageInfo<PCourseCasesVo>>
        getExpertVerifyCourseList(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 综评课程列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult<com.github.pagehelper.PageInfo<com.fh.yanx.service.course.entity.vo.PCourseCasesVo>>
     * <AUTHOR>
     * @date 2024/12/9 11:05
     **/
    @PostMapping("/comprehensive/course-list")
    public AjaxResult<PageInfo<PCourseCasesVo>>
        getComprehensiveCourseList(@RequestBody PCourseCasesConditionBo condition);

    /**
     * 获取专家助理课程列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult<com.github.pagehelper.PageInfo<com.fh.yanx.service.course.entity.vo.PCourseCasesVo>>
     * <AUTHOR>
     * @date 2024/12/16 17:01
     **/
    @PostMapping("/expert-assistant/course-list")
    public AjaxResult<PageInfo<PCourseCasesVo>>
        getExpertAssistantCourseList(@RequestBody PCourseCasesConditionBo condition);

}
