package com.fh.yanx.service.banner.api;

import com.fh.yanx.service.banner.entity.bo.BannerInfoBo;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.fh.yanx.service.banner.entity.vo.BannerInfoVo;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新时代文化校园banner信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:47:43
 */
public interface BannerInfoApi {

    /**
     * 查询新时代文化校园banner信息表分页列表
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
    @PostMapping("/banner/page/list")
    public AjaxResult<PageInfo<BannerInfoVo>> getBannerPageListByCondition(@RequestBody BannerInfoConditionBo condition);

    /**
     * 查询新时代文化校园banner信息表列表
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
    @PostMapping("/banner/list")
    public AjaxResult<List<BannerInfoVo>> getBannerListByCondition(@RequestBody BannerInfoConditionBo condition);


    /**
     * 新增新时代文化校园banner信息表
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
    @PostMapping("/banner/add")
    public AjaxResult addBanner(@Validated @RequestBody BannerInfoBo bannerInfoBo);

    /**
     * 修改新时代文化校园banner信息表
     * @param bannerInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
    @PostMapping("/banner/update")
    public AjaxResult updateBanner(@Validated @RequestBody BannerInfoBo bannerInfoBo);

    /**
     * 查询新时代文化校园banner信息表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
    @GetMapping("/banner/detail")
    public AjaxResult<BannerInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除新时代文化校园banner信息表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-10-24 14:47:43
     */
    @GetMapping("/banner/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 修改banner状态
     *
     * @param bannerInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/25 10:07
     **/
    @PostMapping("/banner/change-state")
    public AjaxResult changeState(@RequestBody BannerInfoBo bannerInfoBo);

}
