package com.fh.yanx.service.pub.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 融合出版书目录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@Data
public class PubBookDirectoryBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long bookDirectoryId;

	/**
	 * FK出版书id，pub_book表逐渐
	 */
	@ApiModelProperty("FK出版书id，pub_book表逐渐")
	private Long bookId;

	/**
	 * 目录名称
	 */
	@ApiModelProperty("目录名称")
	private String bookDirectoryName;

	/**
	 * 作者名称
	 */
	@ApiModelProperty("作者名称")
	private String authorName;

	/**
	 * 作者学校
	 */
	@ApiModelProperty("作者学校")
	private String authorSchool;

	/**
	 * 书籍目录顺序
	 */
	@ApiModelProperty("书籍目录顺序")
	private String bookDirectoryIndex;


	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
