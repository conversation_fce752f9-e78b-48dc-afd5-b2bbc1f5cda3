package com.fh.yanx.service.res.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课题流程记录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@Data
public class ResTopicProcessRecordBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long processRecordId;

	/**
	 * 课题id
	 */
	@ApiModelProperty("课题id")
	private Long topicId;

	/**
	 * 过程名称
	 */
	@ApiModelProperty("过程名称")
	private String processName;

	/**
	 * 课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定
	 */
	@ApiModelProperty("课题过程进度：1暂存，2提交、教师审批中，3教师审核驳回，4教师审核通过，5提交材料，6学生评定，7教师评定，8学校评定")
	private Integer topicProcess;

	/**
	 * 流程提交人
	 */
	@ApiModelProperty("流程提交人")
	private String processSubmitUser;

	/**
	 * 流程审批人
	 */
	@ApiModelProperty("流程审批人")
	private String processVerifyUser;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
