package com.fh.yanx.service.user.entity.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-12-09  13:58
 */
@Data
public class TeacherImportExcelBo extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = {"新时代教师导入模板", "*姓名"}, index = 0)
    private String realName;

    @ExcelProperty(value = {"新时代教师导入模板", "*手机号"}, index = 1)
    private String phone;

    @ExcelProperty(value = {"新时代教师导入模板", "*职务"}, index = 2)
    private String position;

    @ExcelProperty(value = {"新时代教师导入模板", "*职称"}, index = 3)
    private String titleName;
}
