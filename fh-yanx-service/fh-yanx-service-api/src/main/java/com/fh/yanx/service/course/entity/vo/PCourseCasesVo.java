package com.fh.yanx.service.course.entity.vo;

import com.fh.yanx.service.org.entity.vo.UserVoExt;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 校本课程案例
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
public class PCourseCasesVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long userId;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String schoolName;

    /**
     * 课程名称
     */
    @ApiModelProperty("课程名称")
    private String courseName;

    /**
     * 省份ID
     */
    @ApiModelProperty("省份ID")
    private Long provinceId;

    /**
     * 市区ID
     */
    @ApiModelProperty("市区ID")
    private Long cityId;

    /**
     * 区县ID
     */
    @ApiModelProperty("区县ID")
    private Long countyId;

    /**
     * 学段
     */
    @ApiModelProperty("学段")
    private Long phase;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private Long year;

    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String picture;

    /**
     * 
     */
    @ApiModelProperty("")
    private Integer status;

    /**
     * 实施者
     */
    @ApiModelProperty("实施者")
    private String operation;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String phone;

    /**
     * 简介
     */
    @ApiModelProperty("简介")
    private String introduction;

    /**
     * 浏览量
     */
    @ApiModelProperty("浏览量")
    private Long views;

    /**
     * 收藏量
     */
    @ApiModelProperty("收藏量")
    private Long store;

    /**
     * 评论数量
     */
    @ApiModelProperty("评论数量")
    private Long discussNum;

    /**
     * 是否授权 0 否 1是
     */
    @ApiModelProperty("是否授权 0 否 1是")
    private Integer isAuth;

    /**
     * 展示首页的类型，1：大首页，2：校本课程平台首页
     */
    @ApiModelProperty("展示首页的类型，1：大首页，2：校本课程平台首页")
    private Integer homeType;

    /**
     * 0-未删除 1-已删除
     */
    @ApiModelProperty("0-未删除 1-已删除")
    private Integer isDelete;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date createDate;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date updateDate;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String filePath;

    /**
     * word文档地址
     */
    @ApiModelProperty("word文档地址")
    private String wordPath;

    /**
     * 是否上传文件 ： 0 否 1是
     */
    @ApiModelProperty("是否上传文件 ： 0 否 1是")
    private Integer isUpload;

    /**
     * 圆梦表的课程上传人id
     */
    @ApiModelProperty("")
    private Long customerId;

    /**
     * 校本课程案例类型：0课程案例，1典型案例，2精品课程-特色课程，3精品课程-整体课程
     */
    @ApiModelProperty("校本课程案例类型：0课程案例，1典型案例，2精品课程")
    private Integer isExcellent;

    /**
     * 课程上下架：1上架，2下架
     */
    @ApiModelProperty("课程上下架：1上架，2下架")
    private Integer holdType;

    /*
     * 方便steam流存入自身
     * */
    public PCourseCasesVo returnOwn() {
        return this;
    }

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 校本课程案例详细信息
     */
    private PCourseCasesInfoVo courseCasesInfoVo;

    /**
     * 类型名称集合
     */
    private List<String> typeNameList;

    /**
     * 学段名称
     */
    private String phaseName;

    /**
     * 年份名称
     */
    private String yearName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区名称
     */
    private String countyName;

    /**
     * 浏览量
     */
    private String viewsStr;

    /**
     * 收藏量
     */
    private String storeStr;

    /**
     * 推荐说明
     */
    private String recommendText;

    /**
     * 内容管理-内容模块信息
     */
    private List<CourseModuleVo> courseModuleVos;

    /**
     * 新时代用户的oid
     */
    @ApiModelProperty("新时代用户的oid")
    private String userOid;
    /**
     * 来源类型：1资源网导入，2新时代新增
     */
    @ApiModelProperty("来源类型：1资源网导入，2新时代新增")
    private Integer sourceType;
    /**
     * 精品课程审核状态：1待审核，2审核通过，3审核不通过
     */
    @ApiModelProperty("精品课程审核状态：1待审核，2审核通过，3审核不通过")
    private Integer bestVerifyType;
    /**
     * 模板类型：默认空，1特色课程模板，2整体课程模板
     */
    @ApiModelProperty("模板类型：默认空，1特色课程模板，2整体课程模板")
    private Integer templateType;
    /**
     * 精品课程的模板内容布局-tab1的布局，存储布局json信息
     */
    @ApiModelProperty("精品课程的模板内容布局-tab1的布局，存储布局json信息")
    private String templateLayoutOne;
    /**
     * 精品课程的模板内容布局-tab2的布局，存储布局json信息
     */
    @ApiModelProperty("精品课程的模板内容布局-tab2的布局，存储布局json信息")
    private String templateLayoutTwo;
    /**
     * 精品课程的模板内容布局-tab3的布局，存储布局json信息
     */
    @ApiModelProperty("精品课程的模板内容布局-tab3的布局，存储布局json信息")
    private String templateLayoutThree;

    /**
     * 普通课程审核状态：默认空，1待审核（已提交），2审核通过，3审核不通过，4仅保存
     */
    @ApiModelProperty("普通课程审核状态")
    private Integer normalVerifyType;

    /**
     * 审核进度类型，默认空：1初审，2二审，3运营审核
     */
    @ApiModelProperty("审核进度类型")
    private Integer verifyProcessType;
    /**
     * 课程类型标签，曾经是这个类型的都会被记录，类型同is_excellent，多个使用英文逗号分割
     */
    @ApiModelProperty("课程类型标签，曾经是这个类型的都会被记录，类型同is_excellent，多个使用英文逗号分割")
    private String isExcellentLabel;

    /**
     * 年级
     */
    private List<PCourseCasesGradeVo> courseCasesGradeVoList;
    /**
     * 类型
     */
    private List<PCourseCasesTypeVo> courseCasesTypeVoList;

    /**
     * 该课程所有专家打分总和。（course_verify_log表一个课程所有专家打分的总和）
     */
    private String courseVerifyScoreTotal;

    /**
     * 该课程所有专家打分的平均分（course_verity_log表一个课程所有专家打分总和的平均值）
     */
    private String courseVerifyScoreAvg;

    /**
     * 当前用户打分
     */
    private String currentUserVerifyScore;

    /**
     * 登录状态 true：已登录
     */
    private boolean loginStatus = false;

    /**
     * 组织认证状态 1-已认证 2-未认证
     */
    private Integer orgAuthType;

    /**
     * 审核操作状态 1-未审核 2-已审核
     */
    private Integer verifyOperateType;

    /**
     * 审核记录
     */
    private List<CourseVerifyLogVo> courseVerifyLogList;

    /**
     * 加权分信息
     */
    private CourseWeightedScoreVo courseWeightedScoreVo;

    /**
     * 观看权限（数字,分隔） 2-未认证学校用户 3-一认证学校用户 4-案例持有者
     */
    @ApiModelProperty("观看权限")
    private String viewPermission;

    /**
     * true:有权限查看 false:无权限查看
     */
    @ApiModelProperty("是否有权限查看")
    private boolean viewAuth;

    @ApiModelProperty("专家助理列表")
    private List<UserVoExt> expertAssistantList;

    @ApiModelProperty("审核专家列表")
    private List<UserVoExt> auditExpertsList;

    @ApiModelProperty("指导专家列表")
    private List<UserVoExt> guidanceSpecialistsList;

    @ApiModelProperty("当前用户采纳意见 1-建议采纳 2-建议不采纳")
    private Integer currentUserAdoptType;

    @ApiModelProperty("采纳意见 1-建议采纳 2-建议不采纳")
    private Integer adoptType;

    @ApiModelProperty("建议采纳数量")
    private Integer adoptCount;

    @ApiModelProperty("建议不采纳数量")
    private Integer rejectCount;

    @ApiModelProperty("是否需要评分 1-不需要评分 2-需要评分")
    private Integer needScore;

    @ApiModelProperty("课程案例版本记录")
    private List<CourseCasesEditionVo> courseCasesEditionList;

    @ApiModelProperty("是否收藏")
    private Boolean isCollected;

    @ApiModelProperty("宗评状态（1：未评，2已评）")
    private Integer comprehensiveOperateTypes;

    @ApiModelProperty("派单状态（1：未派单，2已派单）")
    private Integer dispatchOperateType;

    @ApiModelProperty("案例审核及版本提交记录")
    private List<CourseCasesVerifyLogAndEditionVo> courseCasesVerifyLogAndEditionList;

    @ApiModelProperty("是否专家推荐,True,推荐，false:不推荐")
    private Boolean isRecommend;
    /**
     * 是否增补，0：不增补，1：增补
     */
    @ApiModelProperty("是否增补，0：不增补，1：增补")
    private Integer isSupplement;

    /**
     * 推荐类型集合
     */
    @ApiModelProperty("推荐类型集合")
    private List<Integer> recommendTypeList;

    /**
     * 是否查询评语
     */
    private Boolean queryReviews;

    @ApiModelProperty("育人理念")
    private String eduPhilosophy;
}
