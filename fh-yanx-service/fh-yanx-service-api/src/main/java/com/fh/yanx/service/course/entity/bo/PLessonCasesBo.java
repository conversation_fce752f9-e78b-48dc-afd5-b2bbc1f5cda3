package com.fh.yanx.service.course.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课例表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
@Data
public class PLessonCasesBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@ApiModelProperty("ID")
	private Long id;

	/**
	 * 标题
	 */
	@ApiModelProperty("标题")
	private String title;

	/**
	 * 描述
	 */
	@ApiModelProperty("描述")
	private String description;

	/**
	 * 封面
	 */
	@ApiModelProperty("封面")
	private String cover;

	/**
	 * 课例实验区id
	 */
	@ApiModelProperty("课例实验区id")
	private Long lcZone;

	/**
	 * 课例科目
	 */
	@ApiModelProperty("课例科目")
	private Long lcSubject;

	/**
	 * 媒体id
	 */
	@ApiModelProperty("媒体id")
	private String mediaId;

	/**
	 * 数据来源：1汇景数据
	 */
	@ApiModelProperty("数据来源：1汇景数据")
	private Integer sourceType;



	/**
	 * 
	 */
	@ApiModelProperty("")
	private String createUserOid;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String remark;

	/**
	 * 组织ID
	 */
	@ApiModelProperty("组织ID")
	private Long organizationId;

	/**
	 * 删除状态
	 */
	@ApiModelProperty("删除状态")
	private Integer isDelete;

}
