package com.fh.yanx.service.org.api;


import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogConditionBo;
import com.fh.yanx.service.org.entity.bo.OrganizationAuthLogBo;
import com.fh.yanx.service.org.entity.vo.OrganizationAuthLogVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 组织认证日志记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-19 15:36:19
 */
public interface OrganizationAuthLogApi {

    /**
     * 查询组织认证日志记录表分页列表
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
    @PostMapping("/organization/auth/log/page/list")
    public AjaxResult<PageInfo<OrganizationAuthLogVo>> getOrganizationAuthLogPageListByCondition(@RequestBody OrganizationAuthLogConditionBo condition);

    /**
     * 查询组织认证日志记录表列表
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
    @PostMapping("/organization/auth/log/list")
    public AjaxResult<List<OrganizationAuthLogVo>> getOrganizationAuthLogListByCondition(@RequestBody OrganizationAuthLogConditionBo condition);


    /**
     * 新增组织认证日志记录表
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
    @PostMapping("/organization/auth/log/add")
    public AjaxResult addOrganizationAuthLog(@Validated @RequestBody OrganizationAuthLogBo organizationAuthLogBo);

    /**
     * 修改组织认证日志记录表
     * @param organizationAuthLogBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
    @PostMapping("/organization/auth/log/update")
    public AjaxResult updateOrganizationAuthLog(@Validated @RequestBody OrganizationAuthLogBo organizationAuthLogBo);

    /**
     * 查询组织认证日志记录表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
    @GetMapping("/organization/auth/log/detail")
    public AjaxResult<OrganizationAuthLogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除组织认证日志记录表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-19 15:36:19
     */
    @GetMapping("/organization/auth/log/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
