package com.fh.yanx.service.pub.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.pub.api.PubBookApi;
import com.fh.yanx.service.pub.entity.bo.PubBookBo;
import com.fh.yanx.service.pub.entity.bo.PubBookConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 融合出版书
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 18:40:43
 */
@FeignClient(contextId = "pubBookApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = PubBookApiService.PubBookApiFallbackFactory.class)
@Component
public interface PubBookApiService extends PubBookApi {

    @Component
    class PubBookApiFallbackFactory implements FallbackFactory<PubBookApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(PubBookApiFallbackFactory.class);
        @Override
        public PubBookApiService create(Throwable cause) {
            PubBookApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new PubBookApiService() {
                public AjaxResult getPubBookPageListByCondition(PubBookConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getPubBookListByCondition(PubBookConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addPubBook(PubBookBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updatePubBook(PubBookBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult incrBookReadTimes(Long bookId) {
                    return AjaxResult.fail("增加阅读次数失败");
                }
            };
        }
    }
}