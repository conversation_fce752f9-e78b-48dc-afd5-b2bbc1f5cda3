package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesGradeBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesGradeVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 校本课程案例年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesGradeApi {

    /**
     * 查询校本课程案例年级分页列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/grade/page/list")
    public AjaxResult<PageInfo<PCourseCasesGradeVo>> getPCourseCasesGradePageListByCondition(@RequestBody PCourseCasesGradeConditionBo condition);

    /**
     * 查询校本课程案例年级列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/grade/list")
    public AjaxResult<List<PCourseCasesGradeVo>> getPCourseCasesGradeListByCondition(@RequestBody PCourseCasesGradeConditionBo condition);


    /**
     * 新增校本课程案例年级
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/grade/add")
    public AjaxResult addPCourseCasesGrade(@Validated @RequestBody PCourseCasesGradeBo pCourseCasesGradeBo);

    /**
     * 修改校本课程案例年级
     * @param pCourseCasesGradeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/grade/update")
    public AjaxResult updatePCourseCasesGrade(@Validated @RequestBody PCourseCasesGradeBo pCourseCasesGradeBo);

    /**
     * 查询校本课程案例年级详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/grade/detail")
    public AjaxResult<PCourseCasesGradeVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除校本课程案例年级
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/grade/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
