package com.fh.yanx.service.order.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 订单表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
@Data
public class ActivOrderBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ApiModelProperty("订单id")
	private Long orderId;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 * 订单编号
	 */
	@ApiModelProperty("订单编号")
	private String orderNumber;

	/**
	 * 订单名称
	 */
	@ApiModelProperty("订单名称")
	private String orderName;

	/**
	 * 订单类型：1消费、2充值、3管理员修改
	 */
	@ApiModelProperty("订单类型：1消费、2充值、3管理员修改")
	private Integer orderType;

	/**
	 * 交易流水号
	 */
	@ApiModelProperty("交易流水号")
	private String transactionId;

	/**
	 * 订单状态（ 1：待支付、2：已付款、3：待退款、4：已退款、5：退款驳回、 6：已取消、7：已发货、8：超时未支付)
	 */
	@ApiModelProperty("订单状态（ 1：待支付、2：已付款、3：待退款、4：已退款、5：退款驳回、 6：已取消、7：已发货、8：超时未支付)")
	private Integer orderState;

	/**
	 * 支付方式：1支付宝、2微信、3苹果、4管理员修改
	 */
	@ApiModelProperty("支付方式：1支付宝、2微信、3苹果、4管理员修改")
	private Integer payMode;

	/**
	 * 商品类型（1-活动报名bm_info）
	 */
	@ApiModelProperty("商品类型（1-活动报名bm_info）")
	private Integer goodsType;

	/**
	 * 商品id，根据订单类型记录商品id
	 */
	@ApiModelProperty("商品id，根据订单类型记录商品id")
	private Long goodsId;

	/**
	 * 商品数量
	 */
	@ApiModelProperty("商品数量")
	private Long goodsQuantity;

	/**
	 * 地址
	 */
	@ApiModelProperty("地址")
	private String addressInfo;

	/**
	 * 终端类型（1：iOS、2：安卓、3：web、4：PAD、5：H5）
	 */
	@ApiModelProperty("终端类型（1：iOS、2：安卓、3：web、4：PAD、5：H5）")
	private Integer terminalType;

	/**
	 * 实际支付金额
	 */
	@ApiModelProperty("实际支付金额")
	private BigDecimal payAmount;

	/**
	 * 订单金额
	 */
	@ApiModelProperty("订单金额")
	private BigDecimal orderAmount;

	/**
	 * 支付场景（1：正式、2：沙箱）
	 */
	@ApiModelProperty("支付场景（1：正式、2：沙箱）")
	private Integer payScene;

	/**
	 * 结束时间
	 */
	@ApiModelProperty("结束时间")
	private Date endTime;

	/**
	 * 付款时间
	 */
	@ApiModelProperty("付款时间")
	private Date payTime;

	/**
	 * 申请退款金额
	 */
	@ApiModelProperty("申请退款金额")
	private BigDecimal applyRefundMoney;

	/**
	 * 申请退款原因
	 */
	@ApiModelProperty("申请退款原因")
	private String applyRefundReason;

	/**
	 * 申请退款时间
	 */
	@ApiModelProperty("申请退款时间")
	private Date applyRefundTime;

	/**
	 * 实际退款时间
	 */
	@ApiModelProperty("实际退款时间")
	private Date refundTime;

	/**
	 * 实际退款金额
	 */
	@ApiModelProperty("实际退款金额")
	private BigDecimal refundMoney;

	/**
	 * 驳回时间
	 */
	@ApiModelProperty("驳回时间")
	private Date rejectTime;

	/**
	 * 取消时间
	 */
	@ApiModelProperty("取消时间")
	private Date cancelTime;

	/**
	 * 首次支付标识:  1: 是首次支付  2:非首次支付
	 */
	@ApiModelProperty("首次支付标识:  1: 是首次支付  2:非首次支付")
	private Integer firstPay;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String note;

	/**
	 * 是否删除：0：否，1：是
	 */
	@ApiModelProperty("是否删除：0：否，1：是")
	private Integer isDelete;

	/**
	 * openId，前端获取到后，创建订单的时候传递过来
	 */
	@ApiModelProperty("openId，前端获取到后，创建订单的时候传递过来")
	private String openId;



}
