package com.fh.yanx.service.bm.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 报名活动沟通确认表（本表只有新增记录）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
@Data
public class BmInfoConnectVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 活动报名记录id
     */
    @ApiModelProperty("活动报名记录id")
    private Long infoId;

    /**
     * 确认状态：1待确认(本表不会出现该状态的数据)，2沟通中，3已确认，4已拒绝
     */
    @ApiModelProperty("确认状态：1待确认(本表不会出现该状态的数据)，2沟通中，3已确认，4已拒绝")
    private Integer submitType;

    /**
     * 沟通内容
     */
    @ApiModelProperty("沟通内容")
    private String connectContent;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public BmInfoConnectVo returnOwn() {
        return this;
    }

}
