package com.fh.yanx.service.enums;

/**
 * 组织申请处理状态
 *
 * <AUTHOR>
 * @date 2023-07-18 10:16
 */
public enum OrganizationApplyType {
    IN_PROCESS(1, "处理中"),
    PROCESSED(2, "已处理");

    private Integer code;
    private String value;

    OrganizationApplyType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
