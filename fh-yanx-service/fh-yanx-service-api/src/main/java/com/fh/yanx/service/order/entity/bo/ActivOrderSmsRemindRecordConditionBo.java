package com.fh.yanx.service.order.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 09:29:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivOrderSmsRemindRecordConditionBo extends PageLimitBo {

	private Long id;

	/**
	 * 订单id
	 */
	private Long orderId;


	/**
	 * 是否删除：0：否，1：是
	 */
	private Integer isDelete;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 更新人
	 */
	private String updateBy;

}
