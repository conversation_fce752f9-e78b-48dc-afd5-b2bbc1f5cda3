package com.fh.yanx.service.res.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.res.api.ResOpenCourseApi;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseBo;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 公开课表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
@FeignClient(contextId = "resOpenCourseApiService", value = ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ResOpenCourseApiService.ResOpenCourseApiFallbackFactory.class)
@Component
public interface ResOpenCourseApiService extends ResOpenCourseApi {

    @Component
    class ResOpenCourseApiFallbackFactory implements FallbackFactory<ResOpenCourseApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ResOpenCourseApiFallbackFactory.class);

        @Override
        public ResOpenCourseApiService create(Throwable cause) {
            ResOpenCourseApiFallbackFactory.LOGGER.error("yanx-service服务调用失败:{}", cause.getMessage());
            return new ResOpenCourseApiService() {
                public AjaxResult getResOpenCoursePageListByCondition(ResOpenCourseConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getResOpenCourseListByCondition(ResOpenCourseConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addResOpenCourse(ResOpenCourseBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateResOpenCourse(ResOpenCourseBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}