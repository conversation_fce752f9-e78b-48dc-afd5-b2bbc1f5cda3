package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicSubjectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectBo;
import com.fh.yanx.service.res.entity.vo.ResTopicSubjectVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 关联科目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
public interface ResTopicSubjectApi {

    /**
     * 查询关联科目表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/subject/page/list")
    public AjaxResult<PageInfo<ResTopicSubjectVo>> getResTopicSubjectPageListByCondition(@RequestBody ResTopicSubjectConditionBo condition);

    /**
     * 查询关联科目表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/subject/list")
    public AjaxResult<List<ResTopicSubjectVo>> getResTopicSubjectListByCondition(@RequestBody ResTopicSubjectConditionBo condition);


    /**
     * 新增关联科目表
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/subject/add")
    public AjaxResult addResTopicSubject(@Validated @RequestBody ResTopicSubjectBo resTopicSubjectBo);

    /**
     * 修改关联科目表
     * @param resTopicSubjectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("res/topic/subject/update")
    public AjaxResult updateResTopicSubject(@Validated @RequestBody ResTopicSubjectBo resTopicSubjectBo);

    /**
     * 查询关联科目表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/subject/detail")
    public AjaxResult<ResTopicSubjectVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除关联科目表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("res/topic/subject/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
