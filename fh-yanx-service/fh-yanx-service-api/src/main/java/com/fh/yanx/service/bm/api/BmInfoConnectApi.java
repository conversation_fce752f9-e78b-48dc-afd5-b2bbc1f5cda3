package com.fh.yanx.service.bm.api;

import com.fh.yanx.service.bm.entity.bo.BmInfoConnectConditionBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoConnectVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 报名活动沟通确认表（本表只有新增记录）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-15 17:58:18
 */
public interface BmInfoConnectApi {

    /**
     * 查询报名活动沟通确认表（本表只有新增记录）分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @PostMapping("/bm/info/connect/page/list")
    public AjaxResult<PageInfo<BmInfoConnectVo>>
        getBmInfoConnectPageListByCondition(@RequestBody BmInfoConnectConditionBo condition);

    /**
     * 查询报名活动沟通确认表（本表只有新增记录）列表
     * 
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @PostMapping("/bm/info/connect/list")
    public AjaxResult<List<BmInfoConnectVo>>
        getBmInfoConnectListByCondition(@RequestBody BmInfoConnectConditionBo condition);

    /**
     * 新增报名活动沟通确认表（本表只有新增记录）
     * 
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @PostMapping("/bm/info/connect/add")
    public AjaxResult addBmInfoConnect(@Validated @RequestBody BmInfoConnectBo bmInfoConnectBo);

    /**
     * 新增报名活动沟通确认表（本表只有新增记录）-同时更新BmInfo主表
     * 
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @PostMapping("/bm/info/connect/add-bm")
    public AjaxResult addBmInfoConnectWithUpdateBmInfo(@Validated @RequestBody BmInfoConnectBo bmInfoConnectBo);

    /**
     * 修改报名活动沟通确认表（本表只有新增记录）
     * 
     * @param bmInfoConnectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @PostMapping("/bm/info/connect/update")
    public AjaxResult updateBmInfoConnect(@Validated @RequestBody BmInfoConnectBo bmInfoConnectBo);

    /**
     * 查询报名活动沟通确认表（本表只有新增记录）详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @GetMapping("/bm/info/connect/detail")
    public AjaxResult<BmInfoConnectVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除报名活动沟通确认表（本表只有新增记录）
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-15 17:58:18
     */
    @GetMapping("/bm/info/connect/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
