package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseModuleAttachmentApi;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentBo;
import com.fh.yanx.service.course.entity.bo.CourseModuleAttachmentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程资源或成果样例附件表-模块附件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-15 09:49:48
 */
@FeignClient(contextId = "courseModuleAttachmentApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseModuleAttachmentApiService.CourseModuleAttachmentApiFallbackFactory.class)
@Component
public interface CourseModuleAttachmentApiService extends CourseModuleAttachmentApi {

    @Component
    class CourseModuleAttachmentApiFallbackFactory implements FallbackFactory<CourseModuleAttachmentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseModuleAttachmentApiFallbackFactory.class);
        @Override
        public CourseModuleAttachmentApiService create(Throwable cause) {
            CourseModuleAttachmentApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new CourseModuleAttachmentApiService() {
                public AjaxResult getCourseModuleAttachmentPageListByCondition(CourseModuleAttachmentConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseModuleAttachmentListByCondition(CourseModuleAttachmentConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseModuleAttachment(CourseModuleAttachmentBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseModuleAttachment(CourseModuleAttachmentBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}