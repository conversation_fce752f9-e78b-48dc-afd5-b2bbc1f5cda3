package com.fh.yanx.service.activ.api;

import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordViewVo;

import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新时代文化校园活动内容观看记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 10:47:51
 */
public interface ActivInfoRecordViewApi {

    /**
     * 查询新时代文化校园活动内容观看记录表分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @PostMapping("/activ/info/record/view/page/list")
    public AjaxResult<PageInfo<ActivInfoRecordViewVo>>
        getActivInfoRecordViewPageListByCondition(@RequestBody ActivInfoRecordViewConditionBo condition);

    /**
     * 查询新时代文化校园活动内容观看记录表列表
     * 
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @PostMapping("/activ/info/record/view/list")
    public AjaxResult<List<ActivInfoRecordViewVo>>
        getActivInfoRecordViewListByCondition(@RequestBody ActivInfoRecordViewConditionBo condition);

    /**
     * 新增新时代文化校园活动内容观看记录表
     * 
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @PostMapping("/activ/info/record/view/add")
    public AjaxResult addActivInfoRecordView(@Validated @RequestBody ActivInfoRecordViewBo activInfoRecordViewBo);

    /**
     * 修改新时代文化校园活动内容观看记录表
     * 
     * @param activInfoRecordViewBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @PostMapping("/activ/info/record/view/update")
    public AjaxResult updateActivInfoRecordView(@Validated @RequestBody ActivInfoRecordViewBo activInfoRecordViewBo);

    /**
     * 查询新时代文化校园活动内容观看记录表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @GetMapping("/activ/info/record/view/detail")
    public AjaxResult<ActivInfoRecordViewVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除新时代文化校园活动内容观看记录表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @GetMapping("/activ/info/record/view/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询统计信息
     * 
     * <AUTHOR>
     * @date 2023-08-16 10:47:51
     */
    @PostMapping("/activ/info/record/view/list-statistics")
    public AjaxResult<List<ActivInfoRecordVo>>
        getActivInfoRecordViewStatistics(@RequestBody ActivInfoRecordViewConditionBo condition);

}
