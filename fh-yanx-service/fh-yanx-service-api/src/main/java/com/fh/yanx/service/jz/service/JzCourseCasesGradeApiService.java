package com.fh.yanx.service.jz.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.jz.api.JzCourseCasesGradeApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeBo;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesGradeConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 金中-校本课程案例年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 13:49:33
 */
@FeignClient(contextId = "jzCourseCasesGradeApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = JzCourseCasesGradeApiService.JzCourseCasesGradeApiFallbackFactory.class)
@Component
public interface JzCourseCasesGradeApiService extends JzCourseCasesGradeApi {

    @Component
    class JzCourseCasesGradeApiFallbackFactory implements FallbackFactory<JzCourseCasesGradeApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(JzCourseCasesGradeApiFallbackFactory.class);
        @Override
        public JzCourseCasesGradeApiService create(Throwable cause) {
            JzCourseCasesGradeApiFallbackFactory.LOGGER.error("研学服务服务调用失败:{}", cause.getMessage());
            return new JzCourseCasesGradeApiService() {
                public AjaxResult getJzCourseCasesGradePageListByCondition(JzCourseCasesGradeConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getJzCourseCasesGradeListByCondition(JzCourseCasesGradeConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addJzCourseCasesGrade(JzCourseCasesGradeBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateJzCourseCasesGrade(JzCourseCasesGradeBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}