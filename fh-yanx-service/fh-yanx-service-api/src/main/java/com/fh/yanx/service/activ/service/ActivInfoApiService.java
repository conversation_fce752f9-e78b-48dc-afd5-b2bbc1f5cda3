package com.fh.yanx.service.activ.service;


import com.fh.yanx.service.activ.api.ActivInfoApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoShowBo;
import com.fh.yanx.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 新时代文化校园活动信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:34:04
 */
@FeignClient(contextId = "activInfoApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ActivInfoApiService.ActivInfoApiFallbackFactory.class)
@Component
public interface ActivInfoApiService extends ActivInfoApi {

    @Component
    class ActivInfoApiFallbackFactory implements FallbackFactory<ActivInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ActivInfoApiFallbackFactory.class);
        @Override
        public ActivInfoApiService create(Throwable cause) {
            ActivInfoApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new ActivInfoApiService() {
                public AjaxResult getActivInfoPageListByCondition(ActivInfoConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getActivInfoListByCondition(ActivInfoConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addActivInfo(ActivInfoBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateActivInfo(ActivInfoBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id, Integer activType){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult updateActivType(ActivInfoBo activInfoBo) {
                    return AjaxResult.fail("上下架失败");
                }

                @Override
                public AjaxResult setActivInfoShow(ActivInfoShowBo activInfoShowBo) {
                    return AjaxResult.fail("设置活动展示失败");
                }

                @Override
                public AjaxResult getUserActivList(ActivInfoConditionBo conditionBo) {
                    return AjaxResult.fail("获取用户活动列表失败");
                }

            };
        }
    }
}