package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.vo.ResTopicProcessRecordVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课题流程记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
public interface ResTopicProcessRecordApi {

    /**
     * 查询课题流程记录分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/process/record/page/list")
    public AjaxResult<PageInfo<ResTopicProcessRecordVo>> getResTopicProcessRecordPageListByCondition(@RequestBody ResTopicProcessRecordConditionBo condition);

    /**
     * 查询课题流程记录列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/process/record/list")
    public AjaxResult<List<ResTopicProcessRecordVo>> getResTopicProcessRecordListByCondition(@RequestBody ResTopicProcessRecordConditionBo condition);


    /**
     * 新增课题流程记录
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/process/record/add")
    public AjaxResult addResTopicProcessRecord(@Validated @RequestBody ResTopicProcessRecordBo resTopicProcessRecordBo);

    /**
     * 修改课题流程记录
     * @param resTopicProcessRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("res/topic/process/record/update")
    public AjaxResult updateResTopicProcessRecord(@Validated @RequestBody ResTopicProcessRecordBo resTopicProcessRecordBo);

    /**
     * 查询课题流程记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/process/record/detail")
    public AjaxResult<ResTopicProcessRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课题流程记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("res/topic/process/record/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
