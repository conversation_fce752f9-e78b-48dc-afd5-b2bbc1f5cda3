package com.fh.yanx.service.res.api;


import com.fh.yanx.service.res.entity.bo.ResOpenCourseConditionBo;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseBo;
import com.fh.yanx.service.res.entity.vo.ResOpenCourseVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 公开课表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
public interface ResOpenCourseApi {

    /**
     * 查询公开课表分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("res/open/course/page/list")
    public AjaxResult<PageInfo<ResOpenCourseVo>> getResOpenCoursePageListByCondition(@RequestBody ResOpenCourseConditionBo condition);

    /**
     * 查询公开课表列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("res/open/course/list")
    public AjaxResult<List<ResOpenCourseVo>> getResOpenCourseListByCondition(@RequestBody ResOpenCourseConditionBo condition);


    /**
     * 新增公开课表
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("res/open/course/add")
    public AjaxResult addResOpenCourse(@Validated @RequestBody ResOpenCourseBo resOpenCourseBo);

    /**
     * 修改公开课表
     * @param resOpenCourseBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("res/open/course/update")
    public AjaxResult updateResOpenCourse(@Validated @RequestBody ResOpenCourseBo resOpenCourseBo);

    /**
     * 查询公开课表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @GetMapping("res/open/course/detail")
    public AjaxResult<ResOpenCourseVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除公开课表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @GetMapping("res/open/course/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
