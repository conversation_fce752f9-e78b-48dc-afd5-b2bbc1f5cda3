package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseStoreApi;
import com.fh.yanx.service.course.entity.bo.CourseStoreBo;
import com.fh.yanx.service.course.entity.bo.CourseStoreConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程收藏表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-04 18:33:34
 */
@FeignClient(contextId = "courseStoreApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseStoreApiService.CourseStoreApiFallbackFactory.class)
@Component
public interface CourseStoreApiService extends CourseStoreApi {

    @Component
    class CourseStoreApiFallbackFactory implements FallbackFactory<CourseStoreApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseStoreApiFallbackFactory.class);
        @Override
        public CourseStoreApiService create(Throwable cause) {
            CourseStoreApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new CourseStoreApiService() {
                public AjaxResult getCourseStorePageListByCondition(CourseStoreConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseStoreListByCondition(CourseStoreConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseStore(CourseStoreBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseStore(CourseStoreBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult deleteCourseStore(CourseStoreBo courseStoreBo) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}