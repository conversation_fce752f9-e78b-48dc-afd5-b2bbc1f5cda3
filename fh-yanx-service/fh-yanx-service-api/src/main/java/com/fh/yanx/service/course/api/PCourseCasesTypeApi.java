package com.fh.yanx.service.course.api;


import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesTypeBo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesTypeVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 校本课程案例类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 11:09:59
 */
public interface PCourseCasesTypeApi {

    /**
     * 查询校本课程案例类型分页列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/type/page/list")
    public AjaxResult<PageInfo<PCourseCasesTypeVo>> getPCourseCasesTypePageListByCondition(@RequestBody PCourseCasesTypeConditionBo condition);

    /**
     * 查询校本课程案例类型列表
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/type/list")
    public AjaxResult<List<PCourseCasesTypeVo>> getPCourseCasesTypeListByCondition(@RequestBody PCourseCasesTypeConditionBo condition);


    /**
     * 新增校本课程案例类型
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/type/add")
    public AjaxResult addPCourseCasesType(@Validated @RequestBody PCourseCasesTypeBo pCourseCasesTypeBo);

    /**
     * 修改校本课程案例类型
     * @param pCourseCasesTypeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @PostMapping("/p/course/cases/type/update")
    public AjaxResult updatePCourseCasesType(@Validated @RequestBody PCourseCasesTypeBo pCourseCasesTypeBo);

    /**
     * 查询校本课程案例类型详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/type/detail")
    public AjaxResult<PCourseCasesTypeVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除校本课程案例类型
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-14 11:09:59
     */
    @GetMapping("/p/course/cases/type/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
