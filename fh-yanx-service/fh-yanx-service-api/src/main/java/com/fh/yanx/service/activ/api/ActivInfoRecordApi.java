package com.fh.yanx.service.activ.api;


import com.fh.yanx.service.activ.entity.bo.ActivInfoBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 新时代文化校园活动内容表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-04 10:33:55
 */
public interface ActivInfoRecordApi {

    /**
     * 查询新时代文化校园活动内容表分页列表
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
    @PostMapping("/activ/info/record/page/list")
    public AjaxResult<PageInfo<ActivInfoRecordVo>> getActivInfoRecordPageListByCondition(@RequestBody ActivInfoRecordConditionBo condition);

    /**
     * 查询新时代文化校园活动内容表列表
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
    @PostMapping("/activ/info/record/list")
    public AjaxResult<List<ActivInfoRecordVo>> getActivInfoRecordListByCondition(@RequestBody ActivInfoRecordConditionBo condition);


    /**
     * 新增新时代文化校园活动内容表
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
    @PostMapping("/activ/info/record/add")
    public AjaxResult addActivInfoRecord(@Validated @RequestBody ActivInfoRecordBo activInfoRecordBo);

    /**
     * 修改新时代文化校园活动内容表
     * @param activInfoRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
    @PostMapping("/activ/info/record/update")
    public AjaxResult updateActivInfoRecord(@Validated @RequestBody ActivInfoRecordBo activInfoRecordBo);

    /**
     * 查询新时代文化校园活动内容表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
    @GetMapping("/activ/info/record/detail")
    public AjaxResult<ActivInfoRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除新时代文化校园活动内容表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-04 10:33:55
     */
    @GetMapping("/activ/info/record/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 保存活动内容
     *
     * @param activInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 17:09
     **/
    @PostMapping("/activ/info/record/save")
    public AjaxResult saveActivInfoRecords(@RequestBody ActivInfoBo activInfoBo);

}
