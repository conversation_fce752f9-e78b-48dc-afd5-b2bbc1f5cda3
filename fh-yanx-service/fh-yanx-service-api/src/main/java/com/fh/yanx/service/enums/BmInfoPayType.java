package com.fh.yanx.service.enums;

/**
 * 报名缴费状态
 *
 * <AUTHOR>
 * @date 2023-08-02 16:35
 */
public enum BmInfoPayType {
    NO_PAY(1, "未缴费"),
    ALREADY_PAY(2, "已缴费"),
    REFUND(3, "已退款"),
    CANCEL(4, "已取消");

    private Integer code;
    private String value;

    BmInfoPayType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
