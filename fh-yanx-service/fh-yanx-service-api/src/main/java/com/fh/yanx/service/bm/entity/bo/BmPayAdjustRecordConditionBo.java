package com.fh.yanx.service.bm.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 支付信息调整记录表
 * 
 * <AUTHOR>
 * @CreateTime 2025-05-27 16:03
 */
@Data
public class BmPayAdjustRecordConditionBo extends PageLimitBo {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 活动报名记录id
     */
    @ApiModelProperty("活动报名记录id")
    private Long infoId;

    /**
     * 缴费凭证文件oid
     */
    @ApiModelProperty("缴费凭证文件oid")
    private String payId;

    /**
     * 缴费凭证文件地址
     */
    @ApiModelProperty("缴费凭证文件地址")
    private String payUrl;

    /**
     * 付款凭证上传时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("付款凭证上传时间")
    private Date payTime;

    /**
     * 付款凭证上传人oid
     */
    @ApiModelProperty("付款凭证上传人oid")
    private String payUserOid;

    /**
     * 付款凭证上传人
     */
    @ApiModelProperty("付款凭证上传人")
    private String payUserName;

    /**
     * 支付方式类型：1微信，2对公账户支付
     */
    @ApiModelProperty("支付方式类型：1微信，2对公账户支付")
    private Integer payWayType;

    /**
     * 转账记录
     */
    @ApiModelProperty("转账记录")
    private String payRecord;

    /**
     * 交易流水号
     */
    @ApiModelProperty("交易流水号")
    private String transactionId;

    /**
     * 调整原因
     */
    @ApiModelProperty("调整原因")
    private String payAdjustReason;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;
}