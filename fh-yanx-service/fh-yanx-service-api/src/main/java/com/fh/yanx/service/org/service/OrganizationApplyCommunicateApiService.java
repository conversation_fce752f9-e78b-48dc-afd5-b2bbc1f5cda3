package com.fh.yanx.service.org.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.org.api.OrganizationApplyCommunicateApi;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateBo;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyCommunicateConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 组织申请沟通记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-14 10:49:54
 */
@FeignClient(contextId = "organizationApplyCommunicateApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = OrganizationApplyCommunicateApiService.OrganizationApplyCommunicateApiFallbackFactory.class)
@Component
public interface OrganizationApplyCommunicateApiService extends OrganizationApplyCommunicateApi {

    @Component
    class OrganizationApplyCommunicateApiFallbackFactory implements FallbackFactory<OrganizationApplyCommunicateApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(OrganizationApplyCommunicateApiFallbackFactory.class);
        @Override
        public OrganizationApplyCommunicateApiService create(Throwable cause) {
            OrganizationApplyCommunicateApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new OrganizationApplyCommunicateApiService() {
                public AjaxResult getOrganizationApplyCommunicatePageListByCondition(OrganizationApplyCommunicateConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getOrganizationApplyCommunicateListByCondition(OrganizationApplyCommunicateConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addOrganizationApplyCommunicate(OrganizationApplyCommunicateBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateOrganizationApplyCommunicate(OrganizationApplyCommunicateBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}