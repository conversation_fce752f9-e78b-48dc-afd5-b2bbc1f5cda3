package com.fh.yanx.service.enums;

/**
 *
 * 活动观看权限
 * <AUTHOR>
 * @date 2023-07-31 15:53
 */
public enum ViewPermission {
    ACTIV_JOINER(1, "活动报名人员"),
    NO_AUTH_PEOPLE(2, "未认证学校用户"),
    AUTH_PEOPLE(3, "已认证学校用户"),
    COURSE_CASE_OWNER(4, "案例持有者"),
    ;

    private Integer code;
    private String value;

    ViewPermission(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
