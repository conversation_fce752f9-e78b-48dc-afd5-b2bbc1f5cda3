package com.fh.yanx.service.activ.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 活动内容观看权限表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-27 17:28:46
 */
@Data
public class ActivInfoViewPermissionConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 活动id
	 */
	@ApiModelProperty("活动id")
	private Long activId;

	/**
	 * 活动内容 1-活动日程（直播） 2-内容回看
	 */
	@ApiModelProperty("活动内容 1-活动日程（直播） 2-内容回看")
	private Integer viewType;

	/**
	 * 观看权限 1-活动报名人员 2-未认证学校用户 3-已认证学校用户
	 */
	@ApiModelProperty("观看权限 1-活动报名人员 2-未认证学校用户 3-已认证学校用户")
	private Integer viewPermission;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
