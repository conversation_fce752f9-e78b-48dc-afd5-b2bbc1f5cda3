package com.fh.yanx.service.course.service;


import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.course.api.CourseDiscussSectionApi;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionBo;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程讨论区表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
@FeignClient(contextId = "courseDiscussSectionApiService", value= ConstServiceName.FH_YANX_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseDiscussSectionApiService.CourseDiscussSectionApiFallbackFactory.class)
@Component
public interface CourseDiscussSectionApiService extends CourseDiscussSectionApi {

    @Component
    class CourseDiscussSectionApiFallbackFactory implements FallbackFactory<CourseDiscussSectionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseDiscussSectionApiFallbackFactory.class);
        @Override
        public CourseDiscussSectionApiService create(Throwable cause) {
            CourseDiscussSectionApiFallbackFactory.LOGGER.error("courseDiscussSectionApiService服务调用失败:{}", cause.getMessage());
            return new CourseDiscussSectionApiService() {
                public AjaxResult getCourseDiscussSectionPageListByCondition(CourseDiscussSectionConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseDiscussSectionListByCondition(CourseDiscussSectionConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseDiscussSection(CourseDiscussSectionBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseDiscussSection(CourseDiscussSectionBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<Boolean> deleteByIdAndUserOid(Long id, String userOid) {
                    return AjaxResult.fail("删除数据失败");
                }
            };
        }
    }
}