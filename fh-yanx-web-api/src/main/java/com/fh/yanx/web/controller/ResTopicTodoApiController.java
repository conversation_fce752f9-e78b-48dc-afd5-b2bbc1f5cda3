package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicTodoApi;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTodoConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTodoVo;
import com.fh.yanx.service.res.service.ResTopicTodoApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户待办事项
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
@RequestMapping("res/topic/todo")
@Api(value = "", tags = "用户待办事项接口")
public class ResTopicTodoApiController {

    @Autowired
    private ResTopicTodoApi resTopicTodoApi;

    /**
     * 查询用户待办事项分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询用户待办事项列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicTodoPageListByCondition(@RequestBody ResTopicTodoConditionBo condition) {
        PageInfo<ResTopicTodoVo> page = resTopicTodoApi.getResTopicTodoPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询用户待办事项列表
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询用户待办事项列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicTodoListByCondition(@RequestBody ResTopicTodoConditionBo condition) {
        List<ResTopicTodoVo> list = resTopicTodoApi.getResTopicTodoListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }

    /**
     * 新增用户待办事项
     * 
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增用户待办事项", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicTodo(@RequestBody ResTopicTodoBo resTopicTodoBo) {
        return resTopicTodoApi.addResTopicTodo(resTopicTodoBo);
    }

    /**
     * 修改用户待办事项
     * 
     * @param resTopicTodoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新用户待办事项", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicTodo(@RequestBody ResTopicTodoBo resTopicTodoBo) {
        if (null == resTopicTodoBo.getTodoId()) {
            return AjaxResult.fail("用户待办事项id不能为空");
        }
        return resTopicTodoApi.updateResTopicTodo(resTopicTodoBo);
    }

    /**
     * 查询用户待办事项详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询用户待办事项详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "用户待办事项id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicTodoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("用户待办事项id不能为空");
        }
        ResTopicTodoVo vo = resTopicTodoApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicTodoVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除用户待办事项
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除用户待办事项", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "用户待办事项id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicTodoApi.delete(id);
    }

    /**
     * 完成待办
     *
     * @param id the id
     * @return ajax result
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022 -09-02 15:00:24
     */
    @GetMapping("/complete")
    @ApiOperation(value = "完成待办", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult updateResTopicTodo(Long id, boolean student) {
        if (null == id) {
            return AjaxResult.fail("用户待办事项id不能为空");
        }
        return resTopicTodoApi.completeTodo(id, student);
    }
}
