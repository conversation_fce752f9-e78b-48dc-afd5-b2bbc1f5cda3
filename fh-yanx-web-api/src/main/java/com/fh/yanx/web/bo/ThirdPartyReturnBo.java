package com.fh.yanx.web.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登陆对接返回Json对应bean
 *
 * <AUTHOR>
 * @date 2022/9/7
 */
@Data
public class ThirdPartyReturnBo {

    /**
     * 微信返回json
     */
    private String access_token;
    private int expires_in;
    private String refresh_token;
    private String openid;
    private String scope;
    private String unionid;

    /**
     * 应用ID
     */
    @ApiModelProperty("应用ID")
    private String appId;
    /**
     * 登陆类型1：微信，2：qq
     */
    @ApiModelProperty("登陆类型1：微信，2：qq")
    private Integer loginType;

    /**
     * 微信错误返回 {"errcode":40029,"errmsg":"invalid code, rid: 632d2660-3683af1c-0e0a961e"}
     */
    @ApiModelProperty("微信错误标识")
    private String errcode;
    @ApiModelProperty("微信错误返回提示")
    private String errmsg;
}
