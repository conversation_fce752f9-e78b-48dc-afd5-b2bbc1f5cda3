package com.fh.yanx.web.controller;

import com.fh.yanx.service.pub.api.PubBookApi;
import com.fh.yanx.service.pub.api.PubBookDirectoryApi;
import com.fh.yanx.service.pub.api.PubBookDirectoryAttachmentApi;
import com.fh.yanx.service.pub.entity.bo.PubBookConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryAttachmentConditionBo;
import com.fh.yanx.service.pub.entity.bo.PubBookDirectoryConditionBo;
import com.fh.yanx.service.pub.entity.vo.PubBookVo;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.fh.yanx.service.res.api.ResApplyApi;
import com.fh.yanx.service.res.entity.bo.ResApplyBo;
import com.fh.yanx.service.res.api.ResTopicApi;
import com.fh.yanx.service.res.api.ResTopicRepAttachmentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import javax.annotation.Resource;
import java.util.List;

/**
 * 不需要登录的路由（包括前台用户的几个路由地址），研学平台大首页
 *
 * <AUTHOR>
 * @date 2022 /8/29 15:08
 */
@RestController
@RequestMapping("/yanx-index")
@Api(value = "首页信息", tags = "首页信息")
public class YanxIndexController {
    @Autowired
    private RedisComponent redisComponent;
    /**
     * 研学mock数据缓存前缀
     */
    private static final String yanx_mock_prefix = "yanx_mock_prefix_";

    @Resource
    private ResApplyApi resApplyApi;
    @Resource
    private ResTopicApi resTopicApi;
    @Resource
    private ResTopicRepAttachmentApi resTopicRepAttachmentApi;
    @Resource
    private BaseDataApi baseDataApi;
    @Resource
    private PubBookApi pubBookApi;
    @Resource
    private PubBookDirectoryApi pubBookDirectoryApi;
    @Resource
    private PubBookDirectoryAttachmentApi pubBookDirectoryAttachmentApi;

    /**
     * 虚拟数据保存
     *
     * @param data the data：缓存的字符串
     * @param type the type：1课程数据，2学生数据，3教师数据
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "虚拟数据保存", httpMethod = "POST")
    @RequestMapping(value = "/mock", method = RequestMethod.POST)
    public AjaxResult mockSave(String data, Integer type) throws Exception {
        String key = StringUtils.join(yanx_mock_prefix, type);
        redisComponent.set(key, data);
        return AjaxResult.success();
    }

    /**
     * 虚拟数据查询
     *
     * @param type the type：1课程数据，2学生数据，3教师数据
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "虚拟数据查询", httpMethod = "GET")
    @RequestMapping(value = "/mock", method = RequestMethod.GET)
    public AjaxResult mockGet(Integer type) throws Exception {
        String key = StringUtils.join(yanx_mock_prefix, type);
        String data = "";
        if (redisComponent.hasKey(key)) {
            data = (String)redisComponent.get(key);
        }
        return AjaxResult.success(data);
    }

    /**
     * 合作意向申请提交
     *
     * @param resApplyBo the res apply bo
     * @return ajax result
     */
    @ApiOperation("合作意向申请提交")
    @PostMapping("/apply/add")
    public AjaxResult addApply(@RequestBody ResApplyBo resApplyBo) {
        return resApplyApi.addResApply(resApplyBo);
    }

    /**
     * 查询课题列表
     *
     * @param resTopicConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/26 14:25
     */
    @ApiOperation("研学首页课题列表")
    @PostMapping("/topic-list")
    public AjaxResult getIndexTopic(@RequestBody ResTopicConditionBo resTopicConditionBo) {
        return resTopicApi.getResTopicPageListByCondition(resTopicConditionBo);
    }

    /**
     * 查询课题详情
     *
     * @param topicId
     * @return com.light.core.entity.AjaxResult<com.fh.yanx.service.res.entity.vo.ResTopicVo>
     * <AUTHOR>
     * @date 2022/10/27 11:42
     */
    @GetMapping("/topic-detail")
    public AjaxResult<ResTopicVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("topicId") Long topicId) {
        return resTopicApi.getDetail(topicId);
    }

    /**
     * 查询论文列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/26 14:25
     */
    @ApiOperation("查询论文列表")
    @PostMapping("/res-rep-attachment-list")
    public AjaxResult
        getResTopicRepAttachmentPageListByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition) {
        return resTopicRepAttachmentApi.getResTopicRepAttachmentPageListByCondition(condition);
    }

    /**
     * 查询论文年份集合
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/26 14:25
     */
    @ApiOperation("查询论文年份集合")
    @PostMapping("/res-rep-attachment-years")
    public AjaxResult
        getResTopicRepAttachmentYearsByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition) {
        return resTopicRepAttachmentApi.getResTopicRepAttachmentYearsByCondition(condition);
    }

    /**
     * 获取研学学科列表
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/2 14:19
     */
    @GetMapping("/subject/list")
    @ApiOperation(value = "获取研学学科列表", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getSubjectList() {
        return baseDataApi.getSubjectList();
    }

    /**
     * 融合出版书籍信息
     *
     */
    @PostMapping("/pub-book/list")
    @ApiOperation(value = "融合出版书籍信息", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getPubBookList(@RequestBody PubBookConditionBo pubBookConditionBo) {
        AjaxResult<List<PubBookVo>> ajaxResultPubBook = pubBookApi.getPubBookListByCondition(pubBookConditionBo);
        incrBookReadTimes(pubBookConditionBo);
        return ajaxResultPubBook;
    }

    /**
     * 融合出版书籍信息-分页
     *
     */
    @PostMapping("/pub-book/list-page")
    @ApiOperation(value = "融合出版书籍信息", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getPubBookListPage(@RequestBody PubBookConditionBo pubBookConditionBo) {
        AjaxResult<PageInfo<PubBookVo>> ajaxResultPubBook =
            pubBookApi.getPubBookPageListByCondition(pubBookConditionBo);
        incrBookReadTimes(pubBookConditionBo);
        return ajaxResultPubBook;
    }

    /**
     * 融合出版书籍目录信息
     *
     */
    @PostMapping("/pub-book-directory/list")
    @ApiOperation(value = "融合出版书籍信息", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getPubBookDirectoryList(@RequestBody PubBookDirectoryConditionBo pubBookDirectoryConditionBo) {
        return pubBookDirectoryApi.getPubBookDirectoryListByCondition(pubBookDirectoryConditionBo);
    }

    /**
     * 融合出版书籍目录信息-分页
     *
     */
    @PostMapping("/pub-book-directory/list-page")
    @ApiOperation(value = "融合出版书籍信息", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult
        getPubBookDirectoryListPage(@RequestBody PubBookDirectoryConditionBo pubBookDirectoryConditionBo) {
        return pubBookDirectoryApi.getPubBookDirectoryPageListByCondition(pubBookDirectoryConditionBo);
    }

    /**
     * 融合出版书籍目录附件信息
     *
     */
    @PostMapping("/pub-book-directory-attachment/list")
    @ApiOperation(value = "融合出版书籍信息", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getPubBookDirectoryAttachmentList(
        @RequestBody PubBookDirectoryAttachmentConditionBo pubBookDirectoryAttachmentConditionBo) {
        return pubBookDirectoryAttachmentApi
            .getPubBookDirectoryAttachmentListByCondition(pubBookDirectoryAttachmentConditionBo);
    }

    /**
     * 融合出版书籍目录附件信息-分页
     *
     */
    @PostMapping("/pub-book-directory-attachment/list-page")
    @ApiOperation(value = "融合出版书籍信息", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getPubBookDirectoryAttachmentListPage(
        @RequestBody PubBookDirectoryAttachmentConditionBo pubBookDirectoryAttachmentConditionBo) {
        return pubBookDirectoryAttachmentApi
            .getPubBookDirectoryAttachmentPageListByCondition(pubBookDirectoryAttachmentConditionBo);
    }

    /**
     * 增加书籍阅读次数
     *
     * @param pubBookConditionBo the pub book condition bo
     * <AUTHOR>
     * @date 2023 -12-20 11:13:34
     */
    private void incrBookReadTimes(PubBookConditionBo pubBookConditionBo) {
        // 如果id不为空则更新阅读次数
        if (null != pubBookConditionBo.getBookId()) {
            pubBookApi.incrBookReadTimes(pubBookConditionBo.getBookId());
        }
    }
}
