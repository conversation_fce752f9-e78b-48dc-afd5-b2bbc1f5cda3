package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResStationApi;
import com.fh.yanx.service.res.entity.bo.ResStationBo;
import com.fh.yanx.service.res.entity.bo.ResStationConditionBo;
import com.fh.yanx.service.res.entity.vo.ResStationVo;
import com.fh.yanx.service.res.service.ResStationApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作站和学校表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/station")
@Api(value = "", tags = "工作站和学校表接口")
public class ResStationApiController {

    @Autowired
    private ResStationApi resStationApi;

    /**
     * 查询工作站和学校表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询工作站和学校表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResStationPageListByCondition(@RequestBody ResStationConditionBo condition) {
        PageInfo<ResStationVo> page = resStationApi.getResStationPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询工作站和学校表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询工作站和学校表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResStationListByCondition(@RequestBody ResStationConditionBo condition) {
        List<ResStationVo> list = resStationApi.getResStationListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增工作站和学校表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增工作站和学校表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResStation(@RequestBody ResStationBo resStationBo) {
        return resStationApi.addResStation(resStationBo);
    }

    /**
     * 修改工作站和学校表
     *
     * @param resStationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新工作站和学校表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResStation(@RequestBody ResStationBo resStationBo) {
        if (null == resStationBo.getStationId()) {
            return AjaxResult.fail("工作站和学校表id不能为空");
        }
        return resStationApi.updateResStation(resStationBo);
    }

    /**
     * 查询工作站和学校表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询工作站和学校表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "工作站和学校表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResStationVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("工作站和学校表id不能为空");
        }
        ResStationVo vo = resStationApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resStationVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除工作站和学校表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除工作站和学校表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "工作站和学校表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resStationApi.delete(id);
    }
}
