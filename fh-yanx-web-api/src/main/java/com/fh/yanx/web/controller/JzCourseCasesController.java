package com.fh.yanx.web.controller;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.jz.api.JzCourseCasesApi;
import com.fh.yanx.service.jz.entity.bo.JzCourseCasesConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.ApiOperation;

/**
 * 金中云端课程中心，用于演示或者临时使用，最终金中的课程中心是需要本地化部署的。
 * <AUTHOR>
 * @date 2024/1/17 15:50
 */
@RestController
@RequestMapping("/jzCourseCases")
@Validated
public class JzCourseCasesController {
    @Resource
    private JzCourseCasesApi jzCourseCasesApi;

    /**
     * 查询门户校本案例库
     *
     * @param conditionBo the condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("查询门户校本案例库")
    @PostMapping("/homeList")
    public AjaxResult homeList(@RequestBody JzCourseCasesConditionBo conditionBo) {
        return jzCourseCasesApi.getHomeList(conditionBo);
    }

    /**
     * 查询校本课程案例详情
     *
     * @param casesId the cases id
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:47
     */
    @ApiOperation("查询门户校本案例详情")
    @GetMapping("/homeDetail")
    public AjaxResult homeDetail(Long casesId) {
        return jzCourseCasesApi.homeDetail(casesId);
    }
}
