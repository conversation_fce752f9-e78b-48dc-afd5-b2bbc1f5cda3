package com.fh.yanx.web.controller;

import com.fh.yanx.service.baseinfo.BaseDataService;
import com.fh.yanx.service.res.api.ResTopicApi;
import com.fh.yanx.service.res.entity.bo.ResTopicBo;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.fh.yanx.service.res.service.ResTopicApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/topic")
@Api(value = "", tags = "课题表接口")
public class ResTopicApiController {

    @Resource
    private ResTopicApi resTopicApi;

    /**
     * 查询课题表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicPageListByCondition(@RequestBody ResTopicConditionBo condition) {
        PageInfo<ResTopicVo> page = resTopicApi.getResTopicPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicListByCondition(@RequestBody ResTopicConditionBo condition) {
        List<ResTopicVo> list = resTopicApi.getResTopicListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增课题表（暂存、提交）
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopic(@RequestBody ResTopicBo resTopicBo) {
        return resTopicApi.addResTopic(resTopicBo);
    }

    /**
     * 修改课题表 进程状态
     *
     * @param resTopicBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update-status")
    @ApiOperation(value = "更新课题表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicStatus(@RequestBody ResTopicBo resTopicBo) {
        if (null == resTopicBo.getTopicId()) {
            return AjaxResult.fail("课题表id不能为空");
        }
        return resTopicApi.updateResTopicStatus(resTopicBo);
    }

    /**
     * 查询课题表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题表id不能为空");
        }
        ResTopicVo vo = resTopicApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicVo", vo);
        return AjaxResult.success(map);
    }

    /**
    *查询当前用户课题表详情
    *
    * @return com.light.core.entity.AjaxResult
    * <AUTHOR>
    * @date 2023/2/1 17:41
    */
    @GetMapping("/user-detail")
    @ApiOperation(value = "查询当前用户课题表详情", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getCurrentUserTopicDetail(@RequestParam("organizationId")Long organizationId,@RequestParam("currentUserOid")String currentUserOid) {
        return resTopicApi.getCurrentUserTopicDetail(organizationId,currentUserOid);
    }


    /**
     * 删除课题表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicApi.delete(id);
    }
}
