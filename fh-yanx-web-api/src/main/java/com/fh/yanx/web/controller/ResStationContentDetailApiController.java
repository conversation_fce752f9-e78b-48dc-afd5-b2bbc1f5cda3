package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResStationContentDetailApi;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentDetailConditionBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentDetailVo;
import com.fh.yanx.service.res.service.ResStationContentDetailApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作站具体内容详情
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/station/content/detail")
@Api(value = "", tags = "工作站具体内容详情接口")
public class ResStationContentDetailApiController {

    @Autowired
    private ResStationContentDetailApi resStationContentDetailApi;

    /**
     * 查询工作站具体内容详情分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询工作站具体内容详情列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResStationContentDetailPageListByCondition(@RequestBody ResStationContentDetailConditionBo condition) {
        PageInfo<ResStationContentDetailVo> page = resStationContentDetailApi.getResStationContentDetailPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询工作站具体内容详情列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询工作站具体内容详情列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResStationContentDetailListByCondition(@RequestBody ResStationContentDetailConditionBo condition) {
        List<ResStationContentDetailVo> list = resStationContentDetailApi.getResStationContentDetailListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增工作站具体内容详情
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增工作站具体内容详情", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResStationContentDetail(@RequestBody ResStationContentDetailBo resStationContentDetailBo) {
        return resStationContentDetailApi.addResStationContentDetail(resStationContentDetailBo);
    }

    /**
     * 修改工作站具体内容详情
     *
     * @param resStationContentDetailBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新工作站具体内容详情", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResStationContentDetail(@RequestBody ResStationContentDetailBo resStationContentDetailBo) {
        if (null == resStationContentDetailBo.getStationContentDetailId()) {
            return AjaxResult.fail("工作站具体内容详情id不能为空");
        }
        return resStationContentDetailApi.updateResStationContentDetail(resStationContentDetailBo);
    }

    /**
     * 查询工作站具体内容详情详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询工作站具体内容详情详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "工作站具体内容详情id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResStationContentDetailVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("工作站具体内容详情id不能为空");
        }
        ResStationContentDetailVo vo = resStationContentDetailApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resStationContentDetailVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除工作站具体内容详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除工作站具体内容详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "工作站具体内容详情id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resStationContentDetailApi.delete(id);
    }
}
