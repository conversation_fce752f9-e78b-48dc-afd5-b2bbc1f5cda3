package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicPvApi;
import com.fh.yanx.service.res.entity.bo.ResTopicPvBo;
import com.fh.yanx.service.res.entity.bo.ResTopicPvConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicPvVo;
import com.fh.yanx.service.res.service.ResTopicPvApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题pv记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/topic/pv")
@Api(value = "", tags = "课题pv记录接口")
public class ResTopicPvApiController {

    @Autowired
    private ResTopicPvApi resTopicPvApi;

    /**
     * 查询课题pv记录分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题pv记录列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicPvPageListByCondition(@RequestBody ResTopicPvConditionBo condition) {
        PageInfo<ResTopicPvVo> page = resTopicPvApi.getResTopicPvPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题pv记录列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题pv记录列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicPvListByCondition(@RequestBody ResTopicPvConditionBo condition) {
        List<ResTopicPvVo> list = resTopicPvApi.getResTopicPvListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增课题pv记录
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题pv记录", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicPv(@RequestBody ResTopicPvBo resTopicPvBo) {
        return resTopicPvApi.addResTopicPv(resTopicPvBo);
    }

    /**
     * 修改课题pv记录
     *
     * @param resTopicPvBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新课题pv记录", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicPv(@RequestBody ResTopicPvBo resTopicPvBo) {
        if (null == resTopicPvBo.getPvId()) {
            return AjaxResult.fail("课题pv记录id不能为空");
        }
        return resTopicPvApi.updateResTopicPv(resTopicPvBo);
    }

    /**
     * 查询课题pv记录详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题pv记录详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题pv记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicPvVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题pv记录id不能为空");
        }
        ResTopicPvVo vo = resTopicPvApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicPvVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除课题pv记录
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题pv记录", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题pv记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicPvApi.delete(id);
    }
}
