package com.fh.yanx.web.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.fh.yanx.service.bm.api.BmInfoJoinerApi;
import com.fh.yanx.service.bm.entity.bo.*;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.fh.yanx.service.enums.*;
import com.fh.yanx.service.order.api.ActivOrderApi;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.web.bo.OrderBo;
import com.light.core.utils.RSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.bm.api.BmInfoApi;
import com.fh.yanx.service.bm.api.BmInfoConnectApi;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportVo;
import com.fh.yanx.web.consts.ConstantsRedis;
import com.fh.yanx.web.service.SmsYmService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import com.light.redis.utils.ExcelUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 新时代报名活动controller(无需登录)
 * 
 * <AUTHOR>
 * @date 2023/6/8 15:04
 */
@Slf4j
@RestController
@Validated
@RequestMapping("bm/info")
@Api(value = "", tags = "新时代报名活动")
public class BmH5Controller {
    @Resource
    private BmInfoApi bmInfoApi;
    @Resource
    private BmInfoConnectApi bmInfoConnectApi;
    @Autowired
    private SmsYmService smsYmService;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private ActivOrderApi activOrderApi;

    // 解密私钥
    @Value("${server.encry.privateKey}")
    private String PRIVATE_KEY;

    @Resource
    private BmInfoJoinerApi bmInfoJoinerApi;

    /**
     * 新增报名内容-无需登录
     *
     * @param bmInfoBo the bm info bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增报名内容", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addBmInfo(@RequestBody BmInfoBo bmInfoBo) {
        return bmInfoApi.addBmInfoUg(bmInfoBo);
    }

    /**
     * 发送短信验证码
     *
     * @param bmInfoBo the bm info bo
     * @return ajax result
     */
    @PostMapping("/sms")
    @ApiOperation(value = "发送短信验证码", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult sendSms(@RequestBody BmInfoBo bmInfoBo) {
        if (StringUtils.isBlank(bmInfoBo.getContactMobile())) {
            return AjaxResult.fail("手机号码不允许为空");
        }
        if (redisComponent.get(ConstantsRedis.SMS_COUNT_PREFIX + bmInfoBo.getContactMobile()) != null
            && (Integer)redisComponent
                .get(ConstantsRedis.SMS_COUNT_PREFIX + bmInfoBo.getContactMobile()) >= ConstantsRedis.SMS_COUNT_LIMIT) {
            return AjaxResult.fail("短信验证码发送达到上限，请稍后重试");
        }

        AjaxResult codeResult = smsYmService.send(bmInfoBo.getContactMobile());
        if (codeResult == null || codeResult.isFail() || StringUtils.isBlank(codeResult.getData().toString())) {
            return AjaxResult.fail("短信验证码发送失败，请重试");
        }
        log.info("sms send success, phone[" + bmInfoBo.getContactMobile() + "], code[" + codeResult.getData() + "]");
        redisComponent.incr(ConstantsRedis.SMS_COUNT_PREFIX + bmInfoBo.getContactMobile(), 1);
        redisComponent.expire(ConstantsRedis.SMS_COUNT_PREFIX + bmInfoBo.getContactMobile(),
            ConstantsRedis.SMS_COUNT_EXPIRE_IN);

        String code = codeResult.getData().toString();
        redisComponent.set(ConstantsRedis.SMS_PREFIX + bmInfoBo.getContactMobile(), code, ConstantsRedis.SMS_EXPIRE_IN);
        // 验证码不返回给前端
        codeResult.setData(null);
        return codeResult;
    }

    /**
     * 查询报名内容-分页
     *
     * @param bmInfoConditionBo the bm info condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询报名内容", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listBmInfo(@RequestBody BmInfoConditionBo bmInfoConditionBo) {
        bmInfoConditionBo.setOrderBy("create_time desc");
        return bmInfoApi.getBmInfoPageListByCondition(bmInfoConditionBo);
    }

    /**
     * 查询报名内容详情
     *
     * @param infoId the info id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询报名内容详情", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult detailBmInfo(@RequestParam("infoId") Long infoId) {
        return bmInfoApi.getDetailWithJoiner(infoId);
    }

    /**
     * 更新报名记录
     *
     * @param bmInfoBo the bm info bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @Deprecated
    @PostMapping("/update")
    @ApiOperation(value = "更新报名记录", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateBmInfo(@RequestBody BmInfoBo bmInfoBo) {
        return bmInfoApi.updateBmInfo(bmInfoBo);
    }

    /**
     * 更新报名记录-同时更新参与人（with bm_info_joiner）
     *
     * @param bmInfoBo the bm info bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @Deprecated
    @PostMapping("/update-wbij")
    @ApiOperation(value = "更新报名记录-同时更新参与人", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateBmInfoWbij(@RequestBody BmInfoBo bmInfoBo) {
        // 转换参数，防止更新数据状态
        BmInfoBo bmInfo = new BmInfoBo();
        bmInfo.setInfoId(bmInfoBo.getInfoId());
        bmInfo.setProvinceId(bmInfoBo.getProvinceId());
        bmInfo.setProvinceName(bmInfoBo.getProvinceName());
        bmInfo.setCityId(bmInfoBo.getCityId());
        bmInfo.setCityName(bmInfoBo.getCityName());
        bmInfo.setAreaId(bmInfoBo.getAreaId());
        bmInfo.setAreaName(bmInfoBo.getAreaName());
        bmInfo.setDepartName(bmInfoBo.getDepartName());
        bmInfo.setPeriod(bmInfoBo.getPeriod());
        bmInfo.setContactName(bmInfoBo.getContactName());
        bmInfo.setContactMobile(bmInfoBo.getContactMobile());
        bmInfo.setJoinType(bmInfoBo.getJoinType());
        bmInfo.setBmInfoJoinerBoList(bmInfoBo.getBmInfoJoinerBoList());
        return bmInfoApi.updateBmInfoWbij(bmInfo);
    }

    /**
     * 更新发票信息
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/17 16:56
     **/
    @Deprecated
    @PostMapping("/update-invoice")
    @ApiOperation(value = "更新发票信息", notes = "更新发票信息")
    public AjaxResult updateBmInfoInvoice(@RequestBody BmInfoBo bmInfoBo) {
        // 转换参数，只更新发票信息
        BmInfoBo bmInfo = new BmInfoBo();
        bmInfo.setInfoId(bmInfoBo.getInfoId());
        bmInfo.setInvoicingType(bmInfoBo.getInvoicingType());
        bmInfo.setInvoiceHeader(bmInfoBo.getInvoiceHeader());
        bmInfo.setDutyCode(bmInfoBo.getDutyCode());
        bmInfo.setEmail(bmInfoBo.getEmail());
        bmInfo.setNotValidateMobile(true);
        return bmInfoApi.updateBmInfo(bmInfo);
    }

    /**
     * 新增联系确认表信息(会同时更新主记录表的信息)-废弃移动到后台
     *
     * @param bmInfoConnectBo the bm info connect bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @Deprecated
    @PostMapping("/add-connect")
    @ApiOperation(value = "新增联系确认表信息-同时更新报名确认状态", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addConnect(@RequestBody BmInfoConnectBo bmInfoConnectBo) {
        return bmInfoConnectApi.addBmInfoConnectWithUpdateBmInfo(bmInfoConnectBo);
    }

    /**
     * 查询沟通记录列表-移动到后台
     *
     * @param bmInfoConnectConditionBo the bm info connect condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @Deprecated
    @PostMapping("/list-connect")
    @ApiOperation(value = "查询沟通记录列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listConnect(@RequestBody BmInfoConnectConditionBo bmInfoConnectConditionBo) {
        return bmInfoConnectApi.getBmInfoConnectListByCondition(bmInfoConnectConditionBo);
    }

    /**
     * 查询沟通记录列表-分页-废弃移动到后台
     *
     * @param bmInfoConnectConditionBo the bm info connect condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @Deprecated
    @PostMapping("/list-connect-page")
    @ApiOperation(value = "查询沟通记录列表-分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listConnectPage(@RequestBody BmInfoConnectConditionBo bmInfoConnectConditionBo) {
        return bmInfoConnectApi.getBmInfoConnectPageListByCondition(bmInfoConnectConditionBo);
    }

    /**
     * 导出报名数据-废弃，已移动到后台
     *
     * @param bmInfoConditionBo the bm info condition bo
     * @param response the response
     * @return void
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 /4/21 17:55
     */
    @Deprecated
    @PostMapping("/export")
    @ApiOperation(value = "导出报名数据", httpMethod = SystemConstants.POST_REQUEST)
    public void exportUser(@RequestBody BmInfoConditionBo bmInfoConditionBo, HttpServletResponse response)
        throws IOException {
        AjaxResult<List<BmInfoExportVo>> bmInfoListByConditionExport =
            bmInfoApi.getBmInfoListByConditionExport(bmInfoConditionBo);
        if (bmInfoListByConditionExport.isFail() || CollectionUtils.isEmpty(bmInfoListByConditionExport.getData())) {
            return;
        }
        List<BmInfoExportVo> exportList = bmInfoListByConditionExport.getData();
        ExcelUtils.exportExcel(exportList, "新时代活动报名信息", "新时代活动报名信列表", BmInfoExportVo.class, "新时代活动报名信息", response);
    }

    /**
     * 后台新增报名
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/28 14:35
     **/
    @PostMapping("/add-bm")
    @ApiOperation(value = "后台新增报名", notes = "后台新增报名")
    public AjaxResult addActivBmInfo(@RequestBody BmInfoBo bmInfoBo) {
        return bmInfoApi.addBmInfoWithJoiner(bmInfoBo);
    }

    /**
     * 修改费用
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/2 16:30
     **/
    @PostMapping("/change-order-Amount")
    @ApiOperation(value = "修改费用", notes = "修改费用")
    public AjaxResult changeOrderAmount(@RequestBody BmInfoBo bmInfoBo) {
        if (bmInfoBo.getInfoId() == null) {
            return AjaxResult.fail("报名id不能为空");
        }
        AjaxResult<BmInfoVo> ajaxResult = bmInfoApi.getDetail(bmInfoBo.getInfoId());
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        BmInfoVo bmInfoVo = ajaxResult.getData();
        if (bmInfoVo == null) {
            return AjaxResult.fail("报名信息不存在");
        }
        if (BmInfoPayType.ALREADY_PAY.getCode().equals(bmInfoVo.getPayType())) {
            return AjaxResult.fail("已缴费无法修改");
        }

        ActivOrderBo orderBo = new ActivOrderBo();
        orderBo.setGoodsId(bmInfoBo.getInfoId());
        orderBo.setGoodsType(GoodsType.ACTIV_BM.getCode());
        orderBo.setOrderAmount(bmInfoBo.getOrderAmount());
        return activOrderApi.changeOrderAmount(orderBo);
    }

    /**
     * 校验联系人是否已报名（无需登录）
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/3 18:06
     **/
    @PostMapping("/check/contact-mobile")
    @ApiOperation(value = "校验联系人是否已报名", notes = "校验联系人是否已报名")
    public AjaxResult checkActivContact(@RequestBody BmInfoBo bmInfoBo) {
        // 参数校验
        if (StringUtils.isBlank(bmInfoBo.getContactMobile())) {
            return AjaxResult.fail("手机号码不允许为空");
        }
        if (bmInfoBo.getActivId() == null) {
            return AjaxResult.fail("活动id不能为空");
        }
        if (!bmInfoBo.isNoValidateSmsCode()) {
            // 短信验证码校验
            if (StringUtils.isBlank(bmInfoBo.getCode())) {
                return AjaxResult.fail("短信验证码不允许为空");
            }
            if (!bmInfoBo.getCode().equals(redisComponent.get(ConstantsRedis.SMS_PREFIX + bmInfoBo.getContactMobile()))) {
                return AjaxResult.fail("短信验证码不匹配");
            }
            // 短信验证码校验成功后删除redis缓存
            redisComponent.del(ConstantsRedis.SMS_PREFIX + bmInfoBo.getContactMobile());
        }
        return bmInfoApi.checkActivContact(bmInfoBo);
    }

    /**
     * 校验是否参会
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/3 18:06
     **/
    @PostMapping("/check/join-mobile")
    @ApiOperation(value = "校验是否参会", notes = "校验是否参会")
    public AjaxResult checkJoinMobile(@RequestBody BmInfoBo bmInfoBo) {
        // 参数校验
        if (StringUtils.isBlank(bmInfoBo.getContactMobile())) {
            return AjaxResult.fail("手机号码不允许为空");
        }
        if (bmInfoBo.getActivId() == null) {
            return AjaxResult.fail("活动id不能为空");
        }
        //跳过联系人手机号校验
        bmInfoBo.setSkipCheckBmContact(true);
        return bmInfoApi.checkActivContact(bmInfoBo);
    }

    /**
     * 根据联系人手机号查询报名记录（无需登录）
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/4 14:42
     **/
    @PostMapping("/detail-by-mobile")
    @ApiOperation(value = "根据联系人手机号查询报名记录", notes = "根据联系人手机号查询报名记录")
    public AjaxResult bmInfoDetailByContactMobile(@RequestBody BmInfoConditionBo condition) {
        return bmInfoApi.bmInfoDetailByMobile(condition);
    }

    /**
     * 活动签到
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/8 9:56
     **/
    @PostMapping("/sign")
    @ApiOperation(value = "活动签到", notes = "活动签到")
    public AjaxResult bmInfoSignIn(@RequestBody BmInfoBo bmInfoBo) {
        if (bmInfoBo.getActivId() == null) {
            return AjaxResult.fail("活动id不能为空");
        }
        if (StringUtils.isBlank(bmInfoBo.getContactMobile())) {
            return AjaxResult.fail("联系人手机号不能为空");
        }
        if (StringUtils.isBlank(bmInfoBo.getSignInCode())) {
            return AjaxResult.fail("签到码不能为空");
        }
        return bmInfoApi.bmInfoSignIn(bmInfoBo);
    }

    /**
     * 我的订单
     *
     * @param activId 活动id
     * @param orderKey 参数为手机号的加密字符串，为了防止被爬虫爬取到手机号
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2023 -10-27 11:39:01
     */
    @PostMapping("/my-order")
    @ApiOperation(value = "我的订单", notes = "我的订单")
    public AjaxResult myOrder(@RequestBody OrderBo orderBo) {
        if (orderBo.getActivId() == null || StringUtils.isBlank(orderBo.getOrderKey())) {
            return AjaxResult.fail("参数不能为空");
        }
        String phone = "";
        try {
            phone = RSAUtil.privateDecrypt(orderBo.getOrderKey(), PRIVATE_KEY);
            if (StringUtils.isBlank(phone)) {
                return AjaxResult.fail("参数不能为空");
            }
        } catch (Exception e) {
            log.error("解密失败!原因:{}", e);
            return AjaxResult.fail("非法参数");
        }
        return bmInfoApi.myOrder(orderBo.getActivId(), phone);
    }

    /**
     * 获取活动报名信息 包含活动名称等信息
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/3/26 14:18
     **/
    @PostMapping("/get-joiner")
    @ApiOperation(value = "获取参会人信息", notes = "获取参会人信息")
    public AjaxResult getJoiner(@RequestBody BmInfoJoinerBo bmInfoJoinerBo) {
        return bmInfoJoinerApi.getByMobile(bmInfoJoinerBo);
    }

    /**
     * 提交学时证明
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/3/26 15:04
     **/
    @PostMapping("/submit-study-prove")
    @ApiOperation(value = "提交学时证明", notes = "提交学时证明")
    public AjaxResult submitStudyProve(@RequestBody BmInfoJoinerBo bmInfoJoinerBo) {
        BmInfoJoinerBo bo = new BmInfoJoinerBo();
        bo.setJoinProveId(bmInfoJoinerBo.getJoinProveId());
        bo.setJoinProveName(bmInfoJoinerBo.getJoinProveName());
        bo.setJoinProveUrl(bmInfoJoinerBo.getJoinProveUrl());
        bo.setJoinProveNameOri(bmInfoJoinerBo.getJoinProveNameOri());
        bo.setStudyHours(bmInfoJoinerBo.getStudyHours());
        bo.setInfoId(bmInfoJoinerBo.getInfoId());
        bo.setJoinerMobile(bmInfoJoinerBo.getJoinerMobile());
        bo.setStudyProveType(BmInfoStudyProveType.IN_GENERATE.getValue());
        return bmInfoJoinerApi.updateByMobile(bo);
    }

//    @GetMapping("/asposeWord")
//    @ApiOperation(value = "替换word", notes = "替换word")
//    public void submitStudyProve(HttpServletResponse response) throws IOException {
//        byte[] bytes = bmInfoApi.asposeWord();
//        if (Objects.isNull(bytes)||bytes.length==0){
//            return;
//        }
//        // 设置响应头
//        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//        String encodedFileName = URLEncoder.encode("替换后的文档.docx", StandardCharsets.UTF_8.name());
//        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
//
//        // 写入响应流
//        ServletOutputStream servletOut = response.getOutputStream();
//        servletOut.write(bytes);
//        servletOut.flush();
//        servletOut.close(); // 显式关闭流
//
//    }
}
