package com.fh.yanx.web.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.regex.Pattern;

/**
 * 字符集规范化过滤器
 * 统一将各种形式的utf-8字符集标识转换为标准的UTF-8格式
 * 解决MessageConverter因字符集大小写不一致导致的解析问题
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Slf4j
@Component
public class CharsetNormalizationFilter implements Filter, Ordered {

    /**
     * UTF-8字符集匹配模式（不区分大小写）
     * 匹配: utf-8, UTF-8, Utf-8, utf8, UTF8等各种形式
     */
    private static final Pattern UTF8_PATTERN = Pattern.compile("utf-?8", Pattern.CASE_INSENSITIVE);
    
    /**
     * 标准UTF-8字符集标识
     */
    private static final String STANDARD_UTF8 = "UTF-8";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("CharsetNormalizationFilter initialized - 字符集规范化过滤器已启动");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            String contentType = httpRequest.getContentType();

            // 检查Content-Type是否包含UTF-8相关字符集
            if (StringUtils.hasText(contentType) && UTF8_PATTERN.matcher(contentType).find()) {
                // 将所有形式的utf-8统一替换为标准的UTF-8
                String normalizedContentType = UTF8_PATTERN.matcher(contentType).replaceAll(STANDARD_UTF8);
                
                if (log.isDebugEnabled()) {
                    log.debug("字符集规范化: {} -> {}", contentType, normalizedContentType);
                }
                
                // 创建请求包装器，重写Content-Type相关方法
                HttpServletRequestWrapper wrapper = new HttpServletRequestWrapper(httpRequest) {
                    @Override
                    public String getContentType() {
                        return normalizedContentType;
                    }

                    @Override
                    public String getHeader(String name) {
                        if ("Content-Type".equalsIgnoreCase(name)) {
                            return normalizedContentType;
                        }
                        return super.getHeader(name);
                    }

                    @Override
                    public Enumeration<String> getHeaders(String name) {
                        if ("Content-Type".equalsIgnoreCase(name)) {
                            return Collections.enumeration(Collections.singletonList(normalizedContentType));
                        }
                        return super.getHeaders(name);
                    }
                };
                
                chain.doFilter(wrapper, response);
            } else {
                // Content-Type不包含UTF-8相关字符集，直接传递
                chain.doFilter(request, response);
            }
        } else {
            // 非HTTP请求，直接传递
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
        log.info("CharsetNormalizationFilter destroyed - 字符集规范化过滤器已销毁");
    }

    /**
     * 设置过滤器优先级为最高，确保在其他过滤器之前执行
     * 特别是在MessageConverter处理之前进行字符集规范化
     */
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}
