package com.fh.yanx.web.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import com.fh.yanx.service.enums.SmsUseType;
import com.fh.yanx.service.org.entity.bo.OrganizationUserBo;
import com.fh.yanx.web.consts.ConstantsRedis;
import com.fh.yanx.web.service.SmsYmService;
import com.fh.yanx.web.utils.ValidateUtil;
import com.google.code.kaptcha.Producer;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 短信验证码
 *
 * <AUTHOR>
 * @date 2023-07-19 9:28
 */
@RestController
@RequestMapping("/yanx/sms")
@Slf4j
@Api(value = "短信验证码", tags = "短信验证码")
public class SmsController {
    @Value("${captcha.type}")
    private String CAPTCHA_TYPE;

    @Value("${captcha.expire.time}")
    private Long CAPTCHA_EXPIRE_TIME;

    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Resource
    RedisComponent redisComponent;
    @Autowired
    private SmsYmService smsYmService;

    /**
     * 生成验证码
     */
    @GetMapping("/captcha/image")
    @ApiOperation(value = "获取验证码图片")
    public AjaxResult getCode(HttpServletResponse response) throws IOException {
        // 保存验证码信息
        String uuid = RandomUtil.randomString(64);
        String verifyKey = RedisKeyEnum.CAPTCHA_CODE_KEY.getValue() + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 生成验证码
        if ("math".equals(CAPTCHA_TYPE)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(CAPTCHA_TYPE)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisComponent.set(verifyKey, code, CAPTCHA_EXPIRE_TIME);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return AjaxResult.fail("验证码图片生成失败");
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("uuid", uuid);
        map.put("image", Base64.encode(os.toByteArray()));
        return AjaxResult.success(map);
    }

    /**
     * 发送短信验证码
     *
     * @param organizationUserBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 10:06
     **/
    @PostMapping("/send")
    @ApiOperation(value = "发送验证码", notes = "发送验证码")
    public AjaxResult sendSms(@RequestBody OrganizationUserBo organizationUserBo) {
        if (StringUtils.isBlank(organizationUserBo.getPhone())) {
            return AjaxResult.fail("手机号码不允许为空");
        }
        if (!ValidateUtil.validatePhone(organizationUserBo.getPhone())) {
            return AjaxResult.fail("请输入正确的手机号");
        }
        if (StringUtils.isBlank(organizationUserBo.getCode())) {
            return AjaxResult.fail("图形验证码为空");
        }
        if (StringUtils.isBlank(organizationUserBo.getUuid())) {
            return AjaxResult.fail("uuid为空");
        }
        String uuid = organizationUserBo.getUuid();
        String verifyKey = RedisKeyEnum.CAPTCHA_CODE_KEY.getValue() + uuid;
        Object captcha = redisComponent.get(verifyKey);
        redisComponent.del(verifyKey);
        if (captcha == null) {
            return AjaxResult.fail("图形验证码已失效");
        }
        if (!organizationUserBo.getCode().equalsIgnoreCase(captcha.toString())) {
            return AjaxResult.fail("图形验证码错误");
        }
        if (redisComponent.get(ConstantsRedis.SMS_COUNT_PREFIX + organizationUserBo.getPhone()) != null
            && (Integer)redisComponent.get(
                ConstantsRedis.SMS_COUNT_PREFIX + organizationUserBo.getPhone()) >= ConstantsRedis.SMS_COUNT_LIMIT) {
            return AjaxResult.fail("短信验证码发送达到上限，请稍后重试");
        }
        AjaxResult codeResult = smsYmService.send(organizationUserBo.getPhone());
        if (codeResult == null || codeResult.isFail() || StringUtils.isBlank(codeResult.getData().toString())) {
            return AjaxResult.fail("短信验证码发送失败，请重试");
        }
        log.info("sms send success, phone[" + organizationUserBo.getPhone() + "], code[" + codeResult.getData() + "]");
        redisComponent.incr(ConstantsRedis.SMS_COUNT_PREFIX + organizationUserBo.getPhone(), 1);
        redisComponent.expire(ConstantsRedis.SMS_COUNT_PREFIX + organizationUserBo.getPhone(),
            ConstantsRedis.SMS_COUNT_EXPIRE_IN);

        String code = codeResult.getData().toString();
        redisComponent.set(ConstantsRedis.SMS_PREFIX + organizationUserBo.getPhone(), code,
            ConstantsRedis.SMS_EXPIRE_IN);
        // 验证码不返回给前端
        codeResult.setData(null);
        return codeResult;
    }

    /**
     * 验证码校验(如果是登录，需要传smsUseType=1)
     *
     * @param organizationUserBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/25 16:32
     **/
    @PostMapping("/valid-phone-code")
    @ApiOperation(value = "验证码校验", notes = "验证码校验")
    public AjaxResult validPhoneCode(@RequestBody OrganizationUserBo organizationUserBo) {
        if (StringUtils.isBlank(organizationUserBo.getPhone())) {
            return AjaxResult.fail("手机号码不能为空");
        }
        if (!ValidateUtil.validatePhone(organizationUserBo.getPhone())) {
            return AjaxResult.fail("请输入正确的手机号");
        }
        if (StringUtils.isBlank(organizationUserBo.getCode())) {
            return AjaxResult.fail("验证码不能为空");
        }
        if (!organizationUserBo.getCode()
            .equals(redisComponent.get(ConstantsRedis.SMS_PREFIX + organizationUserBo.getPhone()))) {
            return AjaxResult.fail("短信验证码不匹配");
        }
        // 写入短信验证码登录的key（有效期5分钟），注意登录用完需要删除
        String smsLoginCode = UUID.fastUUID().toString();
        if (organizationUserBo.getSmsUseType() != null
            && organizationUserBo.getSmsUseType().equals(SmsUseType.LOGIN.getValue())) {
            redisComponent.set(ConstantsRedis.SMS_LOGIN_PREFIX + smsLoginCode, organizationUserBo.getPhone(),
                ConstantsRedis.SMS_LOGIN_EXPIRE_IN);
        }
        return AjaxResult.success(smsLoginCode);
    }
}
