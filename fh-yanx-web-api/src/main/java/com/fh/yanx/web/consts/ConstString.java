/*
 * Copyright 2014 Focus Technology, Co., Ltd. All rights reserved.
 */
package com.fh.yanx.web.consts;

/**
 * 字符串枚举值
 *
 * <AUTHOR>
 */
public interface ConstString {
    /**
     * #
     */
    String jh = "#";

    /**
     * *
     */
    String xh = "*";

    /**
     * 中文逗号
     */
    String zwdh = "，";

    /**
     * 英文逗号
     */
    String ywdh = ",";

    /**
     * 顿号
     */
    String dh = "、";

    /**
     * 英文中划线
     */
    String ywzhx = "-";

    /**
     * 斜杠
     */
    String xg = "/";

    /**
     * 百分号
     */
    String bfh = "%";

    /**
     * 空格
     */
    String kg = " ";

    /**
     * 空字符串
     */
    String kzf = "";

    /**
     * 英文下划线
     */
    String ywxhx = "_";

    /**
     * 英文分号
     */
    String ywfh = ";";

    /**
     * 空字符串
     */
    String blank = "";

    /**
     * 主题浏览量key
     */
    String PV_KEY = "pv:";

    /**
     * 随机数生成样本-纯数字
     */
    String RANDOM_SAMPLE_NUMBER = "0123456789";
    /**
     * 随机数生成样本-数字+字母（去掉0,1,l,o）
     */
    String RANDOM_SAMPLE_STRING = "23456789abcdefghijkmnpqrstuvwxyz";
}
