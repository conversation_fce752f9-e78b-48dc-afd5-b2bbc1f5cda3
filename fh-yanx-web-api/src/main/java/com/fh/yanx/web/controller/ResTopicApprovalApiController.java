package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicApprovalApi;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalBo;
import com.fh.yanx.service.res.entity.bo.ResTopicApprovalConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicApprovalVo;
import com.fh.yanx.service.res.service.ResTopicApprovalApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题审批意见表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/topic/approval")
@Api(value = "", tags = "课题审批意见表接口")
public class ResTopicApprovalApiController {

    @Autowired
    private ResTopicApprovalApi resTopicApprovalApi;

    /**
     * 查询课题审批意见表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题审批意见表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicApprovalPageListByCondition(@RequestBody ResTopicApprovalConditionBo condition) {
        PageInfo<ResTopicApprovalVo> page = resTopicApprovalApi.getResTopicApprovalPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题审批意见表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题审批意见表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicApprovalListByCondition(@RequestBody ResTopicApprovalConditionBo condition) {
        List<ResTopicApprovalVo> list = resTopicApprovalApi.getResTopicApprovalListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增课题审批意见表（教师审核）
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题审批意见表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicApproval(@RequestBody ResTopicApprovalBo resTopicApprovalBo) {
        return resTopicApprovalApi.addResTopicApproval(resTopicApprovalBo);
    }

    /**
     * 修改课题审批意见表
     *
     * @param resTopicApprovalBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新课题审批意见表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicApproval(@RequestBody ResTopicApprovalBo resTopicApprovalBo) {
        if (null == resTopicApprovalBo.getTopicApprovalId()) {
            return AjaxResult.fail("课题审批意见表id不能为空");
        }
        return resTopicApprovalApi.updateResTopicApproval(resTopicApprovalBo);
    }

    /**
     * 查询课题审批意见表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题审批意见表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题审批意见表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicApprovalVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题审批意见表id不能为空");
        }
        ResTopicApprovalVo vo = resTopicApprovalApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicApprovalVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除课题审批意见表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题审批意见表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题审批意见表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicApprovalApi.delete(id);
    }
}
