package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.BaseDataApi;
import com.light.core.entity.AjaxResult;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023-07-20 14:29
 */
@RestController
@RequestMapping("/yanx/teacher")
@Api(value = "人员管理", tags = "人员管理")
@Slf4j
public class TeacherController {
    @Resource
    BaseDataApi baseDataApi;

    /**
     * 添加教师
     *
     * @param teacherBo 教师信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "添加教师", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public AjaxResult addTeacher(@RequestBody TeacherBo teacherBo) throws Exception {
        return baseDataApi.addTeacher(teacherBo);
    }

    /**
     * 修改教师信息
     *
     * @param teacherBo 教师信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "修改教师信息", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updateTeacher(@RequestBody TeacherBo teacherBo) throws Exception {
        return baseDataApi.updateTeacher(teacherBo);
    }

    /**
     * 根据userOid查看教师信息详情
     *
     * @param userOid the teacher id
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "根据userOid查看教师信息详情", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/detail-byuseroid", method = RequestMethod.GET)
    public AjaxResult getTeacherDetail(@NotNull(message = "请选择数据") @RequestParam("userOid") String userOid) throws Exception {
        return baseDataApi.getTeacherDetail(userOid);
    }

    /**
     * 删除教师
     *
     * @param teacherId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 15:19
     **/
    @GetMapping("/del-teacher")
    @ApiOperation(value = "删除教师", notes = "删除教师")
    public AjaxResult delTeacher(@NotNull(message = "请选择数据") @RequestParam("teacherId") Long teacherId) {
        return baseDataApi.delTeacher(teacherId);
    }

    /**
     * 教师列表
     *
     * @param teacherConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/21 11:22
     **/
    @PostMapping("/teacher-list")
    @ApiOperation(value = "教师列表", notes = "教师列表")
    public AjaxResult teacherList(@RequestBody TeacherConditionBo teacherConditionBo) {
        return baseDataApi.getTeacherList(teacherConditionBo);
    }

    /**
     * 重置密码
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/21 13:48
     **/
    @GetMapping("/reset-password")
    @ApiOperation(value = "重置密码", notes = "重置密码")
    public AjaxResult resetPasswordByUserOid(@NotNull(message = "请选择数据") @RequestParam("userOid") String userOid) {
        return baseDataApi.resetPasswordByUserOid(userOid);
    }

}
