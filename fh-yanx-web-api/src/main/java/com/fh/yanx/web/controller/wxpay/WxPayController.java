package com.fh.yanx.web.controller.wxpay;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.dtflys.forest.Forest;
import com.fh.yanx.service.enums.OrderEnum;
import com.fh.yanx.service.order.api.ActivOrderApi;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.order.entity.bo.ActivOrderConditionBo;
import com.fh.yanx.service.order.entity.vo.ActivOrderVo;
import com.fh.yanx.web.bo.ThirdPartyReturnBo;
import com.fh.yanx.web.entity.WxPayBean;
import com.google.common.collect.Maps;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.enums.TradeType;
import com.ijpay.core.kit.HttpKit;
import com.ijpay.core.kit.IpKit;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.WxPayApiConfigKit;
import com.ijpay.wxpay.model.OrderQueryModel;
import com.ijpay.wxpay.model.UnifiedOrderModel;
import com.light.core.entity.AjaxResult;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.apache.bcel.classfile.ConstantLong;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.ijpay.wxpay.WxPayApiConfig;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付controller-v2版：https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=7_1
 * 
 * <AUTHOR>
 * @date 2023/8/8 11:30
 */
@RestController
@RequestMapping("/wxPay")
@Slf4j
@Api(value = "微信支付controller-v2版", tags = "微信支付controller-v2版")
public class WxPayController extends AbstractWxPayApiController {
    @Resource
    private WxPayBean wxPayBean;
    @Resource
    private ActivOrderApi activOrderApi;
    // 支付回调地址
    private String notifyUrl;
    // 退款通知回调url
    private String refundNotifyUrl;
    // 刷卡支付-支付中-状态码
    // private static final String USER_PAYING = "USERPAYING";

    @Override
    public WxPayApiConfig getApiConfig() {
        WxPayApiConfig apiConfig;

        try {
            apiConfig = WxPayApiConfigKit.getApiConfig(wxPayBean.getAppId());
        } catch (Exception e) {
            apiConfig = WxPayApiConfig.builder().appId(wxPayBean.getAppId()).mchId(wxPayBean.getMchId())
                .partnerKey(wxPayBean.getPartnerKey()).certPath(wxPayBean.getCertPath()).domain(wxPayBean.getDomain())
                .build();
        }
        notifyUrl = wxPayBean.getNotifyUrl();
        // notifyUrl = apiConfig.getDomain().concat("/wxPay/payNotify");
        // refundNotifyUrl = apiConfig.getDomain().concat("/wxPay/refundNotify");
        return apiConfig;
    }

    /**
     * 微信回调 (通过code获取AccessToken和openId)
     *
     * @param code
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/9 10:07
     */
    @RequestMapping(value = "/wx-invoke", method = {RequestMethod.POST, RequestMethod.GET})
    public AjaxResult weChatInvoke(@RequestParam String code) {
        // 通过code换取网页授权access_token 和 openId
        // String code = request.getParameter("code");
        String weChatAccessTokenUrl = wxPayBean.getAccessTokenUrl();
        String url = weChatAccessTokenUrl.replace("{1}", wxPayBean.getAppId()).replace("{2}", wxPayBean.getAppSecret())
            .replace("{3}", code);
        ThirdPartyReturnBo weChatBo = Forest.get(url).execute(ThirdPartyReturnBo.class);
        if (null == weChatBo.getAccess_token() || StringUtils.isNotBlank(weChatBo.getErrcode())) {
            return AjaxResult.fail("通过code换取网页授权access_token" + weChatBo.getErrmsg());
        }
        return AjaxResult.success(weChatBo);
    }

    /**
     * 公众号支付:JSAPI
     *
     * @param request the request
     * @param activOrderBo the activ order bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -09-22 11:09:20
     */
    @RequestMapping(value = "/webPay", method = {RequestMethod.POST, RequestMethod.GET})
    public AjaxResult webPay(HttpServletRequest request, @RequestBody ActivOrderBo activOrderBo) {
        if (StrUtil.isEmpty(activOrderBo.getOpenId())) {
            return AjaxResult.fail("openId is null");
        }
        String ip = IpKit.getRealIp(request);
        if (StrUtil.isEmpty(ip)) {
            ip = "127.0.0.1";
        }
        // sunqbtodo activOrderBo.getOrderAmount() 需要从数据库中查询（activ_order.order_amount ->
        // 改为activ_order.pay_amount），不能从前端传过来，另外需要转换成“分”传递给微信
        AjaxResult<ActivOrderVo> detailByOrderNumber =
            activOrderApi.getDetailByOrderNumber(activOrderBo.getOrderNumber());
        if (detailByOrderNumber.isFail() || detailByOrderNumber.getData() == null) {
            return AjaxResult.fail("订单不存在");
        }
        ActivOrderVo activOrderVo = detailByOrderNumber.getData();
        if (activOrderVo.getOrderAmount() == null || activOrderVo.getOrderAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return AjaxResult.fail("订单金额必须大于0");
        }

        WxPayApiConfig wxPayApiConfig = WxPayApiConfigKit.getWxPayApiConfig();
        // 金额*100得到分的金额(取long整数部分)
        BigDecimal multiply = activOrderVo.getOrderAmount().multiply(BigDecimal.valueOf(100));
        Map<String,
            String> params = UnifiedOrderModel.builder().appid(wxPayApiConfig.getAppId())
                .mch_id(wxPayApiConfig.getMchId()).nonce_str(WxPayKit.generateStr()).body(activOrderBo.getOrderName())
                .attach(activOrderBo.getOrderName()).out_trade_no(activOrderBo.getOrderNumber())
                .total_fee(String.valueOf(multiply.longValue())).spbill_create_ip(ip).notify_url(notifyUrl)
                .trade_type(TradeType.JSAPI.getTradeType()).openid(activOrderBo.getOpenId()).build()
                .createSign(wxPayApiConfig.getPartnerKey(), SignType.HMACSHA256);

        String xmlResult = WxPayApi.pushOrder(false, params);
        log.info(xmlResult);
        Map<String, String> resultMap = WxPayKit.xmlToMap(xmlResult);
        String returnCode = resultMap.get("return_code");
        String returnMsg = resultMap.get("return_msg");
        if (!WxPayKit.codeIsOk(returnCode)) {
            return AjaxResult.fail(returnMsg);
        }
        String resultCode = resultMap.get("result_code");
        if (!WxPayKit.codeIsOk(resultCode)) {
            return AjaxResult.fail(returnMsg);
        }

        // 以下字段在 return_code 和 result_code 都为 SUCCESS 的时候有返回
        String prepayId = resultMap.get("prepay_id");
        Map<String, String> packageParams = WxPayKit.prepayIdCreateSign(prepayId, wxPayApiConfig.getAppId(),
            wxPayApiConfig.getPartnerKey(), SignType.HMACSHA256);
        String jsonStr = JSON.toJSONString(packageParams);
        return AjaxResult.success(jsonStr);
    }

    /**
     * 查询订单,使用订单号+流水号查询
     * 
     * @param transactionId
     * @param outTradeNo
     * @return
     */
    @RequestMapping(value = "/queryOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public AjaxResult queryOrder(@RequestBody ActivOrderConditionBo activOrderConditionBo) {
        try {
            WxPayApiConfig wxPayApiConfig = WxPayApiConfigKit.getWxPayApiConfig();

            Map<String,
                String> params = OrderQueryModel.builder().appid(wxPayApiConfig.getAppId())
                    .mch_id(wxPayApiConfig.getMchId()).transaction_id(activOrderConditionBo.getTransactionId())
                    .out_trade_no(activOrderConditionBo.getOrderNumber()).nonce_str(WxPayKit.generateStr()).build()
                    .createSign(wxPayApiConfig.getPartnerKey(), SignType.MD5);
            log.info("请求参数：{}", WxPayKit.toXml(params));
            String query = WxPayApi.orderQuery(params);
            log.info("查询结果: {}", query);
            Map<String, String> resultMap = WxPayKit.xmlToMap(query);
            return AjaxResult.success(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.fail("系统错误");
        }
    }

    /**
     * 异步通知
     */
    @RequestMapping(value = "/payNotify", method = {RequestMethod.POST, RequestMethod.GET})
    public String payNotify(HttpServletRequest request) {
        String xmlMsg = HttpKit.readData(request);
        log.info("支付通知=" + xmlMsg);
        Map<String, String> params = WxPayKit.xmlToMap(xmlMsg);

        String returnCode = params.get("return_code");

        // 注意重复通知的情况，同一订单号可能收到多次通知，请注意一定先判断订单状态
        // 注意此处签名方式需与统一下单的签名类型一致
        if (WxPayKit.verifyNotify(params, WxPayApiConfigKit.getWxPayApiConfig().getPartnerKey(), SignType.HMACSHA256)) {
            if (WxPayKit.codeIsOk(returnCode)) {
                String outTradeNo = params.get("out_trade_no");
                String transactionId = params.get("transaction_id");
                String timeEnd = params.get("time_end");
                // 更新订单信息
                ActivOrderBo activOrderBo = new ActivOrderBo();
                activOrderBo.setOrderNumber(outTradeNo);
                activOrderBo.setTransactionId(transactionId);
                activOrderBo.setPayMode(OrderEnum.PAY_MODE_WE_CHAT.getCode());
                activOrderBo.setFirstPay(OrderEnum.FIRST_PAY.getCode());
                activOrderBo.setOrderState(OrderEnum.ORDER_STATE_PAY.getCode());
                activOrderBo.setPayTime(new Date());
                activOrderApi.completeOrder(activOrderBo);
                // 更新活动报名信息bm_info.pay_type @刘泽宇
                // 发送通知等
                Map<String, String> xml = Maps.newHashMapWithExpectedSize(2);
                xml.put("return_code", "SUCCESS");
                xml.put("return_msg", "OK");
                return WxPayKit.toXml(xml);
            }
        }
        return null;
    }

}
