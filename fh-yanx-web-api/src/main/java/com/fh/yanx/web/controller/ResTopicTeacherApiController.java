package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicTeacherApi;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherBo;
import com.fh.yanx.service.res.entity.bo.ResTopicTeacherConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicTeacherVo;
import com.fh.yanx.service.res.service.ResTopicTeacherApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.swagger.constants.SwaggerConstant;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题指导老师表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
@RequestMapping("res/topic/teacher")
@Api(value = "", tags = "课题指导老师表接口")
public class ResTopicTeacherApiController {

    @Resource
    private ResTopicTeacherApi resTopicTeacherApi;

    /**
     * 查询课题指导老师表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题指导老师表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicTeacherPageListByCondition(@RequestBody ResTopicTeacherConditionBo condition) {
        PageInfo<ResTopicTeacherVo> page = resTopicTeacherApi.getResTopicTeacherPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题指导老师表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题指导老师表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicTeacherListByCondition(@RequestBody ResTopicTeacherConditionBo condition) {
        List<ResTopicTeacherVo> list = resTopicTeacherApi.getResTopicTeacherListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增课题指导老师表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题指导老师表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicTeacher(@RequestBody ResTopicTeacherBo resTopicTeacherBo) {
        return resTopicTeacherApi.addResTopicTeacher(resTopicTeacherBo);
    }

    /**
     * 修改课题指导老师表
     *
     * @param resTopicTeacherBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add-evaluate")
    @ApiOperation(value = "更新课题指导老师表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addEvaluate(@RequestBody ResTopicTeacherBo resTopicTeacherBo) {
        if (null == resTopicTeacherBo.getId()) {
            return AjaxResult.fail("课题指导老师表id不能为空");
        }
        resTopicTeacherBo.setIsAddEvaluate(StatusEnum.YES.getCode());
        return resTopicTeacherApi.updateResTopicTeacher(resTopicTeacherBo);
    }

    /**
     * 查询课题指导老师表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题指导老师表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题指导老师表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicTeacherVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题指导老师表id不能为空");
        }
        ResTopicTeacherVo vo = resTopicTeacherApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicTeacherVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除课题指导老师表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题指导老师表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题指导老师表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicTeacherApi.delete(id);
    }

    @PostMapping("/teacher-list")
    AjaxResult getTeacherListByCondition(@RequestBody TeacherConditionBo condition){
        return resTopicTeacherApi.getTeacherListByCondition(condition);
    }
}
