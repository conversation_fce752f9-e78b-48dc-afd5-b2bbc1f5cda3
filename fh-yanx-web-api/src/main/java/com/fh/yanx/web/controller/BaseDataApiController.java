package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.BaseDataApi;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基础数据controller
 *
 * <AUTHOR>
 * @date 2022 /12/12 19:30
 */
@RestController
@RequestMapping("res/base/data")
@Api(value = "", tags = "基础数据controller")
@Slf4j
public class BaseDataApiController {

    @Resource
    private BaseDataApi baseDataApi;

    /**
     * 教师详情查询接口
     *
     * @param userOid the user oid
     * @return index topic
     */
    @ApiOperation("教师详情查询")
    @GetMapping("/teacher/detail")
    public AjaxResult teacherDetail(@RequestParam("userOid") String userOid) {
        return baseDataApi.getTeacherVoByUserOid(userOid);
    }

    /**
     * 学生详情查询接口
     *
     * @param userOid the user oid
     * @return index topic
     */
    @ApiOperation("学生详情查询")
    @GetMapping("/student/detail")
    public AjaxResult studentDetail(@RequestParam("userOid") String userOid) {
        return baseDataApi.getStudentVoByUserOid(userOid);
    }

    /**
     * 获取字典数据
     *
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:40:40
     */
    @ApiOperation(value = "获取字典数据", httpMethod = "POST")
    @ResponseBody
    @PostMapping(value = "/dicts")
    public AjaxResult listValueByTypes(@RequestBody List<String> dictTypes) throws Exception {
        return baseDataApi.listValueByTypes(dictTypes);
    }
}
