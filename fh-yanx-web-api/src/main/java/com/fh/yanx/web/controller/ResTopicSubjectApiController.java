package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicSubjectApi;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectBo;
import com.fh.yanx.service.res.entity.bo.ResTopicSubjectConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicSubjectVo;
import com.fh.yanx.service.res.service.ResTopicSubjectApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关联科目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
@RequestMapping("res/topic/subject")
@Api(value = "", tags = "关联科目表接口")
public class ResTopicSubjectApiController {

    @Autowired
    private ResTopicSubjectApi resTopicSubjectApi;

    /**
     * 查询关联科目表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询关联科目表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicSubjectPageListByCondition(@RequestBody ResTopicSubjectConditionBo condition) {
        PageInfo<ResTopicSubjectVo> page = resTopicSubjectApi.getResTopicSubjectPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询关联科目表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询关联科目表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicSubjectListByCondition(@RequestBody ResTopicSubjectConditionBo condition) {
        List<ResTopicSubjectVo> list = resTopicSubjectApi.getResTopicSubjectListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增关联科目表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增关联科目表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicSubject(@RequestBody ResTopicSubjectBo resTopicSubjectBo) {
        return resTopicSubjectApi.addResTopicSubject(resTopicSubjectBo);
    }

    /**
     * 修改关联科目表
     *
     * @param resTopicSubjectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新关联科目表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicSubject(@RequestBody ResTopicSubjectBo resTopicSubjectBo) {
        if (null == resTopicSubjectBo.getId()) {
            return AjaxResult.fail("关联科目表id不能为空");
        }
        return resTopicSubjectApi.updateResTopicSubject(resTopicSubjectBo);
    }

    /**
     * 查询关联科目表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询关联科目表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "关联科目表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicSubjectVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("关联科目表id不能为空");
        }
        ResTopicSubjectVo vo = resTopicSubjectApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicSubjectVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除关联科目表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除关联科目表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "关联科目表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicSubjectApi.delete(id);
    }
}
