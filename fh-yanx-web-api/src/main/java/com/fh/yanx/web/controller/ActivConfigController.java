package com.fh.yanx.web.controller;

import com.fh.yanx.service.activ.api.ActivInfoApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoShowBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 活动配置controller
 *
 * <AUTHOR>
 * @date 2023-08-01 10:11
 */
@RestController
@RequestMapping("/activ/config")
@Slf4j
@Api(value = "活动配置", tags = "活动配置")
public class ActivConfigController {
    @Resource
    private ActivInfoApi activInfoApi;

    /**
     * 设置活动展示
     *
     * @param activInfoShowBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/1 10:21
     **/
    @PostMapping("/set-show")
    @ApiOperation(value = "设置活动展示", notes = "设置活动展示")
    public AjaxResult setActivInfoShow(@RequestBody ActivInfoShowBo activInfoShowBo) {
        return activInfoApi.setActivInfoShow(activInfoShowBo);
    }
}
