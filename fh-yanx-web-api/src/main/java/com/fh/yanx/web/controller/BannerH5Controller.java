package com.fh.yanx.web.controller;

import com.fh.yanx.service.banner.api.BannerInfoApi;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * banner信息controller
 *
 * <AUTHOR>
 * @date 2023-10-24 17:50
 */
@RestController
@RequestMapping("/banner/info")
@Slf4j
@Api(value = "banner信息", tags = "banner信息")
public class BannerH5Controller {
    @Resource
    private BannerInfoApi bannerInfoApi;

    /**
     * banner列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/24 17:52
     **/
    @ApiOperation(value = "banner列表", notes = "banner列表")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody BannerInfoConditionBo condition) {
        return bannerInfoApi.getBannerListByCondition(condition);
    }

}
