package com.fh.yanx.web.controller;

import java.util.ResourceBundle;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.PCategoryApi;
import com.fh.yanx.service.course.entity.bo.PCategoryConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 资源类别 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@RestController
@RequestMapping("/category")
@Api(value = "类别管理")
@Slf4j
public class PCategoryController {
    @Resource
    private PCategoryApi pCategoryApi;

    /**
     * 需要前端传入组织Code和organizationId
     * @param pCategoryConditionBo
     * @return
     */
    @ApiOperation("类别展示")
    @PostMapping("/list")
    public AjaxResult list(@Validated @RequestBody PCategoryConditionBo pCategoryConditionBo) {
        return pCategoryApi.getPageTreeList(pCategoryConditionBo);
    }
}
