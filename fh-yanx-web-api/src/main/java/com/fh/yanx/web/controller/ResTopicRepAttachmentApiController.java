package com.fh.yanx.web.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.res.api.ResTopicRepAttachmentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicRepAttachmentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicRepAttachmentVo;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * 结题答辩附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
@RequestMapping("res/topic/rep/attachment")
@Api(value = "", tags = "结题答辩附件表接口")
public class ResTopicRepAttachmentApiController {

    @Autowired
    private ResTopicRepAttachmentApi resTopicRepAttachmentApi;

    /**
     * 查询结题答辩附件表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询结题答辩附件表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicRepAttachmentPageListByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition) {
        PageInfo<ResTopicRepAttachmentVo> page = resTopicRepAttachmentApi.getResTopicRepAttachmentPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询结题答辩附件表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询结题答辩附件表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicRepAttachmentListByCondition(@RequestBody ResTopicRepAttachmentConditionBo condition) {
        List<ResTopicRepAttachmentVo> list = resTopicRepAttachmentApi.getResTopicRepAttachmentListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增结题答辩附件表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增结题答辩附件表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicRepAttachment(@RequestBody ResTopicRepAttachmentBo resTopicRepAttachmentBo) {
        return resTopicRepAttachmentApi.addResTopicRepAttachment(resTopicRepAttachmentBo);
    }

    /**
     * 修改结题答辩附件表
     *
     * @param resTopicRepAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新结题答辩附件表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicRepAttachment(@RequestBody ResTopicRepAttachmentBo resTopicRepAttachmentBo) {
        if (null == resTopicRepAttachmentBo.getId()) {
            return AjaxResult.fail("结题答辩附件表id不能为空");
        }
        return resTopicRepAttachmentApi.updateResTopicRepAttachment(resTopicRepAttachmentBo);
    }

    /**
     * 批量修改附件表
     *
     * @param resTopicRepAttachmentBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/8 14:58
     */
    @PostMapping("/update-batch")
    @ApiOperation(value = "更新结题答辩附件表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateBatchResTopicRepAttachment(@RequestBody List<ResTopicRepAttachmentBo> resTopicRepAttachmentBos) {
        return resTopicRepAttachmentApi.updateBatchResTopicRepAttachment(resTopicRepAttachmentBos);
    }

    /**
     * 查询结题答辩附件表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询结题答辩附件表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "结题答辩附件表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicRepAttachmentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("结题答辩附件表id不能为空");
        }
        ResTopicRepAttachmentVo vo = resTopicRepAttachmentApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicRepAttachmentVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除结题答辩附件表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除结题答辩附件表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "结题答辩附件表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicRepAttachmentApi.delete(id);
    }

    /**
     * 删除结题答辩附件表-根据repFileOid
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete-oid")
    @ApiOperation(value = "删除结题答辩附件表-根据repFileOid", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult deleteByFileOid(@NotNull(message = "repFileOid不能为空") String repFileOid) {
        return resTopicRepAttachmentApi.deleteByFileOid(repFileOid);
    }
}
