package com.fh.yanx.web.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.service.course.api.CourseCasesEditionApi;
import com.fh.yanx.service.course.api.CourseStoreApi;
import com.fh.yanx.service.course.api.CourseVerifyLogApi;
import com.fh.yanx.service.course.api.PCourseCasesApi;
import com.fh.yanx.service.course.entity.bo.*;
import com.fh.yanx.service.enums.*;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.fh.yanx.web.consts.ConstString;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.user.entity.vo.UserRoleVo;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 校本课程案例库，需要登录
 * 
 * <AUTHOR>
 * @date 2023/11/9 15:10
 */
@RestController
@RequestMapping("/course-cases")
@Validated
public class CourseCasesController {
    @Resource
    private PCourseCasesApi pCourseCasesApi;
    @Resource
    private CourseVerifyLogApi courseVerifyLogApi;
    @Resource
    private CourseStoreApi courseStoreApi;
    @Resource
    private BaseDataApi baseDataApi;
    @Resource
    private CourseCasesEditionApi courseCasesEditionApi;

    /**
     * 查询校本课程列表-不分页，用于查询我的课程
     *
     * @param conditionBo the condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("查询校本课程列表-不分页")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody PCourseCasesConditionBo conditionBo) {
        return pCourseCasesApi.getPCourseCasesListByCondition(conditionBo);
    }

    /**
     * 查询校本课程列表-分页，用于查询我的课程
     *
     * @param conditionBo the condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("查询校本课程列表-分页")
    @PostMapping("/list-page")
    public AjaxResult listPage(@RequestBody PCourseCasesConditionBo conditionBo) {
        return pCourseCasesApi.getPCourseCasesPageListByCondition(conditionBo);
    }

    /**
     * 查询校本课程列表-分页，用于查询我的课程
     *
     * @param conditionBo the condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("查询我的收藏校本课程列表-分页")
    @PostMapping("/store-list-page")
    public AjaxResult storeListPage(@RequestBody PCourseCasesConditionBo conditionBo) {
        return pCourseCasesApi.getStoreCourseCasesPageListByCondition(conditionBo);
    }

    /**
     * 保存课程（新增和编辑）
     *
     * @param pCourseCasesBo the p course cases bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("保存课程（新增和编辑）")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody PCourseCasesBo pCourseCasesBo) {
        // 保存不允许直接改为审核通过或审核不通过-精品
        if (pCourseCasesBo.getBestVerifyType() != null
            && (pCourseCasesBo.getBestVerifyType().equals(CourseBestVerifyType.YES.getValue())
                || (pCourseCasesBo.getBestVerifyType().equals(CourseBestVerifyType.NO.getValue())))) {
            return AjaxResult.fail("参数错误");
        }
        // 保存不允许直接改为审核通过或审核不通过-普通
        if (pCourseCasesBo.getNormalVerifyType() != null
                && (pCourseCasesBo.getNormalVerifyType().equals(CourseNormalVerifyType.YES.getValue())
                || (pCourseCasesBo.getNormalVerifyType().equals(CourseNormalVerifyType.NO.getValue())))) {
            return AjaxResult.fail("参数错误");
        }
        // 教师申报校本课程，设置为典型案例
        if (pCourseCasesBo.getNormalVerifyType() != null) {
            pCourseCasesBo.setIsExcellent(IsExcellentType.DXAL.getValue());
            pCourseCasesBo.setIsExcellentLabel(IsExcellentType.KCAL.getValue() + "," + IsExcellentType.DXAL.getValue());
        }
        return pCourseCasesApi.saveContentModule(pCourseCasesBo);
    }

    /**
     * 课程详情
     *
     * @param pCourseCasesConditionBo the p course cases condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("课程详情")
    @PostMapping("/detail")
    public AjaxResult detail(@RequestBody PCourseCasesConditionBo pCourseCasesConditionBo) {
        return pCourseCasesApi.getContentModule(pCourseCasesConditionBo);
    }

    /**
     * 申报课程，写死智能改为申报中。暂时前端没用到，只用到了save接口。（20240218）
     *
     * @param pCourseCasesBoParam 只需要传递参数：id
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @Deprecated
    @ApiOperation("申报课程")
    @PostMapping("/apply")
    public AjaxResult apply(@RequestBody PCourseCasesBo pCourseCasesBoParam) {
        if (pCourseCasesBoParam.getId() == null) {
            return AjaxResult.fail("请选择课程");
        }

        PCourseCasesBo pCourseCasesBo = new PCourseCasesBo();
        pCourseCasesBo.setId(pCourseCasesBoParam.getId());
        pCourseCasesBo.setBestVerifyType(CourseBestVerifyType.DOING.getValue());
        pCourseCasesBo.setNormalVerifyType(CourseNormalVerifyType.DOING.getValue());
        return pCourseCasesApi.updatePCourseCases(pCourseCasesBo);
    }

    /**
     * 课程审批通过或者驳，支持精品和普通课程的审核
     *
     * @param courseCasesBoParam 参数：id，bestVerifyType或normalVerifyType，verifyProcessType，isExcellentLabel
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/verify")
    @ApiOperation(value = "课程审批通过或者驳回", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult verify(@RequestBody PCourseCasesBo courseCasesBoParam) {
        if (courseCasesBoParam.getId() == null) {
            return AjaxResult.fail("参数错误");
        }
        AjaxResult<LoginAccountVo> getCurrentUserResult = baseDataApi.getCurrentUser();
        if (getCurrentUserResult.isFail()) {
            return AjaxResult.fail("请先登录");
        }
        List<Long> expertRoleIds = Arrays.stream(ExpertRoleEnum.values()).map(ExpertRoleEnum::getRoleId)
                .collect(Collectors.toList());

        PCourseCasesBo pCourseCasesBo = new PCourseCasesBo();
        pCourseCasesBo.setId(courseCasesBoParam.getId());
        pCourseCasesBo.setBestVerifyType(courseCasesBoParam.getBestVerifyType());
        pCourseCasesBo.setNormalVerifyType(courseCasesBoParam.getNormalVerifyType());
        // 精品课程审核通过，设置为精品课程
        if (courseCasesBoParam.getBestVerifyType() != null
                && courseCasesBoParam.getBestVerifyType().equals(CourseBestVerifyType.YES.getValue())) {
            pCourseCasesBo.setIsExcellent(IsExcellentType.JPKC.getValue());
            if (StringUtils.isBlank(courseCasesBoParam.getIsExcellentLabel())) {
                pCourseCasesBo.setIsExcellentLabel(String.valueOf(IsExcellentType.JPKC.getValue()));
            } else {
                pCourseCasesBo.setIsExcellentLabel(courseCasesBoParam.getIsExcellentLabel().concat(ConstString.ywdh)
                        .concat(String.valueOf(IsExcellentType.JPKC.getValue())));
            }
        }
        // verifyProcessType不为空表示正在进行审核，插入审批意见流水
        if (courseCasesBoParam.getVerifyProcessType() != null) {
            pCourseCasesBo.setVerifyProcessType(courseCasesBoParam.getVerifyProcessType());
            CourseVerifyLogBo courseVerifyLogBo = courseCasesBoParam.getCourseVerifyLogBo();
            // 设置当前审核人信息
            courseVerifyLogBo.setUserOid(getCurrentUserResult.getData().getCurrentUser().getUserOid());
            courseVerifyLogBo.setRealName(getCurrentUserResult.getData().getCurrentUser().getRealName());
            List<UserRoleVo> userRoleVos = getCurrentUserResult.getData().getCurrentUser().getLoginUserRoles().stream()
                    .filter(x -> expertRoleIds.contains(x.getRoleId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userRoleVos)) {
                courseVerifyLogBo.setRoleId(userRoleVos.get(0).getRoleId());
            }
            // 运营更新审核专家的审核意见，直接调用update方法
            if (courseVerifyLogBo.getId() != null) {
                courseVerifyLogApi.updateCourseVerifyLog(courseVerifyLogBo);
            } else {
                courseVerifyLogApi.deleteAndSave(courseVerifyLogBo);
            }
        }
        return pCourseCasesApi.updatePCourseCases(pCourseCasesBo);
    }

    /**
     * 课程审批通过或者驳，支持精品和普通课程的审核
     *
     * @param courseCasesBoParams the course cases bo params
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/verify-batch")
    @ApiOperation(value = "课程审批通过或者驳回", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult verify(@RequestBody List<PCourseCasesBo> courseCasesBoParams) {
        if (CollectionUtils.isEmpty(courseCasesBoParams)) {
            return AjaxResult.fail("参数错误");
        }
        AjaxResult<LoginAccountVo> getCurrentUserResult = baseDataApi.getCurrentUser();
        if (getCurrentUserResult.isFail()) {
            return AjaxResult.fail("请先登录");
        }
        List<Long> expertRoleIds = Arrays.stream(ExpertRoleEnum.values()).map(ExpertRoleEnum::getRoleId)
                .collect(Collectors.toList());
        List<PCourseCasesBo> courseCasesBos = courseCasesBoParams.stream()
                .filter(courseCasesBoParam -> courseCasesBoParam != null && courseCasesBoParam.getId() != null)
                .map(courseCasesBoParam -> {
                    PCourseCasesBo pCourseCasesBo = new PCourseCasesBo();
                    pCourseCasesBo.setId(courseCasesBoParam.getId());
                    pCourseCasesBo.setBestVerifyType(courseCasesBoParam.getBestVerifyType());
                    pCourseCasesBo.setNormalVerifyType(courseCasesBoParam.getNormalVerifyType());
                    // 精品课程审核通过，设置为精品课程
                    if (courseCasesBoParam.getBestVerifyType() != null
                            && courseCasesBoParam.getBestVerifyType().equals(CourseBestVerifyType.YES.getValue())) {
                        pCourseCasesBo.setIsExcellent(IsExcellentType.JPKC.getValue());
                        if (StringUtils.isBlank(courseCasesBoParam.getIsExcellentLabel())) {
                            pCourseCasesBo.setIsExcellentLabel(String.valueOf(IsExcellentType.JPKC.getValue()));
                        } else {
                            pCourseCasesBo.setIsExcellentLabel(courseCasesBoParam.getIsExcellentLabel()
                                    .concat(ConstString.ywdh).concat(String.valueOf(IsExcellentType.JPKC.getValue())));
                        }
                    }
                    if (courseCasesBoParam.getVerifyProcessType() != null) {
                        pCourseCasesBo.setVerifyProcessType(courseCasesBoParam.getVerifyProcessType());
                    }
                    return pCourseCasesBo;
                }).collect(Collectors.toList());

        // 批量审批
        for (PCourseCasesBo courseCasesBoParam : courseCasesBoParams) {
            if (courseCasesBoParam.getVerifyProcessType() != null) {
                CourseVerifyLogBo courseVerifyLogBo = courseCasesBoParam.getCourseVerifyLogBo();
                // 设置当前审核人信息
                courseVerifyLogBo.setUserOid(getCurrentUserResult.getData().getCurrentUser().getUserOid());
                courseVerifyLogBo.setRealName(getCurrentUserResult.getData().getCurrentUser().getRealName());
                List<UserRoleVo> userRoleVos = getCurrentUserResult.getData().getCurrentUser().getLoginUserRoles().stream()
                        .filter(x -> expertRoleIds.contains(x.getRoleId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userRoleVos)) {
                    courseVerifyLogBo.setRoleId(userRoleVos.get(0).getRoleId());
                }
                courseVerifyLogApi.deleteAndSave(courseVerifyLogBo);
            }
        }
        for (PCourseCasesBo pCourseCasesBo : courseCasesBos) {
            pCourseCasesApi.updatePCourseCases(pCourseCasesBo);
        }
        return AjaxResult.success();
    }

    /**
     * 审核日志流水查询-不分页
     *
     * @param conditionBo the condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/verify-log/list")
    @ApiOperation(value = "审核日志流水查询-不分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listVerifyLog(@RequestBody CourseVerifyLogConditionBo conditionBo) {
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return courseVerifyLogApi.getCourseVerifyLogListByCondition(conditionBo);
    }

    @ApiOperation(value = "教师首页课程列表", httpMethod = "POST")
    @PostMapping("/teacher/apply-course-list-page")
    public AjaxResult teacherApplyCourseList(@RequestBody PCourseCasesConditionBo conditionBo) {
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return pCourseCasesApi.getTeacherApplyCourseList(conditionBo);
    }


    /**
     * 添加课程收藏
     * <p>
     * 传入参数: @param courseStoreBo 课程商店 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/05
     */
    @PostMapping("/store/add")
    @ApiOperation(value = "添加课程收藏", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addCourseStore(@RequestBody CourseStoreBo courseStoreBo) {
        courseStoreBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return courseStoreApi.addCourseStore(courseStoreBo);
    }

    /**
     * 删除课程收藏
     * <p>
     * 传入参数: @param courseStoreBo 课程商店 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/05
     */
    @PostMapping("/store/delete")
    @ApiOperation(value = "删除课程收藏", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult deleteCourseStore(@RequestBody CourseStoreBo courseStoreBo) {
        return courseStoreApi.deleteCourseStore(courseStoreBo);
    }

    /**
     * 课程详情
     *
     * @param condition the p course cases condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("课程版本详情")
    @PostMapping("/edition-detail")
    public AjaxResult courseEditionDetail(@RequestBody CourseCasesEditionConditionBo condition) {
        return courseCasesEditionApi.getContentModuleEdition(condition);
    }

    /**
     * 审核课程列表（审核专家、指导专家）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/9 9:39
     **/
    @ApiOperation(value = "审核课程列表", httpMethod = "POST")
    @PostMapping("/expert/verify-course-list-page")
    public AjaxResult expertVerifyCourseList(@RequestBody PCourseCasesConditionBo conditionBo) {
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return pCourseCasesApi.getExpertVerifyCourseList(conditionBo);
    }

    /**
     * 查询综评课程列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/9 11:07
     **/
    @ApiOperation(value = "综评课程列表", httpMethod = "POST")
    @PostMapping("/comprehensive/course-list-page")
    public AjaxResult comprehensiveCourseList(@RequestBody PCourseCasesConditionBo conditionBo) {
        return pCourseCasesApi.getComprehensiveCourseList(conditionBo);
    }

    /**
     * 专家助理课程列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/16 17:05
     **/
    @ApiOperation(value = "专家助理课程列表", httpMethod = "POST")
    @PostMapping("/expert-assistant/course-list-page")
    public AjaxResult expertAssistantCourseList(@RequestBody PCourseCasesConditionBo condition) {
        return pCourseCasesApi.getExpertAssistantCourseList(condition);
    }

}
