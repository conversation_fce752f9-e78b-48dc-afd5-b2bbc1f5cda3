package com.fh.yanx.web.controller;

import com.fh.yanx.service.course.api.CourseDiscussSectionApi;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionBo;
import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 课程讨论区表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("courseDiscussSection")
@Api(value = "", tags = "课程讨论区表接口" )
public class CourseDiscussSectionController {

    @Autowired
    private CourseDiscussSectionApi courseDiscussSectionApi;

    @Resource
    private CurrentUserService currentUserService;


    /**
     * 查询课程讨论区表列表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @PostMapping("/getByCases")
    @ApiOperation(value = "查询某个课程案例评论")
    public AjaxResult<List<CourseDiscussSectionVo>> getByCases(@RequestBody CourseDiscussSectionConditionBo condition){
        Long casesId = condition.getCasesId();
        if(casesId == null){
            return AjaxResult.fail("校本课程案例ID不能为空");
        }
        Long parentId = condition.getParentId();
        if(parentId == null){
            condition.setParentId(0L);
        }
        List<CourseDiscussSectionVo> list = courseDiscussSectionApi.getCourseDiscussSectionListByCondition(condition).getData();
        // 获取用户信息
        return AjaxResult.success(list);
    }


    /**
     * 新增课程讨论区表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课程讨论区表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addCourseDiscussSection(@RequestBody CourseDiscussSectionBo courseDiscussSectionBo){
        courseDiscussSectionBo.setUserOid(this.currentUserService.getCurrentOid());
        return courseDiscussSectionApi.addCourseDiscussSection(courseDiscussSectionBo);
    }

    /**
     * 删除课程讨论区表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课程讨论区表",httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult delete(@RequestParam("id") Long id) {
        return courseDiscussSectionApi.deleteByIdAndUserOid(id, this.currentUserService.getCurrentOid());
    }

}
