package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResOpenCourseApi;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseBo;
import com.fh.yanx.service.res.entity.bo.ResOpenCourseConditionBo;
import com.fh.yanx.service.res.entity.vo.ResOpenCourseVo;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公开课表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:17
 */
@RestController
@Validated
@RequestMapping("res/open/course")
@Api(value = "", tags = "公开课表接口")
public class ResOpenCourseApiController {

    @Autowired
    private ResOpenCourseApi resOpenCourseApi;

    /**
     * 查询公开课表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询公开课表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResOpenCoursePageListByCondition(@RequestBody ResOpenCourseConditionBo condition) {
        PageInfo<ResOpenCourseVo> page = resOpenCourseApi.getResOpenCoursePageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询公开课表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询公开课表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResOpenCourseListByCondition(@RequestBody ResOpenCourseConditionBo condition) {
        List<ResOpenCourseVo> list = resOpenCourseApi.getResOpenCourseListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增公开课表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增公开课表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResOpenCourse(@RequestBody ResOpenCourseBo resOpenCourseBo) {
        return resOpenCourseApi.addResOpenCourse(resOpenCourseBo);
    }

    /**
     * 修改公开课表
     *
     * @param resOpenCourseBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新公开课表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResOpenCourse(@RequestBody ResOpenCourseBo resOpenCourseBo) {
        if (null == resOpenCourseBo.getOpenCourseId()) {
            return AjaxResult.fail("公开课表id不能为空");
        }
        return resOpenCourseApi.updateResOpenCourse(resOpenCourseBo);
    }

    /**
     * 查询公开课表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询公开课表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "公开课表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResOpenCourseVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("公开课表id不能为空");
        }
        ResOpenCourseVo vo = resOpenCourseApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resOpenCourseVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除公开课表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:17
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除公开课表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "公开课表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resOpenCourseApi.delete(id);
    }
}
