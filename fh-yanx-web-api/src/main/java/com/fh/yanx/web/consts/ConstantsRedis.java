package com.fh.yanx.web.consts;

/**
 * redis的key的常量
 */
public interface ConstantsRedis {

    /**
     * 短信验证码缓存:yanx_bm_sms_code_{手机号}
     */
    String SMS_PREFIX = "yanx_bm_sms_code_";
    /**
     * 短信验证码缓存缓存5分钟
     */
    long SMS_EXPIRE_IN = 5L * 60;

    /**
     * 短信验证码发送次数缓存:yanx_bm_sms_code_{手机号}
     */
    String SMS_COUNT_PREFIX = "yanx_bm_sms_count_";
    /**
     * 短信验证码发送次数缓存2h
     */
    long SMS_COUNT_EXPIRE_IN = 2L * 60 * 60;
    /**
     * 2h内短信发送的上限（2h内有发送过验证码的会累加记录，2h内没有发送过验证码的手机号会重新记录）
     */
    int SMS_COUNT_LIMIT = 20;

    /**
     * 短信登录标识缓存:yanx_bm_sms_login_{校验码uuid}，里面存储的值为手机号
     */
    String SMS_LOGIN_PREFIX = "yanx_bm_sms_login_";
    /**
     * 短信登录标识缓存缓存5分钟
     */
    long SMS_LOGIN_EXPIRE_IN = 5L * 60;
}
