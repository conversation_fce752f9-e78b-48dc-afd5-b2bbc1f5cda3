package com.fh.yanx.web.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.yanx.service.course.entity.bo.PLessonCasesConditionBo;
import com.fh.yanx.service.course.entity.vo.PLessonCasesVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.PLessonCasesApi;
import com.google.common.collect.Lists;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 课例表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-23
 */
@RestController
@RequestMapping("/lc")
@Api("课例表")
public class PLessonCasesController {

    @Resource
    private PLessonCasesApi pLessonCasesApi;

    @ApiOperation("查询课例列表数据")
    @PostMapping("/list")
    public AjaxResult getList(@RequestBody PLessonCasesConditionBo condition) {
        return pLessonCasesApi.getPLessonCasesPageListByCondition(condition);
    }

    @ApiOperation("查询课例详情数据")
    @GetMapping("/detail")
    public AjaxResult getDetail(@RequestParam("id") Long id) {
        return pLessonCasesApi.getDetail(id);
    }

}
