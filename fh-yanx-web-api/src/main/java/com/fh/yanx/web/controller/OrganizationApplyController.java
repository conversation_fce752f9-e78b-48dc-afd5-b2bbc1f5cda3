package com.fh.yanx.web.controller;

import com.fh.yanx.service.org.api.OrganizationApplyApi;
import com.fh.yanx.service.org.entity.bo.OrganizationApplyBo;
import com.fh.yanx.web.consts.ConstantsRedis;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 组织申请（无需登录）
 *
 * <AUTHOR>
 * @date 2023-07-14 13:43
 */
@RestController
@RequestMapping("/org-apply")
@Slf4j
@Api(value = "组织申请", tags = "组织申请")
public class OrganizationApplyController {
    @Resource
    OrganizationApplyApi organizationApplyApi;
    @Resource
    RedisComponent redisComponent;

    /**
     * 提交申请
     *
     * @param organizationApplyBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/14 14:04
     **/
    @PostMapping("/submit")
    @ApiOperation(value = "提交申请", notes = "提交申请")
    public AjaxResult submit(@RequestBody OrganizationApplyBo organizationApplyBo) {
        if (StringUtils.isBlank(organizationApplyBo.getCode())) {
            return AjaxResult.fail("短信验证码不允许为空");
        }
        if (!organizationApplyBo.getCode()
                .equals(redisComponent.get(ConstantsRedis.SMS_PREFIX + organizationApplyBo.getContact()))) {
            return AjaxResult.fail("短信验证码不匹配");
        }
        return organizationApplyApi.addOrganizationApply(organizationApplyBo);
    }

}
