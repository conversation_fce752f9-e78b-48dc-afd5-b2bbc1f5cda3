package com.fh.yanx.web.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fh.yanx.service.res.api.ResTopicApi;
import com.fh.yanx.service.res.api.ResTopicCollectApi;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectBo;
import com.fh.yanx.service.res.entity.bo.ResTopicCollectConditionBo;
import com.fh.yanx.service.res.entity.bo.ResTopicConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicCollectVo;
import com.fh.yanx.service.res.entity.vo.ResTopicVo;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户收藏的课题
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/topic/collect")
@Api(value = "", tags = "用户收藏的课题接口")
public class ResTopicCollectApiController {

    @Resource
    private ResTopicCollectApi resTopicCollectApi;


    /**
     * 查询用户收藏的课题分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询用户收藏的课题列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicCollectPageListByCondition(@RequestBody ResTopicCollectConditionBo condition) {
        return resTopicCollectApi.getResTopicCollectPageListByCondition(condition);
    }

    /**
     * 查询用户收藏的课题列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询用户收藏的课题列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicCollectListByCondition(@RequestBody ResTopicCollectConditionBo condition) {
        List<ResTopicCollectVo> list = resTopicCollectApi.getResTopicCollectListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增用户收藏的课题
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增用户收藏的课题", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicCollect(@RequestBody ResTopicCollectBo resTopicCollectBo) {
        if (StringUtils.isBlank(resTopicCollectBo.getUserOid())){
            return AjaxResult.fail("参数错误");
        }
        return resTopicCollectApi.addResTopicCollect(resTopicCollectBo);
    }

    /**
     * 修改用户收藏的课题
     *
     * @param resTopicCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新用户收藏的课题", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicCollect(@RequestBody ResTopicCollectBo resTopicCollectBo) {
        if (null == resTopicCollectBo.getCollectId()) {
            return AjaxResult.fail("用户收藏的课题id不能为空");
        }
        return resTopicCollectApi.updateResTopicCollect(resTopicCollectBo);
    }

    /**
     * 查询用户收藏的课题详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询用户收藏的课题详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "用户收藏的课题id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicCollectVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("用户收藏的课题id不能为空");
        }
        ResTopicCollectVo vo = resTopicCollectApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicCollectVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除用户收藏的课题
     *
     * @param topicId,userOid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除用户收藏的课题", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult deleteByTopicAndUser(@RequestParam("topicId") Long topicId,@RequestParam("userOid") String userOid) {
        return resTopicCollectApi.deleteByTopicAndUser(topicId,userOid);
    }
}
