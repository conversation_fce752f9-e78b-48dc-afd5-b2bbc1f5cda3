package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResInformationApi;
import com.fh.yanx.service.res.api.ResTopicTodoApi;
import com.fh.yanx.service.res.entity.bo.ResInformationBo;
import com.fh.yanx.service.res.entity.bo.ResInformationConditionBo;
import com.fh.yanx.service.res.entity.vo.ResInformationVo;
import com.fh.yanx.service.res.service.ResInformationApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资讯
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:16
 */
@RestController
@Validated
@RequestMapping("res/information")
@Api(value = "", tags = "资讯接口")
public class ResInformationApiController {

    @Autowired
    private ResInformationApi resInformationApi;

    /**
     * 查询资讯分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询资讯列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResInformationPageListByCondition(@RequestBody ResInformationConditionBo condition) {
        PageInfo<ResInformationVo> page = resInformationApi.getResInformationPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询资讯列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询资讯列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResInformationListByCondition(@RequestBody ResInformationConditionBo condition) {
        List<ResInformationVo> list = resInformationApi.getResInformationListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增资讯
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增资讯", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResInformation(@RequestBody ResInformationBo resInformationBo) {
        return resInformationApi.addResInformation(resInformationBo);
    }

    /**
     * 修改资讯
     *
     * @param resInformationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新资讯", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResInformation(@RequestBody ResInformationBo resInformationBo) {
        if (null == resInformationBo.getInformationId()) {
            return AjaxResult.fail("资讯id不能为空");
        }
        return resInformationApi.updateResInformation(resInformationBo);
    }

    /**
     * 查询资讯详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询资讯详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "资讯id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResInformationVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("资讯id不能为空");
        }
        ResInformationVo vo = resInformationApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resInformationVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除资讯
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:16
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除资讯", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "资讯id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resInformationApi.delete(id);
    }
}
