package com.fh.yanx.web.entity;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * wxPay的bean
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "wxpay")
public class WxPayBean {
    @Value("${wxpay.appId:}")
    private String appId;
    @Value("${wxpay.appSecret:}")
    private String appSecret;
    @Value("${wxpay.mchId:}")
    private String mchId;
    @Value("${wxpay.partnerKey:}")
    private String partnerKey;
    @Value("${wxpay.certPath:}")
    private String certPath;
    @Value("${wxpay.domain:}")
    private String domain;
    @Value("${wxpay.access.token.url:}")
    private String accessTokenUrl;
    @Value("${wxpay.notify.url:}")
    private String notifyUrl;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getPartnerKey() {
        return partnerKey;
    }

    public void setPartnerKey(String partnerKey) {
        this.partnerKey = partnerKey;
    }

    public String getCertPath() {
        return certPath;
    }

    public void setCertPath(String certPath) {
        this.certPath = certPath;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getAccessTokenUrl() {
        return accessTokenUrl;
    }

    public void setAccessTokenUrl(String accessTokenUrl) {
        this.accessTokenUrl = accessTokenUrl;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    @Override
    public String toString() {
        return "WxPayBean [appId=" + appId + ", appSecret=" + appSecret + ", mchId=" + mchId + ", partnerKey="
            + partnerKey + ", certPath=" + certPath + ", domain=" + domain + "]";
    }
}