package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResStationContentApi;
import com.fh.yanx.service.res.entity.bo.ResStationContentBo;
import com.fh.yanx.service.res.entity.bo.ResStationContentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResStationContentVo;
import com.fh.yanx.service.res.service.ResStationContentApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 工作站具体内容
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/station/content")
@Api(value = "", tags = "工作站具体内容接口" )
public class ResStationContentApiController {

    @Autowired
    private ResStationContentApi resStationContentApi;

    /**
     * 查询工作站具体内容分页列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询工作站具体内容列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResStationContentPageListByCondition(@RequestBody ResStationContentConditionBo condition){
        PageInfo<ResStationContentVo> page = resStationContentApi.getResStationContentPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询工作站具体内容列表
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询工作站具体内容列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResStationContentListByCondition(@RequestBody ResStationContentConditionBo condition){
        List<ResStationContentVo> list = resStationContentApi.getResStationContentListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增工作站具体内容
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增工作站具体内容",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResStationContent(@RequestBody ResStationContentBo resStationContentBo){
        return resStationContentApi.addResStationContent(resStationContentBo);
    }

    /**
     * 修改工作站具体内容
     * @param resStationContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新工作站具体内容",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResStationContent(@RequestBody ResStationContentBo resStationContentBo) {
        if(null == resStationContentBo.getStationContentId()) {
            return AjaxResult.fail("工作站具体内容id不能为空");
        }
        return resStationContentApi.updateResStationContent(resStationContentBo);
    }

    /**
     * 查询工作站具体内容详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询工作站具体内容详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "工作站具体内容id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResStationContentVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("工作站具体内容id不能为空");
        }
        ResStationContentVo vo = resStationContentApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resStationContentVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除工作站具体内容
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除工作站具体内容",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "工作站具体内容id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resStationContentApi.delete(id);
    }
}
