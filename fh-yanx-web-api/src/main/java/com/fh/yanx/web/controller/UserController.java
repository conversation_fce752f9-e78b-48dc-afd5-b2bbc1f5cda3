package com.fh.yanx.web.controller;

import com.fh.yanx.service.activ.api.ActivInfoApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.bm.api.BmInfoApi;
import com.fh.yanx.service.consts.ConstOrgAuth;
import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.enums.ExpertRoleEnum;
import com.fh.yanx.service.enums.OrgType;
import com.fh.yanx.service.enums.OrganizationAuthType;
import com.fh.yanx.service.enums.ReceptionRoleEnum;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.fh.yanx.service.org.entity.bo.OrganizationUserBo;
import com.fh.yanx.service.org.entity.bo.OrganizationVoExt;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.web.consts.ConstantsRedis;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import com.light.user.account.entity.bo.AccountBo;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.organization.entity.bo.OrganizationSettingConditionBo;
import com.light.user.organization.entity.vo.OrganizationSettingVo;
import com.light.user.role.entity.vo.RoleVo;
import com.light.user.user.entity.bo.UserBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人信息
 *
 * <AUTHOR>
 * @date 2023-07-19 9:26
 */
@RestController
@RequestMapping("/yanx/user")
@Slf4j
@Api(value = "个人信息", tags = "个人信息")
public class UserController {
    @Resource
    BaseDataApi baseDataApi;
    @Resource
    RedisComponent redisComponent;
    @Resource
    ActivInfoApi activInfoApi;
    @Resource
    BmInfoApi bmInfoApi;

    /**
     * 修改密码
     *
     * @param oldPassword
     * @param password
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/7 16:50
     **/
    @GetMapping("/change-password")
    @ApiOperation(value = "修改密码", notes = "修改密码")
    public AjaxResult changePassword(@RequestParam("oldPassword") String oldPassword,
                                     @RequestParam("password") String password) {
        return baseDataApi.changePassword(oldPassword, password);
    }

    /**
     * 修改手机号码
     *
     * @param organizationUserBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 10:05
     **/
    @PostMapping("/change-phone")
    @ApiOperation(value = "修改手机号码", notes = "修改手机号码")
    public AjaxResult changePhone(@RequestBody OrganizationUserBo organizationUserBo) {
        if (StringUtils.isBlank(organizationUserBo.getCode())) {
            return AjaxResult.fail("短信验证码不允许为空");
        }
        if (!organizationUserBo.getCode()
                .equals(redisComponent.get(ConstantsRedis.SMS_PREFIX + organizationUserBo.getPhone()))) {
            return AjaxResult.fail("短信验证码不匹配");
        }
        UserBo userBo = new UserBo();
        userBo.setPhone(organizationUserBo.getPhone());
        return baseDataApi.changePhone(userBo);
    }

    /**
     * 获取组织认证信息
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/24 9:08
     **/
    @GetMapping("/org-auth-type")
    @ApiOperation(value = "获取组织认证信息", notes = "获取组织认证信息")
    public AjaxResult orgAuthType() {
        LoginAccountVo loginAccountVo = baseDataApi.getCurrentUser().getData();
        Long organizationId = loginAccountVo.getCurrentUser().getUserOrg().getOrganizationId();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date nowDay = new Date();
        OrganizationSettingConditionBo conditionBo = new OrganizationSettingConditionBo();
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        conditionBo.setOrganizationId(organizationId);
        conditionBo.setK(ConstOrgAuth.ORG_AUTH);
        AjaxResult<OrganizationSettingVo> ajaxResult = baseDataApi.getOrganizationSetting(conditionBo);
        OrganizationVoExt organizationVoExt = new OrganizationVoExt();
        organizationVoExt.setId(organizationId);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            organizationVoExt.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
        }
        try {
            OrganizationSettingVo settingVo = ajaxResult.getData();
            if (OrganizationAuthType.AUTH.getCode().equals(Integer.valueOf(settingVo.getRemark()))
                    && sdf.parse(sdf.format(nowDay)).compareTo(sdf.parse(settingVo.getVal())) <= 0) {
                organizationVoExt.setAuthType(OrganizationAuthType.AUTH.getCode());
            } else {
                organizationVoExt.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
            }
            organizationVoExt.setAuthEndDay(sdf.parse(settingVo.getVal()));
        } catch (Exception e) {
            log.error("orgAuthType error : ", e);
            organizationVoExt.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
        }
        return AjaxResult.success(organizationVoExt);
    }

    /**
     * 绑定手机号
     *
     * @param organizationUserBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/25 17:00
     **/
    @PostMapping("/bind-phone")
    @ApiOperation(value = "绑定手机号", notes = "绑定手机号")
    public AjaxResult bindPhone(@RequestBody OrganizationUserBo organizationUserBo) {
        if (StringUtils.isBlank(organizationUserBo.getPhone())) {
            return AjaxResult.fail("手机号不能为空");
        }
        if (StringUtils.isBlank(organizationUserBo.getCode())) {
            return AjaxResult.fail("验证码不能为空");
        }
        if (organizationUserBo.getAccountId() == null) {
            return AjaxResult.fail("账号id不能为空");
        }
        if (!organizationUserBo.getCode()
                .equals(redisComponent.get(ConstantsRedis.SMS_PREFIX + organizationUserBo.getPhone()))) {
            return AjaxResult.fail("短信验证码不匹配");
        }
        AccountBo accountBo = new AccountBo();
        accountBo.setId(organizationUserBo.getAccountId());
        accountBo.setPhone(organizationUserBo.getPhone());
        return baseDataApi.updateAccount(accountBo);
    }

    /**
     * 校验组织是否有新时代登录权限（校验学校是否市新时代的学校）
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/3 14:27
     **/
    @GetMapping("/check-org")
    @ApiOperation(value = "校验组织是否有新时代登录权限", notes = "校验组织是否有新时代登录权限")
    public AjaxResult checkOrganization() {
        LoginAccountVo loginAccountVo = baseDataApi.getCurrentUser().getData();
        Long organizationId = loginAccountVo.getCurrentUser().getUserOrg().getOrganizationId();
        String phone = loginAccountVo.getPhone();
        do {
            if (StringUtils.isNotBlank(phone)) {
                AjaxResult<Boolean> checkPhoneResult = bmInfoApi.checkPhoneForLogin(phone);
                if (checkPhoneResult.isFail()) {
                    break;
                }
                if (checkPhoneResult.getData()) {
                    return AjaxResult.success();
                }
            }
        } while (false);

        AjaxResult<OrganizationVoExt> organizationVoAjaxResult = baseDataApi.getOrganizationDetail(organizationId);
        if (organizationVoAjaxResult.isFail()) {
            return AjaxResult.fail("基础服务调用失败");
        }
        OrganizationVoExt organizationVoExt = organizationVoAjaxResult.getData();
        if (OrgType.ORG.getValue() == organizationVoExt.getType()) {
            return AjaxResult.success();
        }

        OrganizationSettingConditionBo conditionBo = new OrganizationSettingConditionBo();
        conditionBo.setOrganizationId(organizationId);
        conditionBo.setK(ConstServiceName.PLATFORM_KEY);
        conditionBo.setEqVal(ConstServiceName.PLATFORM_NAME);
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult<OrganizationSettingVo> ajaxResult = baseDataApi.getOrganizationSetting(conditionBo);
        if (ajaxResult.isFail()) {
            return AjaxResult.fail("基础服务调用失败");
        }
        if (ajaxResult.getData() == null) {
            return AjaxResult.fail("当前账号暂无权限");
        }
        return AjaxResult.success();
    }

    /**
     * 获取用户参与活动列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/8 15:28
     **/
    @RequestMapping("/activ-list")
    @ApiOperation(value = "获取用户参与活动列表", notes = "获取用户参与活动列表")
    public AjaxResult getUserActivList(@RequestBody ActivInfoConditionBo conditionBo) {
        LoginAccountVo loginAccountVo = baseDataApi.getCurrentUser().getData();
        String phone = loginAccountVo.getPhone();
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.success(new ArrayList<>());
        }
        conditionBo.setPhone(phone);
        return activInfoApi.getUserActivList(conditionBo);
    }

    /**
     * 获取用户订单列表
     *
     * @param activOrderBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/25 14:45
     **/
    @ApiOperation(value = "获取用户订单列表", notes = "获取用户订单列表")
    @PostMapping("/order-list")
    public AjaxResult getUserOrderList(@RequestBody ActivOrderBo activOrderBo) {
        LoginAccountVo loginAccountVo = baseDataApi.getCurrentUser().getData();
        String phone = loginAccountVo.getPhone();
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.success(new ArrayList<>());
        }
        return AjaxResult.success();
    }

    /**
     * 获取用户详情
     *
     * @param userOid
     * @return
     */
    @GetMapping("/user-detail")
    @ApiOperation(value = "用户详情", httpMethod = "GET")
    public AjaxResult getUserDetail() {
        AjaxResult<LoginAccountVo> ajaxResult = baseDataApi.getCurrentUser();
        if (ajaxResult.isSuccess() && ajaxResult.getData() != null) {
            String userOid = ajaxResult.getData().getCurrentUser().getOid();
            return baseDataApi.getUserDetail(userOid);
        }
        return AjaxResult.fail();
    }

    /**
     * 更新用户
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 14:42
     **/
    @PostMapping("/update-user")
    @ApiOperation(value = "更新用户", httpMethod = "POST")
    public AjaxResult updateUser(@RequestBody UserBo userBo) {
        AjaxResult<LoginAccountVo> ajaxResult = baseDataApi.getCurrentUser();
        if (ajaxResult.isSuccess() && ajaxResult.getData() != null) {
            String userOid = ajaxResult.getData().getCurrentUser().getOid();
            Long userId = ajaxResult.getData().getCurrentUser().getUserId();
            userBo.setOid(userOid);
            userBo.setUserId(userId);
            return baseDataApi.updateUser(userBo);
        }
        return AjaxResult.fail();
    }

    /**
     * 获取专家助理列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/12 11:57
     **/
    @PostMapping("/expert-assistant-list")
    @ApiOperation(value = "获取专家助理列表", httpMethod = "POST")
    public AjaxResult expertAssistantList(@RequestBody UserConditionBoExt conditionBo) {
        return baseDataApi.expertAssistantList(conditionBo);
    }

    /**
     * 专家和案例持有者角色列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/16 10:39
     **/
    @GetMapping("/expert-and-owner-roles")
    @ApiOperation(value = "获取专家和案例持有人角色列表", httpMethod = "GET")
    public AjaxResult getExpertAndCaseOwnerRoles() {
        return baseDataApi.expertAndCaseOwnerRoles();
    }
}
