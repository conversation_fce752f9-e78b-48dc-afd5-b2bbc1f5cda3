package com.fh.yanx.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Description:线程池
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@Slf4j
@Configuration
@EnableAsync // 开启异步调用
public class ThreadExecutorConfig {

    /** 核心线程数 */
    private final int corePoolSize = 8;
    /** 最大线程数 */
    private final int maxPoolSize = 16;
    /** 队列数 */
    private final AtomicInteger nextId = new AtomicInteger(1);

    /**
     * @Configuration = <beans></beans>
     * @Bean = <bean></bean> 返回值类型为executor中的属性"class"对应的value 方法名为asyncServiceExecutor中的属性"id"对应的value
     * @return
     */
    @Bean
    public ThreadPoolExecutor asyncServiceExecutor() {
        log.info("启动执行程序 asyncServiceExecutor");
        return new ThreadPoolExecutor(corePoolSize, maxPoolSize, 1000, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1024),
            r -> new Thread(null, r, "ThreadFactoryMain-Cover-worker-" + nextId.incrementAndGet()));
    }

}
