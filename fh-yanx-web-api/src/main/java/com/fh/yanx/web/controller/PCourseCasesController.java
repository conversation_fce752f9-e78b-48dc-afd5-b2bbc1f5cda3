package com.fh.yanx.web.controller;

import javax.annotation.Resource;

import com.fh.yanx.service.course.api.CourseRecommendApi;
import com.fh.yanx.service.course.entity.bo.CourseRecommendAddBo;
import com.fh.yanx.service.course.entity.bo.CourseRecommendBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.yanx.service.course.api.PCourseCasesApi;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 校本课程案例，无需登录，从新时代迁移过来的接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/courseCases")
@Validated
public class PCourseCasesController {

    @Resource
    private PCourseCasesApi pCourseCasesApi;

    @Resource
    private CourseRecommendApi courseRecommendApi;

    /**
     * 查询门户校本案例库
     *
     * @param conditionBo the condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:49
     */
    @ApiOperation("查询门户校本案例库")
    @PostMapping("/homeList")
    public AjaxResult homeList(@RequestBody PCourseCasesConditionBo conditionBo) {
        return pCourseCasesApi.getHomeList(conditionBo);
    }

    /**
     * 查询校本课程案例详情
     *
     * @param casesId the cases id
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:47
     */
    @ApiOperation("查询门户校本案例详情")
    @GetMapping("/homeDetail")
    public AjaxResult homeDetail(@RequestParam("casesId") Long casesId, @RequestParam(value = "queryEdition", required = false) Boolean queryEdition) {
        return pCourseCasesApi.homeDetail(casesId, queryEdition);
    }

    /**
     * 查询首页和课程首页推荐的课程
     *
     * @param conditionBo 支持参数：is_excellent
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:47
     */
    @ApiOperation("查询首页和课程首页推荐的课程")
    @PostMapping("/top-list")
    public AjaxResult topList(@RequestBody PCourseCasesConditionBo conditionBo) {
        return pCourseCasesApi.topList(conditionBo);
    }

    /**
     * 课程推荐
     *
     * @param conditionBo 支持参数：is_excellent
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -08-14 11:44:47
     */
    @ApiOperation("课程推荐")
    @PostMapping("/recommended")
    public AjaxResult recommended(@RequestBody CourseRecommendAddBo conditionBo) {
        return courseRecommendApi.addCourseRecommend(conditionBo);
    }
}
