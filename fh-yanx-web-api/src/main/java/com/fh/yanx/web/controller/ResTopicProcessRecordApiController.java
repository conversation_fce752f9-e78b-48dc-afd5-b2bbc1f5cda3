package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicProcessRecordApi;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordBo;
import com.fh.yanx.service.res.entity.bo.ResTopicProcessRecordConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicProcessRecordVo;
import com.fh.yanx.service.res.service.ResTopicProcessRecordApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题流程记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/topic/process/record")
@Api(value = "", tags = "课题流程记录接口")
public class ResTopicProcessRecordApiController {

    @Autowired
    private ResTopicProcessRecordApi resTopicProcessRecordApi;

    /**
     * 查询课题流程记录分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题流程记录列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicProcessRecordPageListByCondition(@RequestBody ResTopicProcessRecordConditionBo condition) {
        PageInfo<ResTopicProcessRecordVo> page = resTopicProcessRecordApi.getResTopicProcessRecordPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题流程记录列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题流程记录列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicProcessRecordListByCondition(@RequestBody ResTopicProcessRecordConditionBo condition) {
        List<ResTopicProcessRecordVo> list = resTopicProcessRecordApi.getResTopicProcessRecordListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增课题流程记录
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题流程记录", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicProcessRecord(@RequestBody ResTopicProcessRecordBo resTopicProcessRecordBo) {
        return resTopicProcessRecordApi.addResTopicProcessRecord(resTopicProcessRecordBo);
    }

    /**
     * 修改课题流程记录
     *
     * @param resTopicProcessRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新课题流程记录", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicProcessRecord(@RequestBody ResTopicProcessRecordBo resTopicProcessRecordBo) {
        if (null == resTopicProcessRecordBo.getProcessRecordId()) {
            return AjaxResult.fail("课题流程记录id不能为空");
        }
        return resTopicProcessRecordApi.updateResTopicProcessRecord(resTopicProcessRecordBo);
    }

    /**
     * 查询课题流程记录详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题流程记录详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题流程记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicProcessRecordVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题流程记录id不能为空");
        }
        ResTopicProcessRecordVo vo = resTopicProcessRecordApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicProcessRecordVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除课题流程记录
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题流程记录", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题流程记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicProcessRecordApi.delete(id);
    }
}
