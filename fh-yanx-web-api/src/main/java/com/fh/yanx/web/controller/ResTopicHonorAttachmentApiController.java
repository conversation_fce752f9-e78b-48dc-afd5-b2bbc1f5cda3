package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicHonorAttachmentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicHonorAttachmentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicHonorAttachmentVo;
import com.fh.yanx.service.res.service.ResTopicHonorAttachmentApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题荣誉表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:23
 */
@RestController
@Validated
@RequestMapping("res/topic/honor/attachment")
@Api(value = "", tags = "课题荣誉表接口")
public class ResTopicHonorAttachmentApiController {

    @Autowired
    private ResTopicHonorAttachmentApi resTopicHonorAttachmentApi;

    /**
     * 查询课题荣誉表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题荣誉表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult
        getResTopicHonorAttachmentPageListByCondition(@RequestBody ResTopicHonorAttachmentConditionBo condition) {
        PageInfo<ResTopicHonorAttachmentVo> page =
            resTopicHonorAttachmentApi.getResTopicHonorAttachmentPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题荣誉表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题荣誉表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult
        getResTopicHonorAttachmentListByCondition(@RequestBody ResTopicHonorAttachmentConditionBo condition) {
        List<ResTopicHonorAttachmentVo> list =
            resTopicHonorAttachmentApi.getResTopicHonorAttachmentListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }

    /**
     * 新增课题荣誉表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题荣誉表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicHonorAttachment(@RequestBody ResTopicHonorAttachmentBo resTopicHonorAttachmentBo) {
        return resTopicHonorAttachmentApi.addResTopicHonorAttachment(resTopicHonorAttachmentBo);
    }

    /**
     * 修改课题荣誉表
     *
     * @param resTopicHonorAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新课题荣誉表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicHonorAttachment(@RequestBody ResTopicHonorAttachmentBo resTopicHonorAttachmentBo) {
        if (null == resTopicHonorAttachmentBo.getId()) {
            return AjaxResult.fail("课题荣誉表id不能为空");
        }
        return resTopicHonorAttachmentApi.updateResTopicHonorAttachment(resTopicHonorAttachmentBo);
    }

    /**
     * 查询课题荣誉表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题荣誉表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题荣誉表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicHonorAttachmentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题荣誉表id不能为空");
        }
        ResTopicHonorAttachmentVo vo = resTopicHonorAttachmentApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicHonorAttachmentVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除课题荣誉表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:23
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题荣誉表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题荣誉表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicHonorAttachmentApi.delete(id);
    }

    /**
     * 删除课题荣誉表并新增
     *
     * @param resTopicHonorAttachmentBos the res topic honor attachment bos
     * @return ajax result
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022 -09-02 15:00:23
     */
    @PostMapping("/delete-save")
    @ApiOperation(value = "删除课题荣誉表并新增", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult deleteSave(@RequestBody List<ResTopicHonorAttachmentBo> resTopicHonorAttachmentBos) {
        return resTopicHonorAttachmentApi.deleteAndSaveTopicHonorAttachmentBatch(resTopicHonorAttachmentBos);
    }

}
