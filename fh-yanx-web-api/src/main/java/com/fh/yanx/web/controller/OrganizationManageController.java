package com.fh.yanx.web.controller;

import cn.hutool.core.date.DateUtil;
import com.fh.yanx.service.consts.ConstOrgAuth;
import com.fh.yanx.service.consts.ConstServiceName;
import com.fh.yanx.service.enums.OrganizationAuthType;
import com.fh.yanx.service.org.api.OrganizationApplyApi;
import com.fh.yanx.service.org.api.OrganizationApplyCommunicateApi;
import com.fh.yanx.service.org.api.OrganizationAuthLogApi;
import com.fh.yanx.service.org.entity.bo.*;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.organization.entity.bo.OrganizationBo;
import com.light.user.organization.entity.bo.OrganizationConditionBo;
import com.light.user.organization.entity.bo.OrganizationSettingBo;
import com.light.user.organization.entity.vo.OrganizationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;

/**
 * 组织管理
 *
 * <AUTHOR>
 * @date 2023-07-14 13:51
 */
@RestController
@RequestMapping("/org-manage")
@Slf4j
@Api(value = "组织管理", tags = "组织管理")
public class OrganizationManageController {
    @Resource
    OrganizationApplyApi organizationApplyApi;
    @Resource
    OrganizationApplyCommunicateApi organizationApplyCommunicateApi;
    @Resource
    BaseDataApi baseDataApi;
    @Resource
    OrganizationAuthLogApi organizationAuthLogApi;

    /**
     * 分页查询组织申请列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/14 14:10
     **/
    @PostMapping("/org/apply/page/list")
    @ApiOperation(value = "分页查询组织申请列表", notes = "分页查询组织申请列表")
    public AjaxResult getOrganizationApplyPageList(@RequestBody OrganizationApplyConditionBo conditionBo) {
        return organizationApplyApi.getOrganizationApplyPageListByCondition(conditionBo);
    }

    /**
     * 提交组织申请沟通内容
     *
     * @param communicateBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/14 15:04
     **/
    @PostMapping("/communicate/add")
    @ApiOperation(value = "提交组织申请沟通内容", notes = "提交组织申请沟通内容")
    public AjaxResult addCommunicate(@RequestBody OrganizationApplyCommunicateBo communicateBo) {
        return organizationApplyCommunicateApi.addOrganizationApplyCommunicate(communicateBo);
    }

    /**
     * 获取沟通记录
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/14 15:29
     **/
    @PostMapping("/communicate/list")
    @ApiOperation(value = "获取沟通记录", notes = "获取沟通记录")
    public AjaxResult getCommunicateList(@RequestBody OrganizationApplyCommunicateConditionBo conditionBo) {
        return organizationApplyCommunicateApi.getOrganizationApplyCommunicateListByCondition(conditionBo);
    }

    /**
     * 获取组织列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/18 11:17
     **/
    @PostMapping("/org-info/list")
    @ApiOperation(value = "获取组织列表", notes = "获取组织列表")
    public AjaxResult organizationList(@RequestBody OrganizationExtConditionBo conditionBo) {
        return baseDataApi.getOrganizationListByCondition(conditionBo);
    }

    /**
     * 新增组织信息
     *
     * @param organizationBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/18 11:17
     **/
    @PostMapping("/org/add")
    @ApiOperation(value = "新增组织信息", notes = "新增组织信息")
    public AjaxResult addOrganization(@RequestBody OrganizationBo organizationBo) {
        OrganizationConditionBo conditionBo = new OrganizationConditionBo();
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setName(organizationBo.getName());
        conditionBo.setParentId(organizationBo.getParentId());
        AjaxResult<OrganizationVo> orgVoResult = baseDataApi.getOrganizationByCondition(conditionBo);
        if (orgVoResult.isFail()) {
            return orgVoResult;
        }
        OrganizationVo organizationVo = orgVoResult.getData();
        if (organizationVo == null) {
            AjaxResult<Long> addOrgResult = baseDataApi.addOrganization(organizationBo);
            if (addOrgResult.isSuccess()) {
                OrganizationSettingBo settingBo = new OrganizationSettingBo();
                settingBo.setOrganizationId(addOrgResult.getData());
                settingBo.setK(ConstServiceName.PLATFORM_KEY);
                settingBo.setVal(ConstServiceName.PLATFORM_NAME);
                AjaxResult settingResult = baseDataApi.addOrUpdateOrganizationSetting(settingBo);
                if (settingResult.isSuccess()) {
                    return AjaxResult.success("新增组织信息成功");
                }
            }
            return AjaxResult.fail("新增组织信息失败");
        } else {
            OrganizationSettingBo settingBo = new OrganizationSettingBo();
            settingBo.setOrganizationId(organizationVo.getId());
            settingBo.setK(ConstServiceName.PLATFORM_KEY);
            settingBo.setVal(ConstServiceName.PLATFORM_NAME);
            AjaxResult settingResult = baseDataApi.addOrUpdateOrganizationSetting(settingBo);
            if (settingResult.isSuccess()) {
                return AjaxResult.success("新增组织信息成功");
            }
            return AjaxResult.fail("新增组织信息失败");
        }
    }

    /**
     * 组织详情
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 9:24
     **/
    @GetMapping("/org/detail")
    @ApiOperation(value = "组织详情", notes = "组织详情")
    public AjaxResult getOrganizationDetail(@NotNull(message = "请选择数据") @RequestParam("organizationId") Long organizationId) {
        return baseDataApi.getOrganizationDetail(organizationId);
    }

    /**
     * 编辑组织信息
     *
     * @param organizationBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/18 11:18
     **/
    @PostMapping("/org/update")
    @ApiOperation(value = "编辑组织信息", notes = "编辑组织信息")
    public AjaxResult updateOrganization(@RequestBody OrganizationBoExt organizationBo) {
        return baseDataApi.updateOrganization(organizationBo);
    }

    /**
     * 重置密码
     *
     * @param accountId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/18 11:18
     **/
    @GetMapping("/reset-password")
    @ApiOperation(value = "重置密码", notes = "重置密码")
    public AjaxResult resetPassword(@NotNull(message = "请选择数据") @RequestParam("accountId") Long accountId) {
        return baseDataApi.resetPassword(accountId);
    }

    /**
     * 根据组织名称获取组织信息
     *
     * @param name
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/18 11:18
     **/
    @GetMapping("/org/by-name")
    @ApiOperation(value = "根据组织名称获取组织信息", notes = "根据组织名称获取组织信息")
    public AjaxResult getOrganization(@NotNull(message = "请选择数据") @RequestParam("name") String name,
                                      @NotNull(message = "请选择数据") @RequestParam("parentId") Long parentId) {
        OrganizationConditionBo conditionBo = new OrganizationConditionBo();
        conditionBo.setName(name);
        conditionBo.setParentId(parentId);
        return baseDataApi.getOrganizationByCondition(conditionBo);
    }

    /**
     * 组织认证
     *
     * @param authBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 17:27
     **/
    @PostMapping("/org/auth")
    @ApiOperation(value = "组织认证", notes = "组织认证")
    public AjaxResult organizationAuth(@RequestBody OrganizationAuthLogBo authBo) {
        authBo.setAuthType(OrganizationAuthType.AUTH.getCode());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        OrganizationSettingBo settingBo = new OrganizationSettingBo();
        settingBo.setRemark(OrganizationAuthType.AUTH.getCode().toString());
        settingBo.setVal(sdf.format(authBo.getAuthEndDay()));
        settingBo.setOrganizationId(authBo.getOrganizationId());
        settingBo.setK(ConstOrgAuth.ORG_AUTH);
        AjaxResult ajaxResult = baseDataApi.addOrUpdateOrganizationSetting(settingBo);
        if (ajaxResult.isSuccess()) {
            organizationAuthLogApi.addOrganizationAuthLog(authBo);
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    /**
     * 关闭认证
     *
     * @Param authBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/19 17:29
     **/
    @PostMapping("/org/close-auth")
    @ApiOperation(value = "关闭认证", notes = "关闭认证")
    public AjaxResult organizationUnAuth(@RequestBody OrganizationAuthLogBo authBo) {
        authBo.setAuthType(OrganizationAuthType.UN_AUTH.getCode());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        OrganizationSettingBo settingBo = new OrganizationSettingBo();
        settingBo.setRemark(OrganizationAuthType.UN_AUTH.getCode().toString());
        settingBo.setVal(sdf.format(DateUtil.yesterday()));
        settingBo.setOrganizationId(authBo.getOrganizationId());
        authBo.setAuthEndDay(DateUtil.yesterday());
        settingBo.setK(ConstOrgAuth.ORG_AUTH);
        AjaxResult ajaxResult = baseDataApi.addOrUpdateOrganizationSetting(settingBo);
        if (ajaxResult.isSuccess()) {
            organizationAuthLogApi.addOrganizationAuthLog(authBo);
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    /**
     * 组织认证记录
     *
     * @param organizationId 
     * @return com.light.core.entity.AjaxResult 
     * <AUTHOR>
     * @date 2023/7/20 11:45
     **/
    @GetMapping("/org/auth-list")
    @ApiOperation(value = "组织认证记录", notes = "组织认证记录")
    public AjaxResult organizationAuthList(@RequestParam("organizationId") Long organizationId) {
        OrganizationAuthLogConditionBo authLogConditionBo = new OrganizationAuthLogConditionBo();
        authLogConditionBo.setOrganizationId(organizationId);
        return organizationAuthLogApi.getOrganizationAuthLogListByCondition(authLogConditionBo);
    }

}
