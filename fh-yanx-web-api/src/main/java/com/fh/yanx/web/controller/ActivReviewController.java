package com.fh.yanx.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpStatus;
import com.fh.yanx.service.activ.api.*;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoRecordViewBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoScheduleConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoViewPermissionConditionBo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoVo;
import com.fh.yanx.service.consts.ConstOrgAuth;
import com.fh.yanx.service.enums.*;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.MessageConstants;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.organization.entity.bo.OrganizationSettingConditionBo;
import com.light.user.organization.entity.vo.OrganizationSettingVo;
import com.light.user.user.entity.vo.UserRoleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 前台活动回看（需要移动接口，这里建议都免登录，然后拆分一个需要登录的controller路由）TODO
 *
 * <AUTHOR>
 * @date 2023-07-10 11:12
 */
@RestController
@RequestMapping("/activ-review")
@Slf4j
@Api(value = "活动回看", tags = "活动回看")
public class ActivReviewController {
    @Resource
    BaseDataApi baseDataApi;
    @Resource
    ActivInfoApi activInfoApi;
    @Resource
    ActivInfoRecordApi activInfoRecordApi;
    @Resource
    ActivInfoScheduleApi activInfoScheduleApi;
    @Resource
    ActivInfoViewPermissionApi activInfoViewPermissionApi;
    @Resource
    private ActivInfoRecordViewApi activInfoRecordViewApi;

    /**
     * 分页获取活动列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:02
     **/
    @PostMapping("/page/list")
    @ApiOperation(value = "分页获取活动列表", notes = "分页获取活动列表")
    public AjaxResult activPageList(@RequestBody ActivInfoConditionBo conditionBo) {
        conditionBo.setActivType(ActivType.ACTIV_TYPE_UP.getCode());
        return activInfoApi.getActivInfoPageListByCondition(conditionBo);
    }

    /**
     * 获取活动列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:03
     **/
    @PostMapping("/list")
    @ApiOperation(value = "获取活动列表", notes = "获取活动列表")
    public AjaxResult activList(@RequestBody ActivInfoConditionBo conditionBo) {
        conditionBo.setActivType(ActivType.ACTIV_TYPE_UP.getCode());
        // 查询配置的活动列表，增加排序
        if (conditionBo.getShowType() != null) {
            conditionBo.setOrderBy("show_index asc");
        }
        return activInfoApi.getActivInfoListByCondition(conditionBo);
    }

    /**
     * 获取活动详情
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:14
     **/
    @GetMapping("/detail")
    @ApiOperation(value = "活动详情", notes = "活动详情")
    public AjaxResult getActivInfoDetail(@RequestParam("activId") Long activId) {
        return activInfoApi.getDetail(activId, ActivType.ACTIV_TYPE_UP.getCode());
    }

    /**
     * 活动内容列表
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:32
     **/
    @GetMapping("/activ-record/list")
    @ApiOperation(value = "活动内容列表", notes = "活动内容列表")
    public AjaxResult getActivInfoRecordList(@RequestParam("activId") Long activId) {
        AjaxResult<ActivInfoVo> activInfoVoAjaxResult =
            activInfoApi.getDetail(activId, ActivType.ACTIV_TYPE_UP.getCode());
        if (activInfoVoAjaxResult.isFail() || activInfoVoAjaxResult.getData() == null) {
            return AjaxResult.fail("活动不存在或活动未上架，无法获取活动内容");
        }
        ActivInfoRecordConditionBo conditionBo = new ActivInfoRecordConditionBo();
        conditionBo.setActivId(activId);
        conditionBo.setOrderBy("record_sort");
        return activInfoRecordApi.getActivInfoRecordListByCondition(conditionBo);
    }

    /**
     * 活动内容列表
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:32
     **/
    @GetMapping("/activ-record/record-list")
    @ApiOperation(value = "活动内容列表", notes = "活动内容列表")
    public AjaxResult getActivInfoRecordListWithPermission(@RequestParam("activId") Long activId) {
        AjaxResult<ActivInfoVo> activInfoVoAjaxResult =
            activInfoApi.getDetail(activId, ActivType.ACTIV_TYPE_UP.getCode());
        if (activInfoVoAjaxResult.isFail() || activInfoVoAjaxResult.getData() == null) {
            return AjaxResult.fail("活动不存在或活动未上架，无法获取活动内容");
        }
        Map<String, Object> map = new HashMap<>();

        LoginAccountVo loginAccountVo = baseDataApi.getCurrentUser().getData();
        if (loginAccountVo == null) {
            return AjaxResult.fail(HttpStatus.HTTP_NOT_ACCEPTABLE, MessageConstants.TOKEN_EXPIRE);
        }
        String phone = loginAccountVo.getPhone();
        // 获取活动观看权限列表
        ActivInfoViewPermissionConditionBo viewPermissionConditionBo = new ActivInfoViewPermissionConditionBo();
        viewPermissionConditionBo.setViewType(ActivInfoViewType.ACTIV_RECORD.getCode());
        viewPermissionConditionBo.setActivId(activId);
        AjaxResult<List<ActivInfoViewPermissionVo>> result =
            activInfoViewPermissionApi.getActivInfoViewPermissionListByCondition(viewPermissionConditionBo);
        if (result.isFail()) {
            return AjaxResult.fail("活动回看观看权限获取失败");
        }
        List<ActivInfoViewPermissionVo> list = result.getData();
        if (CollectionUtil.isEmpty(list) || (list.size() == 1
            && ViewPermission.ACTIV_JOINER.getCode().equals(list.get(0).getViewPermission()))) {// 参会人可看
            // 判断用户是不是参会人
            ActivInfoConditionBo condition = new ActivInfoConditionBo();
            condition.setPhone(phone);
            condition.setActivId(activId);
            AjaxResult<PageInfo<ActivInfoVo>> ajaxResult = activInfoApi.getUserActivList(condition);
            if (ajaxResult.isFail()) {
                return AjaxResult.fail("获取用户参会信息失败");
            }
            if (CollectionUtil.isEmpty(ajaxResult.getData().getList())) {
                map.put("viewAuth", ActivInfoViewAuth.NO_VIEW_AUTH.getCode());
                return AjaxResult.success(map);
            }
        } else {
            // 案例持有者
            boolean caseOwnerAuth = false;
            List<Long> userRoleIds = loginAccountVo.getCurrentUser().getLoginUserRoles().stream()
                    .map(UserRoleVo::getRoleId).collect(Collectors.toList());

            List<Integer> viewPermissions =
                    list.stream().map(ActivInfoViewPermissionVo::getViewPermission).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(userRoleIds)
                    && userRoleIds.contains(ReceptionRoleEnum.CASE_OWNER.getRoleId())) {
                if (viewPermissions.contains(ViewPermission.COURSE_CASE_OWNER.getCode())) {
                    caseOwnerAuth = true;
                }
            }

            // 组织权限
            Long organizationId = loginAccountVo.getCurrentUser().getUserOrg().getOrganizationId();
            // 获取用户组织认证状态
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date nowDay = new Date();
            OrganizationSettingConditionBo conditionBo = new OrganizationSettingConditionBo();
            conditionBo.setPageNo(SystemConstants.NO_PAGE);
            conditionBo.setOrganizationId(organizationId);
            conditionBo.setK(ConstOrgAuth.ORG_AUTH);
            AjaxResult<OrganizationSettingVo> ajaxResult = baseDataApi.getOrganizationSetting(conditionBo);
            Integer authType = OrganizationAuthType.UN_AUTH.getCode();
            try {
                OrganizationSettingVo settingVo = ajaxResult.getData();
                if (OrganizationAuthType.AUTH.getCode().equals(Integer.valueOf(settingVo.getRemark()))
                    && sdf.parse(sdf.format(nowDay)).compareTo(sdf.parse(settingVo.getVal())) <= 0) {
                    authType = OrganizationAuthType.AUTH.getCode();
                }
            } catch (Exception e) {
                log.error("orgAuthType error : ", e);
            }
            Integer viewPermission;
            if (authType.equals(OrganizationAuthType.AUTH.getCode())) {
                viewPermission = ViewPermission.AUTH_PEOPLE.getCode();
            } else {
                viewPermission = ViewPermission.NO_AUTH_PEOPLE.getCode();
            }

            // 非案例持有者权限且无组织权限
            if (!caseOwnerAuth && !viewPermissions.contains(viewPermission)) {
                map.put("viewAuth", ActivInfoViewAuth.NO_VIEW_AUTH.getCode());
                return AjaxResult.success(map);
            }
        }

        ActivInfoRecordConditionBo conditionBo = new ActivInfoRecordConditionBo();
        conditionBo.setActivId(activId);
        AjaxResult<List<ActivInfoRecordVo>> ajaxResult =
            activInfoRecordApi.getActivInfoRecordListByCondition(conditionBo);
        map.put("viewAuth", ActivInfoViewAuth.CAN_VIEW.getCode());
        map.put("list", ajaxResult.getData());
        return AjaxResult.success(map);
    }

    /**
     * 活动日程列表
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/7 17:41
     **/
    @GetMapping("/activ-schedule/list")
    @ApiOperation(value = "活动日程列表", notes = "活动日程列表")
    public AjaxResult getActivInfoScheduleList(@RequestParam("activId") Long activId) {
        AjaxResult<ActivInfoVo> activInfoVoAjaxResult =
            activInfoApi.getDetail(activId, ActivType.ACTIV_TYPE_UP.getCode());
        if (activInfoVoAjaxResult.isFail() || activInfoVoAjaxResult.getData() == null) {
            return AjaxResult.fail("活动不存在或活动未上架，无法获取活动日程");
        }
        Map<String, Object> map = new HashMap<>();

        LoginAccountVo loginAccountVo = baseDataApi.getCurrentUser().getData();
        if (loginAccountVo == null) {
            return AjaxResult.fail(HttpStatus.HTTP_NOT_ACCEPTABLE, MessageConstants.TOKEN_EXPIRE);
        }
        String phone = loginAccountVo.getPhone();
        // 获取活动观看权限列表
        ActivInfoViewPermissionConditionBo viewPermissionConditionBo = new ActivInfoViewPermissionConditionBo();
        viewPermissionConditionBo.setViewType(ActivInfoViewType.ACTIV_SCHEDULE.getCode());
        viewPermissionConditionBo.setActivId(activId);
        AjaxResult<List<ActivInfoViewPermissionVo>> result =
            activInfoViewPermissionApi.getActivInfoViewPermissionListByCondition(viewPermissionConditionBo);
        if (result.isFail()) {
            return AjaxResult.fail("活动回看观看权限获取失败");
        }
        List<ActivInfoViewPermissionVo> list = result.getData();
        if (CollectionUtil.isEmpty(list) || (list.size() == 1
            && ViewPermission.ACTIV_JOINER.getCode().equals(list.get(0).getViewPermission()))) {// 参会人可看
            // 判断用户是不是参会人
            ActivInfoConditionBo condition = new ActivInfoConditionBo();
            condition.setPhone(phone);
            condition.setActivId(activId);
            AjaxResult<PageInfo<ActivInfoVo>> ajaxResult = activInfoApi.getUserActivList(condition);
            if (ajaxResult.isFail()) {
                return AjaxResult.fail("获取用户参会信息失败");
            }
            if (CollectionUtil.isEmpty(ajaxResult.getData().getList())) {
                map.put("viewAuth", ActivInfoViewAuth.NO_VIEW_AUTH.getCode());
                return AjaxResult.success(map);
            }
        } else {
            // 案例持有者
            boolean caseOwnerAuth = false;
            List<Long> userRoleIds = loginAccountVo.getCurrentUser().getLoginUserRoles().stream()
                    .map(UserRoleVo::getRoleId).collect(Collectors.toList());

            List<Integer> viewPermissions =
                    list.stream().map(ActivInfoViewPermissionVo::getViewPermission).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(userRoleIds)
                    && userRoleIds.contains(ReceptionRoleEnum.CASE_OWNER.getRoleId())) {
                if (viewPermissions.contains(ViewPermission.COURSE_CASE_OWNER.getCode())) {
                    caseOwnerAuth = true;
                }
            }

            Long organizationId = loginAccountVo.getCurrentUser().getUserOrg().getOrganizationId();
            // 获取用户组织认证状态
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date nowDay = new Date();
            OrganizationSettingConditionBo conditionBo = new OrganizationSettingConditionBo();
            conditionBo.setPageNo(SystemConstants.NO_PAGE);
            conditionBo.setOrganizationId(organizationId);
            conditionBo.setK(ConstOrgAuth.ORG_AUTH);
            AjaxResult<OrganizationSettingVo> ajaxResult = baseDataApi.getOrganizationSetting(conditionBo);
            Integer authType = OrganizationAuthType.UN_AUTH.getCode();
            try {
                OrganizationSettingVo settingVo = ajaxResult.getData();
                if (OrganizationAuthType.AUTH.getCode().equals(Integer.valueOf(settingVo.getRemark()))
                    && sdf.parse(sdf.format(nowDay)).compareTo(sdf.parse(settingVo.getVal())) <= 0) {
                    authType = OrganizationAuthType.AUTH.getCode();
                }
            } catch (Exception e) {
                log.error("orgAuthType error : ", e);
            }
            Integer viewPermission;
            if (authType.equals(OrganizationAuthType.AUTH.getCode())) {
                viewPermission = ViewPermission.AUTH_PEOPLE.getCode();
            } else {
                viewPermission = ViewPermission.NO_AUTH_PEOPLE.getCode();
            }

            // 非案例持有者权限且无组织权限
            if (!caseOwnerAuth && !viewPermissions.contains(viewPermission)) {
                map.put("viewAuth", ActivInfoViewAuth.NO_VIEW_AUTH.getCode());
                return AjaxResult.success(map);
            }
        }

        ActivInfoScheduleConditionBo conditionBo = new ActivInfoScheduleConditionBo();
        conditionBo.setActivId(activId);
        AjaxResult<List<ActivInfoScheduleVo>> ajaxResult =
            activInfoScheduleApi.getActivInfoScheduleListByCondition(conditionBo);
        map.put("viewAuth", ActivInfoViewAuth.CAN_VIEW.getCode());
        map.put("list", ajaxResult.getData());
        return AjaxResult.success(map);
    }

    /**
     * 获取活动报名配置信息
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/8 9:37
     **/
    @GetMapping("/activ-bm-config")
    @ApiOperation(value = "获取报名配置信息", notes = "获取报名配置信息")
    public AjaxResult getActivBmConfig(@RequestParam("activId") Long activId) {
        AjaxResult<ActivInfoVo> activInfoVoAjaxResult =
            activInfoApi.getDetail(activId, ActivType.ACTIV_TYPE_UP.getCode());
        if (activInfoVoAjaxResult.isFail() || activInfoVoAjaxResult.getData() == null) {
            return AjaxResult.fail("活动不存在或活动未上架，无法获取活动报名信息");
        }
        return activInfoVoAjaxResult;
    }

    /**
     * 观看具体的活动资源记录
     *
     * @param activInfoRecordViewBo the activ info record view bo
     * @return the activ info detail
     * <AUTHOR>
     * @date 2023 -08-16 11:08:09
     */
    @PostMapping("/view")
    @ApiOperation(value = "观看活动资源", notes = "观看活动资源")
    public AjaxResult view(@RequestBody ActivInfoRecordViewBo activInfoRecordViewBo) {
        if (activInfoRecordViewBo.getActivId() == null || activInfoRecordViewBo.getRecordId() == null) {
            return AjaxResult.fail("参数错误");
        }
        activInfoRecordViewBo.setViewDate(new Date());
        return activInfoRecordViewApi.addActivInfoRecordView(activInfoRecordViewBo);
    }
}
