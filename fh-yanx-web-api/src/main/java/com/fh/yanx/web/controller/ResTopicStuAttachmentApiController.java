package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicStuAttachmentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStuAttachmentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStuAttachmentVo;
import com.fh.yanx.service.res.service.ResTopicStuAttachmentApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题研究附件表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
@RequestMapping("res/topic/stu/attachment")
@Api(value = "", tags = "课题研究附件表接口")
public class ResTopicStuAttachmentApiController {

    @Autowired
    private ResTopicStuAttachmentApi resTopicStuAttachmentApi;

    /**
     * 查询课题研究附件表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题研究附件表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult
        getResTopicStuAttachmentPageListByCondition(@RequestBody ResTopicStuAttachmentConditionBo condition) {
        PageInfo<ResTopicStuAttachmentVo> page =
            resTopicStuAttachmentApi.getResTopicStuAttachmentPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题研究附件表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题研究附件表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicStuAttachmentListByCondition(@RequestBody ResTopicStuAttachmentConditionBo condition) {
        List<ResTopicStuAttachmentVo> list =
            resTopicStuAttachmentApi.getResTopicStuAttachmentListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }

    /**
     * 新增课题研究附件表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题研究附件表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicStuAttachment(@RequestBody ResTopicStuAttachmentBo resTopicStuAttachmentBo) {
        return resTopicStuAttachmentApi.addResTopicStuAttachment(resTopicStuAttachmentBo);
    }

    /**
     * 修改课题研究附件表
     *
     * @param resTopicStuAttachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新课题研究附件表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateResTopicStuAttachment(@RequestBody ResTopicStuAttachmentBo resTopicStuAttachmentBo) {
        if (null == resTopicStuAttachmentBo.getId()) {
            return AjaxResult.fail("课题研究附件表id不能为空");
        }
        return resTopicStuAttachmentApi.updateResTopicStuAttachment(resTopicStuAttachmentBo);
    }

    /**
     * 批量修改附件
     *
     * @param resTopicStuAttachmentBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/2/8 14:54
     */
    @PostMapping("/update-batch")
    @ApiOperation(value = "更新课题研究附件表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult
        updateBatchResTopicStuAttachment(@RequestBody List<ResTopicStuAttachmentBo> resTopicStuAttachmentBos) {
        return resTopicStuAttachmentApi.updateBatchResTopicStuAttachment(resTopicStuAttachmentBos);
    }

    /**
     * 查询课题研究附件表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题研究附件表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题研究附件表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicStuAttachmentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题研究附件表id不能为空");
        }
        ResTopicStuAttachmentVo vo = resTopicStuAttachmentApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicStuAttachmentVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除课题研究附件表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题研究附件表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题研究附件表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicStuAttachmentApi.delete(id);
    }

    /**
     * 删除课题研究附件表-根据stuFileOid删除
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete-oid")
    @ApiOperation(value = "删除课题研究附件表-根据stuFileOid", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult deleteByFileOid(@NotNull(message = "stuFileOid不能为空") String stuFileOid) {
        return resTopicStuAttachmentApi.deleteByFileOid(stuFileOid);
    }
}
