package com.fh.yanx.web.config;

import com.fh.yanx.web.filter.CharsetNormalizationFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * 过滤器配置类
 * 统一管理项目中的所有过滤器注册
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Slf4j
@Configuration
public class FilterConfig {

    /**
     * 注册字符集规范化过滤器
     * 将各种形式的utf-8字符集标识统一转换为标准的UTF-8格式
     *
     * @return FilterRegistrationBean
     */
    @Bean
    public FilterRegistrationBean<CharsetNormalizationFilter> charsetNormalizationFilter() {
        log.info("注册字符集规范化过滤器");
        
        FilterRegistrationBean<CharsetNormalizationFilter> registrationBean = new FilterRegistrationBean<>();
        
        // 设置过滤器实例
        registrationBean.setFilter(new CharsetNormalizationFilter());
        
        // 设置过滤器拦截的URL模式，拦截所有请求
        registrationBean.addUrlPatterns("/*");
        
        // 设置过滤器名称
        registrationBean.setName("charsetNormalizationFilter");
        
        // 设置最高优先级，确保在其他过滤器（特别是字符编码相关过滤器）之前执行
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        
        // 启用过滤器
        registrationBean.setEnabled(true);
        
        log.info("字符集规范化过滤器注册完成，优先级: {}", Ordered.HIGHEST_PRECEDENCE);
        
        return registrationBean;
    }
}
