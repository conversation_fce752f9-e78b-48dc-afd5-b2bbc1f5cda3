package com.fh.yanx.web.utils;

import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

public class MD5Sign {

    /**
     * 方法描述:将字符串MD5加码 生成32位md5码
     *
     * @param inStr
     * @return
     */
    public static String md5(String inStr) {
        try {
            return DigestUtils.md5Hex(inStr.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("MD5签名过程中出现错误");
        }
    }

    /**
     * 方法描述:签名字符串
     *
     * @param params 需要签名的参数
     * @param secret 签名密钥
     * @return
     */
    public static String sign(HashMap<String, String> params, String secret) {
        StringBuilder valueSb = new StringBuilder();
        params.put("secret", secret);
        // 将参数以参数名的字典升序排序
        Map<String, String> sortParams = new TreeMap<String, String>(params);
        Set<Map.Entry<String, String>> entries = sortParams.entrySet();
        // 遍历排序的字典,并拼接value1+value2......格式
        final int size = sortParams.size();
        int i = 1;
        for (Map.Entry<String, String> entry : entries) {
            valueSb.append(entry.getKey()).append("=").append(entry.getValue());
            if (i != size) {
                valueSb.append("&");
            }
            i++;
        }
        params.remove("secret");
        return md5(valueSb.toString());
    }

    /**
     * 方法描述:验证签名
     *
     * @param appSecret 加密秘钥
     * @return
     * @throws Exception
     */
    public static boolean verify(String sign, String appSecret, HashMap<String, String> params) {
        String mysign = sign(params, appSecret);
        return mysign.equals(sign);

    }

    public static void main(String[] args) {
        HashMap<String, String> map = new HashMap<>();
        map.put("appid", "os2b268vonxjy53g");

        map.put("timestamp", "1687141217670");
        map.put("phone", "17301590100");

        final String sign = MD5Sign.sign(map, "5HItP5SXv6JCiUOUknuFDduGiYSVTmMs");

        System.out.println(sign);
    }

}
