package com.fh.yanx.web.controller;

import com.fh.yanx.service.res.api.ResTopicStudentApi;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentBo;
import com.fh.yanx.service.res.entity.bo.ResTopicStudentConditionBo;
import com.fh.yanx.service.res.entity.vo.ResTopicStudentVo;
import com.fh.yanx.service.res.service.ResTopicStudentApiService;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.swagger.constants.SwaggerConstant;
import com.light.user.student.entity.bo.StudentConditionBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课题组成员表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-02 15:00:24
 */
@RestController
@Validated
@RequestMapping("res/topic/student")
@Api(value = "", tags = "课题组成员表接口")
public class ResTopicStudentApiController {

    @Autowired
    private ResTopicStudentApi resTopicStudentApi;

    /**
     * 查询课题组成员表分页列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询课题组成员表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicStudentPageListByCondition(@RequestBody ResTopicStudentConditionBo condition) {
        PageInfo<ResTopicStudentVo> page = resTopicStudentApi.getResTopicStudentPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询课题组成员表列表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询课题组成员表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getResTopicStudentListByCondition(@RequestBody ResTopicStudentConditionBo condition) {
        List<ResTopicStudentVo> list = resTopicStudentApi.getResTopicStudentListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增课题组成员表
     *
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增课题组成员表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addResTopicStudent(@RequestBody ResTopicStudentBo resTopicStudentBo) {
        return resTopicStudentApi.addResTopicStudent(resTopicStudentBo);
    }

    /**
     * 修改课题组成员(添加评语接口)
     *
     * @param resTopicStudentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @PostMapping("/add-evaluate")
    @ApiOperation(value = "更新课题组成员表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addEvaluate(@RequestBody ResTopicStudentBo resTopicStudentBo) {
        if (null == resTopicStudentBo.getId()) {
            return AjaxResult.fail("课题组成员表id不能为空");
        }
        resTopicStudentBo.setIsAddEvaluate(StatusEnum.YES.getCode());
        return resTopicStudentApi.updateResTopicStudent(resTopicStudentBo);
    }

    /**
     * 查询课题组成员表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询课题组成员表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题组成员表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ResTopicStudentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课题组成员表id不能为空");
        }
        ResTopicStudentVo vo = resTopicStudentApi.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("resTopicStudentVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除课题组成员表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-02 15:00:24
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课题组成员表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课题组成员表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return resTopicStudentApi.delete(id);
    }

    /**
    *根据条件获取学生信息
    *
    * @param condition
    * @return com.light.core.entity.AjaxResult
    * <AUTHOR>
    * @date 2023/2/2 15:21
    */
    @PostMapping("/student-list")
    AjaxResult getStudentListByCondition(@RequestBody StudentConditionBo condition){
        return resTopicStudentApi.getStudentListByCondition(condition);
    }
}
