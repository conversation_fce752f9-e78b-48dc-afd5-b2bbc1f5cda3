package com.fh.yanx.web.service;

import com.light.core.entity.AjaxResult;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.CountDownLatch;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 单元测试
 * 
 * <AUTHOR>
 * @date 2023/6/20 11:24
 */
@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
class SmsYmTest {

    @Autowired
    private SmsYmService smsYmService;

    @Test
    void testSms() throws Exception {
        AjaxResult<String> send = smsYmService.send("18512541853");
        boolean result = send != null && send.isSuccess() && StringUtils.isNotBlank(send.getData());
        assertTrue(result);
        System.out.println("code is :" + send.getData());
    }

    @Test
    void testSmsContent() throws Exception {
        AjaxResult<Integer> send = smsYmService.sendContent("13140723913","登录新时代文化校园平台https://training.ineoschool.com/,以13140723913/fue9h3登录平台查看课程回放");
        // 以此为结果的标准
        boolean result = send != null && send.isSuccess();
        assertTrue(result);
        // 返回值忽略
        System.out.println("result is :" + send.getData());
    }

}
