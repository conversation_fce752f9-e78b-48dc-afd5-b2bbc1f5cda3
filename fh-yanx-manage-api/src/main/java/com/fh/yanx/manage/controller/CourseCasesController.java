package com.fh.yanx.manage.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.aspose.words.*;
import com.fh.yanx.manage.consts.ConstString;
import com.fh.yanx.manage.service.AsposeService;
import com.fh.yanx.service.course.api.CourseVerifyLogApi;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogBo;
import com.fh.yanx.service.course.entity.bo.CourseVerifyLogConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseVerifyLogVo;
import com.fh.yanx.service.course.entity.vo.PCourseCasesVo;
import com.fh.yanx.service.enums.CourseBestVerifyType;
import com.fh.yanx.service.enums.IsExcellentType;
import com.google.common.collect.Sets;
import com.light.core.enums.StatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.course.api.CourseHomeConfigApi;
import com.fh.yanx.service.course.api.PCourseCasesApi;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigBo;
import com.fh.yanx.service.course.entity.bo.CourseHomeConfigConditionBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesBo;
import com.fh.yanx.service.course.entity.bo.PCourseCasesConditionBo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 新时代课程管理
 *
 * <AUTHOR>
 * @date 2023 /8/15 17:01
 */
@Slf4j
@RestController
@RequestMapping("/course/cases")
@Api(value = "", tags = "新时代课程管理")
public class CourseCasesController {
    @Resource
    private PCourseCasesApi pCourseCasesApi;
    @Resource
    private CourseHomeConfigApi courseHomeConfigApi;
    @Resource
    private CourseVerifyLogApi courseVerifyLogApi;
    @Resource
    private AsposeService asposeService;

    public static final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Value("${courseCases.export.template:}")
    private String courseCasesTemplate;

    /**
     * 课程列表-分页
     *
     * @param pCourseCasesConditionBo the p course cases condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/list-page")
    @ApiOperation(value = "课程列表-分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listCourseCasesPage(@RequestBody PCourseCasesConditionBo pCourseCasesConditionBo) {
        // 后台回显配置的专家信息
        pCourseCasesConditionBo.setShowExpertUsers(true);
        return pCourseCasesApi.getPCourseCasesPageListByCondition(pCourseCasesConditionBo);
    }

    /**
     * 课程列表-不分页
     *
     * @param pCourseCasesConditionBo the p course cases condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/list")
    @ApiOperation(value = "课程列表-不分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listCourseCases(@RequestBody PCourseCasesConditionBo pCourseCasesConditionBo) {
        return pCourseCasesApi.getPCourseCasesListByCondition(pCourseCasesConditionBo);
    }

    /**
     * 课程详情，废弃，不再使用
     *
     * @param id the id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @Deprecated
    @GetMapping("/detail")
    @ApiOperation(value = "课程详情", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult detailCourseCases(@RequestParam("id") Long id) {
        return pCourseCasesApi.getDetail(id);
    }

    /**
     * 上下架操作
     *
     * @param pCourseCasesBo 需要传参：id,holdType
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/hold")
    @ApiOperation(value = "上下架操作", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult holdCourseCases(@RequestBody PCourseCasesBo pCourseCasesBo) {
        return pCourseCasesApi.updatePCourseCases(pCourseCasesBo);
    }

    /**
     * 课程内容管理查看
     *
     * @param pCourseCasesBo 需要传参：id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/content/detail")
    @ApiOperation(value = "课程内容管理查看", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult courseCasesContentDetail(@RequestBody PCourseCasesConditionBo pCourseCasesConditionBo) {
        return pCourseCasesApi.getContentModule(pCourseCasesConditionBo);
    }

    /**
     * 课程内容管理保存
     *
     * @param pCourseCasesBo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/content/save")
    @ApiOperation(value = "课程内容管理保存", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult courseCasesCSave(@RequestBody PCourseCasesBo pCourseCasesBo) {
        return pCourseCasesApi.saveContentModule(pCourseCasesBo);
    }

    /**
     * 门户推荐配置列表-不分页
     *
     * @param courseHomeConfigConditionBo the course home config condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/home-config/list")
    @ApiOperation(value = "门户推荐配置", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult courseHomeConfig(@RequestBody CourseHomeConfigConditionBo courseHomeConfigConditionBo) {
        return courseHomeConfigApi.getCourseHomeConfigListByCondition(courseHomeConfigConditionBo);
    }

    /**
     * 门户推荐配置-保存，可以删除旧的并保存新的
     *
     * @param courseHomeConfigBo the course home config bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/home-config/save")
    @ApiOperation(value = "门户推荐配置保存", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult courseHomeConfig(@RequestBody CourseHomeConfigBo courseHomeConfigBo) {
        return courseHomeConfigApi.saveCourseHomeConfig(courseHomeConfigBo);
    }

    /**
     * 课程审批通过或者驳，支持精品和普通课程的审核
     *
     * @param courseCasesBoParam 参数：id，bestVerifyType或normalVerifyType，verifyProcessType，isExcellentLabel
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/verify")
    @ApiOperation(value = "课程审批通过或者驳回", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult verify(@RequestBody PCourseCasesBo courseCasesBoParam) {
        if (courseCasesBoParam.getId() == null) {
            return AjaxResult.fail("参数错误");
        }
        PCourseCasesBo pCourseCasesBo = new PCourseCasesBo();
        pCourseCasesBo.setId(courseCasesBoParam.getId());
        pCourseCasesBo.setBestVerifyType(courseCasesBoParam.getBestVerifyType());
        pCourseCasesBo.setNormalVerifyType(courseCasesBoParam.getNormalVerifyType());
        // 精品课程审核通过，设置为精品课程
        if (courseCasesBoParam.getBestVerifyType() != null && courseCasesBoParam.getBestVerifyType()
            .equals(CourseBestVerifyType.YES.getValue())) {
            pCourseCasesBo.setIsExcellent(IsExcellentType.JPKC.getValue());
            if (StringUtils.isBlank(courseCasesBoParam.getIsExcellentLabel())) {
                pCourseCasesBo.setIsExcellentLabel(String.valueOf(IsExcellentType.JPKC.getValue()));
            } else {
                pCourseCasesBo.setIsExcellentLabel(courseCasesBoParam.getIsExcellentLabel().concat(ConstString.ywdh)
                    .concat(String.valueOf(IsExcellentType.JPKC.getValue())));
            }
        }
        // verifyProcessType不为空表示正在进行审核，插入审批意见流水
        if (courseCasesBoParam.getVerifyProcessType() != null) {
            pCourseCasesBo.setVerifyProcessType(courseCasesBoParam.getVerifyProcessType());
            CourseVerifyLogBo courseVerifyLogBo = courseCasesBoParam.getCourseVerifyLogBo();
            // 运营更新审核专家的审核意见，直接调用update方法
            if (courseVerifyLogBo.getId() != null) {
                courseVerifyLogApi.updateCourseVerifyLog(courseVerifyLogBo);
            } else {
                courseVerifyLogApi.deleteAndSave(courseVerifyLogBo);
            }
        }
        return pCourseCasesApi.updatePCourseCases(pCourseCasesBo);
    }

    /**
     * 课程审批通过或者驳，支持精品和普通课程的审核
     *
     * @param courseCasesBoParams the course cases bo params
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/verify-batch")
    @ApiOperation(value = "课程审批通过或者驳回", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult verify(@RequestBody List<PCourseCasesBo> courseCasesBoParams) {
        if (CollectionUtils.isEmpty(courseCasesBoParams)) {
            return AjaxResult.fail("参数错误");
        }

        List<PCourseCasesBo> courseCasesBos = courseCasesBoParams.stream()
            .filter(courseCasesBoParam -> courseCasesBoParam != null && courseCasesBoParam.getId() != null)
            .map(courseCasesBoParam -> {
                PCourseCasesBo pCourseCasesBo = new PCourseCasesBo();
                pCourseCasesBo.setId(courseCasesBoParam.getId());
                pCourseCasesBo.setBestVerifyType(courseCasesBoParam.getBestVerifyType());
                pCourseCasesBo.setNormalVerifyType(courseCasesBoParam.getNormalVerifyType());
                // 精品课程审核通过，设置为精品课程
                if (courseCasesBoParam.getBestVerifyType() != null && courseCasesBoParam.getBestVerifyType()
                    .equals(CourseBestVerifyType.YES.getValue())) {
                    pCourseCasesBo.setIsExcellent(IsExcellentType.JPKC.getValue());
                    if (StringUtils.isBlank(courseCasesBoParam.getIsExcellentLabel())) {
                        pCourseCasesBo.setIsExcellentLabel(String.valueOf(IsExcellentType.JPKC.getValue()));
                    } else {
                        pCourseCasesBo.setIsExcellentLabel(
                            courseCasesBoParam.getIsExcellentLabel().concat(ConstString.ywdh)
                                .concat(String.valueOf(IsExcellentType.JPKC.getValue())));
                    }
                }
                if (courseCasesBoParam.getVerifyProcessType() != null) {
                    pCourseCasesBo.setVerifyProcessType(courseCasesBoParam.getVerifyProcessType());
                }
                return pCourseCasesBo;
            }).collect(Collectors.toList());

        // 批量审批
        for (PCourseCasesBo courseCasesBoParam : courseCasesBoParams) {
            if (courseCasesBoParam.getVerifyProcessType() != null) {
                CourseVerifyLogBo courseVerifyLogBo = courseCasesBoParam.getCourseVerifyLogBo();
                courseVerifyLogApi.deleteAndSave(courseVerifyLogBo);
            }
        }
        for (PCourseCasesBo pCourseCasesBo : courseCasesBos) {
            pCourseCasesApi.updatePCourseCases(pCourseCasesBo);
        }
        return AjaxResult.success();
    }

    /**
     * 审核日志流水查询-不分页
     *
     * @param conditionBo the condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/verify-log/list")
    @ApiOperation(value = "conditionBo", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listVerifyLog(@RequestBody CourseVerifyLogConditionBo conditionBo) {
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return courseVerifyLogApi.getCourseVerifyLogListByCondition(conditionBo);
    }

    /**
     * 修改课程案例
     *
     * @param casesBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/9 17:32
     **/
    @PostMapping("/update")
    @ApiOperation(value = "修改课程案例", httpMethod = "POST")
    public AjaxResult update(@RequestBody PCourseCasesBo casesBo) {
        return pCourseCasesApi.updatePCourseCases(casesBo);
    }

    /**
     * 更新专家评论展示
     *
     * @param courseVerifyLogList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/9 17:32
     **/
    @PostMapping("/updateExpertCommentaryShowcase")
    @ApiOperation(value = "更新专家评论展示", httpMethod = "POST")
    public AjaxResult updateExpertCommentaryShowcase(@RequestBody List<CourseVerifyLogBo> courseVerifyLogList) {
        return pCourseCasesApi.updateExpertCommentaryShowcase(courseVerifyLogList);
    }

    /**
     * 更新增补状态
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/9 17:32
     **/
    @PostMapping("/updateSupplementState")
    @ApiOperation(value = "更新增补状态", httpMethod = "POST")
    public AjaxResult updateSupplementState(@RequestBody PCourseCasesBo pCourseCasesBo) {
        return pCourseCasesApi.updateSupplementState(pCourseCasesBo);
    }

    @PostMapping("/export")
    @ApiOperation("导出课程案例")
    public void exportCourseCases(@RequestBody PCourseCasesConditionBo condition, HttpServletResponse response) {
        if (CollectionUtil.isEmpty(condition.getCourseCasesIds())) {
            throw new RuntimeException("请选择要导出的课程案例");
        }
        condition.setQueryCourseCasesModule(true);
        condition.setSkipConvertHomeResult(true);
        AjaxResult<List<PCourseCasesVo>> result = pCourseCasesApi.getPCourseCasesListByCondition(condition);
        if (result.isFail()) {
            throw new RuntimeException(result.getMsg());
        }
        List<PCourseCasesVo> courseCasesVos = result.getData();
        if (CollectionUtil.isEmpty(courseCasesVos)) {
            throw new RuntimeException("没有要导出的课程案例");
        }

        log.info("export courseCases size:" + courseCasesVos.size());

        String exportFileName = "校本课程案例.zip";
        try (OutputStream out = response.getOutputStream();
            ZipOutputStream zipOutputStream = new ZipOutputStream(out)) {

            this.setResponseHeader(response, exportFileName);

            Map<Integer, CompletableFuture<GeneratedFile>> indexedFutures = new HashMap<>();

            int index = 0;
            for (PCourseCasesVo courseCasesVo : courseCasesVos) {
                int currentIndex = index++;
                CompletableFuture<GeneratedFile> future = CompletableFuture.supplyAsync(() -> generateDocx(courseCasesVo), executorService)
                    .exceptionally(ex -> {
                        log.error("生成文档失败: {}", courseCasesVo.getCourseName(), ex);
                        return null;
                    });
                indexedFutures.put(currentIndex, future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(indexedFutures.values().toArray(new CompletableFuture[0])).join();

            // 按照原始顺序写入 ZIP
            for (int i = 0; i < courseCasesVos.size(); i++) {
                CompletableFuture<GeneratedFile> future = indexedFutures.get(i);
                GeneratedFile file = future.get();
                if (file != null) {
                    zipOutputStream.putNextEntry(new ZipEntry(file.fileName));
                    zipOutputStream.write(file.content);
                    zipOutputStream.closeEntry();
                }
            }

            zipOutputStream.flush();

        } catch (Exception e) {
            log.error("导出课程案例异常", e);
            throw new RuntimeException("导出校本课程案例失败");
        }

    }

    // 封装生成文档的逻辑
    private GeneratedFile generateDocx(PCourseCasesVo courseCasesVo) {
        log.info("courseName: {}, id: {}, generateDocx start..." , courseCasesVo.getCourseName(), courseCasesVo.getId());
        try {
            // 模板文件路径，建议放到 resources 目录下
            byte[] fileBytes = Files.readAllBytes(Paths.get(courseCasesTemplate));
            ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
            Document doc = new Document(inputStream);

            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("courseName", courseCasesVo.getCourseName());
            paramMap.put("schoolName", courseCasesVo.getSchoolName());
            paramMap.put("operation", courseCasesVo.getOperation());
            paramMap.put("introduction", courseCasesVo.getIntroduction());
            paramMap.put("background", courseCasesVo.getCourseCasesInfoVo().getBackground());
            paramMap.put("goal", courseCasesVo.getCourseCasesInfoVo().getGoal());
            paramMap.put("content", courseCasesVo.getCourseCasesInfoVo().getContent());
            paramMap.put("operationInfo", courseCasesVo.getCourseCasesInfoVo().getOperationInfo());
            paramMap.put("eval", courseCasesVo.getCourseCasesInfoVo().getEval());
            paramMap.put("exp", courseCasesVo.getCourseCasesInfoVo().getExp());
            paramMap.put("effect", courseCasesVo.getCourseCasesInfoVo().getEffect());
            paramMap.put("problem", courseCasesVo.getCourseCasesInfoVo().getProblem());
            paramMap.put("structure", courseCasesVo.getCourseCasesInfoVo().getStructure());
            paramMap.put("teacherCaseInfo", courseCasesVo.getCourseCasesInfoVo().getTeacherCaseInfo());

            // 存在非富文本，只有图片url的数据，特殊处理
            Set<String> imageUrlParamKeys = Sets.newHashSet("structure");

            asposeService.replaceText(paramMap, doc, imageUrlParamKeys, true);

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            doc.save(byteArrayOutputStream, SaveFormat.DOCX);
            byte[] bytes = byteArrayOutputStream.toByteArray();
            byteArrayOutputStream.close();

            log.info("courseName: {}, id: {}, generateDocx end." , courseCasesVo.getCourseName(), courseCasesVo.getId());

            return new GeneratedFile(courseCasesVo.getCourseName() + ".docx", bytes);
        } catch (Exception e) {
            throw new RuntimeException("生成文档失败", e);
        }
    }

    // 内部类用于封装生成的文件内容和名称
    private static class GeneratedFile {
        final String fileName;
        final byte[] content;

        GeneratedFile(String fileName, byte[] content) {
            this.fileName = fileName;
            this.content = content;
        }
    }

    /**
     * 设置下载zip响应头
     *
     * @param response
     * @param exportFileName
     */
    public void setResponseHeader(HttpServletResponse response, String exportFileName) {
        try {
            URI uri = new URI(null, null, exportFileName, null);
            response.setContentType("application/zip;charset=UTF-8");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition",
                "attachment;filename=" + uri.toASCIIString() + ";" + "filename*=utf-8''" + uri.toASCIIString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
