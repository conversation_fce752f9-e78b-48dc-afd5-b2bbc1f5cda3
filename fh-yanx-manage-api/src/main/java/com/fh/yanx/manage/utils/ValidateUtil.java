package com.fh.yanx.manage.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023-08-02 15:09
 */
public class ValidateUtil {
    /**
     * 正则表达式：验证手机号
     */
    public static final String REGEX_MOBILE = "^1\\d{10}$";

    /**
     * 正则校验手机号
     * @param phoneNum
     * @return
     */
    public static boolean validatePhone(String phoneNum) {
        Pattern compile = Pattern.compile(REGEX_MOBILE);// 验证手机号
        Matcher m = compile.matcher(phoneNum);
        return m.matches();
    }
}
