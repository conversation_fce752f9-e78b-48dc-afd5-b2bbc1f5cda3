package com.fh.yanx.manage.consts;

/**
 * redis的key的常量
 */
public interface ConstantsRedis {

    /**
     * 短信验证码缓存:yanx_bm_sms_code_{手机号}
     */
    String SMS_PREFIX = "yanx_bm_sms_code_";
    /**
     * 短信验证码缓存缓存2分钟
     */
    long SMS_EXPIRE_IN = 5L * 60;

    /**
     * 短信验证码发送次数缓存:yanx_bm_sms_code_{手机号}
     */
    String SMS_COUNT_PREFIX = "yanx_bm_sms_count_";
    /**
     * 短信验证码发送次数缓存2h
     */
    long SMS_COUNT_EXPIRE_IN = 2L * 60 * 60;
    /**
     * 12h内短信发送的上限
     */
    int SMS_COUNT_LIMIT = 10;
}
