package com.fh.yanx.manage.controller;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.yanx.service.cm.api.CmInfoApi;
import com.fh.yanx.service.cm.entity.bo.CmInfoBo;
import com.fh.yanx.service.cm.entity.bo.CmInfoConditionBo;
import com.fh.yanx.service.cm.entity.vo.CmInfoVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 新时代代理商信息
 * 
 * <AUTHOR>
 * @date 2024/7/16 11:49
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/cm/info")
@Api(value = "", tags = "新时代代理商信息")
public class CmManageController {
    @Resource
    private CmInfoApi cmInfoApi;

    /**
     * 查询代理商信息表分页列表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询代理商信息表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getCmInfoPageListByCondition(@RequestBody CmInfoConditionBo condition) {
        return cmInfoApi.getCmInfoPageListByCondition(condition);
    }

    /**
     * 查询代理商信息表列表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询代理商信息表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getCmInfoListByCondition(@RequestBody CmInfoConditionBo condition) {
        return cmInfoApi.getCmInfoListByCondition(condition);
    }

    /**
     * 新增代理商信息表
     * 
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增代理商信息表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addCmInfo(@RequestBody CmInfoBo cmInfoBo) {
        return cmInfoApi.addCmInfo(cmInfoBo);
    }

    /**
     * 批量新增代理商信息表
     *
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/add-batch")
    @ApiOperation(value = "批量新增代理商信息表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addCmInfoBatch(@RequestBody List<CmInfoBo> cmInfoBos) {
        return cmInfoApi.addCmInfoBatch(cmInfoBos);
    }

    /**
     * 修改代理商信息表
     * 
     * @param cmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新代理商信息表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateCmInfo(@RequestBody CmInfoBo cmInfoBo) {
        if (null == cmInfoBo.getId()) {
            return AjaxResult.fail("代理商信息表id不能为空");
        }
        return cmInfoApi.updateCmInfo(cmInfoBo);
    }

    /**
     * 查询代理商信息表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询代理商信息表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "代理商信息表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<CmInfoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("代理商信息表id不能为空");
        }
        return cmInfoApi.getDetail(id);
    }

    /**
     * 删除代理商信息表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-16 11:35:44
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除代理商信息表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "代理商信息表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@RequestParam("id") Long id) {
        return cmInfoApi.delete(id);
    }
}
