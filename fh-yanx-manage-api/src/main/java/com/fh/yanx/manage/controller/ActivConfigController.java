package com.fh.yanx.manage.controller;

import com.fh.yanx.service.activ.api.ActivInfoApi;
import com.fh.yanx.service.activ.entity.bo.ActivInfoConditionBo;
import com.fh.yanx.service.activ.entity.bo.ActivInfoShowBo;
import com.fh.yanx.service.enums.ActivType;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 活动配置controller
 *
 * <AUTHOR>
 * @date 2023-08-01 10:11
 */
@RestController
@RequestMapping("/activ/config")
@Slf4j
@Api(value = "活动配置", tags = "活动配置")
public class ActivConfigController {
    @Resource
    private ActivInfoApi activInfoApi;

    /**
     * 设置活动展示
     *
     * @param activInfoShowBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/1 10:21
     **/
    @PostMapping("/set-show")
    @ApiOperation(value = "设置活动展示", notes = "设置活动展示")
    public AjaxResult setActivInfoShow(@RequestBody ActivInfoShowBo activInfoShowBo) {
        return activInfoApi.setActivInfoShow(activInfoShowBo);
    }

    /**
     * 获取活动展示列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/14 15:10
     **/
    @PostMapping("/show-list")
    @ApiOperation(value = "获取活动展示列表", notes = "获取活动展示列表")
    public AjaxResult getActivShowList(@RequestBody ActivInfoConditionBo condition) {
        if (condition.getShowType() == null) {
            return AjaxResult.fail("请选择展示类型");
        }
        condition.setActivType(ActivType.ACTIV_TYPE_UP.getCode());
        condition.setOrderBy("show_index asc");
        return activInfoApi.getActivInfoListByCondition(condition);
    }
}
