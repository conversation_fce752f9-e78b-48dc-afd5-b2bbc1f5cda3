package com.fh.yanx.manage.controller;

import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.fh.yanx.manage.listener.ExpertBatchImportListener;
import com.fh.yanx.manage.listener.TeacherBatchImportListener;
import com.fh.yanx.service.enums.ReceptionRoleEnum;
import com.fh.yanx.service.enums.TeacherType;
import com.fh.yanx.service.user.entity.bo.ExpertImportExcelBo;
import com.fh.yanx.service.user.entity.bo.TeacherImportExcelBo;
import com.fh.yanx.service.user.entity.bo.UserConditionBoExt;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.light.core.entity.AjaxResult;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.user.entity.bo.UserBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-28  13:43
 */
@RestController
@RequestMapping("/user")
@Slf4j
@Api("人员管理")
public class UserController {
    @Resource
    private BaseDataApi baseDataApi;

    @Value("${expert.batchImport.template:}")
    private String expertBatchImportTemplateFilePath;

    /**
     * 新增用户
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 14:36
     **/
    @PostMapping("/add-user")
    @ApiOperation(value = "新增用户", httpMethod = "POST")
    public AjaxResult addUser(@RequestBody UserBo userBo) {
        return baseDataApi.addUserAndGeneratorAccount(userBo);
    }

    /**
     * 更新用户
     *
     * @param userBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 14:42
     **/
    @PostMapping("/update-user")
    @ApiOperation(value = "更新用户", httpMethod = "POST")
    public AjaxResult updateUser(@RequestBody UserBo userBo) {
        return baseDataApi.updateExpertUser(userBo);
    }

    /**
     * 获取用户列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/11/28 16:40
     **/
    @PostMapping("/user-list")
    @ApiOperation(value = "用户列表", httpMethod = "POST")
    public AjaxResult userList(@RequestBody UserConditionBoExt conditionBo) {
        // 后台用户列表，只查询专家列表
        conditionBo.setOnlyQueryExpert(true);
        return baseDataApi.userList(conditionBo);
    }

    /**
     * 获取用户详情
     *
     * @param userOid
     * @return
     */
    @GetMapping("/user-detail")
    @ApiOperation(value = "用户详情", httpMethod = "GET")
    public AjaxResult getUserDetail(@RequestParam("userOid") String userOid) {
        return baseDataApi.getUserDetail(userOid);
    }

    @PostMapping("/expert-assistant-list")
    @ApiOperation(value = "获取专家助理列表", httpMethod = "POST")
    public AjaxResult expertAssistantList(@RequestBody UserConditionBoExt conditionBo) {
        return baseDataApi.expertAssistantList(conditionBo);
    }

    /**
     * 重置密码
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/21 13:48
     **/
    @GetMapping("/reset-password")
    @ApiOperation(value = "重置密码", notes = "重置密码")
    public AjaxResult resetPasswordByUserOid(@NotNull(message = "请选择数据") @RequestParam("userOid") String userOid) {
        return baseDataApi.resetPasswordByUserOid(userOid);
    }

    /**
     * 删除用户
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/12/2 14:58
     **/
    @GetMapping("/del-user")
    public AjaxResult delUser(@NotNull(message = "请选择数据") @RequestParam("userOid") String userOid) {
        return baseDataApi.delUser(userOid);
    }

    @ApiOperation(value = "专家导入模板下载", httpMethod = "GET")
    @GetMapping("/expert-template/download")
    public AjaxResult downloadTeacherTemplate(HttpServletRequest request, HttpServletResponse response) {
        BufferedInputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        try {
            inputStream = new BufferedInputStream(new FileInputStream(expertBatchImportTemplateFilePath));
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            response.reset();
            response.setContentType("application/octet-stream; charset=utf-8");
            //重新设置文件名
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("新时代专家导入模板.xlsx", "utf-8"));
            response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.addHeader("Access-Control-Allow-Methods", "*");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
        } catch (Exception e) {
            log.warn("文件读取出错{}", e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error("流关闭异常，e:" + e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e) {
                    log.error("流关闭异常，e：" + e);
                }
            }
        }
        return AjaxResult.success();
    }

    @ApiOperation(value = "导入专家", httpMethod = "POST")
    @PostMapping("/import-expert")
    public AjaxResult importTeacher(@RequestParam("file") MultipartFile file, @RequestParam("organizationId") Long organizationId) {
        if (file == null) {
            return AjaxResult.fail("导入文件为空");
        }
        if (file.getSize() > (1024 * 1024 * 10L)) {
            return AjaxResult.fail("文件大小超过10M，请分批导入。");
        }
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            return AjaxResult.fail(e.getMessage());
        }
        InputStream inputStream1 = new BufferedInputStream(inputStream);
        ExpertBatchImportListener listener = new ExpertBatchImportListener();
        try {
            ExcelReader excelReader = new ExcelReader(inputStream1, ExcelTypeEnum.XLSX, null, listener);
            excelReader.read(new Sheet(1, 3, ExpertImportExcelBo.class));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.fail("读取数据异常，请按《新时代专家导入模板》规范填写后上传。");
        }
        List<UserBo> userBos = listener.getUserBos();
        if (CollectionUtils.isEmpty(userBos)) {
            if (listener.getErrorCount() <= 0) {
                return AjaxResult.fail("导入模板数据为空，请检查");
            }
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.setMsg("成功导入0条数据，另有" + listener.getErrorCount() + "条数据由于数据填写有误，导入失败");
            return ajaxResult;
        }
        for (UserBo userBo : userBos) {
            userBo.setOrganizationId(organizationId);
        }
        AjaxResult ajaxResult = baseDataApi.addBatchUser(userBos);
        if (ajaxResult.isSuccess()) {
            return AjaxResult.success("成功导入" + userBos.size() + "条数据，另有" + listener.getErrorCount() + "条数据由于数据填写有误，导入失败");
        }
        return ajaxResult;
    }
}
