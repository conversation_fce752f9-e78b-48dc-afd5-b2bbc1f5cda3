package com.fh.yanx.manage.controller;

import cn.hutool.core.util.RandomUtil;
import com.fh.yanx.service.cm.api.CmInfoApi;
import com.fh.yanx.service.cm.entity.bo.CmInfoBo;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.google.common.collect.Lists;
import com.light.core.entity.AjaxResult;
import com.light.user.admin.entity.bo.AdminBo;
import com.light.user.admin.entity.bo.AdminConditionBo;
import com.light.user.admin.entity.vo.AdminRoleVo;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.role.entity.vo.RoleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.apache.bcel.classfile.ConstantLong;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 基础数据controller
 *
 * <AUTHOR>
 * @date 2022 /12/12 19:30
 */
@RestController
@RequestMapping("res/base/data")
@Api(value = "", tags = "基础数据controller")
@Slf4j
public class BaseDataApiController {

    @Resource
    private BaseDataApi baseDataApi;
    @Resource
    private CmInfoApi cmInfoApi;

    /**
     * 教师详情查询接口
     *
     * @param userOid the user oid
     * @return index topic
     */
    @ApiOperation("教师详情查询")
    @GetMapping("/teacher/detail")
    public AjaxResult teacherDetail(@RequestParam("userOid") String userOid) {
        return baseDataApi.getTeacherVoByUserOid(userOid);
    }

    /**
     * 学生详情查询接口
     *
     * @param userOid the user oid
     * @return index topic
     */
    @ApiOperation("学生详情查询")
    @GetMapping("/student/detail")
    public AjaxResult studentDetail(@RequestParam("userOid") String userOid) {
        return baseDataApi.getStudentVoByUserOid(userOid);
    }

    /**
     * 获取字典数据
     *
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:40:40
     */
    @ApiOperation(value = "获取字典数据", httpMethod = "POST")
    @ResponseBody
    @PostMapping(value = "/dicts")
    public AjaxResult listValueByTypes(@RequestBody List<String> dictTypes) throws Exception {
        return baseDataApi.listValueByTypes(dictTypes);
    }

    /**
     * 后台账号管理（查询人员列表）
     *
     * @param userOid the user oid
     * @return index topic
     */
    @ApiOperation("后台账号管理（查询人员列表）")
    @PostMapping("/admin/list")
    public AjaxResult getAdminListByCondition(@RequestBody AdminConditionBo conditionBo) {
        return baseDataApi.getAdminListByCondition(conditionBo);
    }

    /**
     * 后台账号管理（查询人员列表-分页）
     *
     * @param conditionBo the condition bo
     * @return the admin page list by condition
     */
    @PostMapping(value = "/admin/list-page")
    public AjaxResult getAdminPageListByCondition(@RequestBody AdminConditionBo conditionBo) {
        return baseDataApi.getAdminPageListByCondition(conditionBo);
    }

    /**
     * 后台添加人员
     *
     * @param adminBo the admin bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2024 -07-16 14:45:28
     */
    @PostMapping(value = "/admin/add")
    public AjaxResult addAdmin(@RequestBody AdminBo adminBo) {
        String accountName = RandomUtil.randomString(8);
        adminBo.setAccountName(accountName);
        adminBo.setOrganizationId(0L);
        return baseDataApi.addAdmin(adminBo);
    }

    /**
     * 后台修改人员
     *
     * @param adminBo the admin bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2024 -07-16 14:45:34
     */
    @PostMapping(value = "/admin/update")
    public AjaxResult updateAdmin(@RequestBody AdminBo adminBo) {
        // 更新账号信息
        AjaxResult ajaxResult = baseDataApi.updateAdmin(adminBo);
        // 账号更新成功后同步修改代理商信息（代理商名称，手机号）
        if(ajaxResult.isSuccess()){
            CmInfoBo cmInfoBo = new CmInfoBo();
            cmInfoBo.setAdminOid(adminBo.getOid());
            cmInfoBo.setRealName(adminBo.getAdminName());
            cmInfoBo.setPhone(adminBo.getPhone());
            cmInfoApi.updateCmInfoByAdmin(cmInfoBo);
        }
        return ajaxResult;
    }

    /**
     * 后台删除人员
     *
     * @param adminId the admin id
     * @return the ajax result
     * <AUTHOR>
     * @date 2024 -07-16 14:45:36
     */
    @GetMapping(value = "/admin/delete")
    public AjaxResult delAdmin(@RequestParam("adminId") Long adminId) {
        return baseDataApi.delAdmin(adminId);
    }

    /**
     * 后台查询人员
     *
     * @param adminId the admin id
     * @return the admin detail
     */
    @GetMapping(value = "/admin/detail")
    public AjaxResult<AdminVo> getAdminDetail(@RequestParam("adminId") Long adminId) {
        return baseDataApi.getAdminDetail(adminId);
    }

    /**
     * 启用禁用后台人员
     *
     * @param adminId the admin id
     * @param status the status
     * @return the ajax result
     * <AUTHOR>
     * @date 2024 -07-16 14:45:42
     */
    @GetMapping(value = "/admin/lock")
    public AjaxResult enableAdmin(@RequestParam("adminId") Long adminId, @RequestParam("status") Integer status) {
        return baseDataApi.enableAdmin(adminId, status);
    }

    /**
     * 后台重置密码
     *
     * @param adminId the admin id
     * @param initPwd the init pwd
     * @return the ajax result
     * <AUTHOR>
     * @date 2024 -07-16 14:45:44
     */
    @GetMapping(value = "/admin/reset-pwd")
    public AjaxResult resetAdminPassword(@RequestParam("adminId") Long adminId,
        @RequestParam("initPwd") String initPwd) {
        return baseDataApi.resetAdminPassword(adminId, initPwd);
    }

    /**
     * 查询所有新时代运营的角色（暂时写常量值）
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -07-16 15:08:31
     */
    @GetMapping(value = "/yanx-roles")
    public AjaxResult yanxRoles() {
        return baseDataApi.getAllYanxRoles();
    }

    /**
     * 获取新时代案例持有者角色
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/9 9:45
     **/
    @GetMapping("/yanx/reception-roles")
    public AjaxResult yanxReceptionRoles() {
        return baseDataApi.receptionRoles();
    }

    /**
     * 获取新时代案例持有者角色
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/9 9:45
     **/
    @GetMapping("/yanx/expert-roles")
    public AjaxResult yanxExpertRoles() {
        return baseDataApi.expertRoles();
    }

    /**
     * 获取新时代专家助理角色
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/9 9:45
     **/
    @GetMapping("/yanx/expert-assistant-roles")
    public AjaxResult yanxExpertAssistantRoles() {
        return baseDataApi.expertAssistantRoles();
    }
}
