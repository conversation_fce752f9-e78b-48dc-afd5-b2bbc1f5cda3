package com.fh.yanx.manage.controller;

import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.fh.yanx.manage.listener.TeacherBatchImportListener;
import com.fh.yanx.service.enums.ReceptionRoleEnum;
import com.fh.yanx.service.enums.TeacherType;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.fh.yanx.service.user.entity.bo.TeacherImportExcelBo;
import com.light.core.entity.AjaxResult;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.user.entity.bo.UserTransferBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023-07-20 14:29
 */
@RestController
@RequestMapping("/yanx/teacher")
@Api(value = "人员管理", tags = "人员管理")
@Slf4j
public class TeacherController {
    @Resource
    BaseDataApi baseDataApi;

    @Value("${teacher.batchImport.template:}")
    private String teacherBatchImportFilePath;

    /**
     * 添加教师
     *
     * @param teacherBo 教师信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "添加教师", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public AjaxResult addTeacher(@RequestBody TeacherBo teacherBo) throws Exception {
        return baseDataApi.addTeacher(teacherBo);
    }

    /**
     * 修改教师信息
     *
     * @param teacherBo 教师信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "修改教师信息", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updateTeacher(@RequestBody TeacherBo teacherBo) throws Exception {
        return baseDataApi.updateTeacher(teacherBo);
    }

    /**
     * 根据userOid查看教师信息详情
     *
     * @param userOid the teacher id
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "根据userOid查看教师信息详情", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/detail-byuseroid", method = RequestMethod.GET)
    public AjaxResult getTeacherDetail(@NotNull(message = "请选择数据") @RequestParam("userOid") String userOid) throws Exception {
        return baseDataApi.getTeacherDetail(userOid);
    }

    /**
     * 删除教师
     *
     * @param teacherId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/20 15:19
     **/
    @GetMapping("/del-teacher")
    @ApiOperation(value = "删除教师", notes = "删除教师")
    public AjaxResult delTeacher(@NotNull(message = "请选择数据") @RequestParam("teacherId") Long teacherId) {
        return baseDataApi.delTeacher(teacherId);
    }

    /**
     * 教师列表
     *
     * @param teacherConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/21 11:22
     **/
    @PostMapping("/teacher-list")
    @ApiOperation(value = "教师列表", notes = "教师列表")
    public AjaxResult teacherList(@RequestBody TeacherConditionBo teacherConditionBo) {
        return baseDataApi.getTeacherList(teacherConditionBo);
    }

    /**
     * 重置密码
     *
     * @param userOid
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/21 13:48
     **/
    @GetMapping("/reset-password")
    @ApiOperation(value = "重置密码", notes = "重置密码")
    public AjaxResult resetPasswordByUserOid(@NotNull(message = "请选择数据") @RequestParam("userOid") String userOid) {
        return baseDataApi.resetPasswordByUserOid(userOid);
    }

    /**
     * 教师转出
     *
     * @param userTransferBo the user transfer bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "教师转出", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/remove-out", method = RequestMethod.POST)
    public AjaxResult removeOutTeacher(@RequestBody UserTransferBo userTransferBo) throws Exception {
        return baseDataApi.removeOutTeacher(userTransferBo);
    }

    @ApiOperation(value = "教师导入模板下载", httpMethod = "GET")
    @GetMapping("/teacher-template/download")
    public AjaxResult downloadTeacherTemplate(HttpServletRequest request, HttpServletResponse response) {
        BufferedInputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        try {
            inputStream = new BufferedInputStream(new FileInputStream(teacherBatchImportFilePath));
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            response.reset();
            response.setContentType("application/octet-stream; charset=utf-8");
            //重新设置文件名
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("新时代教师导入模板.xlsx", "utf-8"));
            response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.addHeader("Access-Control-Allow-Methods", "*");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
        } catch (Exception e) {
            log.warn("文件读取出错{}", e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error("流关闭异常，e:" + e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e) {
                    log.error("流关闭异常，e：" + e);
                }
            }
        }
        return AjaxResult.success();
    }

    @ApiOperation(value = "导入教师", httpMethod = "POST")
    @PostMapping("/import-teacher")
    public AjaxResult importTeacher(@RequestParam("file") MultipartFile file, @RequestParam("organizationId") Long organizationId) {
        if (file == null) {
            return AjaxResult.fail("导入文件为空");
        }
        if (file.getSize() > (1024 * 1024 * 10L)) {
            return AjaxResult.fail("文件大小超过10M，请分批导入。");
        }
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            return AjaxResult.fail(e.getMessage());
        }
        InputStream inputStream1 = new BufferedInputStream(inputStream);
        TeacherBatchImportListener listener = new TeacherBatchImportListener();
        try {
            ExcelReader excelReader = new ExcelReader(inputStream1, ExcelTypeEnum.XLSX, null, listener);
            excelReader.read(new Sheet(1, 3, TeacherImportExcelBo.class));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.fail("读取数据异常，请按《新时代教师导入模板》规范填写后上传。");
        }
        List<TeacherBo> teacherBos = listener.getTeacherBos();
        if (CollectionUtils.isEmpty(teacherBos)) {
            if (listener.getErrorCount() <= 0) {
                return AjaxResult.fail("导入模板数据为空，请检查");
            }
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.setMsg("成功导入0条数据，另有" + listener.getErrorCount() + "条数据由于数据填写有误，导入失败");
            return ajaxResult;
        }
        for (TeacherBo teacherBo : teacherBos) {
            teacherBo.getUser().setOrganizationId(organizationId);
            teacherBo.getUser().setRoleIds(Stream.of(ReceptionRoleEnum.SCHOOL_TEACHER.getRoleId()).collect(Collectors.toList()));
            teacherBo.setType(TeacherType.TEACH_USER.getCode());
        }
        AjaxResult ajaxResult = baseDataApi.addBatchTeacher(teacherBos);
        if (ajaxResult.isSuccess()) {
            return AjaxResult.success("成功导入" + teacherBos.size() + "条数据，另有" + listener.getErrorCount() + "条数据由于数据填写有误，导入失败");
        }
        return ajaxResult;
    }

}
