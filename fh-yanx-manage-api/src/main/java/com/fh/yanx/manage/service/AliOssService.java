package com.fh.yanx.manage.service;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.google.common.collect.Maps;
import com.light.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 阿里ossservice
 *
 * <AUTHOR>
 * @date 2023/7/19 17:51
 */
@Slf4j
@Service
public class AliOssService {

    @Value("${oss.endpoint:}")
    private String endpoint;
    @Value("${oss.access.key.id:}")
    private String accessKeyId;
    @Value("${oss.access.key.secret:}")
    private String accessKeySecret;
    @Value("${oss.access.bucket.name:}")
    private String bucketName;
    @Value("${sts.endpoint:}")
    private String stsEndpoint;
    @Value("${sts.role:}")
    private String stsRoleArn;
    @Value("${sts.region:}")
    private String stsRegion;

    /**
     * 初始化client
     *
     * @return
     */
    public OSSClient initOssClient() {
        OSSClient ossClient = (OSSClient)new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        return ossClient;
    }

    /**
     * 生成前端需要的签名，适用于：服务端签名直传
     *
     * @param objectName the dir
     * @return post object params
     */
    public Map<String, Object> getPostObjectParams(String dir) {
        Map<String, Object> respMap = new LinkedHashMap();
        // 限制参数的生效时间，单位为分钟，默认值为20。
        int expireTime = 60;
        // 限制上传文件的大小，单位为MB，默认值为100。
        int maxSize = 1024 * 2048;
        // 设置上传到OSS文件的前缀，可置空此项。置空后，文件将上传至Bucket的根目录下。
        // 如果值为"test"那么前端的key参数必须以"test"开头，如test/*、test1.jpg、test/comment/11.jpg

        // 创建OSSClient实例。
        OSSClient ossClient = initOssClient();
        try {
            long expireEndTime = System.currentTimeMillis() + expireTime * 1000 * 60;
            Date expiration = new Date(expireEndTime);
            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, **********);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

            String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes("utf-8");
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            respMap.put("accessKeyId", ossClient.getCredentialsProvider().getCredentials().getAccessKeyId());
            respMap.put("policy", encodedPolicy);
            respMap.put("signature", postSignature);
            respMap.put("host", "http://" + bucketName + "." + endpoint);

            respMap.put("expire", expireEndTime / 1000);
            respMap.put("dir", dir);
        } catch (Exception e) {
            log.error("getPostObjectParams", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return respMap;
    }

    /**
     * 获取文件的临时访问url
     *
     * @return
     */
    public String getTempFileUrl(String objectName) {
        if (StringUtils.isBlank(objectName)) {
            return "";
        }
        OSSClient ossClient = initOssClient();
        try {
            // 设置签名URL过期时间，单位为毫秒。默认1H
            Date expiration = new Date(new Date().getTime() + 3600 * 1000);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
            if (url != null) {
                return url.toString();
            }
        } catch (Exception e) {
            log.error("getTempFileUrl error:", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return "";
    }

    /**
     * 获取文件的临时访问url-用于下载
     *
     * @return
     */
    public String getTempFileUrlDownload(String objectName) {
        if (StringUtils.isBlank(objectName)) {
            return "";
        }
        OSSClient ossClient = initOssClient();
        try {
            // 设置签名URL过期时间，单位为毫秒。默认1H
            Date expiration = new Date(new Date().getTime() + 3600 * 1000);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            GeneratePresignedUrlRequest request =
                new GeneratePresignedUrlRequest(bucketName, objectName, HttpMethod.GET);
            request.setExpiration(expiration);
            URL url = ossClient.generatePresignedUrl(request);
            if (url != null) {
                return url.toString();
            }
        } catch (Exception e) {
            log.error("getTempFileUrlDownload error:", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return "";
    }

    /**
     * 获取临时的STS信息
     *
     * @return sts
     */
    public Map<String, Object> getSTS() {
        // RoleSessionName 角色会话名称，自定义参数
        String roleSessionName = "session-name";// 自定义即可
        // 定制你的policy
        String policy =
            "{\n" + "  \"Version\": \"1\",\n" + "  \"Statement\": [\n" + "    {\n" + "      \"Action\": \"vod:*\",\n"
                + "      \"Resource\": \"*\",\n" + "      \"Effect\": \"Allow\"\n" + "    }\n" + "  ]\n" + "}";
        try {
            AssumeRoleResponse response = assumeRole(accessKeyId, accessKeySecret, stsRoleArn, roleSessionName, policy);
            Map<String, Object> resultMap = Maps.newHashMapWithExpectedSize(5);
            resultMap.put("expiration", response.getCredentials().getExpiration());
            resultMap.put("accessKeyId", response.getCredentials().getAccessKeyId());
            resultMap.put("accessKeySecret", response.getCredentials().getAccessKeySecret());
            resultMap.put("securityToken", response.getCredentials().getSecurityToken());
            resultMap.put("requestId", response.getRequestId());
            resultMap.put("region", stsRegion);
            return resultMap;
        } catch (Exception e) {
            log.error("Failed to get a token.");
        }
        return null;
    }

    /**
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param roleArn
     * @param roleSessionName
     * @param policy
     * @return
     * @throws ClientException
     */
    private AssumeRoleResponse assumeRole(String accessKeyId, String accessKeySecret, String roleArn,
        String roleSessionName, String policy) throws ClientException {
        try {
            // 构造default profile（参数留空，无需添加Region ID）
            /*
            说明：当设置SysEndpoint为sts.aliyuncs.com时，regionId可填可不填；反之，regionId必填，根据使用的服务区域填写，例如：cn-shanghai
            详情参考STS各地域的Endpoint。
             */
            IClientProfile profile = DefaultProfile.getProfile(stsRegion, accessKeyId, accessKeySecret);
            // 用profile构造client
            DefaultAcsClient client = new DefaultAcsClient(profile);
            // 创建一个 AssumeRoleRequest 并设置请求参数
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setSysEndpoint(stsEndpoint);
            request.setSysMethod(MethodType.POST);
            request.setRoleArn(roleArn);
            request.setRoleSessionName(roleSessionName);
            request.setPolicy(policy);
            // 发起请求，并得到response
            final AssumeRoleResponse response = client.getAcsResponse(request);
            return response;
        } catch (ClientException e) {
            throw e;
        } catch (ServerException e) {
            throw new RuntimeException(e);
        } catch (com.aliyuncs.exceptions.ClientException e) {
            throw new RuntimeException(e);
        }
    }
}
