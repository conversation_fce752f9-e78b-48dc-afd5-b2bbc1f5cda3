package com.fh.yanx.manage.controller;

import com.fh.yanx.service.activ.api.ActivInfoApi;
import com.fh.yanx.service.activ.api.ActivInfoRecordApi;
import com.fh.yanx.service.activ.api.ActivInfoRecordViewApi;
import com.fh.yanx.service.activ.api.ActivInfoScheduleApi;
import com.fh.yanx.service.activ.api.ActivInfoViewPermissionApi;
import com.fh.yanx.service.activ.entity.bo.*;
import com.fh.yanx.service.activ.entity.vo.ActivInfoRecordVo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoScheduleVo;
import com.fh.yanx.service.activ.entity.vo.ActivInfoViewPermissionVo;
import com.fh.yanx.service.enums.ActivInfoViewType;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动管理
 *
 * <AUTHOR>
 * @date 2023-07-04 14:49
 */
@RestController
@RequestMapping("/activ/info")
@Slf4j
@Api(value = "活动管理", tags = "活动管理")
public class ActivInfoController {
    @Resource
    private ActivInfoApi activInfoApi;
    @Resource
    private ActivInfoRecordApi activInfoRecordApi;
    @Resource
    private ActivInfoScheduleApi activInfoScheduleApi;
    @Resource
    private ActivInfoViewPermissionApi activInfoViewPermissionApi;
    @Resource
    private ActivInfoRecordViewApi activInfoRecordViewApi;

    /**
     * 分页获取活动列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:02
     **/
    @PostMapping("/page/list")
    @ApiOperation(value = "分页获取活动列表", notes = "分页获取活动列表")
    public AjaxResult activPageList(@RequestBody ActivInfoConditionBo conditionBo) {
        return activInfoApi.getActivInfoPageListByCondition(conditionBo);
    }

    /**
     * 获取活动列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:03
     **/
    @PostMapping("/list")
    @ApiOperation(value = "获取活动列表", notes = "获取活动列表")
    public AjaxResult activList(@RequestBody ActivInfoConditionBo conditionBo) {
        return activInfoApi.getActivInfoListByCondition(conditionBo);
    }

    /**
     * 新增活动
     *
     * @param activInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:11
     **/
    @PostMapping("/add-activ")
    @ApiOperation(value = "新增活动", notes = "新增活动")
    public AjaxResult addActivInfo(@RequestBody ActivInfoBo activInfoBo) {
        return activInfoApi.addActivInfo(activInfoBo);
    }

    /**
     * 编辑活动
     *
     * @param activInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:12
     **/
    @PostMapping("/update-activ")
    @ApiOperation(value = "编辑活动", notes = "编辑活动")
    public AjaxResult updateActivInfo(@RequestBody ActivInfoBo activInfoBo) {
        return activInfoApi.updateActivInfo(activInfoBo);
    }

    /**
     * 获取活动详情
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:14
     **/
    @GetMapping("/detail")
    @ApiOperation(value = "活动详情", notes = "活动详情")
    public AjaxResult getActivInfoDetail(@RequestParam("activId") Long activId) {
        return activInfoApi.getDetail(activId, null);
    }

    /**
     * 保存活动内容
     *
     * @param activInfoBo 
     * @return com.light.core.entity.AjaxResult 
     * <AUTHOR>
     * @date 2023/7/4 15:30
     **/
    @PostMapping("/save-activ-record")
    @ApiOperation(value = "保存活动内容", notes = "保存活动内容")
    public AjaxResult saveActivRecord(@RequestBody ActivInfoBo activInfoBo) {
         return activInfoRecordApi.saveActivInfoRecords(activInfoBo);
    }

    /**
     * 活动内容列表
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/4 15:32
     **/
    @GetMapping("/activ-record/list")
    @ApiOperation(value = "活动内容列表", notes = "活动内容列表")
    public AjaxResult getActivInfoRecordList(@RequestParam("activId") Long activId) {
        Map<String, Object> map = new HashMap<>();
        ActivInfoRecordConditionBo conditionBo = new ActivInfoRecordConditionBo();
        conditionBo.setActivId(activId);
        AjaxResult<List<ActivInfoRecordVo>> ajaxResult = activInfoRecordApi.getActivInfoRecordListByCondition(conditionBo);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        map.put("activRecordList", ajaxResult.getData());
        ActivInfoViewPermissionConditionBo permissionConditionBo = new ActivInfoViewPermissionConditionBo();
        permissionConditionBo.setActivId(activId);
        permissionConditionBo.setViewType(ActivInfoViewType.ACTIV_RECORD.getCode());
        AjaxResult<List<ActivInfoViewPermissionVo>> permissionResult = activInfoViewPermissionApi
                .getActivInfoViewPermissionListByCondition(permissionConditionBo);
        if (permissionResult.isFail()) {
            map.put("activViewPermissionList", new ArrayList<>());
        } else {
            map.put("activViewPermissionList", permissionResult.getData());
        }
        return AjaxResult.success(map);
    }

    /**
     * 上下架
     *
     * @param activInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/5 9:28
     **/
    @PostMapping("/update-activ-type")
    @ApiOperation(value = "上下架", notes = "上下架")
    public AjaxResult updateActivType(@RequestBody ActivInfoBo activInfoBo) {
        return activInfoApi.updateActivType(activInfoBo);
    }

    /**
     * 保存活动日程
     *
     * @param activInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/28 14:51
     **/
    @PostMapping("/save-activ-schedule")
    @ApiOperation(value = "保存活动日程", notes = "保存活动日程")
    public AjaxResult saveActivInfoSchedule(@RequestBody ActivInfoBo activInfoBo) {
        return activInfoScheduleApi.saveActivSchedules(activInfoBo);
    }

    /**
     * 活动日程列表
     *
     * @param activId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/31 16:48
     **/
    @GetMapping("/activ-schedule/list")
    @ApiOperation(value = "活动日程列表", notes = "活动日程列表")
    public AjaxResult getActivInfoScheduleList(@RequestParam("activId") Long activId) {
        Map<String, Object> map = new HashMap<>();
        ActivInfoScheduleConditionBo scheduleConditionBo = new ActivInfoScheduleConditionBo();
        scheduleConditionBo.setActivId(activId);
        AjaxResult<List<ActivInfoScheduleVo>> scheduleResult = activInfoScheduleApi
                .getActivInfoScheduleListByCondition(scheduleConditionBo);
        if (scheduleResult.isFail()) {
            return scheduleResult;
        }
        map.put("activScheduleList", scheduleResult.getData());
        ActivInfoViewPermissionConditionBo permissionConditionBo = new ActivInfoViewPermissionConditionBo();
        permissionConditionBo.setActivId(activId);
        permissionConditionBo.setViewType(ActivInfoViewType.ACTIV_SCHEDULE.getCode());
        AjaxResult<List<ActivInfoViewPermissionVo>> permissionResult = activInfoViewPermissionApi
                .getActivInfoViewPermissionListByCondition(permissionConditionBo);
        if (permissionResult.isFail()) {
            map.put("activViewPermissionList", new ArrayList<>());
        } else {
            map.put("activViewPermissionList", permissionResult.getData());
        }
        return AjaxResult.success(map);
    }

    /**
     * 资源观看统计
     *
     * @param activInfoBo the activ info bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -08-16 14:06:05
     */
    @PostMapping("/view-statistics")
    @ApiOperation(value = "资源观看统计", notes = "资源观看统计")
    public AjaxResult viewStatistics(@RequestBody ActivInfoRecordViewConditionBo activInfoRecordViewConditionBo) {
        return activInfoRecordViewApi.getActivInfoRecordViewStatistics(activInfoRecordViewConditionBo);
    }
}
