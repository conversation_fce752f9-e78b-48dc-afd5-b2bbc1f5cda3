package com.fh.yanx.manage.factory;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-12-10  11:50
 */
@Component
public class SpringJobBeanFactory implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringJobBeanFactory.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) throws BeansException {
        if (applicationContext == null) {
            return  null;
        }
        return (T)applicationContext.getBean(name);
    }

    public static <T> T getBean(Class<T> name) throws BeansException {
        if (applicationContext == null) {
            return null;
        }
        return applicationContext.getBean(name);
    }
}
