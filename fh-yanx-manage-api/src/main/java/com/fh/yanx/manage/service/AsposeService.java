package com.fh.yanx.manage.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.aspose.words.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * aspose相关服务工具service
 *
 * <AUTHOR>
 * @date 2024/3/14 11:57
 */
@Slf4j
@Service
public class AsposeService {
    private static InputStream license;

    public static final String PARAM_MATCH = "\\{[a-zA-Z]+\\}";

    /**
     * 获取license
     *
     * @return
     */
    public boolean getLicense() throws Exception {
        boolean result = false;
        license = AsposeService.class.getClassLoader().getResourceAsStream("license.xml");
        if (license != null) {
            License aposeLic = new License();
            aposeLic.setLicense(license);
            result = true;
        }
        return result;
    }

    /**
     * 替换文档中的参数
     *
     * @param paramMap the param map
     * @param doc      the doc
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2024 -03-14 13:51:32
     */
    public void replaceText(Map<String, String> paramMap, Document doc) throws Exception {
        FindReplaceOptions opt = new FindReplaceOptions();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            String key = String.format("{%s}", entry.getKey());
            String value = Objects.isNull(entry.getValue()) ? "" : entry.getValue();
            doc.getRange().replace(key, value, opt);
        }
        int replace = doc.getRange().replace(Pattern.compile(PARAM_MATCH), "", opt);
        if (replace > 0) {
            log.error("替换失败，参数:{}", JSON.toJSONString(paramMap));
        }
    }

    /**
     * 替换文档中的参数
     *
     * @param paramMap          参数映射
     * @param doc               文档对象
     * @param imageUrlParamKeys 图片URL占位符key集合
     * @param removeFontStyle   是否移除字体样式
     * @throws Exception 异常
     */
    public void replaceText(Map<String, String> paramMap, Document doc, Set<String> imageUrlParamKeys,
        Boolean removeFontStyle) throws Exception {
        if (paramMap == null || paramMap.isEmpty() || doc == null) {
            return;
        }

        // 预编译正则表达式
        FindReplaceOptions opt = new FindReplaceOptions();
        DocumentBuilder builder = new DocumentBuilder(doc);

        // 一次性获取所有段落
        NodeCollection<Paragraph> paragraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);

        // 在 replaceText 方法中添加段落缓存结构
        Map<String, List<Paragraph>> placeholderToParagraphs = new HashMap<>();

        // 一次性遍历所有段落，建立占位符和段落的映射关系
        for (Paragraph para : (Iterable<Paragraph>)paragraphs::iterator) {
            String text = para.getText();
            for (String key : paramMap.keySet()) {
                String placeholder = String.format("{%s}", key);
                if (text.contains(placeholder)) {
                    placeholderToParagraphs.computeIfAbsent(placeholder, k -> new ArrayList<>()).add(para);
                }
            }
        }

        // 获取页面设置和页面宽度
        PageSetup pageSetup = builder.getPageSetup();
        double pageWidth = pageSetup.getPageWidth(); // 单位：pt
        double maxImageWith = pageWidth * 2 / 3;

        // 将图片处理集合转换为 HashSet 提高查找效率
        Set<String> imageKeys =
            CollectionUtil.isNotEmpty(imageUrlParamKeys) ? new HashSet<>(imageUrlParamKeys) : Collections.emptySet();

        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            long start = System.currentTimeMillis();
            String key = String.format("{%s}", entry.getKey());
            String value = Optional.ofNullable(entry.getValue()).map(String::trim).orElse("");

            boolean isHtmlContent = isHtml(value);
            boolean isImageContent = imageKeys.contains(entry.getKey());

            // 根据内容类型处理
            if (isHtmlContent) {
                processHtmlContent(builder, placeholderToParagraphs, key, value, removeFontStyle, maxImageWith);
            } else if (isImageContent) {
                processImageContent(builder, placeholderToParagraphs, key, value, opt);
            } else {
                doc.getRange().replace(key, value, opt);
            }
            long end = System.currentTimeMillis();
            log.info("courseName:{}, 替换参数:{}, 耗时：{} ms", paramMap.get("courseName"), entry.getKey(), end - start);
        }

        // 检查未被替换的占位符
        int replace = doc.getRange().replace(Pattern.compile(PARAM_MATCH), "", opt);
        if (replace > 0) {
            log.error("替换失败，参数:{}", JSON.toJSONString(paramMap));
        }

        // 检查空行并移除
        NodeCollection<Paragraph> allParagraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);
        for (Paragraph para : (Iterable<Paragraph>)allParagraphs::iterator) {
            String text = para.getText().trim();

            // 判断段落是否为空或者只包含未替换的占位符（例如："{key}"）
            boolean isEmptyOrPlaceholder = text.isEmpty() || Pattern.matches(PARAM_MATCH, text);

            // 检查段落中是否包含图片（Shape）
            boolean containsImage = false;

            // 使用 getChildNodes 获取 Shape 节点
            NodeCollection<Shape> shapes = para.getChildNodes(NodeType.SHAPE, true);
            for (Shape shape : shapes) {
                if (shape.hasImage()) {
                    containsImage = true;

                    // 检查图片宽度是否超过页面宽度
                    if (shape.getWidth() > maxImageWith) {
                        // 设置图片最大宽度为页面宽度，高度自适应
                        shape.setWidth(maxImageWith);
                        shape.setHeight(shape.getHeight() * (maxImageWith / shape.getWidth()));
                    }

                    break;
                }
            }

            // 仅当段落为空或为占位符且不包含图片时才移除
            if (isEmptyOrPlaceholder && !containsImage) {
                para.remove();
            }
        }
    }

    // 处理图片内容
    private void processImageContent(DocumentBuilder builder, NodeCollection<Paragraph> paragraphs, String key,
        String url, FindReplaceOptions opt) throws Exception {
        if (url == null)
            return;

        try (InputStream is = new URL(url).openStream()) {
            byte[] imageBytes = DocumentBuilderHelper.inputStreamToByteArray(is);

            for (Paragraph para : (Iterable<Paragraph>)paragraphs::iterator) {
                if (para.getText().contains(key)) {
                    clearParagraph(builder, para);
                    builder.insertImage(imageBytes);
                    break; // 处理完当前段落后提前退出
                }
            }
        } catch (Exception e) {
            log.error("图片插入失败，URL: {}", url, e);
            builder.getDocument().getRange().replace(key, "", opt);
        }
    }

    // 处理图片内容时直接获取对应段落
    private void processImageContent(DocumentBuilder builder, Map<String, List<Paragraph>> placeholderToParagraphs,
        String key, String url, FindReplaceOptions opt) throws Exception {
        if (StringUtil.isBlank(url))
            return;

        List<Paragraph> paras = placeholderToParagraphs.get(key);
        if (paras == null || paras.isEmpty()) {
            return;
        }

        for (Paragraph para : paras) {
            try (InputStream is = new URL(url).openStream()) {
                byte[] imageBytes = DocumentBuilderHelper.inputStreamToByteArray(is);

                if (para.getText().contains(key)) {
                    clearParagraph(builder, para);
                    builder.insertImage(imageBytes);
                    break; // 处理完当前段落后提前退出
                }
            } catch (Exception e) {
                log.error("图片插入失败，URL: {}", url, e);
                builder.getDocument().getRange().replace(key, "", opt);
            }
        }
    }

    // 处理HTML内容
    private void processHtmlContent(DocumentBuilder builder, NodeCollection<Paragraph> paragraphs, String key,
        String value, Boolean removeFontStyle) throws Exception {
        for (Paragraph para : (Iterable<Paragraph>)paragraphs::iterator) {
            if (para.getText().contains(key)) {
                clearParagraph(builder, para);

                // 移除字体样式
                if (Boolean.TRUE.equals(removeFontStyle)) {
                    value = removeFontStyle(value);
                }

                // 插入富文本
                builder.insertHtml(value);
            }
        }
    }

    // 处理HTML内容时直接获取对应段落
    private void processHtmlContent(DocumentBuilder builder, Map<String, List<Paragraph>> placeholderToParagraphs,
        String key, String value, Boolean removeFontStyle, double maxImageWith) throws Exception {
        List<Paragraph> paras = placeholderToParagraphs.get(key);
        if (paras == null || paras.isEmpty()) {
            return;
        }

        for (Paragraph para : paras) {
            clearParagraph(builder, para);

            // 移除字体样式
            if (Boolean.TRUE.equals(removeFontStyle)) {
                value = removeFontStyle(value);
            }

            // 调整图片宽度为页面的一半（添加或修改 <img> 标签的 style 属性）
            value = adjustImageWidthInHtml(value, maxImageWith);

            // 识别img路径，如果是file协议，则设置样式为display:none
            value =
                value.replaceAll("(?i)img\\s+src=[\"'](file:\\/\\/.+?)[\"']", "img src=\"$1\" style=\"display:none\"");

            // 插入富文本
            builder.insertHtml(value);
        }
    }

    // 清除段落内容
    private void clearParagraph(DocumentBuilder builder, Paragraph para) throws Exception {
        builder.moveTo(para);
        builder.writeln(); // 清空段落

        para.getRange().getFields().clear();
        NodeCollection<Run> runs = para.getRuns();
        for (Run run : runs) {
            run.remove();
        }
    }

    /**
     * 判断字符串是否为有效的HTML格式
     *
     * @param input the input
     * @return boolean
     */
    public boolean isHtml(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }

        // 更精确的HTML匹配规则
        String htmlTagPattern = "<([a-zA-Z][a-zA-Z0-9]*)(\\s+[^>]*)?>.*?</\\1>|<([a-zA-Z][a-zA-Z0-9]*)[^>]*/?>";

        // 编译正则表达式以提高性能
        Pattern pattern = Pattern.compile(htmlTagPattern, Pattern.DOTALL | Pattern.CASE_INSENSITIVE);

        // 判断输入是否符合HTML格式
        return pattern.matcher(input).find();
    }

    /**
     * 移除富文本中的字体样式
     *
     * @param value the value
     * @return the string
     * <AUTHOR>
     * @date 2024 -03-14 13:51:32
     */
    public String removeFontStyle(String value) {
        if (value == null)
            return null;

        // Step 1: 移除 <font> 标签
        String result = value.replaceAll("<font[^>]*>", "").replaceAll("</font>", "");

        // Step 2: 移除 style 中的 font-family 和 font-size
        result = result.replaceAll("(?i)(style\\s*=\\s*['\"][^'\"]*)(font-?family|font-?size)[^;]++;", "$1");

        // Step 3: 移除空的 style 属性
        result = result.replaceAll("style\\s*=\\s*['\"]['\"]", "");

        return result;
    }

    /**
     * 解析尺寸值（支持 px、% 或纯数字）。
     *
     * @param value 尺寸字符串
     * @return 解析后的像素值，若无法解析则返回默认值
     */
    private int parseSizeValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return -1;
        }

        // 移除非数字字符（如 px、% 等）
        Pattern numberPattern = Pattern.compile("\\d+");
        Matcher numberMatcher = numberPattern.matcher(value.trim());

        if (numberMatcher.find()) {
            return Integer.parseInt(numberMatcher.group(0));
        }

        return -1;
    }

    /**
     * 调整HTML内容中<img>标签的宽度样式为指定值
     *
     * @param htmlContent 原始HTML内容
     * @param targetWidth 目标宽度（单位：pt）
     * @return 修改后的HTML内容
     */
    private String adjustImageWidthInHtml(String htmlContent, double targetWidth) {
        if (htmlContent == null || targetWidth <= 0) {
            return htmlContent;
        }

        // 使用正则表达式匹配<img>标签并添加/修改style属性
        String imgTagPattern = "<img\\s+([^>]*?)>";
        Pattern pattern = Pattern.compile(imgTagPattern, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlContent);

        StringBuffer modifiedHtml = new StringBuffer();

        while (matcher.find()) {
            String imgTag = matcher.group(0);
            String attrs = matcher.group(1);

            // 检查是否已经有style属性
            Pattern stylePattern = Pattern.compile("\\s+style\\s*=\\s*(['\"])(.*?)\\1", Pattern.CASE_INSENSITIVE);
            Matcher styleMatcher = stylePattern.matcher(attrs);
            String styleValue = String.format("width: %.2fpt;", targetWidth);

            if (styleMatcher.find()) {
                // 替换已有的style属性值
                String newStyle = styleMatcher.group(2) + " " + styleValue;
                attrs = stylePattern.matcher(attrs).replaceFirst(" style=$1" + newStyle + "$1");
            } else {
                // 添加新的style属性
                attrs += String.format(" style=\"%s\"", styleValue);
            }

            String newImgTag = "<img " + attrs + ">";
            matcher.appendReplacement(modifiedHtml, Matcher.quoteReplacement(newImgTag));
        }
        matcher.appendTail(modifiedHtml);

        return modifiedHtml.toString();
    }

}
