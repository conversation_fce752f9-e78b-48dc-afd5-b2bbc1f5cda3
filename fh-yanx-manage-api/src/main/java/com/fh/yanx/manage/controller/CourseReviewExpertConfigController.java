package com.fh.yanx.manage.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.yanx.manage.consts.ConstString;
import com.fh.yanx.service.course.api.CourseHomeConfigApi;
import com.fh.yanx.service.course.api.CourseVerifyLogApi;
import com.fh.yanx.service.course.api.PCourseCasesApi;
import com.fh.yanx.service.course.entity.bo.*;
import com.fh.yanx.service.courseReviewExpertConfig.api.CourseReviewExpertConfigApi;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.bo.CourseReviewExpertConfigConditionBo;
import com.fh.yanx.service.courseReviewExpertConfig.entity.vo.CourseReviewExpertConfigVo;
import com.fh.yanx.service.enums.CourseBestVerifyType;
import com.fh.yanx.service.enums.IsExcellentType;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 父类或者接口链接：
 * <p>
 * 描述：
 * 创建人: 杨圣君
 * 创建时间: 2024/12/02
 */

@Slf4j
@RestController
@RequestMapping("/expert/config")
@Api(value = "", tags = "新时代课程审核专家配置管理")
public class CourseReviewExpertConfigController {
    @Resource
    private CourseReviewExpertConfigApi courseReviewExpertConfigApi;


    /**
     * 列出课程案例页面
     * <p>
     * 传入参数: @param courseReviewExpertConfigConditionBo 课程回顾专家配置条件 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */

    @PostMapping("/list-page")
    @ApiOperation(value = "审核专家列表-分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listCourseCasesPage(@RequestBody CourseReviewExpertConfigConditionBo courseReviewExpertConfigConditionBo) {
        return courseReviewExpertConfigApi.getCourseReviewExpertConfigPageListByCondition(courseReviewExpertConfigConditionBo);
    }


    /**
     * 列出课程案例
     * <p>
     * 传入参数: @param pCourseCasesConditionBo P 课程案例条件 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */

    @PostMapping("/list")
    @ApiOperation(value = "审核专家列表-不分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult<List<CourseReviewExpertConfigVo>> listCourseCases(@RequestBody CourseReviewExpertConfigConditionBo courseReviewExpertConfigConditionBo) {
        return courseReviewExpertConfigApi.getCourseReviewExpertConfigListByCondition(courseReviewExpertConfigConditionBo);
    }


    /**
     * 详细审核专家
     * <p>
     * 传入参数: @param id 身份证
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */

    @GetMapping("/detail")
    @ApiOperation(value = "审核专家详情", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getDetail(@RequestParam("id") Long id) {
        return courseReviewExpertConfigApi.getDetail(id);
    }

    /**
     * 删除审核专家
     * <p>
     * 传入参数: @param id 身份证
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */

    @GetMapping("/delete")
    @ApiOperation(value = "删除审核专家", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult delete(@RequestParam("id") Long id) {
        return courseReviewExpertConfigApi.delete(id);
    }


    /**
     * 传入参数: @param courseReviewExpertConfigBo 课程审核专家配置 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */

    @PostMapping("/updateCourseReviewExpertConfig")
    @ApiOperation(value = "修改审核专家", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateCourseReviewExpertConfig(@RequestBody CourseReviewExpertConfigBo courseReviewExpertConfigBo) {
        return courseReviewExpertConfigApi.updateCourseReviewExpertConfig(courseReviewExpertConfigBo);
    }

    /**
     * 传入参数: @param courseReviewExpertConfigBo 课程审核专家配置 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */

    @PostMapping("/addCourseReviewExpertConfig")
    @ApiOperation(value = "新增审核专家", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addCourseReviewExpertConfig(@RequestBody List<CourseReviewExpertConfigBo> courseReviewExpertConfigBo) {
        return courseReviewExpertConfigApi.addCourseReviewExpertConfig(courseReviewExpertConfigBo);
    }

}
