package com.fh.yanx.manage.controller;

import com.fh.yanx.service.banner.api.BannerInfoApi;
import com.fh.yanx.service.banner.entity.bo.BannerInfoBo;
import com.fh.yanx.service.banner.entity.bo.BannerInfoConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * banner管理
 *
 * <AUTHOR>
 * @date 2023-10-24 17:26
 */
@RestController
@RequestMapping("/banner/manage")
@Slf4j
@Api(value = "banner管理", tags = "banner管理")
public class BannerManageController {
    @Resource
    BannerInfoApi bannerInfoApi;

    /**
     * 新增banner
     *
     * @param bannerInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/24 17:32
     **/
    @ApiOperation(value = "新增banner", notes = "新增banner")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BannerInfoBo bannerInfoBo) {
        return bannerInfoApi.addBanner(bannerInfoBo);
    }

    /**
     * 编辑banner
     *
     * @param bannerInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/24 17:33
     **/
    @ApiOperation(value = "编辑banner", notes = "编辑banner")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody BannerInfoBo bannerInfoBo) {
        return bannerInfoApi.updateBanner(bannerInfoBo);
    }

    /**
     * banner详情
     *
     * @param id
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/24 17:35
     **/
    @ApiOperation(value = "banner详情", notes = "banner详情")
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam("id") Long id) {
        return bannerInfoApi.getDetail(id);
    }

    /**
     * 删除banner
     *
     * @param id
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/24 17:36
     **/
    @ApiOperation(value = "删除banner", notes = "删除banner")
    @GetMapping("/delete")
    public AjaxResult delete(@RequestParam("id") Long id) {
        return bannerInfoApi.delete(id);
    }

    /**
     * banner列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/24 17:37
     **/
    @ApiOperation(value = "banner列表", notes = "banner列表")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody BannerInfoConditionBo condition) {
        return bannerInfoApi.getBannerListByCondition(condition);
    }

    /**
     * banner分页列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/24 17:38
     **/
    @ApiOperation(value = "banner分页列表", notes = "banner分页列表")
    @PostMapping("/page")
    public AjaxResult page(@RequestBody BannerInfoConditionBo condition) {
        return bannerInfoApi.getBannerPageListByCondition(condition);
    }

    /**
     * 修改banner状态
     *
     * @param bannerInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/10/25 10:06
     **/
    @ApiOperation(value = "修改banner状态", notes = "修改banner状态")
    @PostMapping("/change-state")
    public AjaxResult changeState(@RequestBody BannerInfoBo bannerInfoBo) {
        return bannerInfoApi.changeState(bannerInfoBo);
    }

}
