package com.fh.yanx.manage.controller;

import com.fh.yanx.manage.bo.OssBo;
import com.fh.yanx.manage.service.AliOssService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 阿里oss的路由
 *
 * <AUTHOR>
 * @date 2023/7/19 18:12
 */
@RequestMapping("/ali-oss")
@Slf4j
@Api(value = "阿里oss路由", tags = "阿里oss路由")
@RestController
public class AliOssController {
    @Autowired
    private AliOssService aliOssService;

    /**
     * 获取前端直传oss用到的签名，前端获取到签名是什么就必须传到这个下面去
     *
     * @param dir the dir,对象的全限定路径；例如 file/
     * @return index topic
     */
    @ApiOperation("获取签名")
    @GetMapping("/sign")
    public AjaxResult sign(@RequestParam("dir") String dir) {
        Map<String, Object> postObjectParams = aliOssService.getPostObjectParams(dir);
        return AjaxResult.success(postObjectParams);
    }

    /**
     * 获取文件的临时预览地址
     *
     * @param ossBo the oss bo
     * @return index topic
     */
    @ApiOperation("获取文件的临时地址")
    @PostMapping("/file-preview")
    public AjaxResult filePreview(@RequestBody OssBo ossBo) {
        String fileUrl = aliOssService.getTempFileUrl(ossBo.getDirFileFullPath());
        return AjaxResult.success(fileUrl);
    }

    /**
     * 获取文件的下载地址
     *
     * @param ossBo the oss bo
     * @return index topic
     */
    @ApiOperation("获取文件的下载地址")
    @PostMapping("/file-download")
    public AjaxResult fileDownload(@RequestBody OssBo ossBo) {
        String fileUrl = aliOssService.getTempFileUrlDownload(ossBo.getDirFileFullPath());
        return AjaxResult.success(fileUrl);
    }

    /**
     * 获取sts信息
     *
     * @param ossBo the oss bo
     * @return index topic
     */
    @ApiOperation("获取sts")
    @PostMapping("/sts")
    public AjaxResult sts() {
        Map<String, Object> sts = aliOssService.getSTS();
        return AjaxResult.success(sts);
    }

}
