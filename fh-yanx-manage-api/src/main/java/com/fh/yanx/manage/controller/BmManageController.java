package com.fh.yanx.manage.controller;

import com.fh.yanx.manage.consts.ConstantsRedis;
import com.fh.yanx.manage.service.SmsYmService;
import com.fh.yanx.service.bm.api.BmInfoApi;
import com.fh.yanx.service.bm.api.BmInfoConnectApi;
import com.fh.yanx.service.bm.entity.bo.BmInfoBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConditionBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectBo;
import com.fh.yanx.service.bm.entity.bo.BmInfoConnectConditionBo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoExportZdVo;
import com.fh.yanx.service.bm.entity.vo.BmInfoVo;
import com.fh.yanx.service.enums.ActivBizType;
import com.fh.yanx.service.enums.BmInfoPayType;
import com.fh.yanx.service.enums.GoodsType;
import com.fh.yanx.service.order.api.ActivOrderApi;
import com.fh.yanx.service.order.entity.bo.ActivOrderBo;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import com.light.redis.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 新时代报名活动controller(无需登录)
 * 
 * <AUTHOR>
 * @date 2023/6/8 15:04
 */
@Slf4j
@RestController
@Validated
@RequestMapping("bm/info")
@Api(value = "", tags = "新时代报名活动")
public class BmManageController {
    @Resource
    private BmInfoApi bmInfoApi;
    @Resource
    private BmInfoConnectApi bmInfoConnectApi;
    @Autowired
    private SmsYmService smsYmService;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private ActivOrderApi activOrderApi;

    /**
     * 查询报名内容-分页
     *
     * @param bmInfoConditionBo the bm info condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询报名内容", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listBmInfo(@RequestBody BmInfoConditionBo bmInfoConditionBo) {
        bmInfoConditionBo.setOrderBy("create_time desc");
        if (bmInfoConditionBo.getPayType() != null
            && bmInfoConditionBo.getPayType().equals(BmInfoPayType.ALREADY_PAY.getCode())) {
            bmInfoConditionBo.setOrderBy("payTime desc");
        }
        return bmInfoApi.getBmInfoPageListByCondition(bmInfoConditionBo);
    }

    /**
     * 查询报名统计信息
     *
     * @param bmInfoConditionBo the bm info condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/statistics")
    @ApiOperation(value = "查询报名统计信息", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getBmInfoStatistics(@RequestBody BmInfoConditionBo bmInfoConditionBo) {
        return bmInfoApi.getBmInfoStatistics(bmInfoConditionBo);
    }

    /**
     * 查询新时代文化校园报名名单人数
     *
     * @param bmInfoConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/9/19 14:20
     **/
    @PostMapping("/count-joiner")
    @ApiOperation(value = "查询新时代文化校园报名名单人数", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult countBmInfoJoiner(@RequestBody BmInfoConditionBo bmInfoConditionBo) {
        bmInfoConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return bmInfoApi.countBmInfoJoiner(bmInfoConditionBo);
    }

    /**
     * 查询报名内容详情
     *
     * @param infoId the info id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询报名内容详情", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult detailBmInfo(@RequestParam("infoId") Long infoId) {
        return bmInfoApi.getDetailWithJoiner(infoId);
    }

    /**
     * 后台新增报名
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/28 14:35
     **/
    @PostMapping("/add")
    @ApiOperation(value = "后台新增报名", notes = "后台新增报名")
    public AjaxResult addBmInfo(@RequestBody BmInfoBo bmInfoBo) {
        return bmInfoApi.addBmInfoWithJoiner(bmInfoBo);
    }

    /**
     * 更新报名记录，如果不期望同步修改订单价格，bmInfoBo.changeOrderPrice=false即可
     *
     * @param bmInfoBo the bm info bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新报名记录", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateBmInfo(@RequestBody BmInfoBo bmInfoBo) {
        // 不校验手机号是否重复
        bmInfoBo.setNotValidateMobile(true);
        return bmInfoApi.updateBmInfo(bmInfoBo);
    }

    /**
     * 更新报名记录-上传缴费凭证并生成签到码
     *
     * @param bmInfoBo the bm info bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/update-wgcode")
    @ApiOperation(value = "更新报名记录", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateBmInfoWgcode(@RequestBody BmInfoBo bmInfoBo) {
        // 不校验手机号是否重复
        bmInfoBo.setNotValidateMobile(true);
        // 同时生成签到码
        bmInfoBo.setGenerateSignCode(true);
        return bmInfoApi.updateBmInfo(bmInfoBo);
    }

    /**
     * 更新报名记录-同时更新参与人（with bm_info_joiner），只有普通活动调用
     *
     * @param bmInfoBo the bm info bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/update-wbij")
    @ApiOperation(value = "更新报名记录-同时更新参与人", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateBmInfoWbij(@RequestBody BmInfoBo bmInfoBo) {
        // 不校验手机号是否重复
        bmInfoBo.setNotValidateMobile(true);
        return bmInfoApi.updateBmInfoWbij(bmInfoBo);
    }

    /**
     * 新增联系确认表信息(会同时更新主记录表的信息)
     *
     * @param bmInfoConnectBo the bm info connect bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/add-connect")
    @ApiOperation(value = "新增联系确认表信息-同时更新报名确认状态", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addConnect(@RequestBody BmInfoConnectBo bmInfoConnectBo) {
        AjaxResult ajaxResult = bmInfoConnectApi.addBmInfoConnectWithUpdateBmInfo(bmInfoConnectBo);
        return ajaxResult;
    }

    /**
     * 查询沟通记录列表
     *
     * @param bmInfoConnectConditionBo the bm info connect condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/list-connect")
    @ApiOperation(value = "查询沟通记录列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listConnect(@RequestBody BmInfoConnectConditionBo bmInfoConnectConditionBo) {
        return bmInfoConnectApi.getBmInfoConnectListByCondition(bmInfoConnectConditionBo);
    }

    /**
     * 查询沟通记录列表-分页
     *
     * @param bmInfoConnectConditionBo the bm info connect condition bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -09-02 15:00:16
     */
    @PostMapping("/list-connect-page")
    @ApiOperation(value = "查询沟通记录列表-分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult listConnectPage(@RequestBody BmInfoConnectConditionBo bmInfoConnectConditionBo) {
        return bmInfoConnectApi.getBmInfoConnectPageListByCondition(bmInfoConnectConditionBo);
    }

    /**
     * 导出报名数据
     *
     * @param bmInfoConditionBo the bm info condition bo
     * @param response the response
     * @return void
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 /4/21 17:55
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出报名数据", httpMethod = SystemConstants.POST_REQUEST)
    public void exportUser(@RequestBody BmInfoConditionBo bmInfoConditionBo, HttpServletResponse response)
        throws IOException {
        bmInfoConditionBo.setOrderBy("bi.create_time desc");
        AjaxResult<List<BmInfoExportVo>> bmInfoListByConditionExport =
            bmInfoApi.getBmInfoListByConditionExport(bmInfoConditionBo);
        if (bmInfoListByConditionExport.isFail() || CollectionUtils.isEmpty(bmInfoListByConditionExport.getData())) {
            return;
        }
        List<BmInfoExportVo> exportList = bmInfoListByConditionExport.getData();
        ExcelUtils.exportExcel(exportList, "新时代活动报名信息", "新时代活动报名信列表", BmInfoExportVo.class, "新时代活动报名信息", response);
    }

    /**
     * 导出报名数据-征订
     *
     * @param bmInfoConditionBo the bm info condition bo
     * @param response the response
     * @return void
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 /4/21 17:55
     */
    @PostMapping("/export-zd")
    @ApiOperation(value = "导出报名数据", httpMethod = SystemConstants.POST_REQUEST)
    public void exportUserZd(@RequestBody BmInfoConditionBo bmInfoConditionBo, HttpServletResponse response)
        throws IOException {
        bmInfoConditionBo.setOrderBy("bi.create_time desc");
        if (bmInfoConditionBo.getPayType() != null
                && bmInfoConditionBo.getPayType().equals(BmInfoPayType.ALREADY_PAY.getCode())) {
            bmInfoConditionBo.setOrderBy("payTime desc");
        }
        AjaxResult<List<BmInfoExportZdVo>> bmInfoListByConditionExport =
            bmInfoApi.getBmInfoListByConditionExportZd(bmInfoConditionBo);
        if (bmInfoListByConditionExport.isFail() || CollectionUtils.isEmpty(bmInfoListByConditionExport.getData())) {
            return;
        }
        List<BmInfoExportZdVo> exportList = bmInfoListByConditionExport.getData();
        ExcelUtils.exportExcel(exportList, "新时代活动报名信息", "新时代活动报名信列表", BmInfoExportZdVo.class, "新时代活动报名信息", response);
    }

    /**
     * 修改费用
     *
     * @param bmInfoBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/2 16:30
     **/
    @PostMapping("/change-order-amount")
    @ApiOperation(value = "修改费用", notes = "修改费用")
    public AjaxResult changeOrderAmount(@RequestBody BmInfoBo bmInfoBo) {
        if (bmInfoBo.getInfoId() == null) {
            return AjaxResult.fail("报名id不能为空");
        }
        AjaxResult<BmInfoVo> ajaxResult = bmInfoApi.getDetail(bmInfoBo.getInfoId());
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        BmInfoVo bmInfoVo = ajaxResult.getData();
        if (bmInfoVo == null) {
            return AjaxResult.fail("报名信息不存在");
        }
        if (BmInfoPayType.ALREADY_PAY.getCode().equals(bmInfoVo.getPayType())) {
            return AjaxResult.fail("已缴费无法修改");
        }

        ActivOrderBo orderBo = new ActivOrderBo();
        orderBo.setGoodsId(bmInfoBo.getInfoId());
        orderBo.setGoodsType(GoodsType.ACTIV_BM.getCode());
        orderBo.setOrderAmount(bmInfoBo.getOrderAmount());
        AjaxResult result = activOrderApi.changeOrderAmount(orderBo);
        // 征订活动改价同步更新bmInfo.subBookTotalPrice
        if (result.isSuccess() && ActivBizType.SUB.getValue().equals(bmInfoVo.getActivBizType())) {
            BmInfoBo bo = new BmInfoBo();
            bo.setInfoId(bmInfoVo.getInfoId());
            bo.setSubBookTotalPrice(orderBo.getOrderAmount());
            // 不校验手机号是否重复
            bo.setNotValidateMobile(true);
            bmInfoApi.updateBmInfo(bo);
        }
        return result;
    }

    /**
     * 生成一次报名活动所有已确认人员的账号
     *
     * @param bmInfoBo 需要传参：activId
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -08-09 10:09:26
     */
    @PostMapping("/generate-account")
    @ApiOperation(value = "生成一次报名活动所有已确认人员的账号", notes = "生成一次报名活动所有已确认人员的账号")
    public AjaxResult generateAccount(@RequestBody BmInfoBo bmInfoBo) {
        return bmInfoApi.generateAccount(bmInfoBo);
    }
}
