package com.fh.yanx.manage.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fh.yanx.manage.factory.SpringJobBeanFactory;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.fh.yanx.service.user.entity.bo.TeacherImportExcelBo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.core.entity.AjaxResult;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.user.entity.bo.UserBo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-12-09  13:54
 */
@Slf4j
public class TeacherBatchImportListener extends AnalysisEventListener<TeacherImportExcelBo> {
    List<TeacherImportExcelBo> dataList = Lists.newArrayList();

    @Getter
    List<TeacherBo> teacherBos = Lists.newArrayList();

    List<String> headNames = Lists.newArrayList("填写说明： 请按照表格中的字段名称和单元格格式填写，请勿修改。","新时代教师导入模板","*姓名","*手机号","*职务","*职称");

    List<String> dynamicColumnNames = new ArrayList<>();

    private BaseDataApi baseDataApi = SpringJobBeanFactory.getBean(BaseDataApi.class);

    @Getter
    Integer errorCount = 0;

    @Override
    public void invoke(TeacherImportExcelBo data, AnalysisContext context) {
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 校验模板
        checkTemplate();
        // 转换参数
        setTeacherBos();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 在这里获取动态列的列名
        for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
            if (StringUtils.isNotBlank(entry.getValue())) {
                dynamicColumnNames.add(entry.getValue());
            }
        }
    }

    private void checkTemplate() {
        if (dynamicColumnNames.size() != headNames.size()) {
            throw new RuntimeException("模板错误");
        }
        // 校验模板
        for (int i = 0; i < headNames.size(); i++) {
            if (!headNames.get(i).equals(dynamicColumnNames.get(i))) {
                throw new RuntimeException("模板错误");
            }
        }
    }

    private void setTeacherBos() {
        try {
            List<String> dictTypes = Lists.newArrayList("title_type");
            AjaxResult dictionaryDataResult = baseDataApi.listValueByTypes(dictTypes);
            Map<String, String> titleTypeMap = Maps.newHashMap();
            if (dictionaryDataResult.isSuccess()) {
                Map resMap = JSON.parseObject(JSON.toJSONString(dictionaryDataResult.getData()), Map.class);
                List<DictionaryDataVo> titleTypes = JSONArray.parseArray(JSONArray.toJSONString(resMap.get("title_type")), DictionaryDataVo.class);
                titleTypeMap = titleTypes.stream().collect(Collectors.toMap(DictionaryDataVo::getDictLabel, DictionaryDataVo::getDictValue));
            }
            for (TeacherImportExcelBo excelBo : dataList) {
                if (StringUtils.isBlank(excelBo.getRealName())
                        || StringUtils.isBlank(excelBo.getPhone())
                        || StringUtils.isBlank(excelBo.getTitleName())
                        || StringUtils.isBlank(excelBo.getPosition())) {
                    errorCount += 1;
                    continue;
                }
                if (!titleTypeMap.containsKey(excelBo.getTitleName())) {
                    errorCount += 1;
                    continue;
                }
                TeacherBo teacherBo = new TeacherBo();
                UserBo userBo = new UserBo();
                userBo.setRealName(excelBo.getRealName());
                userBo.setPhone(excelBo.getPhone());
                userBo.setPosition(excelBo.getPosition());
                teacherBo.setUser(userBo);
                teacherBo.setTitleType(Integer.parseInt(titleTypeMap.get(excelBo.getTitleName())));
                teacherBo.setGeneratorAccount(true);
                teacherBos.add(teacherBo);
            }
        } catch (Exception e) {
            log.error("教师导入转换bo异常，e:" + e);
        }
    }
}
