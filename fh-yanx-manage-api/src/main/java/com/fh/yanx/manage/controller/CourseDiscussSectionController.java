package com.fh.yanx.manage.controller;

import com.fh.yanx.service.course.entity.bo.CourseDiscussSectionConditionBo;
import com.fh.yanx.service.course.entity.vo.CourseDiscussSectionVo;
import com.fh.yanx.service.course.service.CourseDiscussSectionApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程讨论区表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-05 17:42:00
 */
@RestController
@Validated
@RequestMapping("courseDiscussSection")
@Api(value = "", tags = "课程讨论区表接口" )
public class CourseDiscussSectionController {

    @Autowired
    private CourseDiscussSectionApiService courseDiscussSectionApiService;

    /**
     * 查询课程讨论区表列表
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @PostMapping("/getByCases")
    @ApiOperation(value = "查询某个课程案例评论")
    public AjaxResult getByCases(@RequestBody CourseDiscussSectionConditionBo condition){
        Long casesId = condition.getCasesId();
        if(casesId == null){
            return AjaxResult.fail("校本课程案例ID不能为空");
        }
        Long parentId = condition.getParentId();
        if(parentId == null){
            condition.setParentId(0L);
        }
        List<CourseDiscussSectionVo> list = courseDiscussSectionApiService.getCourseDiscussSectionListByCondition(condition).getData();
        return AjaxResult.success(list);
    }



    /**
     * 删除课程讨论区表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-05 17:42:00
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除课程讨论区表",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "课程讨论区表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return courseDiscussSectionApiService.delete(id);
    }

}
