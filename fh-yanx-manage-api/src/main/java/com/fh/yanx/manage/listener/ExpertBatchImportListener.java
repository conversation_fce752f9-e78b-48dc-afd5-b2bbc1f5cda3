package com.fh.yanx.manage.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONArray;
import com.fh.yanx.manage.factory.SpringJobBeanFactory;
import com.fh.yanx.service.res.api.BaseDataApi;
import com.fh.yanx.service.user.entity.bo.ExpertImportExcelBo;
import com.google.common.collect.Lists;
import com.light.core.entity.AjaxResult;
import com.light.user.role.entity.vo.RoleVo;
import com.light.user.user.entity.bo.UserBo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-12-09  16:51
 */
@Slf4j
public class ExpertBatchImportListener extends AnalysisEventListener<ExpertImportExcelBo> {

    private List<ExpertImportExcelBo> dataList = Lists.newArrayList();

    @Getter
    private List<UserBo> userBos = Lists.newArrayList();

    List<String> headNames = Lists.newArrayList("填写说明： 请按照表格中的字段名称和单元格格式填写，请勿修改。","新时代专家导入模板","*姓名","*手机号","*角色","*职务");

    List<String> dynamicColumnNames = new ArrayList<>();

    @Getter
    Integer errorCount = 0;

    private BaseDataApi baseDataApi = SpringJobBeanFactory.getBean(BaseDataApi.class);

    @Override
    public void invoke(ExpertImportExcelBo data, AnalysisContext context) {
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 校验模板
        checkTemplate();
        // 转换参数
        setUserBos();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 在这里获取动态列的列名
        for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
            if (StringUtils.isNotBlank(entry.getValue())) {
                dynamicColumnNames.add(entry.getValue());
            }
        }
        log.error(JSONArray.toJSONString(dynamicColumnNames));
    }

    private void checkTemplate() {
        if (dynamicColumnNames.size() != headNames.size()) {
            throw new RuntimeException("模板错误");
        }
        // 校验模板
        for (int i = 0; i < headNames.size(); i++) {
            if (!headNames.get(i).equals(dynamicColumnNames.get(i))) {
                throw new RuntimeException("模板错误");
            }
        }
    }

    private void setUserBos() {
        try {
            AjaxResult roleResult = baseDataApi.expertRoles();
            Map<String, RoleVo> roleVoMap = new HashMap<>();
            if (roleResult.isSuccess() && roleResult.getData() != null) {
                List<RoleVo> roleVos = JSONArray.parseArray(JSONArray.toJSONString(roleResult.getData()), RoleVo.class);
                roleVoMap = roleVos.stream().collect(Collectors.toMap(RoleVo::getName, r -> r, (v1, v2) -> v1));
            }
            for (ExpertImportExcelBo excelBo : dataList) {
                if (StringUtils.isBlank(excelBo.getRealName())
                        || StringUtils.isBlank(excelBo.getPhone())
                        || StringUtils.isBlank(excelBo.getRole())
                        || StringUtils.isBlank(excelBo.getPosition())) {
                    errorCount += 1;
                    continue;
                }
                if (!roleVoMap.containsKey(excelBo.getRole())) {
                    errorCount += 1;
                    continue;
                }
                UserBo userBo = new UserBo();
                userBo.setRealName(excelBo.getRealName());
                userBo.setPhone(excelBo.getPhone());
                userBo.setRoleIds(Stream.of(roleVoMap.get(excelBo.getRole()).getId()).collect(Collectors.toList()));
                userBo.setPosition(excelBo.getPosition());
                userBos.add(userBo);
            }
        } catch (Exception e) {
            log.error("专家导入转换bo异常，e:" + e);
        }
    }
}
